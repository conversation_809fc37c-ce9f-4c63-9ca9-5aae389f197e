# Configuração Netlify para Deploy via GitHub
# Renomeie este arquivo para netlify.toml se estiver usando GitHub

[build]
  # Comando de build
  command = "npm ci && npm run build"
  
  # Diretório de publicação
  publish = "dist"
  
  # Ignorar builds se apenas README foi alterado
  ignore = "git diff --quiet $CACHED_COMMIT_REF $COMMIT_REF -- . ':!README.md' ':!*.md'"

[build.environment]
  # Versão do Node.js
  NODE_VERSION = "18"
  
  # Configurações de build
  NPM_FLAGS = "--production=false"

# Configurações de redirecionamento para SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers de segurança
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://esm.sh https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://generativelanguage.googleapis.com https://*.supabase.co wss://*.supabase.co; worker-src 'self' blob: https://cdnjs.cloudflare.com;"

# Headers específicos para arquivos estáticos
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Headers para service worker
[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "no-cache"

# Headers para PDF.js worker
[[headers]]
  for = "*.worker.js"
  [headers.values]
    Content-Type = "application/javascript"
    Cache-Control = "public, max-age=86400"

# Configurações de funções serverless
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Configurações de desenvolvimento local
[dev]
  command = "npm run dev"
  port = 5173
  publish = "dist"
  autoLaunch = false
