[build]
  # Diretório de build
  publish = "dist"

  # Comando de build
  command = "npm run build"

  # Remover base para evitar problemas de diretório

[build.environment]
  # Versão do Node.js
  NODE_VERSION = "18"
  
  # Configurações de build
  NPM_FLAGS = "--production=false"

# Configurações de redirecionamento para SPA
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers de segurança
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://esm.sh https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://generativelanguage.googleapis.com https://*.supabase.co wss://*.supabase.co; worker-src 'self' blob: https://cdnjs.cloudflare.com;"

# Headers específicos para arquivos estáticos
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Headers para service worker
[[headers]]
  for = "/sw.js"
  [headers.values]
    Cache-Control = "no-cache"

# Configurações de funções serverless (se necessário)
[functions]
  directory = "netlify/functions"
  node_bundler = "esbuild"

# Configurações de desenvolvimento local
[dev]
  command = "npm run dev"
  port = 5173
  publish = "dist"
  autoLaunch = false
