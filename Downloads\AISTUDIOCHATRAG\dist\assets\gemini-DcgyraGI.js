var V;(function(t){t.assertEqual=s=>{};function e(s){}t.assertIs=e;function n(s){throw new Error}t.assertNever=n,t.arrayToEnum=s=>{const l={};for(const a of s)l[a]=a;return l},t.getValidEnumValues=s=>{const l=t.objectKeys(s).filter(u=>typeof s[s[u]]!="number"),a={};for(const u of l)a[u]=s[u];return t.objectValues(a)},t.objectValues=s=>t.objectKeys(s).map(function(l){return s[l]}),t.objectKeys=typeof Object.keys=="function"?s=>Object.keys(s):s=>{const l=[];for(const a in s)Object.prototype.hasOwnProperty.call(s,a)&&l.push(a);return l},t.find=(s,l)=>{for(const a of s)if(l(a))return a},t.isInteger=typeof Number.isInteger=="function"?s=>Number.isInteger(s):s=>typeof s=="number"&&Number.isFinite(s)&&Math.floor(s)===s;function o(s,l=" | "){return s.map(a=>typeof a=="string"?`'${a}'`:a).join(l)}t.joinValues=o,t.jsonStringifyReplacer=(s,l)=>typeof l=="bigint"?l.toString():l})(V||(V={}));var At;(function(t){t.mergeShapes=(e,n)=>({...e,...n})})(At||(At={}));const C=V.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),ne=t=>{switch(typeof t){case"undefined":return C.undefined;case"string":return C.string;case"number":return Number.isNaN(t)?C.nan:C.number;case"boolean":return C.boolean;case"function":return C.function;case"bigint":return C.bigint;case"symbol":return C.symbol;case"object":return Array.isArray(t)?C.array:t===null?C.null:t.then&&typeof t.then=="function"&&t.catch&&typeof t.catch=="function"?C.promise:typeof Map<"u"&&t instanceof Map?C.map:typeof Set<"u"&&t instanceof Set?C.set:typeof Date<"u"&&t instanceof Date?C.date:C.object;default:return C.unknown}},v=V.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class j extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=o=>{this.issues=[...this.issues,o]},this.addIssues=(o=[])=>{this.issues=[...this.issues,...o]};const n=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,n):this.__proto__=n,this.name="ZodError",this.issues=e}format(e){const n=e||function(l){return l.message},o={_errors:[]},s=l=>{for(const a of l.issues)if(a.code==="invalid_union")a.unionErrors.map(s);else if(a.code==="invalid_return_type")s(a.returnTypeError);else if(a.code==="invalid_arguments")s(a.argumentsError);else if(a.path.length===0)o._errors.push(n(a));else{let u=o,f=0;for(;f<a.path.length;){const c=a.path[f];f===a.path.length-1?(u[c]=u[c]||{_errors:[]},u[c]._errors.push(n(a))):u[c]=u[c]||{_errors:[]},u=u[c],f++}}};return s(this),o}static assert(e){if(!(e instanceof j))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,V.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=n=>n.message){const n={},o=[];for(const s of this.issues)s.path.length>0?(n[s.path[0]]=n[s.path[0]]||[],n[s.path[0]].push(e(s))):o.push(e(s));return{formErrors:o,fieldErrors:n}}get formErrors(){return this.flatten()}}j.create=t=>new j(t);const Qe=(t,e)=>{let n;switch(t.code){case v.invalid_type:t.received===C.undefined?n="Required":n=`Expected ${t.expected}, received ${t.received}`;break;case v.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(t.expected,V.jsonStringifyReplacer)}`;break;case v.unrecognized_keys:n=`Unrecognized key(s) in object: ${V.joinValues(t.keys,", ")}`;break;case v.invalid_union:n="Invalid input";break;case v.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${V.joinValues(t.options)}`;break;case v.invalid_enum_value:n=`Invalid enum value. Expected ${V.joinValues(t.options)}, received '${t.received}'`;break;case v.invalid_arguments:n="Invalid function arguments";break;case v.invalid_return_type:n="Invalid function return type";break;case v.invalid_date:n="Invalid date";break;case v.invalid_string:typeof t.validation=="object"?"includes"in t.validation?(n=`Invalid input: must include "${t.validation.includes}"`,typeof t.validation.position=="number"&&(n=`${n} at one or more positions greater than or equal to ${t.validation.position}`)):"startsWith"in t.validation?n=`Invalid input: must start with "${t.validation.startsWith}"`:"endsWith"in t.validation?n=`Invalid input: must end with "${t.validation.endsWith}"`:V.assertNever(t.validation):t.validation!=="regex"?n=`Invalid ${t.validation}`:n="Invalid";break;case v.too_small:t.type==="array"?n=`Array must contain ${t.exact?"exactly":t.inclusive?"at least":"more than"} ${t.minimum} element(s)`:t.type==="string"?n=`String must contain ${t.exact?"exactly":t.inclusive?"at least":"over"} ${t.minimum} character(s)`:t.type==="number"?n=`Number must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${t.minimum}`:t.type==="date"?n=`Date must be ${t.exact?"exactly equal to ":t.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(t.minimum))}`:n="Invalid input";break;case v.too_big:t.type==="array"?n=`Array must contain ${t.exact?"exactly":t.inclusive?"at most":"less than"} ${t.maximum} element(s)`:t.type==="string"?n=`String must contain ${t.exact?"exactly":t.inclusive?"at most":"under"} ${t.maximum} character(s)`:t.type==="number"?n=`Number must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="bigint"?n=`BigInt must be ${t.exact?"exactly":t.inclusive?"less than or equal to":"less than"} ${t.maximum}`:t.type==="date"?n=`Date must be ${t.exact?"exactly":t.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(t.maximum))}`:n="Invalid input";break;case v.custom:n="Invalid input";break;case v.invalid_intersection_types:n="Intersection results could not be merged";break;case v.not_multiple_of:n=`Number must be a multiple of ${t.multipleOf}`;break;case v.not_finite:n="Number must be finite";break;default:n=e.defaultError,V.assertNever(t)}return{message:n}};let No=Qe;function ko(){return No}const Do=t=>{const{data:e,path:n,errorMaps:o,issueData:s}=t,l=[...n,...s.path||[]],a={...s,path:l};if(s.message!==void 0)return{...s,path:l,message:s.message};let u="";const f=o.filter(c=>!!c).slice().reverse();for(const c of f)u=c(a,{data:e,defaultError:u}).message;return{...s,path:l,message:u}};function T(t,e){const n=ko(),o=Do({issueData:e,data:t.data,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,n,n===Qe?void 0:Qe].filter(s=>!!s)});t.common.issues.push(o)}class b{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,n){const o=[];for(const s of n){if(s.status==="aborted")return w;s.status==="dirty"&&e.dirty(),o.push(s.value)}return{status:e.value,value:o}}static async mergeObjectAsync(e,n){const o=[];for(const s of n){const l=await s.key,a=await s.value;o.push({key:l,value:a})}return b.mergeObjectSync(e,o)}static mergeObjectSync(e,n){const o={};for(const s of n){const{key:l,value:a}=s;if(l.status==="aborted"||a.status==="aborted")return w;l.status==="dirty"&&e.dirty(),a.status==="dirty"&&e.dirty(),l.value!=="__proto__"&&(typeof a.value<"u"||s.alwaysSet)&&(o[l.value]=a.value)}return{status:e.value,value:o}}}const w=Object.freeze({status:"aborted"}),Me=t=>({status:"dirty",value:t}),Z=t=>({status:"valid",value:t}),Mt=t=>t.status==="aborted",It=t=>t.status==="dirty",ge=t=>t.status==="valid",Le=t=>typeof Promise<"u"&&t instanceof Promise;var _;(function(t){t.errToObj=e=>typeof e=="string"?{message:e}:e||{},t.toString=e=>typeof e=="string"?e:e?.message})(_||(_={}));class X{constructor(e,n,o,s){this._cachedPath=[],this.parent=e,this.data=n,this._path=o,this._key=s}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const xt=(t,e)=>{if(ge(e))return{success:!0,data:e.value};if(!t.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const n=new j(t.common.issues);return this._error=n,this._error}}};function k(t){if(!t)return{};const{errorMap:e,invalid_type_error:n,required_error:o,description:s}=t;if(e&&(n||o))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:s}:{errorMap:(a,u)=>{const{message:f}=t;return a.code==="invalid_enum_value"?{message:f??u.defaultError}:typeof u.data>"u"?{message:f??o??u.defaultError}:a.code!=="invalid_type"?{message:u.defaultError}:{message:f??n??u.defaultError}},description:s}}class D{get description(){return this._def.description}_getType(e){return ne(e.data)}_getOrReturnCtx(e,n){return n||{common:e.parent.common,data:e.data,parsedType:ne(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new b,ctx:{common:e.parent.common,data:e.data,parsedType:ne(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const n=this._parse(e);if(Le(n))throw new Error("Synchronous parse encountered promise.");return n}_parseAsync(e){const n=this._parse(e);return Promise.resolve(n)}parse(e,n){const o=this.safeParse(e,n);if(o.success)return o.data;throw o.error}safeParse(e,n){const o={common:{issues:[],async:n?.async??!1,contextualErrorMap:n?.errorMap},path:n?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ne(e)},s=this._parseSync({data:e,path:o.path,parent:o});return xt(o,s)}"~validate"(e){const n={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ne(e)};if(!this["~standard"].async)try{const o=this._parseSync({data:e,path:[],parent:n});return ge(o)?{value:o.value}:{issues:n.common.issues}}catch(o){o?.message?.toLowerCase()?.includes("encountered")&&(this["~standard"].async=!0),n.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:n}).then(o=>ge(o)?{value:o.value}:{issues:n.common.issues})}async parseAsync(e,n){const o=await this.safeParseAsync(e,n);if(o.success)return o.data;throw o.error}async safeParseAsync(e,n){const o={common:{issues:[],contextualErrorMap:n?.errorMap,async:!0},path:n?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ne(e)},s=this._parse({data:e,path:o.path,parent:o}),l=await(Le(s)?s:Promise.resolve(s));return xt(o,l)}refine(e,n){const o=s=>typeof n=="string"||typeof n>"u"?{message:n}:typeof n=="function"?n(s):n;return this._refinement((s,l)=>{const a=e(s),u=()=>l.addIssue({code:v.custom,...o(s)});return typeof Promise<"u"&&a instanceof Promise?a.then(f=>f?!0:(u(),!1)):a?!0:(u(),!1)})}refinement(e,n){return this._refinement((o,s)=>e(o)?!0:(s.addIssue(typeof n=="function"?n(o,s):n),!1))}_refinement(e){return new Te({schema:this,typeName:R.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:n=>this["~validate"](n)}}optional(){return oe.create(this,this._def)}nullable(){return Ce.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return O.create(this)}promise(){return $e.create(this,this._def)}or(e){return Ge.create([this,e],this._def)}and(e){return Be.create(this,e,this._def)}transform(e){return new Te({...k(this._def),schema:this,typeName:R.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const n=typeof e=="function"?e:()=>e;return new nt({...k(this._def),innerType:this,defaultValue:n,typeName:R.ZodDefault})}brand(){return new ni({typeName:R.ZodBranded,type:this,...k(this._def)})}catch(e){const n=typeof e=="function"?e:()=>e;return new ot({...k(this._def),innerType:this,catchValue:n,typeName:R.ZodCatch})}describe(e){const n=this.constructor;return new n({...this._def,description:e})}pipe(e){return gt.create(this,e)}readonly(){return it.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Vo=/^c[^\s-]{8,}$/i,Fo=/^[0-9a-z]+$/,Lo=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Uo=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Go=/^[a-z0-9_-]{21}$/i,Bo=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,qo=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,$o=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Jo="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$";let Oe;const bo=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Ho=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Wo=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Zo=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Yo=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Ko=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,to="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",zo=new RegExp(`^${to}$`);function no(t){let e="[0-5]\\d";t.precision?e=`${e}\\.\\d{${t.precision}}`:t.precision==null&&(e=`${e}(\\.\\d+)?`);const n=t.precision?"+":"?";return`([01]\\d|2[0-3]):[0-5]\\d(:${e})${n}`}function Oo(t){return new RegExp(`^${no(t)}$`)}function Xo(t){let e=`${to}T${no(t)}`;const n=[];return n.push(t.local?"Z?":"Z"),t.offset&&n.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${n.join("|")})`,new RegExp(`^${e}$`)}function Qo(t,e){return!!((e==="v4"||!e)&&bo.test(t)||(e==="v6"||!e)&&Wo.test(t))}function jo(t,e){if(!Bo.test(t))return!1;try{const[n]=t.split("."),o=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),s=JSON.parse(atob(o));return!(typeof s!="object"||s===null||"typ"in s&&s?.typ!=="JWT"||!s.alg||e&&s.alg!==e)}catch{return!1}}function ei(t,e){return!!((e==="v4"||!e)&&Ho.test(t)||(e==="v6"||!e)&&Zo.test(t))}class K extends D{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==C.string){const l=this._getOrReturnCtx(e);return T(l,{code:v.invalid_type,expected:C.string,received:l.parsedType}),w}const o=new b;let s;for(const l of this._def.checks)if(l.kind==="min")e.data.length<l.value&&(s=this._getOrReturnCtx(e,s),T(s,{code:v.too_small,minimum:l.value,type:"string",inclusive:!0,exact:!1,message:l.message}),o.dirty());else if(l.kind==="max")e.data.length>l.value&&(s=this._getOrReturnCtx(e,s),T(s,{code:v.too_big,maximum:l.value,type:"string",inclusive:!0,exact:!1,message:l.message}),o.dirty());else if(l.kind==="length"){const a=e.data.length>l.value,u=e.data.length<l.value;(a||u)&&(s=this._getOrReturnCtx(e,s),a?T(s,{code:v.too_big,maximum:l.value,type:"string",inclusive:!0,exact:!0,message:l.message}):u&&T(s,{code:v.too_small,minimum:l.value,type:"string",inclusive:!0,exact:!0,message:l.message}),o.dirty())}else if(l.kind==="email")$o.test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"email",code:v.invalid_string,message:l.message}),o.dirty());else if(l.kind==="emoji")Oe||(Oe=new RegExp(Jo,"u")),Oe.test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"emoji",code:v.invalid_string,message:l.message}),o.dirty());else if(l.kind==="uuid")Uo.test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"uuid",code:v.invalid_string,message:l.message}),o.dirty());else if(l.kind==="nanoid")Go.test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"nanoid",code:v.invalid_string,message:l.message}),o.dirty());else if(l.kind==="cuid")Vo.test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"cuid",code:v.invalid_string,message:l.message}),o.dirty());else if(l.kind==="cuid2")Fo.test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"cuid2",code:v.invalid_string,message:l.message}),o.dirty());else if(l.kind==="ulid")Lo.test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"ulid",code:v.invalid_string,message:l.message}),o.dirty());else if(l.kind==="url")try{new URL(e.data)}catch{s=this._getOrReturnCtx(e,s),T(s,{validation:"url",code:v.invalid_string,message:l.message}),o.dirty()}else l.kind==="regex"?(l.regex.lastIndex=0,l.regex.test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"regex",code:v.invalid_string,message:l.message}),o.dirty())):l.kind==="trim"?e.data=e.data.trim():l.kind==="includes"?e.data.includes(l.value,l.position)||(s=this._getOrReturnCtx(e,s),T(s,{code:v.invalid_string,validation:{includes:l.value,position:l.position},message:l.message}),o.dirty()):l.kind==="toLowerCase"?e.data=e.data.toLowerCase():l.kind==="toUpperCase"?e.data=e.data.toUpperCase():l.kind==="startsWith"?e.data.startsWith(l.value)||(s=this._getOrReturnCtx(e,s),T(s,{code:v.invalid_string,validation:{startsWith:l.value},message:l.message}),o.dirty()):l.kind==="endsWith"?e.data.endsWith(l.value)||(s=this._getOrReturnCtx(e,s),T(s,{code:v.invalid_string,validation:{endsWith:l.value},message:l.message}),o.dirty()):l.kind==="datetime"?Xo(l).test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{code:v.invalid_string,validation:"datetime",message:l.message}),o.dirty()):l.kind==="date"?zo.test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{code:v.invalid_string,validation:"date",message:l.message}),o.dirty()):l.kind==="time"?Oo(l).test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{code:v.invalid_string,validation:"time",message:l.message}),o.dirty()):l.kind==="duration"?qo.test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"duration",code:v.invalid_string,message:l.message}),o.dirty()):l.kind==="ip"?Qo(e.data,l.version)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"ip",code:v.invalid_string,message:l.message}),o.dirty()):l.kind==="jwt"?jo(e.data,l.alg)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"jwt",code:v.invalid_string,message:l.message}),o.dirty()):l.kind==="cidr"?ei(e.data,l.version)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"cidr",code:v.invalid_string,message:l.message}),o.dirty()):l.kind==="base64"?Yo.test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"base64",code:v.invalid_string,message:l.message}),o.dirty()):l.kind==="base64url"?Ko.test(e.data)||(s=this._getOrReturnCtx(e,s),T(s,{validation:"base64url",code:v.invalid_string,message:l.message}),o.dirty()):V.assertNever(l);return{status:o.value,value:e.data}}_regex(e,n,o){return this.refinement(s=>e.test(s),{validation:n,code:v.invalid_string,..._.errToObj(o)})}_addCheck(e){return new K({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",..._.errToObj(e)})}url(e){return this._addCheck({kind:"url",..._.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",..._.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",..._.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",..._.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",..._.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",..._.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",..._.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",..._.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",..._.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",..._.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",..._.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",..._.errToObj(e)})}datetime(e){return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof e?.precision>"u"?null:e?.precision,offset:e?.offset??!1,local:e?.local??!1,..._.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof e?.precision>"u"?null:e?.precision,..._.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",..._.errToObj(e)})}regex(e,n){return this._addCheck({kind:"regex",regex:e,..._.errToObj(n)})}includes(e,n){return this._addCheck({kind:"includes",value:e,position:n?.position,..._.errToObj(n?.message)})}startsWith(e,n){return this._addCheck({kind:"startsWith",value:e,..._.errToObj(n)})}endsWith(e,n){return this._addCheck({kind:"endsWith",value:e,..._.errToObj(n)})}min(e,n){return this._addCheck({kind:"min",value:e,..._.errToObj(n)})}max(e,n){return this._addCheck({kind:"max",value:e,..._.errToObj(n)})}length(e,n){return this._addCheck({kind:"length",value:e,..._.errToObj(n)})}nonempty(e){return this.min(1,_.errToObj(e))}trim(){return new K({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new K({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new K({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const n of this._def.checks)n.kind==="min"&&(e===null||n.value>e)&&(e=n.value);return e}get maxLength(){let e=null;for(const n of this._def.checks)n.kind==="max"&&(e===null||n.value<e)&&(e=n.value);return e}}K.create=t=>new K({checks:[],typeName:R.ZodString,coerce:t?.coerce??!1,...k(t)});function ti(t,e){const n=(t.toString().split(".")[1]||"").length,o=(e.toString().split(".")[1]||"").length,s=n>o?n:o,l=Number.parseInt(t.toFixed(s).replace(".","")),a=Number.parseInt(e.toFixed(s).replace(".",""));return l%a/10**s}class se extends D{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==C.number){const l=this._getOrReturnCtx(e);return T(l,{code:v.invalid_type,expected:C.number,received:l.parsedType}),w}let o;const s=new b;for(const l of this._def.checks)l.kind==="int"?V.isInteger(e.data)||(o=this._getOrReturnCtx(e,o),T(o,{code:v.invalid_type,expected:"integer",received:"float",message:l.message}),s.dirty()):l.kind==="min"?(l.inclusive?e.data<l.value:e.data<=l.value)&&(o=this._getOrReturnCtx(e,o),T(o,{code:v.too_small,minimum:l.value,type:"number",inclusive:l.inclusive,exact:!1,message:l.message}),s.dirty()):l.kind==="max"?(l.inclusive?e.data>l.value:e.data>=l.value)&&(o=this._getOrReturnCtx(e,o),T(o,{code:v.too_big,maximum:l.value,type:"number",inclusive:l.inclusive,exact:!1,message:l.message}),s.dirty()):l.kind==="multipleOf"?ti(e.data,l.value)!==0&&(o=this._getOrReturnCtx(e,o),T(o,{code:v.not_multiple_of,multipleOf:l.value,message:l.message}),s.dirty()):l.kind==="finite"?Number.isFinite(e.data)||(o=this._getOrReturnCtx(e,o),T(o,{code:v.not_finite,message:l.message}),s.dirty()):V.assertNever(l);return{status:s.value,value:e.data}}gte(e,n){return this.setLimit("min",e,!0,_.toString(n))}gt(e,n){return this.setLimit("min",e,!1,_.toString(n))}lte(e,n){return this.setLimit("max",e,!0,_.toString(n))}lt(e,n){return this.setLimit("max",e,!1,_.toString(n))}setLimit(e,n,o,s){return new se({...this._def,checks:[...this._def.checks,{kind:e,value:n,inclusive:o,message:_.toString(s)}]})}_addCheck(e){return new se({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:_.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:_.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:_.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:_.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:_.toString(e)})}multipleOf(e,n){return this._addCheck({kind:"multipleOf",value:e,message:_.toString(n)})}finite(e){return this._addCheck({kind:"finite",message:_.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:_.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:_.toString(e)})}get minValue(){let e=null;for(const n of this._def.checks)n.kind==="min"&&(e===null||n.value>e)&&(e=n.value);return e}get maxValue(){let e=null;for(const n of this._def.checks)n.kind==="max"&&(e===null||n.value<e)&&(e=n.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&V.isInteger(e.value))}get isFinite(){let e=null,n=null;for(const o of this._def.checks){if(o.kind==="finite"||o.kind==="int"||o.kind==="multipleOf")return!0;o.kind==="min"?(n===null||o.value>n)&&(n=o.value):o.kind==="max"&&(e===null||o.value<e)&&(e=o.value)}return Number.isFinite(n)&&Number.isFinite(e)}}se.create=t=>new se({checks:[],typeName:R.ZodNumber,coerce:t?.coerce||!1,...k(t)});class le extends D{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==C.bigint)return this._getInvalidInput(e);let o;const s=new b;for(const l of this._def.checks)l.kind==="min"?(l.inclusive?e.data<l.value:e.data<=l.value)&&(o=this._getOrReturnCtx(e,o),T(o,{code:v.too_small,type:"bigint",minimum:l.value,inclusive:l.inclusive,message:l.message}),s.dirty()):l.kind==="max"?(l.inclusive?e.data>l.value:e.data>=l.value)&&(o=this._getOrReturnCtx(e,o),T(o,{code:v.too_big,type:"bigint",maximum:l.value,inclusive:l.inclusive,message:l.message}),s.dirty()):l.kind==="multipleOf"?e.data%l.value!==BigInt(0)&&(o=this._getOrReturnCtx(e,o),T(o,{code:v.not_multiple_of,multipleOf:l.value,message:l.message}),s.dirty()):V.assertNever(l);return{status:s.value,value:e.data}}_getInvalidInput(e){const n=this._getOrReturnCtx(e);return T(n,{code:v.invalid_type,expected:C.bigint,received:n.parsedType}),w}gte(e,n){return this.setLimit("min",e,!0,_.toString(n))}gt(e,n){return this.setLimit("min",e,!1,_.toString(n))}lte(e,n){return this.setLimit("max",e,!0,_.toString(n))}lt(e,n){return this.setLimit("max",e,!1,_.toString(n))}setLimit(e,n,o,s){return new le({...this._def,checks:[...this._def.checks,{kind:e,value:n,inclusive:o,message:_.toString(s)}]})}_addCheck(e){return new le({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:_.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:_.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:_.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:_.toString(e)})}multipleOf(e,n){return this._addCheck({kind:"multipleOf",value:e,message:_.toString(n)})}get minValue(){let e=null;for(const n of this._def.checks)n.kind==="min"&&(e===null||n.value>e)&&(e=n.value);return e}get maxValue(){let e=null;for(const n of this._def.checks)n.kind==="max"&&(e===null||n.value<e)&&(e=n.value);return e}}le.create=t=>new le({checks:[],typeName:R.ZodBigInt,coerce:t?.coerce??!1,...k(t)});class Ue extends D{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==C.boolean){const o=this._getOrReturnCtx(e);return T(o,{code:v.invalid_type,expected:C.boolean,received:o.parsedType}),w}return Z(e.data)}}Ue.create=t=>new Ue({typeName:R.ZodBoolean,coerce:t?.coerce||!1,...k(t)});class ve extends D{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==C.date){const l=this._getOrReturnCtx(e);return T(l,{code:v.invalid_type,expected:C.date,received:l.parsedType}),w}if(Number.isNaN(e.data.getTime())){const l=this._getOrReturnCtx(e);return T(l,{code:v.invalid_date}),w}const o=new b;let s;for(const l of this._def.checks)l.kind==="min"?e.data.getTime()<l.value&&(s=this._getOrReturnCtx(e,s),T(s,{code:v.too_small,message:l.message,inclusive:!0,exact:!1,minimum:l.value,type:"date"}),o.dirty()):l.kind==="max"?e.data.getTime()>l.value&&(s=this._getOrReturnCtx(e,s),T(s,{code:v.too_big,message:l.message,inclusive:!0,exact:!1,maximum:l.value,type:"date"}),o.dirty()):V.assertNever(l);return{status:o.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ve({...this._def,checks:[...this._def.checks,e]})}min(e,n){return this._addCheck({kind:"min",value:e.getTime(),message:_.toString(n)})}max(e,n){return this._addCheck({kind:"max",value:e.getTime(),message:_.toString(n)})}get minDate(){let e=null;for(const n of this._def.checks)n.kind==="min"&&(e===null||n.value>e)&&(e=n.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const n of this._def.checks)n.kind==="max"&&(e===null||n.value<e)&&(e=n.value);return e!=null?new Date(e):null}}ve.create=t=>new ve({checks:[],coerce:t?.coerce||!1,typeName:R.ZodDate,...k(t)});class Rt extends D{_parse(e){if(this._getType(e)!==C.symbol){const o=this._getOrReturnCtx(e);return T(o,{code:v.invalid_type,expected:C.symbol,received:o.parsedType}),w}return Z(e.data)}}Rt.create=t=>new Rt({typeName:R.ZodSymbol,...k(t)});class wt extends D{_parse(e){if(this._getType(e)!==C.undefined){const o=this._getOrReturnCtx(e);return T(o,{code:v.invalid_type,expected:C.undefined,received:o.parsedType}),w}return Z(e.data)}}wt.create=t=>new wt({typeName:R.ZodUndefined,...k(t)});class Pt extends D{_parse(e){if(this._getType(e)!==C.null){const o=this._getOrReturnCtx(e);return T(o,{code:v.invalid_type,expected:C.null,received:o.parsedType}),w}return Z(e.data)}}Pt.create=t=>new Pt({typeName:R.ZodNull,...k(t)});class Nt extends D{constructor(){super(...arguments),this._any=!0}_parse(e){return Z(e.data)}}Nt.create=t=>new Nt({typeName:R.ZodAny,...k(t)});class je extends D{constructor(){super(...arguments),this._unknown=!0}_parse(e){return Z(e.data)}}je.create=t=>new je({typeName:R.ZodUnknown,...k(t)});class ie extends D{_parse(e){const n=this._getOrReturnCtx(e);return T(n,{code:v.invalid_type,expected:C.never,received:n.parsedType}),w}}ie.create=t=>new ie({typeName:R.ZodNever,...k(t)});class kt extends D{_parse(e){if(this._getType(e)!==C.undefined){const o=this._getOrReturnCtx(e);return T(o,{code:v.invalid_type,expected:C.void,received:o.parsedType}),w}return Z(e.data)}}kt.create=t=>new kt({typeName:R.ZodVoid,...k(t)});class O extends D{_parse(e){const{ctx:n,status:o}=this._processInputParams(e),s=this._def;if(n.parsedType!==C.array)return T(n,{code:v.invalid_type,expected:C.array,received:n.parsedType}),w;if(s.exactLength!==null){const a=n.data.length>s.exactLength.value,u=n.data.length<s.exactLength.value;(a||u)&&(T(n,{code:a?v.too_big:v.too_small,minimum:u?s.exactLength.value:void 0,maximum:a?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),o.dirty())}if(s.minLength!==null&&n.data.length<s.minLength.value&&(T(n,{code:v.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),o.dirty()),s.maxLength!==null&&n.data.length>s.maxLength.value&&(T(n,{code:v.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),o.dirty()),n.common.async)return Promise.all([...n.data].map((a,u)=>s.type._parseAsync(new X(n,a,n.path,u)))).then(a=>b.mergeArray(o,a));const l=[...n.data].map((a,u)=>s.type._parseSync(new X(n,a,n.path,u)));return b.mergeArray(o,l)}get element(){return this._def.type}min(e,n){return new O({...this._def,minLength:{value:e,message:_.toString(n)}})}max(e,n){return new O({...this._def,maxLength:{value:e,message:_.toString(n)}})}length(e,n){return new O({...this._def,exactLength:{value:e,message:_.toString(n)}})}nonempty(e){return this.min(1,e)}}O.create=(t,e)=>new O({type:t,minLength:null,maxLength:null,exactLength:null,typeName:R.ZodArray,...k(e)});function de(t){if(t instanceof $){const e={};for(const n in t.shape){const o=t.shape[n];e[n]=oe.create(de(o))}return new $({...t._def,shape:()=>e})}else return t instanceof O?new O({...t._def,type:de(t.element)}):t instanceof oe?oe.create(de(t.unwrap())):t instanceof Ce?Ce.create(de(t.unwrap())):t instanceof ae?ae.create(t.items.map(e=>de(e))):t}class $ extends D{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),n=V.objectKeys(e);return this._cached={shape:e,keys:n},this._cached}_parse(e){if(this._getType(e)!==C.object){const c=this._getOrReturnCtx(e);return T(c,{code:v.invalid_type,expected:C.object,received:c.parsedType}),w}const{status:o,ctx:s}=this._processInputParams(e),{shape:l,keys:a}=this._getCached(),u=[];if(!(this._def.catchall instanceof ie&&this._def.unknownKeys==="strip"))for(const c in s.data)a.includes(c)||u.push(c);const f=[];for(const c of a){const d=l[c],p=s.data[c];f.push({key:{status:"valid",value:c},value:d._parse(new X(s,p,s.path,c)),alwaysSet:c in s.data})}if(this._def.catchall instanceof ie){const c=this._def.unknownKeys;if(c==="passthrough")for(const d of u)f.push({key:{status:"valid",value:d},value:{status:"valid",value:s.data[d]}});else if(c==="strict")u.length>0&&(T(s,{code:v.unrecognized_keys,keys:u}),o.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const d of u){const p=s.data[d];f.push({key:{status:"valid",value:d},value:c._parse(new X(s,p,s.path,d)),alwaysSet:d in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const c=[];for(const d of f){const p=await d.key,m=await d.value;c.push({key:p,value:m,alwaysSet:d.alwaysSet})}return c}).then(c=>b.mergeObjectSync(o,c)):b.mergeObjectSync(o,f)}get shape(){return this._def.shape()}strict(e){return _.errToObj,new $({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(n,o)=>{const s=this._def.errorMap?.(n,o).message??o.defaultError;return n.code==="unrecognized_keys"?{message:_.errToObj(e).message??s}:{message:s}}}:{}})}strip(){return new $({...this._def,unknownKeys:"strip"})}passthrough(){return new $({...this._def,unknownKeys:"passthrough"})}extend(e){return new $({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new $({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:R.ZodObject})}setKey(e,n){return this.augment({[e]:n})}catchall(e){return new $({...this._def,catchall:e})}pick(e){const n={};for(const o of V.objectKeys(e))e[o]&&this.shape[o]&&(n[o]=this.shape[o]);return new $({...this._def,shape:()=>n})}omit(e){const n={};for(const o of V.objectKeys(this.shape))e[o]||(n[o]=this.shape[o]);return new $({...this._def,shape:()=>n})}deepPartial(){return de(this)}partial(e){const n={};for(const o of V.objectKeys(this.shape)){const s=this.shape[o];e&&!e[o]?n[o]=s:n[o]=s.optional()}return new $({...this._def,shape:()=>n})}required(e){const n={};for(const o of V.objectKeys(this.shape))if(e&&!e[o])n[o]=this.shape[o];else{let l=this.shape[o];for(;l instanceof oe;)l=l._def.innerType;n[o]=l}return new $({...this._def,shape:()=>n})}keyof(){return oo(V.objectKeys(this.shape))}}$.create=(t,e)=>new $({shape:()=>t,unknownKeys:"strip",catchall:ie.create(),typeName:R.ZodObject,...k(e)});$.strictCreate=(t,e)=>new $({shape:()=>t,unknownKeys:"strict",catchall:ie.create(),typeName:R.ZodObject,...k(e)});$.lazycreate=(t,e)=>new $({shape:t,unknownKeys:"strip",catchall:ie.create(),typeName:R.ZodObject,...k(e)});class Ge extends D{_parse(e){const{ctx:n}=this._processInputParams(e),o=this._def.options;function s(l){for(const u of l)if(u.result.status==="valid")return u.result;for(const u of l)if(u.result.status==="dirty")return n.common.issues.push(...u.ctx.common.issues),u.result;const a=l.map(u=>new j(u.ctx.common.issues));return T(n,{code:v.invalid_union,unionErrors:a}),w}if(n.common.async)return Promise.all(o.map(async l=>{const a={...n,common:{...n.common,issues:[]},parent:null};return{result:await l._parseAsync({data:n.data,path:n.path,parent:a}),ctx:a}})).then(s);{let l;const a=[];for(const f of o){const c={...n,common:{...n.common,issues:[]},parent:null},d=f._parseSync({data:n.data,path:n.path,parent:c});if(d.status==="valid")return d;d.status==="dirty"&&!l&&(l={result:d,ctx:c}),c.common.issues.length&&a.push(c.common.issues)}if(l)return n.common.issues.push(...l.ctx.common.issues),l.result;const u=a.map(f=>new j(f));return T(n,{code:v.invalid_union,unionErrors:u}),w}}get options(){return this._def.options}}Ge.create=(t,e)=>new Ge({options:t,typeName:R.ZodUnion,...k(e)});function et(t,e){const n=ne(t),o=ne(e);if(t===e)return{valid:!0,data:t};if(n===C.object&&o===C.object){const s=V.objectKeys(e),l=V.objectKeys(t).filter(u=>s.indexOf(u)!==-1),a={...t,...e};for(const u of l){const f=et(t[u],e[u]);if(!f.valid)return{valid:!1};a[u]=f.data}return{valid:!0,data:a}}else if(n===C.array&&o===C.array){if(t.length!==e.length)return{valid:!1};const s=[];for(let l=0;l<t.length;l++){const a=t[l],u=e[l],f=et(a,u);if(!f.valid)return{valid:!1};s.push(f.data)}return{valid:!0,data:s}}else return n===C.date&&o===C.date&&+t==+e?{valid:!0,data:t}:{valid:!1}}class Be extends D{_parse(e){const{status:n,ctx:o}=this._processInputParams(e),s=(l,a)=>{if(Mt(l)||Mt(a))return w;const u=et(l.value,a.value);return u.valid?((It(l)||It(a))&&n.dirty(),{status:n.value,value:u.data}):(T(o,{code:v.invalid_intersection_types}),w)};return o.common.async?Promise.all([this._def.left._parseAsync({data:o.data,path:o.path,parent:o}),this._def.right._parseAsync({data:o.data,path:o.path,parent:o})]).then(([l,a])=>s(l,a)):s(this._def.left._parseSync({data:o.data,path:o.path,parent:o}),this._def.right._parseSync({data:o.data,path:o.path,parent:o}))}}Be.create=(t,e,n)=>new Be({left:t,right:e,typeName:R.ZodIntersection,...k(n)});class ae extends D{_parse(e){const{status:n,ctx:o}=this._processInputParams(e);if(o.parsedType!==C.array)return T(o,{code:v.invalid_type,expected:C.array,received:o.parsedType}),w;if(o.data.length<this._def.items.length)return T(o,{code:v.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),w;!this._def.rest&&o.data.length>this._def.items.length&&(T(o,{code:v.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),n.dirty());const l=[...o.data].map((a,u)=>{const f=this._def.items[u]||this._def.rest;return f?f._parse(new X(o,a,o.path,u)):null}).filter(a=>!!a);return o.common.async?Promise.all(l).then(a=>b.mergeArray(n,a)):b.mergeArray(n,l)}get items(){return this._def.items}rest(e){return new ae({...this._def,rest:e})}}ae.create=(t,e)=>{if(!Array.isArray(t))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new ae({items:t,typeName:R.ZodTuple,rest:null,...k(e)})};class qe extends D{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:n,ctx:o}=this._processInputParams(e);if(o.parsedType!==C.object)return T(o,{code:v.invalid_type,expected:C.object,received:o.parsedType}),w;const s=[],l=this._def.keyType,a=this._def.valueType;for(const u in o.data)s.push({key:l._parse(new X(o,u,o.path,u)),value:a._parse(new X(o,o.data[u],o.path,u)),alwaysSet:u in o.data});return o.common.async?b.mergeObjectAsync(n,s):b.mergeObjectSync(n,s)}get element(){return this._def.valueType}static create(e,n,o){return n instanceof D?new qe({keyType:e,valueType:n,typeName:R.ZodRecord,...k(o)}):new qe({keyType:K.create(),valueType:e,typeName:R.ZodRecord,...k(n)})}}class Dt extends D{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:n,ctx:o}=this._processInputParams(e);if(o.parsedType!==C.map)return T(o,{code:v.invalid_type,expected:C.map,received:o.parsedType}),w;const s=this._def.keyType,l=this._def.valueType,a=[...o.data.entries()].map(([u,f],c)=>({key:s._parse(new X(o,u,o.path,[c,"key"])),value:l._parse(new X(o,f,o.path,[c,"value"]))}));if(o.common.async){const u=new Map;return Promise.resolve().then(async()=>{for(const f of a){const c=await f.key,d=await f.value;if(c.status==="aborted"||d.status==="aborted")return w;(c.status==="dirty"||d.status==="dirty")&&n.dirty(),u.set(c.value,d.value)}return{status:n.value,value:u}})}else{const u=new Map;for(const f of a){const c=f.key,d=f.value;if(c.status==="aborted"||d.status==="aborted")return w;(c.status==="dirty"||d.status==="dirty")&&n.dirty(),u.set(c.value,d.value)}return{status:n.value,value:u}}}}Dt.create=(t,e,n)=>new Dt({valueType:e,keyType:t,typeName:R.ZodMap,...k(n)});class Re extends D{_parse(e){const{status:n,ctx:o}=this._processInputParams(e);if(o.parsedType!==C.set)return T(o,{code:v.invalid_type,expected:C.set,received:o.parsedType}),w;const s=this._def;s.minSize!==null&&o.data.size<s.minSize.value&&(T(o,{code:v.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),n.dirty()),s.maxSize!==null&&o.data.size>s.maxSize.value&&(T(o,{code:v.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),n.dirty());const l=this._def.valueType;function a(f){const c=new Set;for(const d of f){if(d.status==="aborted")return w;d.status==="dirty"&&n.dirty(),c.add(d.value)}return{status:n.value,value:c}}const u=[...o.data.values()].map((f,c)=>l._parse(new X(o,f,o.path,c)));return o.common.async?Promise.all(u).then(f=>a(f)):a(u)}min(e,n){return new Re({...this._def,minSize:{value:e,message:_.toString(n)}})}max(e,n){return new Re({...this._def,maxSize:{value:e,message:_.toString(n)}})}size(e,n){return this.min(e,n).max(e,n)}nonempty(e){return this.min(1,e)}}Re.create=(t,e)=>new Re({valueType:t,minSize:null,maxSize:null,typeName:R.ZodSet,...k(e)});class tt extends D{get schema(){return this._def.getter()}_parse(e){const{ctx:n}=this._processInputParams(e);return this._def.getter()._parse({data:n.data,path:n.path,parent:n})}}tt.create=(t,e)=>new tt({getter:t,typeName:R.ZodLazy,...k(e)});class Vt extends D{_parse(e){if(e.data!==this._def.value){const n=this._getOrReturnCtx(e);return T(n,{received:n.data,code:v.invalid_literal,expected:this._def.value}),w}return{status:"valid",value:e.data}}get value(){return this._def.value}}Vt.create=(t,e)=>new Vt({value:t,typeName:R.ZodLiteral,...k(e)});function oo(t,e){return new ye({values:t,typeName:R.ZodEnum,...k(e)})}class ye extends D{_parse(e){if(typeof e.data!="string"){const n=this._getOrReturnCtx(e),o=this._def.values;return T(n,{expected:V.joinValues(o),received:n.parsedType,code:v.invalid_type}),w}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const n=this._getOrReturnCtx(e),o=this._def.values;return T(n,{received:n.data,code:v.invalid_enum_value,options:o}),w}return Z(e.data)}get options(){return this._def.values}get enum(){const e={};for(const n of this._def.values)e[n]=n;return e}get Values(){const e={};for(const n of this._def.values)e[n]=n;return e}get Enum(){const e={};for(const n of this._def.values)e[n]=n;return e}extract(e,n=this._def){return ye.create(e,{...this._def,...n})}exclude(e,n=this._def){return ye.create(this.options.filter(o=>!e.includes(o)),{...this._def,...n})}}ye.create=oo;class Ft extends D{_parse(e){const n=V.getValidEnumValues(this._def.values),o=this._getOrReturnCtx(e);if(o.parsedType!==C.string&&o.parsedType!==C.number){const s=V.objectValues(n);return T(o,{expected:V.joinValues(s),received:o.parsedType,code:v.invalid_type}),w}if(this._cache||(this._cache=new Set(V.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const s=V.objectValues(n);return T(o,{received:o.data,code:v.invalid_enum_value,options:s}),w}return Z(e.data)}get enum(){return this._def.values}}Ft.create=(t,e)=>new Ft({values:t,typeName:R.ZodNativeEnum,...k(e)});class $e extends D{unwrap(){return this._def.type}_parse(e){const{ctx:n}=this._processInputParams(e);if(n.parsedType!==C.promise&&n.common.async===!1)return T(n,{code:v.invalid_type,expected:C.promise,received:n.parsedType}),w;const o=n.parsedType===C.promise?n.data:Promise.resolve(n.data);return Z(o.then(s=>this._def.type.parseAsync(s,{path:n.path,errorMap:n.common.contextualErrorMap})))}}$e.create=(t,e)=>new $e({type:t,typeName:R.ZodPromise,...k(e)});class Te extends D{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===R.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:n,ctx:o}=this._processInputParams(e),s=this._def.effect||null,l={addIssue:a=>{T(o,a),a.fatal?n.abort():n.dirty()},get path(){return o.path}};if(l.addIssue=l.addIssue.bind(l),s.type==="preprocess"){const a=s.transform(o.data,l);if(o.common.async)return Promise.resolve(a).then(async u=>{if(n.value==="aborted")return w;const f=await this._def.schema._parseAsync({data:u,path:o.path,parent:o});return f.status==="aborted"?w:f.status==="dirty"||n.value==="dirty"?Me(f.value):f});{if(n.value==="aborted")return w;const u=this._def.schema._parseSync({data:a,path:o.path,parent:o});return u.status==="aborted"?w:u.status==="dirty"||n.value==="dirty"?Me(u.value):u}}if(s.type==="refinement"){const a=u=>{const f=s.refinement(u,l);if(o.common.async)return Promise.resolve(f);if(f instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return u};if(o.common.async===!1){const u=this._def.schema._parseSync({data:o.data,path:o.path,parent:o});return u.status==="aborted"?w:(u.status==="dirty"&&n.dirty(),a(u.value),{status:n.value,value:u.value})}else return this._def.schema._parseAsync({data:o.data,path:o.path,parent:o}).then(u=>u.status==="aborted"?w:(u.status==="dirty"&&n.dirty(),a(u.value).then(()=>({status:n.value,value:u.value}))))}if(s.type==="transform")if(o.common.async===!1){const a=this._def.schema._parseSync({data:o.data,path:o.path,parent:o});if(!ge(a))return w;const u=s.transform(a.value,l);if(u instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:n.value,value:u}}else return this._def.schema._parseAsync({data:o.data,path:o.path,parent:o}).then(a=>ge(a)?Promise.resolve(s.transform(a.value,l)).then(u=>({status:n.value,value:u})):w);V.assertNever(s)}}Te.create=(t,e,n)=>new Te({schema:t,typeName:R.ZodEffects,effect:e,...k(n)});Te.createWithPreprocess=(t,e,n)=>new Te({schema:e,effect:{type:"preprocess",transform:t},typeName:R.ZodEffects,...k(n)});class oe extends D{_parse(e){return this._getType(e)===C.undefined?Z(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}oe.create=(t,e)=>new oe({innerType:t,typeName:R.ZodOptional,...k(e)});class Ce extends D{_parse(e){return this._getType(e)===C.null?Z(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Ce.create=(t,e)=>new Ce({innerType:t,typeName:R.ZodNullable,...k(e)});class nt extends D{_parse(e){const{ctx:n}=this._processInputParams(e);let o=n.data;return n.parsedType===C.undefined&&(o=this._def.defaultValue()),this._def.innerType._parse({data:o,path:n.path,parent:n})}removeDefault(){return this._def.innerType}}nt.create=(t,e)=>new nt({innerType:t,typeName:R.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...k(e)});class ot extends D{_parse(e){const{ctx:n}=this._processInputParams(e),o={...n,common:{...n.common,issues:[]}},s=this._def.innerType._parse({data:o.data,path:o.path,parent:{...o}});return Le(s)?s.then(l=>({status:"valid",value:l.status==="valid"?l.value:this._def.catchValue({get error(){return new j(o.common.issues)},input:o.data})})):{status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new j(o.common.issues)},input:o.data})}}removeCatch(){return this._def.innerType}}ot.create=(t,e)=>new ot({innerType:t,typeName:R.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...k(e)});class Lt extends D{_parse(e){if(this._getType(e)!==C.nan){const o=this._getOrReturnCtx(e);return T(o,{code:v.invalid_type,expected:C.nan,received:o.parsedType}),w}return{status:"valid",value:e.data}}}Lt.create=t=>new Lt({typeName:R.ZodNaN,...k(t)});class ni extends D{_parse(e){const{ctx:n}=this._processInputParams(e),o=n.data;return this._def.type._parse({data:o,path:n.path,parent:n})}unwrap(){return this._def.type}}class gt extends D{_parse(e){const{status:n,ctx:o}=this._processInputParams(e);if(o.common.async)return(async()=>{const l=await this._def.in._parseAsync({data:o.data,path:o.path,parent:o});return l.status==="aborted"?w:l.status==="dirty"?(n.dirty(),Me(l.value)):this._def.out._parseAsync({data:l.value,path:o.path,parent:o})})();{const s=this._def.in._parseSync({data:o.data,path:o.path,parent:o});return s.status==="aborted"?w:s.status==="dirty"?(n.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:o.path,parent:o})}}static create(e,n){return new gt({in:e,out:n,typeName:R.ZodPipeline})}}class it extends D{_parse(e){const n=this._def.innerType._parse(e),o=s=>(ge(s)&&(s.value=Object.freeze(s.value)),s);return Le(n)?n.then(s=>o(s)):o(n)}unwrap(){return this._def.innerType}}it.create=(t,e)=>new it({innerType:t,typeName:R.ZodReadonly,...k(e)});var R;(function(t){t.ZodString="ZodString",t.ZodNumber="ZodNumber",t.ZodNaN="ZodNaN",t.ZodBigInt="ZodBigInt",t.ZodBoolean="ZodBoolean",t.ZodDate="ZodDate",t.ZodSymbol="ZodSymbol",t.ZodUndefined="ZodUndefined",t.ZodNull="ZodNull",t.ZodAny="ZodAny",t.ZodUnknown="ZodUnknown",t.ZodNever="ZodNever",t.ZodVoid="ZodVoid",t.ZodArray="ZodArray",t.ZodObject="ZodObject",t.ZodUnion="ZodUnion",t.ZodDiscriminatedUnion="ZodDiscriminatedUnion",t.ZodIntersection="ZodIntersection",t.ZodTuple="ZodTuple",t.ZodRecord="ZodRecord",t.ZodMap="ZodMap",t.ZodSet="ZodSet",t.ZodFunction="ZodFunction",t.ZodLazy="ZodLazy",t.ZodLiteral="ZodLiteral",t.ZodEnum="ZodEnum",t.ZodEffects="ZodEffects",t.ZodNativeEnum="ZodNativeEnum",t.ZodOptional="ZodOptional",t.ZodNullable="ZodNullable",t.ZodDefault="ZodDefault",t.ZodCatch="ZodCatch",t.ZodPromise="ZodPromise",t.ZodBranded="ZodBranded",t.ZodPipeline="ZodPipeline",t.ZodReadonly="ZodReadonly"})(R||(R={}));const re=K.create,Ut=se.create;le.create;const oi=Ue.create;ve.create;const Gt=je.create;ie.create;const Ie=O.create,ii=$.create,ri=Ge.create;Be.create;ae.create;const si=qe.create,li=tt.create,ai=ye.create;$e.create;oe.create;Ce.create;const fe={string:t=>K.create({...t,coerce:!0}),number:t=>se.create({...t,coerce:!0}),boolean:t=>Ue.create({...t,coerce:!0}),bigint:t=>le.create({...t,coerce:!0}),date:t=>ve.create({...t,coerce:!0})};/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */let ui,ci;function fi(){return{geminiUrl:ui,vertexUrl:ci}}function di(t,e,n){var o,s,l;if(!(!((o=t.httpOptions)===null||o===void 0)&&o.baseUrl)){const a=fi();return t.vertexai?(s=a.vertexUrl)!==null&&s!==void 0?s:e:(l=a.geminiUrl)!==null&&l!==void 0?l:n}return t.httpOptions.baseUrl}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class ce{}function E(t,e){const n=/\{([^}]+)\}/g;return t.replace(n,(o,s)=>{if(Object.prototype.hasOwnProperty.call(e,s)){const l=e[s];return l!=null?String(l):""}else throw new Error(`Key '${s}' not found in valueMap.`)})}function r(t,e,n){for(let l=0;l<e.length-1;l++){const a=e[l];if(a.endsWith("[]")){const u=a.slice(0,-2);if(!(u in t))if(Array.isArray(n))t[u]=Array.from({length:n.length},()=>({}));else throw new Error(`Value must be a list given an array path ${a}`);if(Array.isArray(t[u])){const f=t[u];if(Array.isArray(n))for(let c=0;c<f.length;c++){const d=f[c];r(d,e.slice(l+1),n[c])}else for(const c of f)r(c,e.slice(l+1),n)}return}else if(a.endsWith("[0]")){const u=a.slice(0,-3);u in t||(t[u]=[{}]);const f=t[u];r(f[0],e.slice(l+1),n);return}(!t[a]||typeof t[a]!="object")&&(t[a]={}),t=t[a]}const o=e[e.length-1],s=t[o];if(s!==void 0){if(!n||typeof n=="object"&&Object.keys(n).length===0||n===s)return;if(typeof s=="object"&&typeof n=="object"&&s!==null&&n!==null)Object.assign(s,n);else throw new Error(`Cannot set value for an existing key. Key: ${o}`)}else t[o]=n}function i(t,e){try{if(e.length===1&&e[0]==="_self")return t;for(let n=0;n<e.length;n++){if(typeof t!="object"||t===null)return;const o=e[n];if(o.endsWith("[]")){const s=o.slice(0,-2);if(s in t){const l=t[s];return Array.isArray(l)?l.map(a=>i(a,e.slice(n+1))):void 0}else return}else t=t[o]}return t}catch(n){if(n instanceof TypeError)return;throw n}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */var Bt;(function(t){t.OUTCOME_UNSPECIFIED="OUTCOME_UNSPECIFIED",t.OUTCOME_OK="OUTCOME_OK",t.OUTCOME_FAILED="OUTCOME_FAILED",t.OUTCOME_DEADLINE_EXCEEDED="OUTCOME_DEADLINE_EXCEEDED"})(Bt||(Bt={}));var qt;(function(t){t.LANGUAGE_UNSPECIFIED="LANGUAGE_UNSPECIFIED",t.PYTHON="PYTHON"})(qt||(qt={}));var z;(function(t){t.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",t.STRING="STRING",t.NUMBER="NUMBER",t.INTEGER="INTEGER",t.BOOLEAN="BOOLEAN",t.ARRAY="ARRAY",t.OBJECT="OBJECT",t.NULL="NULL"})(z||(z={}));var $t;(function(t){t.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",t.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",t.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",t.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",t.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",t.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY"})($t||($t={}));var Jt;(function(t){t.HARM_BLOCK_METHOD_UNSPECIFIED="HARM_BLOCK_METHOD_UNSPECIFIED",t.SEVERITY="SEVERITY",t.PROBABILITY="PROBABILITY"})(Jt||(Jt={}));var bt;(function(t){t.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",t.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",t.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",t.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",t.BLOCK_NONE="BLOCK_NONE",t.OFF="OFF"})(bt||(bt={}));var Ht;(function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.MODE_DYNAMIC="MODE_DYNAMIC"})(Ht||(Ht={}));var Wt;(function(t){t.AUTH_TYPE_UNSPECIFIED="AUTH_TYPE_UNSPECIFIED",t.NO_AUTH="NO_AUTH",t.API_KEY_AUTH="API_KEY_AUTH",t.HTTP_BASIC_AUTH="HTTP_BASIC_AUTH",t.GOOGLE_SERVICE_ACCOUNT_AUTH="GOOGLE_SERVICE_ACCOUNT_AUTH",t.OAUTH="OAUTH",t.OIDC_AUTH="OIDC_AUTH"})(Wt||(Wt={}));var Zt;(function(t){t.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",t.STOP="STOP",t.MAX_TOKENS="MAX_TOKENS",t.SAFETY="SAFETY",t.RECITATION="RECITATION",t.LANGUAGE="LANGUAGE",t.OTHER="OTHER",t.BLOCKLIST="BLOCKLIST",t.PROHIBITED_CONTENT="PROHIBITED_CONTENT",t.SPII="SPII",t.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",t.IMAGE_SAFETY="IMAGE_SAFETY",t.UNEXPECTED_TOOL_CALL="UNEXPECTED_TOOL_CALL"})(Zt||(Zt={}));var Yt;(function(t){t.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",t.NEGLIGIBLE="NEGLIGIBLE",t.LOW="LOW",t.MEDIUM="MEDIUM",t.HIGH="HIGH"})(Yt||(Yt={}));var Kt;(function(t){t.HARM_SEVERITY_UNSPECIFIED="HARM_SEVERITY_UNSPECIFIED",t.HARM_SEVERITY_NEGLIGIBLE="HARM_SEVERITY_NEGLIGIBLE",t.HARM_SEVERITY_LOW="HARM_SEVERITY_LOW",t.HARM_SEVERITY_MEDIUM="HARM_SEVERITY_MEDIUM",t.HARM_SEVERITY_HIGH="HARM_SEVERITY_HIGH"})(Kt||(Kt={}));var zt;(function(t){t.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",t.SAFETY="SAFETY",t.OTHER="OTHER",t.BLOCKLIST="BLOCKLIST",t.PROHIBITED_CONTENT="PROHIBITED_CONTENT"})(zt||(zt={}));var Ot;(function(t){t.TRAFFIC_TYPE_UNSPECIFIED="TRAFFIC_TYPE_UNSPECIFIED",t.ON_DEMAND="ON_DEMAND",t.PROVISIONED_THROUGHPUT="PROVISIONED_THROUGHPUT"})(Ot||(Ot={}));var Je;(function(t){t.MODALITY_UNSPECIFIED="MODALITY_UNSPECIFIED",t.TEXT="TEXT",t.IMAGE="IMAGE",t.AUDIO="AUDIO"})(Je||(Je={}));var Xt;(function(t){t.MEDIA_RESOLUTION_UNSPECIFIED="MEDIA_RESOLUTION_UNSPECIFIED",t.MEDIA_RESOLUTION_LOW="MEDIA_RESOLUTION_LOW",t.MEDIA_RESOLUTION_MEDIUM="MEDIA_RESOLUTION_MEDIUM",t.MEDIA_RESOLUTION_HIGH="MEDIA_RESOLUTION_HIGH"})(Xt||(Xt={}));var rt;(function(t){t.JOB_STATE_UNSPECIFIED="JOB_STATE_UNSPECIFIED",t.JOB_STATE_QUEUED="JOB_STATE_QUEUED",t.JOB_STATE_PENDING="JOB_STATE_PENDING",t.JOB_STATE_RUNNING="JOB_STATE_RUNNING",t.JOB_STATE_SUCCEEDED="JOB_STATE_SUCCEEDED",t.JOB_STATE_FAILED="JOB_STATE_FAILED",t.JOB_STATE_CANCELLING="JOB_STATE_CANCELLING",t.JOB_STATE_CANCELLED="JOB_STATE_CANCELLED",t.JOB_STATE_PAUSED="JOB_STATE_PAUSED",t.JOB_STATE_EXPIRED="JOB_STATE_EXPIRED",t.JOB_STATE_UPDATING="JOB_STATE_UPDATING",t.JOB_STATE_PARTIALLY_SUCCEEDED="JOB_STATE_PARTIALLY_SUCCEEDED"})(rt||(rt={}));var Qt;(function(t){t.ADAPTER_SIZE_UNSPECIFIED="ADAPTER_SIZE_UNSPECIFIED",t.ADAPTER_SIZE_ONE="ADAPTER_SIZE_ONE",t.ADAPTER_SIZE_TWO="ADAPTER_SIZE_TWO",t.ADAPTER_SIZE_FOUR="ADAPTER_SIZE_FOUR",t.ADAPTER_SIZE_EIGHT="ADAPTER_SIZE_EIGHT",t.ADAPTER_SIZE_SIXTEEN="ADAPTER_SIZE_SIXTEEN",t.ADAPTER_SIZE_THIRTY_TWO="ADAPTER_SIZE_THIRTY_TWO"})(Qt||(Qt={}));var jt;(function(t){t.FEATURE_SELECTION_PREFERENCE_UNSPECIFIED="FEATURE_SELECTION_PREFERENCE_UNSPECIFIED",t.PRIORITIZE_QUALITY="PRIORITIZE_QUALITY",t.BALANCED="BALANCED",t.PRIORITIZE_COST="PRIORITIZE_COST"})(jt||(jt={}));var en;(function(t){t.UNSPECIFIED="UNSPECIFIED",t.BLOCKING="BLOCKING",t.NON_BLOCKING="NON_BLOCKING"})(en||(en={}));var tn;(function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.MODE_DYNAMIC="MODE_DYNAMIC"})(tn||(tn={}));var nn;(function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.AUTO="AUTO",t.ANY="ANY",t.NONE="NONE"})(nn||(nn={}));var on;(function(t){t.URL_RETRIEVAL_STATUS_UNSPECIFIED="URL_RETRIEVAL_STATUS_UNSPECIFIED",t.URL_RETRIEVAL_STATUS_SUCCESS="URL_RETRIEVAL_STATUS_SUCCESS",t.URL_RETRIEVAL_STATUS_ERROR="URL_RETRIEVAL_STATUS_ERROR"})(on||(on={}));var rn;(function(t){t.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",t.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",t.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",t.BLOCK_NONE="BLOCK_NONE"})(rn||(rn={}));var sn;(function(t){t.DONT_ALLOW="DONT_ALLOW",t.ALLOW_ADULT="ALLOW_ADULT",t.ALLOW_ALL="ALLOW_ALL"})(sn||(sn={}));var ln;(function(t){t.auto="auto",t.en="en",t.ja="ja",t.ko="ko",t.hi="hi"})(ln||(ln={}));var an;(function(t){t.MASK_MODE_DEFAULT="MASK_MODE_DEFAULT",t.MASK_MODE_USER_PROVIDED="MASK_MODE_USER_PROVIDED",t.MASK_MODE_BACKGROUND="MASK_MODE_BACKGROUND",t.MASK_MODE_FOREGROUND="MASK_MODE_FOREGROUND",t.MASK_MODE_SEMANTIC="MASK_MODE_SEMANTIC"})(an||(an={}));var un;(function(t){t.CONTROL_TYPE_DEFAULT="CONTROL_TYPE_DEFAULT",t.CONTROL_TYPE_CANNY="CONTROL_TYPE_CANNY",t.CONTROL_TYPE_SCRIBBLE="CONTROL_TYPE_SCRIBBLE",t.CONTROL_TYPE_FACE_MESH="CONTROL_TYPE_FACE_MESH"})(un||(un={}));var cn;(function(t){t.SUBJECT_TYPE_DEFAULT="SUBJECT_TYPE_DEFAULT",t.SUBJECT_TYPE_PERSON="SUBJECT_TYPE_PERSON",t.SUBJECT_TYPE_ANIMAL="SUBJECT_TYPE_ANIMAL",t.SUBJECT_TYPE_PRODUCT="SUBJECT_TYPE_PRODUCT"})(cn||(cn={}));var fn;(function(t){t.EDIT_MODE_DEFAULT="EDIT_MODE_DEFAULT",t.EDIT_MODE_INPAINT_REMOVAL="EDIT_MODE_INPAINT_REMOVAL",t.EDIT_MODE_INPAINT_INSERTION="EDIT_MODE_INPAINT_INSERTION",t.EDIT_MODE_OUTPAINT="EDIT_MODE_OUTPAINT",t.EDIT_MODE_CONTROLLED_EDITING="EDIT_MODE_CONTROLLED_EDITING",t.EDIT_MODE_STYLE="EDIT_MODE_STYLE",t.EDIT_MODE_BGSWAP="EDIT_MODE_BGSWAP",t.EDIT_MODE_PRODUCT_IMAGE="EDIT_MODE_PRODUCT_IMAGE"})(fn||(fn={}));var dn;(function(t){t.OPTIMIZED="OPTIMIZED",t.LOSSLESS="LOSSLESS"})(dn||(dn={}));var pn;(function(t){t.STATE_UNSPECIFIED="STATE_UNSPECIFIED",t.PROCESSING="PROCESSING",t.ACTIVE="ACTIVE",t.FAILED="FAILED"})(pn||(pn={}));var mn;(function(t){t.SOURCE_UNSPECIFIED="SOURCE_UNSPECIFIED",t.UPLOADED="UPLOADED",t.GENERATED="GENERATED"})(mn||(mn={}));var hn;(function(t){t.MODALITY_UNSPECIFIED="MODALITY_UNSPECIFIED",t.TEXT="TEXT",t.IMAGE="IMAGE",t.VIDEO="VIDEO",t.AUDIO="AUDIO",t.DOCUMENT="DOCUMENT"})(hn||(hn={}));var gn;(function(t){t.START_SENSITIVITY_UNSPECIFIED="START_SENSITIVITY_UNSPECIFIED",t.START_SENSITIVITY_HIGH="START_SENSITIVITY_HIGH",t.START_SENSITIVITY_LOW="START_SENSITIVITY_LOW"})(gn||(gn={}));var vn;(function(t){t.END_SENSITIVITY_UNSPECIFIED="END_SENSITIVITY_UNSPECIFIED",t.END_SENSITIVITY_HIGH="END_SENSITIVITY_HIGH",t.END_SENSITIVITY_LOW="END_SENSITIVITY_LOW"})(vn||(vn={}));var yn;(function(t){t.ACTIVITY_HANDLING_UNSPECIFIED="ACTIVITY_HANDLING_UNSPECIFIED",t.START_OF_ACTIVITY_INTERRUPTS="START_OF_ACTIVITY_INTERRUPTS",t.NO_INTERRUPTION="NO_INTERRUPTION"})(yn||(yn={}));var Tn;(function(t){t.TURN_COVERAGE_UNSPECIFIED="TURN_COVERAGE_UNSPECIFIED",t.TURN_INCLUDES_ONLY_ACTIVITY="TURN_INCLUDES_ONLY_ACTIVITY",t.TURN_INCLUDES_ALL_INPUT="TURN_INCLUDES_ALL_INPUT"})(Tn||(Tn={}));var Cn;(function(t){t.SCHEDULING_UNSPECIFIED="SCHEDULING_UNSPECIFIED",t.SILENT="SILENT",t.WHEN_IDLE="WHEN_IDLE",t.INTERRUPT="INTERRUPT"})(Cn||(Cn={}));var _n;(function(t){t.SCALE_UNSPECIFIED="SCALE_UNSPECIFIED",t.C_MAJOR_A_MINOR="C_MAJOR_A_MINOR",t.D_FLAT_MAJOR_B_FLAT_MINOR="D_FLAT_MAJOR_B_FLAT_MINOR",t.D_MAJOR_B_MINOR="D_MAJOR_B_MINOR",t.E_FLAT_MAJOR_C_MINOR="E_FLAT_MAJOR_C_MINOR",t.E_MAJOR_D_FLAT_MINOR="E_MAJOR_D_FLAT_MINOR",t.F_MAJOR_D_MINOR="F_MAJOR_D_MINOR",t.G_FLAT_MAJOR_E_FLAT_MINOR="G_FLAT_MAJOR_E_FLAT_MINOR",t.G_MAJOR_E_MINOR="G_MAJOR_E_MINOR",t.A_FLAT_MAJOR_F_MINOR="A_FLAT_MAJOR_F_MINOR",t.A_MAJOR_G_FLAT_MINOR="A_MAJOR_G_FLAT_MINOR",t.B_FLAT_MAJOR_G_MINOR="B_FLAT_MAJOR_G_MINOR",t.B_MAJOR_A_FLAT_MINOR="B_MAJOR_A_FLAT_MINOR"})(_n||(_n={}));var pe;(function(t){t.PLAYBACK_CONTROL_UNSPECIFIED="PLAYBACK_CONTROL_UNSPECIFIED",t.PLAY="PLAY",t.PAUSE="PAUSE",t.STOP="STOP",t.RESET_CONTEXT="RESET_CONTEXT"})(pe||(pe={}));class st{constructor(e){const n={};for(const o of e.headers.entries())n[o[0]]=o[1];this.headers=n,this.responseInternal=e}json(){return this.responseInternal.json()}}class Ae{get text(){var e,n,o,s,l,a,u,f;if(((s=(o=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||o===void 0?void 0:o.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning text from the first one.");let c="",d=!1;const p=[];for(const m of(f=(u=(a=(l=this.candidates)===null||l===void 0?void 0:l[0])===null||a===void 0?void 0:a.content)===null||u===void 0?void 0:u.parts)!==null&&f!==void 0?f:[]){for(const[h,g]of Object.entries(m))h!=="text"&&h!=="thought"&&(g!==null||g!==void 0)&&p.push(h);if(typeof m.text=="string"){if(typeof m.thought=="boolean"&&m.thought)continue;d=!0,c+=m.text}}return p.length>0&&console.warn(`there are non-text parts ${p} in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.`),d?c:void 0}get data(){var e,n,o,s,l,a,u,f;if(((s=(o=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||o===void 0?void 0:o.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning data from the first one.");let c="";const d=[];for(const p of(f=(u=(a=(l=this.candidates)===null||l===void 0?void 0:l[0])===null||a===void 0?void 0:a.content)===null||u===void 0?void 0:u.parts)!==null&&f!==void 0?f:[]){for(const[m,h]of Object.entries(p))m!=="inlineData"&&(h!==null||h!==void 0)&&d.push(m);p.inlineData&&typeof p.inlineData.data=="string"&&(c+=atob(p.inlineData.data))}return d.length>0&&console.warn(`there are non-data parts ${d} in the response, returning concatenation of all data parts. Please refer to the non data parts for a full response from model.`),c.length>0?btoa(c):void 0}get functionCalls(){var e,n,o,s,l,a,u,f;if(((s=(o=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||o===void 0?void 0:o.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning function calls from the first one.");const c=(f=(u=(a=(l=this.candidates)===null||l===void 0?void 0:l[0])===null||a===void 0?void 0:a.content)===null||u===void 0?void 0:u.parts)===null||f===void 0?void 0:f.filter(d=>d.functionCall).map(d=>d.functionCall).filter(d=>d!==void 0);if(c?.length!==0)return c}get executableCode(){var e,n,o,s,l,a,u,f,c;if(((s=(o=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||o===void 0?void 0:o.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning executable code from the first one.");const d=(f=(u=(a=(l=this.candidates)===null||l===void 0?void 0:l[0])===null||a===void 0?void 0:a.content)===null||u===void 0?void 0:u.parts)===null||f===void 0?void 0:f.filter(p=>p.executableCode).map(p=>p.executableCode).filter(p=>p!==void 0);if(d?.length!==0)return(c=d?.[0])===null||c===void 0?void 0:c.code}get codeExecutionResult(){var e,n,o,s,l,a,u,f,c;if(((s=(o=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||o===void 0?void 0:o.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning code execution result from the first one.");const d=(f=(u=(a=(l=this.candidates)===null||l===void 0?void 0:l[0])===null||a===void 0?void 0:a.content)===null||u===void 0?void 0:u.parts)===null||f===void 0?void 0:f.filter(p=>p.codeExecutionResult).map(p=>p.codeExecutionResult).filter(p=>p!==void 0);if(d?.length!==0)return(c=d?.[0])===null||c===void 0?void 0:c.output}}class Sn{}class En{}class pi{}class mi{}class An{}class Mn{}class In{}class hi{}class xn{}class Rn{}class wn{}class gi{}class vi{}class yi{}class Pn{}class Ti{get text(){var e,n,o;let s="",l=!1;const a=[];for(const u of(o=(n=(e=this.serverContent)===null||e===void 0?void 0:e.modelTurn)===null||n===void 0?void 0:n.parts)!==null&&o!==void 0?o:[]){for(const[f,c]of Object.entries(u))f!=="text"&&f!=="thought"&&c!==null&&a.push(f);if(typeof u.text=="string"){if(typeof u.thought=="boolean"&&u.thought)continue;l=!0,s+=u.text}}return a.length>0&&console.warn(`there are non-text parts ${a} in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.`),l?s:void 0}get data(){var e,n,o;let s="";const l=[];for(const a of(o=(n=(e=this.serverContent)===null||e===void 0?void 0:e.modelTurn)===null||n===void 0?void 0:n.parts)!==null&&o!==void 0?o:[]){for(const[u,f]of Object.entries(a))u!=="inlineData"&&f!==null&&l.push(u);a.inlineData&&typeof a.inlineData.data=="string"&&(s+=atob(a.inlineData.data))}return l.length>0&&console.warn(`there are non-data parts ${l} in the response, returning concatenation of all data parts. Please refer to the non data parts for a full response from model.`),s.length>0?btoa(s):void 0}}class Ci{get audioChunk(){if(this.serverContent&&this.serverContent.audioChunks&&this.serverContent.audioChunks.length>0)return this.serverContent.audioChunks[0]}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function L(t,e){if(!e||typeof e!="string")throw new Error("model is required and must be a string");if(t.isVertexAI()){if(e.startsWith("publishers/")||e.startsWith("projects/")||e.startsWith("models/"))return e;if(e.indexOf("/")>=0){const n=e.split("/",2);return`publishers/${n[0]}/models/${n[1]}`}else return`publishers/google/models/${e}`}else return e.startsWith("models/")||e.startsWith("tunedModels/")?e:`models/${e}`}function io(t,e){const n=L(t,e);return n?n.startsWith("publishers/")&&t.isVertexAI()?`projects/${t.getProject()}/locations/${t.getLocation()}/${n}`:n.startsWith("models/")&&t.isVertexAI()?`projects/${t.getProject()}/locations/${t.getLocation()}/publishers/google/${n}`:n:""}function ro(t){return Array.isArray(t)?t.map(e=>be(e)):[be(t)]}function be(t){if(typeof t=="object"&&t!==null)return t;throw new Error(`Could not parse input as Blob. Unsupported blob type: ${typeof t}`)}function _i(t){const e=be(t);if(e.mimeType&&e.mimeType.startsWith("image/"))return e;throw new Error(`Unsupported mime type: ${e.mimeType}`)}function Si(t){const e=be(t);if(e.mimeType&&e.mimeType.startsWith("audio/"))return e;throw new Error(`Unsupported mime type: ${e.mimeType}`)}function Nn(t){if(t==null)throw new Error("PartUnion is required");if(typeof t=="object")return t;if(typeof t=="string")return{text:t};throw new Error(`Unsupported part type: ${typeof t}`)}function so(t){if(t==null||Array.isArray(t)&&t.length===0)throw new Error("PartListUnion is required");return Array.isArray(t)?t.map(e=>Nn(e)):[Nn(t)]}function lt(t){return t!=null&&typeof t=="object"&&"parts"in t&&Array.isArray(t.parts)}function kn(t){return t!=null&&typeof t=="object"&&"functionCall"in t}function Dn(t){return t!=null&&typeof t=="object"&&"functionResponse"in t}function J(t){if(t==null)throw new Error("ContentUnion is required");return lt(t)?t:{role:"user",parts:so(t)}}function lo(t,e){if(!e)return[];if(t.isVertexAI()&&Array.isArray(e))return e.flatMap(n=>{const o=J(n);return o.parts&&o.parts.length>0&&o.parts[0].text!==void 0?[o.parts[0].text]:[]});if(t.isVertexAI()){const n=J(e);return n.parts&&n.parts.length>0&&n.parts[0].text!==void 0?[n.parts[0].text]:[]}return Array.isArray(e)?e.map(n=>J(n)):[J(e)]}function W(t){if(t==null||Array.isArray(t)&&t.length===0)throw new Error("contents are required");if(!Array.isArray(t)){if(kn(t)||Dn(t))throw new Error("To specify functionCall or functionResponse parts, please wrap them in a Content object, specifying the role for them");return[J(t)]}const e=[],n=[],o=lt(t[0]);for(const s of t){const l=lt(s);if(l!=o)throw new Error("Mixing Content and Parts is not supported, please group the parts into a the appropriate Content objects and specify the roles for them");if(l)e.push(s);else{if(kn(s)||Dn(s))throw new Error("To specify functionCall or functionResponse parts, please wrap them, and any other parts, in Content objects as appropriate, specifying the role for them");n.push(s)}}return o||e.push({role:"user",parts:so(n)}),e}const Ei=new Set(["type","format","title","description","default","items","minItems","maxItems","enum","properties","required","minProperties","maxProperties","minimum","maximum","minLength","maxLength","pattern","anyOf","propertyOrdering"]),Vn=ai(["string","number","integer","object","array","boolean","null"]),Ai=ri([Vn,Ie(Vn)]);function Mi(t=!0){const e=li(()=>{const n=ii({type:Ai.optional(),format:re().optional(),title:re().optional(),description:re().optional(),default:Gt().optional(),items:e.optional(),minItems:fe.string().optional(),maxItems:fe.string().optional(),enum:Ie(Gt()).optional(),properties:si(re(),e).optional(),required:Ie(re()).optional(),minProperties:fe.string().optional(),maxProperties:fe.string().optional(),propertyOrdering:Ie(re()).optional(),minimum:Ut().optional(),maximum:Ut().optional(),minLength:fe.string().optional(),maxLength:fe.string().optional(),pattern:re().optional(),anyOf:Ie(e).optional(),additionalProperties:oi().optional()});return t?n.strict():n});return e}function Ii(t,e){t.includes("null")&&(e.nullable=!0);const n=t.filter(o=>o!=="null");if(n.length===1)e.type=Object.values(z).includes(n[0].toUpperCase())?n[0].toUpperCase():z.TYPE_UNSPECIFIED;else{e.anyOf=[];for(const o of n)e.anyOf.push({type:Object.values(z).includes(o.toUpperCase())?o.toUpperCase():z.TYPE_UNSPECIFIED})}}function me(t){const e={},n=["items"],o=["anyOf"],s=["properties"];if(t.type&&t.anyOf)throw new Error("type and anyOf cannot be both populated.");const l=t.anyOf;l!=null&&l.length==2&&(l[0].type==="null"?(e.nullable=!0,t=l[1]):l[1].type==="null"&&(e.nullable=!0,t=l[0])),t.type instanceof Array&&Ii(t.type,e);for(const[a,u]of Object.entries(t))if(u!=null)if(a=="type"){if(u==="null")throw new Error("type: null can not be the only possible type for the field.");if(u instanceof Array)continue;e.type=Object.values(z).includes(u.toUpperCase())?u.toUpperCase():z.TYPE_UNSPECIFIED}else if(n.includes(a))e[a]=me(u);else if(o.includes(a)){const f=[];for(const c of u){if(c.type=="null"){e.nullable=!0;continue}f.push(me(c))}e[a]=f}else if(s.includes(a)){const f={};for(const[c,d]of Object.entries(u))f[c]=me(d);e[a]=f}else{if(a==="additionalProperties")continue;e[a]=u}return e}function we(t){if(Object.keys(t).includes("$schema")){delete t.$schema;const e=Mi().parse(t);return me(e)}else return me(t)}function vt(t){if(typeof t=="object")return t;if(typeof t=="string")return{voiceConfig:{prebuiltVoiceConfig:{voiceName:t}}};throw new Error(`Unsupported speechConfig type: ${typeof t}`)}function yt(t){if("multiSpeakerVoiceConfig"in t)throw new Error("multiSpeakerVoiceConfig is not supported in the live API.");return t}function _e(t){if(t.functionDeclarations)for(const e of t.functionDeclarations)e.parameters&&(e.parameters=we(e.parameters)),e.response&&(e.response=we(e.response));return t}function Se(t){if(t==null)throw new Error("tools is required");if(!Array.isArray(t))throw new Error("tools is required and must be an array of Tools");const e=[];for(const n of t)e.push(n);return e}function xi(t,e,n,o=1){const s=!e.startsWith(`${n}/`)&&e.split("/").length===o;return t.isVertexAI()?e.startsWith("projects/")?e:e.startsWith("locations/")?`projects/${t.getProject()}/${e}`:e.startsWith(`${n}/`)?`projects/${t.getProject()}/locations/${t.getLocation()}/${e}`:s?`projects/${t.getProject()}/locations/${t.getLocation()}/${n}/${e}`:e:s?`${n}/${e}`:e}function ee(t,e){if(typeof e!="string")throw new Error("name must be a string");return xi(t,e,"cachedContents")}function ao(t){switch(t){case"STATE_UNSPECIFIED":return"JOB_STATE_UNSPECIFIED";case"CREATING":return"JOB_STATE_RUNNING";case"ACTIVE":return"JOB_STATE_SUCCEEDED";case"FAILED":return"JOB_STATE_FAILED";default:return t}}function te(t){if(typeof t!="string")throw new Error("fromImageBytes must be a string");return t}function Ri(t){return t!=null&&typeof t=="object"&&"name"in t}function wi(t){return t!=null&&typeof t=="object"&&"video"in t}function Pi(t){return t!=null&&typeof t=="object"&&"uri"in t}function uo(t){var e;let n;if(Ri(t)&&(n=t.name),!(Pi(t)&&(n=t.uri,n===void 0))&&!(wi(t)&&(n=(e=t.video)===null||e===void 0?void 0:e.uri,n===void 0))){if(typeof t=="string"&&(n=t),n===void 0)throw new Error("Could not extract file name from the provided input.");if(n.startsWith("https://")){const s=n.split("files/")[1].match(/[a-z0-9]+/);if(s===null)throw new Error(`Could not extract file name from URI ${n}`);n=s[0]}else n.startsWith("files/")&&(n=n.split("files/")[1]);return n}}function co(t,e){let n;return t.isVertexAI()?n=e?"publishers/google/models":"models":n=e?"models":"tunedModels",n}function fo(t){for(const e of["models","tunedModels","publisherModels"])if(Ni(t,e))return t[e];return[]}function Ni(t,e){return t!==null&&typeof t=="object"&&e in t}function ki(t,e={}){const n=t,o={name:n.name,description:n.description,parameters:me(He(n.inputSchema))};return e.behavior&&(o.behavior=e.behavior),{functionDeclarations:[o]}}function Di(t,e={}){const n=[],o=new Set;for(const s of t){const l=s.name;if(o.has(l))throw new Error(`Duplicate function name ${l} found in MCP tools. Please ensure function names are unique.`);o.add(l);const a=ki(s,e);a.functionDeclarations&&n.push(...a.functionDeclarations)}return{functionDeclarations:n}}function Vi(t){const e=[];for(const n of t)e.push(He(n));return e}function Fi(t){const e={};for(const[n,o]of Object.entries(t)){const s=o;e[n]=He(s)}return e}function He(t){const e=new Set(["items"]),n=new Set(["anyOf"]),o=new Set(["properties"]),s={};for(const[l,a]of Object.entries(t))if(e.has(l))s[l]=He(a);else if(n.has(l))s[l]=Vi(a);else if(o.has(l))s[l]=Fi(a);else if(l==="type"){const u=a.toUpperCase();s[l]=Object.values(z).includes(u)?u:z.TYPE_UNSPECIFIED}else Ei.has(l)&&(s[l]=a);return s}function po(t,e){if(typeof e!="string"&&!Array.isArray(e)){if(t&&t.isVertexAI()){if(e.gcsUri&&e.bigqueryUri)throw new Error("Only one of `gcsUri` or `bigqueryUri` can be set.");if(!e.gcsUri&&!e.bigqueryUri)throw new Error("One of `gcsUri` or `bigqueryUri` must be set.")}else{if(e.inlinedRequests&&e.fileName)throw new Error("Only one of `inlinedRequests` or `fileName` can be set.");if(!e.inlinedRequests&&!e.fileName)throw new Error("One of `inlinedRequests` or `fileName` must be set.")}return e}else{if(Array.isArray(e))return{inlinedRequests:e};if(typeof e=="string"){if(e.startsWith("gs://"))return{format:"jsonl",gcsUri:[e]};if(e.startsWith("bq://"))return{format:"bigquery",bigqueryUri:e};if(e.startsWith("files/"))return{fileName:e}}}throw new Error(`Unsupported source: ${e}`)}function Li(t){const e=t;if(e.startsWith("gs://"))return{format:"jsonl",gcsUri:e};if(e.startsWith("bq://"))return{format:"bigquery",bigqueryUri:e};throw new Error(`Unsupported destination: ${e}`)}function We(t,e){const n=e;if(!t.isVertexAI()){if(/batches\/[^/]+$/.test(n))return n.split("/").pop();throw new Error(`Invalid batch job name: ${n}.`)}if(/^projects\/[^/]+\/locations\/[^/]+\/batchPredictionJobs\/[^/]+$/.test(n))return n.split("/").pop();if(/^\d+$/.test(n))return n;throw new Error(`Invalid batch job name: ${n}.`)}function mo(t){const e=t;return e==="BATCH_STATE_UNSPECIFIED"?"JOB_STATE_UNSPECIFIED":e==="BATCH_STATE_PENDING"?"JOB_STATE_PENDING":e==="BATCH_STATE_SUCCEEDED"?"JOB_STATE_SUCCEEDED":e==="BATCH_STATE_FAILED"?"JOB_STATE_FAILED":e==="BATCH_STATE_CANCELLED"?"JOB_STATE_CANCELLED":e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Ui(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Gi(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Bi(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function qi(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Ui(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],Gi(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],Bi(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function ho(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>qi(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function $i(t){const e={},n=i(t,["anyOf"]);n!=null&&r(e,["anyOf"],n);const o=i(t,["default"]);o!=null&&r(e,["default"],o);const s=i(t,["description"]);s!=null&&r(e,["description"],s);const l=i(t,["enum"]);l!=null&&r(e,["enum"],l);const a=i(t,["example"]);a!=null&&r(e,["example"],a);const u=i(t,["format"]);u!=null&&r(e,["format"],u);const f=i(t,["items"]);f!=null&&r(e,["items"],f);const c=i(t,["maxItems"]);c!=null&&r(e,["maxItems"],c);const d=i(t,["maxLength"]);d!=null&&r(e,["maxLength"],d);const p=i(t,["maxProperties"]);p!=null&&r(e,["maxProperties"],p);const m=i(t,["maximum"]);m!=null&&r(e,["maximum"],m);const h=i(t,["minItems"]);h!=null&&r(e,["minItems"],h);const g=i(t,["minLength"]);g!=null&&r(e,["minLength"],g);const y=i(t,["minProperties"]);y!=null&&r(e,["minProperties"],y);const S=i(t,["minimum"]);S!=null&&r(e,["minimum"],S);const A=i(t,["nullable"]);A!=null&&r(e,["nullable"],A);const x=i(t,["pattern"]);x!=null&&r(e,["pattern"],x);const M=i(t,["properties"]);M!=null&&r(e,["properties"],M);const P=i(t,["propertyOrdering"]);P!=null&&r(e,["propertyOrdering"],P);const I=i(t,["required"]);I!=null&&r(e,["required"],I);const U=i(t,["title"]);U!=null&&r(e,["title"],U);const G=i(t,["type"]);return G!=null&&r(e,["type"],G),e}function Ji(t){const e={};if(i(t,["method"])!==void 0)throw new Error("method parameter is not supported in Gemini API.");const n=i(t,["category"]);n!=null&&r(e,["category"],n);const o=i(t,["threshold"]);return o!=null&&r(e,["threshold"],o),e}function bi(t){const e={},n=i(t,["behavior"]);n!=null&&r(e,["behavior"],n);const o=i(t,["description"]);o!=null&&r(e,["description"],o);const s=i(t,["name"]);s!=null&&r(e,["name"],s);const l=i(t,["parameters"]);l!=null&&r(e,["parameters"],l);const a=i(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const u=i(t,["response"]);u!=null&&r(e,["response"],u);const f=i(t,["responseJsonSchema"]);return f!=null&&r(e,["responseJsonSchema"],f),e}function Hi(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function Wi(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Hi(n)),e}function Zi(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function Yi(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Zi(n)),e}function Ki(){return{}}function zi(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let u=n;Array.isArray(u)&&(u=u.map(f=>bi(f))),r(e,["functionDeclarations"],u)}if(i(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const o=i(t,["googleSearch"]);o!=null&&r(e,["googleSearch"],Wi(o));const s=i(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],Yi(s)),i(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(i(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");i(t,["urlContext"])!=null&&r(e,["urlContext"],Ki());const a=i(t,["codeExecution"]);return a!=null&&r(e,["codeExecution"],a),e}function Oi(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["allowedFunctionNames"]);return o!=null&&r(e,["allowedFunctionNames"],o),e}function Xi(t){const e={},n=i(t,["latitude"]);n!=null&&r(e,["latitude"],n);const o=i(t,["longitude"]);return o!=null&&r(e,["longitude"],o),e}function Qi(t){const e={},n=i(t,["latLng"]);n!=null&&r(e,["latLng"],Xi(n));const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function ji(t){const e={},n=i(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],Oi(n));const o=i(t,["retrievalConfig"]);return o!=null&&r(e,["retrievalConfig"],Qi(o)),e}function er(t){const e={},n=i(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function go(t){const e={},n=i(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],er(n)),e}function tr(t){const e={},n=i(t,["speaker"]);n!=null&&r(e,["speaker"],n);const o=i(t,["voiceConfig"]);return o!=null&&r(e,["voiceConfig"],go(o)),e}function nr(t){const e={},n=i(t,["speakerVoiceConfigs"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>tr(s))),r(e,["speakerVoiceConfigs"],o)}return e}function or(t){const e={},n=i(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],go(n));const o=i(t,["multiSpeakerVoiceConfig"]);o!=null&&r(e,["multiSpeakerVoiceConfig"],nr(o));const s=i(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function ir(t){const e={},n=i(t,["includeThoughts"]);n!=null&&r(e,["includeThoughts"],n);const o=i(t,["thinkingBudget"]);return o!=null&&r(e,["thinkingBudget"],o),e}function rr(t,e,n){const o={},s=i(e,["systemInstruction"]);n!==void 0&&s!=null&&r(n,["systemInstruction"],ho(J(s)));const l=i(e,["temperature"]);l!=null&&r(o,["temperature"],l);const a=i(e,["topP"]);a!=null&&r(o,["topP"],a);const u=i(e,["topK"]);u!=null&&r(o,["topK"],u);const f=i(e,["candidateCount"]);f!=null&&r(o,["candidateCount"],f);const c=i(e,["maxOutputTokens"]);c!=null&&r(o,["maxOutputTokens"],c);const d=i(e,["stopSequences"]);d!=null&&r(o,["stopSequences"],d);const p=i(e,["responseLogprobs"]);p!=null&&r(o,["responseLogprobs"],p);const m=i(e,["logprobs"]);m!=null&&r(o,["logprobs"],m);const h=i(e,["presencePenalty"]);h!=null&&r(o,["presencePenalty"],h);const g=i(e,["frequencyPenalty"]);g!=null&&r(o,["frequencyPenalty"],g);const y=i(e,["seed"]);y!=null&&r(o,["seed"],y);const S=i(e,["responseMimeType"]);S!=null&&r(o,["responseMimeType"],S);const A=i(e,["responseSchema"]);A!=null&&r(o,["responseSchema"],$i(we(A)));const x=i(e,["responseJsonSchema"]);if(x!=null&&r(o,["responseJsonSchema"],x),i(e,["routingConfig"])!==void 0)throw new Error("routingConfig parameter is not supported in Gemini API.");if(i(e,["modelSelectionConfig"])!==void 0)throw new Error("modelSelectionConfig parameter is not supported in Gemini API.");const M=i(e,["safetySettings"]);if(n!==void 0&&M!=null){let F=M;Array.isArray(F)&&(F=F.map(Y=>Ji(Y))),r(n,["safetySettings"],F)}const P=i(e,["tools"]);if(n!==void 0&&P!=null){let F=Se(P);Array.isArray(F)&&(F=F.map(Y=>zi(_e(Y)))),r(n,["tools"],F)}const I=i(e,["toolConfig"]);if(n!==void 0&&I!=null&&r(n,["toolConfig"],ji(I)),i(e,["labels"])!==void 0)throw new Error("labels parameter is not supported in Gemini API.");const U=i(e,["cachedContent"]);n!==void 0&&U!=null&&r(n,["cachedContent"],ee(t,U));const G=i(e,["responseModalities"]);G!=null&&r(o,["responseModalities"],G);const H=i(e,["mediaResolution"]);H!=null&&r(o,["mediaResolution"],H);const N=i(e,["speechConfig"]);if(N!=null&&r(o,["speechConfig"],or(vt(N))),i(e,["audioTimestamp"])!==void 0)throw new Error("audioTimestamp parameter is not supported in Gemini API.");const B=i(e,["thinkingConfig"]);return B!=null&&r(o,["thinkingConfig"],ir(B)),o}function sr(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["request","model"],L(t,o));const s=i(e,["contents"]);if(s!=null){let a=W(s);Array.isArray(a)&&(a=a.map(u=>ho(u))),r(n,["request","contents"],a)}const l=i(e,["config"]);return l!=null&&r(n,["request","generationConfig"],rr(t,l,n)),n}function lr(t,e){const n={};if(i(e,["format"])!==void 0)throw new Error("format parameter is not supported in Gemini API.");if(i(e,["gcsUri"])!==void 0)throw new Error("gcsUri parameter is not supported in Gemini API.");if(i(e,["bigqueryUri"])!==void 0)throw new Error("bigqueryUri parameter is not supported in Gemini API.");const o=i(e,["fileName"]);o!=null&&r(n,["fileName"],o);const s=i(e,["inlinedRequests"]);if(s!=null){let l=s;Array.isArray(l)&&(l=l.map(a=>sr(t,a))),r(n,["requests","requests"],l)}return n}function ar(t,e){const n={},o=i(t,["displayName"]);if(e!==void 0&&o!=null&&r(e,["batch","displayName"],o),i(t,["dest"])!==void 0)throw new Error("dest parameter is not supported in Gemini API.");return n}function ur(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["src"]);s!=null&&r(n,["batch","inputConfig"],lr(t,po(t,s)));const l=i(e,["config"]);return l!=null&&r(n,["config"],ar(l,n)),n}function cr(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],We(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function fr(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],We(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function dr(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);if(e!==void 0&&s!=null&&r(e,["_query","pageToken"],s),i(t,["filter"])!==void 0)throw new Error("filter parameter is not supported in Gemini API.");return n}function pr(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],dr(n,e)),e}function mr(t){const e={},n=i(t,["format"]);n!=null&&r(e,["instancesFormat"],n);const o=i(t,["gcsUri"]);o!=null&&r(e,["gcsSource","uris"],o);const s=i(t,["bigqueryUri"]);if(s!=null&&r(e,["bigquerySource","inputUri"],s),i(t,["fileName"])!==void 0)throw new Error("fileName parameter is not supported in Vertex AI.");if(i(t,["inlinedRequests"])!==void 0)throw new Error("inlinedRequests parameter is not supported in Vertex AI.");return e}function hr(t){const e={},n=i(t,["format"]);n!=null&&r(e,["predictionsFormat"],n);const o=i(t,["gcsUri"]);o!=null&&r(e,["gcsDestination","outputUriPrefix"],o);const s=i(t,["bigqueryUri"]);if(s!=null&&r(e,["bigqueryDestination","outputUri"],s),i(t,["fileName"])!==void 0)throw new Error("fileName parameter is not supported in Vertex AI.");if(i(t,["inlinedResponses"])!==void 0)throw new Error("inlinedResponses parameter is not supported in Vertex AI.");return e}function gr(t,e){const n={},o=i(t,["displayName"]);e!==void 0&&o!=null&&r(e,["displayName"],o);const s=i(t,["dest"]);return e!==void 0&&s!=null&&r(e,["outputConfig"],hr(Li(s))),n}function vr(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["model"],L(t,o));const s=i(e,["src"]);s!=null&&r(n,["inputConfig"],mr(po(t,s)));const l=i(e,["config"]);return l!=null&&r(n,["config"],gr(l,n)),n}function yr(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],We(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Tr(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],We(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Cr(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);e!==void 0&&s!=null&&r(e,["_query","pageToken"],s);const l=i(t,["filter"]);return e!==void 0&&l!=null&&r(e,["_query","filter"],l),n}function _r(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],Cr(n,e)),e}function Sr(){return{}}function Er(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Ar(t){const e={},n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Mr(t){const e={},n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Ir(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Er(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],Ar(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],Mr(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function xr(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>Ir(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Rr(t){const e={},n=i(t,["citationSources"]);return n!=null&&r(e,["citations"],n),e}function wr(t){const e={},n=i(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const o=i(t,["urlRetrievalStatus"]);return o!=null&&r(e,["urlRetrievalStatus"],o),e}function Pr(t){const e={},n=i(t,["urlMetadata"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>wr(s))),r(e,["urlMetadata"],o)}return e}function Nr(t){const e={},n=i(t,["content"]);n!=null&&r(e,["content"],xr(n));const o=i(t,["citationMetadata"]);o!=null&&r(e,["citationMetadata"],Rr(o));const s=i(t,["tokenCount"]);s!=null&&r(e,["tokenCount"],s);const l=i(t,["finishReason"]);l!=null&&r(e,["finishReason"],l);const a=i(t,["urlContextMetadata"]);a!=null&&r(e,["urlContextMetadata"],Pr(a));const u=i(t,["avgLogprobs"]);u!=null&&r(e,["avgLogprobs"],u);const f=i(t,["groundingMetadata"]);f!=null&&r(e,["groundingMetadata"],f);const c=i(t,["index"]);c!=null&&r(e,["index"],c);const d=i(t,["logprobsResult"]);d!=null&&r(e,["logprobsResult"],d);const p=i(t,["safetyRatings"]);return p!=null&&r(e,["safetyRatings"],p),e}function kr(t){const e={},n=i(t,["candidates"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(u=>Nr(u))),r(e,["candidates"],a)}const o=i(t,["modelVersion"]);o!=null&&r(e,["modelVersion"],o);const s=i(t,["promptFeedback"]);s!=null&&r(e,["promptFeedback"],s);const l=i(t,["usageMetadata"]);return l!=null&&r(e,["usageMetadata"],l),e}function Dr(t){const e={},n=i(t,["response"]);return n!=null&&r(e,["response"],kr(n)),i(t,["error"])!=null&&r(e,["error"],Sr()),e}function Vr(t){const e={},n=i(t,["responsesFile"]);n!=null&&r(e,["fileName"],n);const o=i(t,["inlinedResponses","inlinedResponses"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(l=>Dr(l))),r(e,["inlinedResponses"],s)}return e}function at(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["metadata","displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["metadata","state"]);s!=null&&r(e,["state"],mo(s));const l=i(t,["metadata","createTime"]);l!=null&&r(e,["createTime"],l);const a=i(t,["metadata","endTime"]);a!=null&&r(e,["endTime"],a);const u=i(t,["metadata","updateTime"]);u!=null&&r(e,["updateTime"],u);const f=i(t,["metadata","model"]);f!=null&&r(e,["model"],f);const c=i(t,["metadata","output"]);return c!=null&&r(e,["dest"],Vr(c)),e}function Fr(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["operations"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(l=>at(l))),r(e,["batchJobs"],s)}return e}function Lr(t){const e={},n=i(t,["details"]);n!=null&&r(e,["details"],n);const o=i(t,["code"]);o!=null&&r(e,["code"],o);const s=i(t,["message"]);return s!=null&&r(e,["message"],s),e}function Ur(t){const e={},n=i(t,["instancesFormat"]);n!=null&&r(e,["format"],n);const o=i(t,["gcsSource","uris"]);o!=null&&r(e,["gcsUri"],o);const s=i(t,["bigquerySource","inputUri"]);return s!=null&&r(e,["bigqueryUri"],s),e}function Gr(t){const e={},n=i(t,["predictionsFormat"]);n!=null&&r(e,["format"],n);const o=i(t,["gcsDestination","outputUriPrefix"]);o!=null&&r(e,["gcsUri"],o);const s=i(t,["bigqueryDestination","outputUri"]);return s!=null&&r(e,["bigqueryUri"],s),e}function ut(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["state"]);s!=null&&r(e,["state"],mo(s));const l=i(t,["error"]);l!=null&&r(e,["error"],Lr(l));const a=i(t,["createTime"]);a!=null&&r(e,["createTime"],a);const u=i(t,["startTime"]);u!=null&&r(e,["startTime"],u);const f=i(t,["endTime"]);f!=null&&r(e,["endTime"],f);const c=i(t,["updateTime"]);c!=null&&r(e,["updateTime"],c);const d=i(t,["model"]);d!=null&&r(e,["model"],d);const p=i(t,["inputConfig"]);p!=null&&r(e,["src"],Ur(p));const m=i(t,["outputConfig"]);return m!=null&&r(e,["dest"],Gr(m)),e}function Br(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["batchPredictionJobs"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(l=>ut(l))),r(e,["batchJobs"],s)}return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */var ue;(function(t){t.PAGED_ITEM_BATCH_JOBS="batchJobs",t.PAGED_ITEM_MODELS="models",t.PAGED_ITEM_TUNING_JOBS="tuningJobs",t.PAGED_ITEM_FILES="files",t.PAGED_ITEM_CACHED_CONTENTS="cachedContents"})(ue||(ue={}));class Pe{constructor(e,n,o,s){this.pageInternal=[],this.paramsInternal={},this.requestInternal=n,this.init(e,o,s)}init(e,n,o){var s,l;this.nameInternal=e,this.pageInternal=n[this.nameInternal]||[],this.idxInternal=0;let a={config:{}};o?typeof o=="object"?a=Object.assign({},o):a=o:a={config:{}},a.config&&(a.config.pageToken=n.nextPageToken),this.paramsInternal=a,this.pageInternalSize=(l=(s=a.config)===null||s===void 0?void 0:s.pageSize)!==null&&l!==void 0?l:this.pageInternal.length}initNextPage(e){this.init(this.nameInternal,e,this.paramsInternal)}get page(){return this.pageInternal}get name(){return this.nameInternal}get pageSize(){return this.pageInternalSize}get params(){return this.paramsInternal}get pageLength(){return this.pageInternal.length}getItem(e){return this.pageInternal[e]}[Symbol.asyncIterator](){return{next:async()=>{if(this.idxInternal>=this.pageLength)if(this.hasNextPage())await this.nextPage();else return{value:void 0,done:!0};const e=this.getItem(this.idxInternal);return this.idxInternal+=1,{value:e,done:!1}},return:async()=>({value:void 0,done:!0})}}async nextPage(){if(!this.hasNextPage())throw new Error("No more pages to fetch.");const e=await this.requestInternal(this.params);return this.initNextPage(e),this.page}hasNextPage(){var e;return((e=this.params.config)===null||e===void 0?void 0:e.pageToken)!==void 0}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class qr extends ce{constructor(e){super(),this.apiClient=e,this.create=async n=>{if(this.apiClient.isVertexAI()){if(Array.isArray(n.src))throw new Error("InlinedRequest[] is not supported in Vertex AI. Please use Google Cloud Storage URI or BigQuery URI instead.");if(n.config=n.config||{},n.config.displayName===void 0&&(n.config.displayName="genaiBatchJob_"),n.config.dest===void 0&&typeof n.src=="string")if(n.src.startsWith("gs://")&&n.src.endsWith(".jsonl"))n.config.dest=`${n.src.slice(0,-6)}/dest`;else if(n.src.startsWith("bq://"))n.config.dest=`${n.src}_dest_`;else throw new Error("Unsupported source:"+n.src)}return await this.createInternal(n)},this.list=async(n={})=>new Pe(ue.PAGED_ITEM_BATCH_JOBS,o=>this.listInternal(o),await this.listInternal(n),n)}async createInternal(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=vr(this.apiClient,e);return u=E("batchPredictionJobs",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>ut(d))}else{const c=ur(this.apiClient,e);return u=E("{model}:batchGenerateContent",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>at(d))}}async get(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=yr(this.apiClient,e);return u=E("batchPredictionJobs/{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>ut(d))}else{const c=cr(this.apiClient,e);return u=E("batches/{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>at(d))}}async cancel(e){var n,o,s,l;let a="",u={};if(this.apiClient.isVertexAI()){const f=Tr(this.apiClient,e);a=E("batchPredictionJobs/{name}:cancel",f._url),u=f._query,delete f.config,delete f._url,delete f._query,await this.apiClient.request({path:a,queryParams:u,body:JSON.stringify(f),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal})}else{const f=fr(this.apiClient,e);a=E("batches/{name}:cancel",f._url),u=f._query,delete f.config,delete f._url,delete f._query,await this.apiClient.request({path:a,queryParams:u,body:JSON.stringify(f),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal})}}async listInternal(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=_r(e);return u=E("batchPredictionJobs",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>{const p=Br(d),m=new Pn;return Object.assign(m,p),m})}else{const c=pr(e);return u=E("batches",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>{const p=Fr(d),m=new Pn;return Object.assign(m,p),m})}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function $r(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Jr(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function br(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Hr(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],$r(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],Jr(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],br(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Fn(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>Hr(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Wr(t){const e={},n=i(t,["behavior"]);n!=null&&r(e,["behavior"],n);const o=i(t,["description"]);o!=null&&r(e,["description"],o);const s=i(t,["name"]);s!=null&&r(e,["name"],s);const l=i(t,["parameters"]);l!=null&&r(e,["parameters"],l);const a=i(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const u=i(t,["response"]);u!=null&&r(e,["response"],u);const f=i(t,["responseJsonSchema"]);return f!=null&&r(e,["responseJsonSchema"],f),e}function Zr(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function Yr(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Zr(n)),e}function Kr(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function zr(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Kr(n)),e}function Or(){return{}}function Xr(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let u=n;Array.isArray(u)&&(u=u.map(f=>Wr(f))),r(e,["functionDeclarations"],u)}if(i(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const o=i(t,["googleSearch"]);o!=null&&r(e,["googleSearch"],Yr(o));const s=i(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],zr(s)),i(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(i(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");i(t,["urlContext"])!=null&&r(e,["urlContext"],Or());const a=i(t,["codeExecution"]);return a!=null&&r(e,["codeExecution"],a),e}function Qr(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["allowedFunctionNames"]);return o!=null&&r(e,["allowedFunctionNames"],o),e}function jr(t){const e={},n=i(t,["latitude"]);n!=null&&r(e,["latitude"],n);const o=i(t,["longitude"]);return o!=null&&r(e,["longitude"],o),e}function es(t){const e={},n=i(t,["latLng"]);n!=null&&r(e,["latLng"],jr(n));const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function ts(t){const e={},n=i(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],Qr(n));const o=i(t,["retrievalConfig"]);return o!=null&&r(e,["retrievalConfig"],es(o)),e}function ns(t,e){const n={},o=i(t,["ttl"]);e!==void 0&&o!=null&&r(e,["ttl"],o);const s=i(t,["expireTime"]);e!==void 0&&s!=null&&r(e,["expireTime"],s);const l=i(t,["displayName"]);e!==void 0&&l!=null&&r(e,["displayName"],l);const a=i(t,["contents"]);if(e!==void 0&&a!=null){let d=W(a);Array.isArray(d)&&(d=d.map(p=>Fn(p))),r(e,["contents"],d)}const u=i(t,["systemInstruction"]);e!==void 0&&u!=null&&r(e,["systemInstruction"],Fn(J(u)));const f=i(t,["tools"]);if(e!==void 0&&f!=null){let d=f;Array.isArray(d)&&(d=d.map(p=>Xr(p))),r(e,["tools"],d)}const c=i(t,["toolConfig"]);if(e!==void 0&&c!=null&&r(e,["toolConfig"],ts(c)),i(t,["kmsKeyName"])!==void 0)throw new Error("kmsKeyName parameter is not supported in Gemini API.");return n}function os(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["model"],io(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],ns(s,n)),n}function is(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],ee(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function rs(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],ee(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function ss(t,e){const n={},o=i(t,["ttl"]);e!==void 0&&o!=null&&r(e,["ttl"],o);const s=i(t,["expireTime"]);return e!==void 0&&s!=null&&r(e,["expireTime"],s),n}function ls(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],ee(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],ss(s,n)),n}function as(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);return e!==void 0&&s!=null&&r(e,["_query","pageToken"],s),n}function us(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],as(n,e)),e}function cs(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function fs(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["data"]);o!=null&&r(e,["data"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function ds(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["fileUri"]);o!=null&&r(e,["fileUri"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function ps(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],cs(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],fs(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],ds(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Ln(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>ps(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function ms(t){const e={};if(i(t,["behavior"])!==void 0)throw new Error("behavior parameter is not supported in Vertex AI.");const n=i(t,["description"]);n!=null&&r(e,["description"],n);const o=i(t,["name"]);o!=null&&r(e,["name"],o);const s=i(t,["parameters"]);s!=null&&r(e,["parameters"],s);const l=i(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const a=i(t,["response"]);a!=null&&r(e,["response"],a);const u=i(t,["responseJsonSchema"]);return u!=null&&r(e,["responseJsonSchema"],u),e}function hs(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function gs(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],hs(n)),e}function vs(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function ys(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],vs(n)),e}function Ts(){return{}}function Cs(t){const e={},n=i(t,["apiKeyString"]);return n!=null&&r(e,["apiKeyString"],n),e}function _s(t){const e={},n=i(t,["apiKeyConfig"]);n!=null&&r(e,["apiKeyConfig"],Cs(n));const o=i(t,["authType"]);o!=null&&r(e,["authType"],o);const s=i(t,["googleServiceAccountConfig"]);s!=null&&r(e,["googleServiceAccountConfig"],s);const l=i(t,["httpBasicAuthConfig"]);l!=null&&r(e,["httpBasicAuthConfig"],l);const a=i(t,["oauthConfig"]);a!=null&&r(e,["oauthConfig"],a);const u=i(t,["oidcConfig"]);return u!=null&&r(e,["oidcConfig"],u),e}function Ss(t){const e={},n=i(t,["authConfig"]);return n!=null&&r(e,["authConfig"],_s(n)),e}function Es(){return{}}function As(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(p=>ms(p))),r(e,["functionDeclarations"],d)}const o=i(t,["retrieval"]);o!=null&&r(e,["retrieval"],o);const s=i(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],gs(s));const l=i(t,["googleSearchRetrieval"]);l!=null&&r(e,["googleSearchRetrieval"],ys(l)),i(t,["enterpriseWebSearch"])!=null&&r(e,["enterpriseWebSearch"],Ts());const u=i(t,["googleMaps"]);u!=null&&r(e,["googleMaps"],Ss(u)),i(t,["urlContext"])!=null&&r(e,["urlContext"],Es());const c=i(t,["codeExecution"]);return c!=null&&r(e,["codeExecution"],c),e}function Ms(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["allowedFunctionNames"]);return o!=null&&r(e,["allowedFunctionNames"],o),e}function Is(t){const e={},n=i(t,["latitude"]);n!=null&&r(e,["latitude"],n);const o=i(t,["longitude"]);return o!=null&&r(e,["longitude"],o),e}function xs(t){const e={},n=i(t,["latLng"]);n!=null&&r(e,["latLng"],Is(n));const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function Rs(t){const e={},n=i(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],Ms(n));const o=i(t,["retrievalConfig"]);return o!=null&&r(e,["retrievalConfig"],xs(o)),e}function ws(t,e){const n={},o=i(t,["ttl"]);e!==void 0&&o!=null&&r(e,["ttl"],o);const s=i(t,["expireTime"]);e!==void 0&&s!=null&&r(e,["expireTime"],s);const l=i(t,["displayName"]);e!==void 0&&l!=null&&r(e,["displayName"],l);const a=i(t,["contents"]);if(e!==void 0&&a!=null){let p=W(a);Array.isArray(p)&&(p=p.map(m=>Ln(m))),r(e,["contents"],p)}const u=i(t,["systemInstruction"]);e!==void 0&&u!=null&&r(e,["systemInstruction"],Ln(J(u)));const f=i(t,["tools"]);if(e!==void 0&&f!=null){let p=f;Array.isArray(p)&&(p=p.map(m=>As(m))),r(e,["tools"],p)}const c=i(t,["toolConfig"]);e!==void 0&&c!=null&&r(e,["toolConfig"],Rs(c));const d=i(t,["kmsKeyName"]);return e!==void 0&&d!=null&&r(e,["encryption_spec","kmsKeyName"],d),n}function Ps(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["model"],io(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],ws(s,n)),n}function Ns(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],ee(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function ks(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],ee(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Ds(t,e){const n={},o=i(t,["ttl"]);e!==void 0&&o!=null&&r(e,["ttl"],o);const s=i(t,["expireTime"]);return e!==void 0&&s!=null&&r(e,["expireTime"],s),n}function Vs(t,e){const n={},o=i(e,["name"]);o!=null&&r(n,["_url","name"],ee(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],Ds(s,n)),n}function Fs(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);return e!==void 0&&s!=null&&r(e,["_query","pageToken"],s),n}function Ls(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],Fs(n,e)),e}function De(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["model"]);s!=null&&r(e,["model"],s);const l=i(t,["createTime"]);l!=null&&r(e,["createTime"],l);const a=i(t,["updateTime"]);a!=null&&r(e,["updateTime"],a);const u=i(t,["expireTime"]);u!=null&&r(e,["expireTime"],u);const f=i(t,["usageMetadata"]);return f!=null&&r(e,["usageMetadata"],f),e}function Us(){return{}}function Gs(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["cachedContents"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(l=>De(l))),r(e,["cachedContents"],s)}return e}function Ve(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["model"]);s!=null&&r(e,["model"],s);const l=i(t,["createTime"]);l!=null&&r(e,["createTime"],l);const a=i(t,["updateTime"]);a!=null&&r(e,["updateTime"],a);const u=i(t,["expireTime"]);u!=null&&r(e,["expireTime"],u);const f=i(t,["usageMetadata"]);return f!=null&&r(e,["usageMetadata"],f),e}function Bs(){return{}}function qs(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["cachedContents"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(l=>Ve(l))),r(e,["cachedContents"],s)}return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class $s extends ce{constructor(e){super(),this.apiClient=e,this.list=async(n={})=>new Pe(ue.PAGED_ITEM_CACHED_CONTENTS,o=>this.listInternal(o),await this.listInternal(n),n)}async create(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=Ps(this.apiClient,e);return u=E("cachedContents",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>Ve(d))}else{const c=os(this.apiClient,e);return u=E("cachedContents",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>De(d))}}async get(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=Ns(this.apiClient,e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>Ve(d))}else{const c=is(this.apiClient,e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>De(d))}}async delete(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=ks(this.apiClient,e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(()=>{const d=Bs(),p=new Rn;return Object.assign(p,d),p})}else{const c=rs(this.apiClient,e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(()=>{const d=Us(),p=new Rn;return Object.assign(p,d),p})}}async update(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=Vs(this.apiClient,e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"PATCH",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>Ve(d))}else{const c=ls(this.apiClient,e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"PATCH",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>De(d))}}async listInternal(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=Ls(e);return u=E("cachedContents",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>{const p=qs(d),m=new wn;return Object.assign(m,p),m})}else{const c=us(e);return u=E("cachedContents",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>{const p=Gs(d),m=new wn;return Object.assign(m,p),m})}}}function Un(t){var e=typeof Symbol=="function"&&Symbol.iterator,n=e&&t[e],o=0;if(n)return n.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&o>=t.length&&(t=void 0),{value:t&&t[o++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function q(t){return this instanceof q?(this.v=t,this):new q(t)}function he(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o=n.apply(t,e||[]),s,l=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),u("next"),u("throw"),u("return",a),s[Symbol.asyncIterator]=function(){return this},s;function a(h){return function(g){return Promise.resolve(g).then(h,p)}}function u(h,g){o[h]&&(s[h]=function(y){return new Promise(function(S,A){l.push([h,y,S,A])>1||f(h,y)})},g&&(s[h]=g(s[h])))}function f(h,g){try{c(o[h](g))}catch(y){m(l[0][3],y)}}function c(h){h.value instanceof q?Promise.resolve(h.value.v).then(d,p):m(l[0][2],h)}function d(h){f("next",h)}function p(h){f("throw",h)}function m(h,g){h(g),l.shift(),l.length&&f(l[0][0],l[0][1])}}function xe(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],n;return e?e.call(t):(t=typeof Un=="function"?Un(t):t[Symbol.iterator](),n={},o("next"),o("throw"),o("return"),n[Symbol.asyncIterator]=function(){return this},n);function o(l){n[l]=t[l]&&function(a){return new Promise(function(u,f){a=t[l](a),s(u,f,a.done,a.value)})}}function s(l,a,u,f){Promise.resolve(f).then(function(c){l({value:c,done:u})},a)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Js(t){var e;if(t.candidates==null||t.candidates.length===0)return!1;const n=(e=t.candidates[0])===null||e===void 0?void 0:e.content;return n===void 0?!1:vo(n)}function vo(t){if(t.parts===void 0||t.parts.length===0)return!1;for(const e of t.parts)if(e===void 0||Object.keys(e).length===0||!e.thought&&e.text!==void 0&&e.text==="")return!1;return!0}function bs(t){if(t.length!==0){for(const e of t)if(e.role!=="user"&&e.role!=="model")throw new Error(`Role must be user or model, but got ${e.role}.`)}}function Gn(t){if(t===void 0||t.length===0)return[];const e=[],n=t.length;let o=0;for(;o<n;)if(t[o].role==="user")e.push(t[o]),o++;else{const s=[];let l=!0;for(;o<n&&t[o].role==="model";)s.push(t[o]),l&&!vo(t[o])&&(l=!1),o++;l?e.push(...s):e.pop()}return e}class Hs{constructor(e,n){this.modelsModule=e,this.apiClient=n}create(e){return new Ws(this.apiClient,this.modelsModule,e.model,e.config,structuredClone(e.history))}}class Ws{constructor(e,n,o,s={},l=[]){this.apiClient=e,this.modelsModule=n,this.model=o,this.config=s,this.history=l,this.sendPromise=Promise.resolve(),bs(l)}async sendMessage(e){var n;await this.sendPromise;const o=J(e.message),s=this.modelsModule.generateContent({model:this.model,contents:this.getHistory(!0).concat(o),config:(n=e.config)!==null&&n!==void 0?n:this.config});return this.sendPromise=(async()=>{var l,a,u;const f=await s,c=(a=(l=f.candidates)===null||l===void 0?void 0:l[0])===null||a===void 0?void 0:a.content,d=f.automaticFunctionCallingHistory,p=this.getHistory(!0).length;let m=[];d!=null&&(m=(u=d.slice(p))!==null&&u!==void 0?u:[]);const h=c?[c]:[];this.recordHistory(o,h,m)})(),await this.sendPromise.catch(()=>{this.sendPromise=Promise.resolve()}),s}async sendMessageStream(e){var n;await this.sendPromise;const o=J(e.message),s=this.modelsModule.generateContentStream({model:this.model,contents:this.getHistory(!0).concat(o),config:(n=e.config)!==null&&n!==void 0?n:this.config});this.sendPromise=s.then(()=>{}).catch(()=>{});const l=await s;return this.processStreamResponse(l,o)}getHistory(e=!1){const n=e?Gn(this.history):this.history;return structuredClone(n)}processStreamResponse(e,n){var o,s;return he(this,arguments,function*(){var a,u,f,c;const d=[];try{for(var p=!0,m=xe(e),h;h=yield q(m.next()),a=h.done,!a;p=!0){c=h.value,p=!1;const g=c;if(Js(g)){const y=(s=(o=g.candidates)===null||o===void 0?void 0:o[0])===null||s===void 0?void 0:s.content;y!==void 0&&d.push(y)}yield yield q(g)}}catch(g){u={error:g}}finally{try{!p&&!a&&(f=m.return)&&(yield q(f.call(m)))}finally{if(u)throw u.error}}this.recordHistory(n,d)})}recordHistory(e,n,o){let s=[];n.length>0&&n.every(l=>l.role!==void 0)?s=n:s.push({role:"model",parts:[]}),o&&o.length>0?this.history.push(...Gn(o)):this.history.push(e),this.history.push(...s)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class Ze extends Error{constructor(e){super(e.message),this.name="ApiError",this.status=e.status,Object.setPrototypeOf(this,Ze.prototype)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Zs(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);return e!==void 0&&s!=null&&r(e,["_query","pageToken"],s),n}function Ys(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],Zs(n,e)),e}function Ks(t){const e={},n=i(t,["details"]);n!=null&&r(e,["details"],n);const o=i(t,["message"]);o!=null&&r(e,["message"],o);const s=i(t,["code"]);return s!=null&&r(e,["code"],s),e}function zs(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["mimeType"]);s!=null&&r(e,["mimeType"],s);const l=i(t,["sizeBytes"]);l!=null&&r(e,["sizeBytes"],l);const a=i(t,["createTime"]);a!=null&&r(e,["createTime"],a);const u=i(t,["expirationTime"]);u!=null&&r(e,["expirationTime"],u);const f=i(t,["updateTime"]);f!=null&&r(e,["updateTime"],f);const c=i(t,["sha256Hash"]);c!=null&&r(e,["sha256Hash"],c);const d=i(t,["uri"]);d!=null&&r(e,["uri"],d);const p=i(t,["downloadUri"]);p!=null&&r(e,["downloadUri"],p);const m=i(t,["state"]);m!=null&&r(e,["state"],m);const h=i(t,["source"]);h!=null&&r(e,["source"],h);const g=i(t,["videoMetadata"]);g!=null&&r(e,["videoMetadata"],g);const y=i(t,["error"]);return y!=null&&r(e,["error"],Ks(y)),e}function Os(t){const e={},n=i(t,["file"]);n!=null&&r(e,["file"],zs(n));const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function Xs(t){const e={},n=i(t,["name"]);n!=null&&r(e,["_url","file"],uo(n));const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function Qs(t){const e={},n=i(t,["name"]);n!=null&&r(e,["_url","file"],uo(n));const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function js(t){const e={},n=i(t,["details"]);n!=null&&r(e,["details"],n);const o=i(t,["message"]);o!=null&&r(e,["message"],o);const s=i(t,["code"]);return s!=null&&r(e,["code"],s),e}function ct(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["mimeType"]);s!=null&&r(e,["mimeType"],s);const l=i(t,["sizeBytes"]);l!=null&&r(e,["sizeBytes"],l);const a=i(t,["createTime"]);a!=null&&r(e,["createTime"],a);const u=i(t,["expirationTime"]);u!=null&&r(e,["expirationTime"],u);const f=i(t,["updateTime"]);f!=null&&r(e,["updateTime"],f);const c=i(t,["sha256Hash"]);c!=null&&r(e,["sha256Hash"],c);const d=i(t,["uri"]);d!=null&&r(e,["uri"],d);const p=i(t,["downloadUri"]);p!=null&&r(e,["downloadUri"],p);const m=i(t,["state"]);m!=null&&r(e,["state"],m);const h=i(t,["source"]);h!=null&&r(e,["source"],h);const g=i(t,["videoMetadata"]);g!=null&&r(e,["videoMetadata"],g);const y=i(t,["error"]);return y!=null&&r(e,["error"],js(y)),e}function el(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["files"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(l=>ct(l))),r(e,["files"],s)}return e}function tl(){return{}}function nl(){return{}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class ol extends ce{constructor(e){super(),this.apiClient=e,this.list=async(n={})=>new Pe(ue.PAGED_ITEM_FILES,o=>this.listInternal(o),await this.listInternal(n),n)}async upload(e){if(this.apiClient.isVertexAI())throw new Error("Vertex AI does not support uploading files. You can share files through a GCS bucket.");return this.apiClient.uploadFile(e.file,e.config).then(n=>ct(n))}async download(e){await this.apiClient.downloadFile(e)}async listInternal(e){var n,o;let s,l="",a={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=Ys(e);return l=E("files",u._url),a=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:l,queryParams:a,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),s.then(f=>{const c=el(f),d=new gi;return Object.assign(d,c),d})}}async createInternal(e){var n,o;let s,l="",a={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=Os(e);return l=E("upload/v1beta/files",u._url),a=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:l,queryParams:a,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),s.then(()=>{const f=tl(),c=new vi;return Object.assign(c,f),c})}}async get(e){var n,o;let s,l="",a={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=Xs(e);return l=E("files/{file}",u._url),a=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:l,queryParams:a,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),s.then(f=>ct(f))}}async delete(e){var n,o;let s,l="",a={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=Qs(e);return l=E("files/{file}",u._url),a=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:l,queryParams:a,body:JSON.stringify(u),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),s.then(()=>{const f=nl(),c=new yi;return Object.assign(c,f),c})}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function il(t){const e={},n=i(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function rl(t){const e={},n=i(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function yo(t){const e={},n=i(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],il(n)),e}function sl(t){const e={},n=i(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],rl(n)),e}function ll(t){const e={},n=i(t,["speaker"]);n!=null&&r(e,["speaker"],n);const o=i(t,["voiceConfig"]);return o!=null&&r(e,["voiceConfig"],yo(o)),e}function al(t){const e={},n=i(t,["speakerVoiceConfigs"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>ll(s))),r(e,["speakerVoiceConfigs"],o)}return e}function ul(t){const e={},n=i(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],yo(n));const o=i(t,["multiSpeakerVoiceConfig"]);o!=null&&r(e,["multiSpeakerVoiceConfig"],al(o));const s=i(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function cl(t){const e={},n=i(t,["voiceConfig"]);if(n!=null&&r(e,["voiceConfig"],sl(n)),i(t,["multiSpeakerVoiceConfig"])!==void 0)throw new Error("multiSpeakerVoiceConfig parameter is not supported in Vertex AI.");const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function fl(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function dl(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function pl(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function ml(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["data"]);o!=null&&r(e,["data"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function hl(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function gl(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["fileUri"]);o!=null&&r(e,["fileUri"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function vl(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],fl(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],pl(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],hl(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function yl(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],dl(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],ml(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],gl(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Tl(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>vl(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Cl(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>yl(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function _l(t){const e={},n=i(t,["behavior"]);n!=null&&r(e,["behavior"],n);const o=i(t,["description"]);o!=null&&r(e,["description"],o);const s=i(t,["name"]);s!=null&&r(e,["name"],s);const l=i(t,["parameters"]);l!=null&&r(e,["parameters"],l);const a=i(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const u=i(t,["response"]);u!=null&&r(e,["response"],u);const f=i(t,["responseJsonSchema"]);return f!=null&&r(e,["responseJsonSchema"],f),e}function Sl(t){const e={};if(i(t,["behavior"])!==void 0)throw new Error("behavior parameter is not supported in Vertex AI.");const n=i(t,["description"]);n!=null&&r(e,["description"],n);const o=i(t,["name"]);o!=null&&r(e,["name"],o);const s=i(t,["parameters"]);s!=null&&r(e,["parameters"],s);const l=i(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const a=i(t,["response"]);a!=null&&r(e,["response"],a);const u=i(t,["responseJsonSchema"]);return u!=null&&r(e,["responseJsonSchema"],u),e}function El(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function Al(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function Ml(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],El(n)),e}function Il(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Al(n)),e}function xl(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function Rl(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function wl(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],xl(n)),e}function Pl(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Rl(n)),e}function Nl(){return{}}function kl(t){const e={},n=i(t,["apiKeyString"]);return n!=null&&r(e,["apiKeyString"],n),e}function Dl(t){const e={},n=i(t,["apiKeyConfig"]);n!=null&&r(e,["apiKeyConfig"],kl(n));const o=i(t,["authType"]);o!=null&&r(e,["authType"],o);const s=i(t,["googleServiceAccountConfig"]);s!=null&&r(e,["googleServiceAccountConfig"],s);const l=i(t,["httpBasicAuthConfig"]);l!=null&&r(e,["httpBasicAuthConfig"],l);const a=i(t,["oauthConfig"]);a!=null&&r(e,["oauthConfig"],a);const u=i(t,["oidcConfig"]);return u!=null&&r(e,["oidcConfig"],u),e}function Vl(t){const e={},n=i(t,["authConfig"]);return n!=null&&r(e,["authConfig"],Dl(n)),e}function Fl(){return{}}function Ll(){return{}}function Ul(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let u=n;Array.isArray(u)&&(u=u.map(f=>_l(f))),r(e,["functionDeclarations"],u)}if(i(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const o=i(t,["googleSearch"]);o!=null&&r(e,["googleSearch"],Ml(o));const s=i(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],wl(s)),i(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(i(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");i(t,["urlContext"])!=null&&r(e,["urlContext"],Fl());const a=i(t,["codeExecution"]);return a!=null&&r(e,["codeExecution"],a),e}function Gl(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(p=>Sl(p))),r(e,["functionDeclarations"],d)}const o=i(t,["retrieval"]);o!=null&&r(e,["retrieval"],o);const s=i(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],Il(s));const l=i(t,["googleSearchRetrieval"]);l!=null&&r(e,["googleSearchRetrieval"],Pl(l)),i(t,["enterpriseWebSearch"])!=null&&r(e,["enterpriseWebSearch"],Nl());const u=i(t,["googleMaps"]);u!=null&&r(e,["googleMaps"],Vl(u)),i(t,["urlContext"])!=null&&r(e,["urlContext"],Ll());const c=i(t,["codeExecution"]);return c!=null&&r(e,["codeExecution"],c),e}function Bl(t){const e={},n=i(t,["handle"]);if(n!=null&&r(e,["handle"],n),i(t,["transparent"])!==void 0)throw new Error("transparent parameter is not supported in Gemini API.");return e}function ql(t){const e={},n=i(t,["handle"]);n!=null&&r(e,["handle"],n);const o=i(t,["transparent"]);return o!=null&&r(e,["transparent"],o),e}function Bn(){return{}}function qn(){return{}}function $l(t){const e={},n=i(t,["disabled"]);n!=null&&r(e,["disabled"],n);const o=i(t,["startOfSpeechSensitivity"]);o!=null&&r(e,["startOfSpeechSensitivity"],o);const s=i(t,["endOfSpeechSensitivity"]);s!=null&&r(e,["endOfSpeechSensitivity"],s);const l=i(t,["prefixPaddingMs"]);l!=null&&r(e,["prefixPaddingMs"],l);const a=i(t,["silenceDurationMs"]);return a!=null&&r(e,["silenceDurationMs"],a),e}function Jl(t){const e={},n=i(t,["disabled"]);n!=null&&r(e,["disabled"],n);const o=i(t,["startOfSpeechSensitivity"]);o!=null&&r(e,["startOfSpeechSensitivity"],o);const s=i(t,["endOfSpeechSensitivity"]);s!=null&&r(e,["endOfSpeechSensitivity"],s);const l=i(t,["prefixPaddingMs"]);l!=null&&r(e,["prefixPaddingMs"],l);const a=i(t,["silenceDurationMs"]);return a!=null&&r(e,["silenceDurationMs"],a),e}function bl(t){const e={},n=i(t,["automaticActivityDetection"]);n!=null&&r(e,["automaticActivityDetection"],$l(n));const o=i(t,["activityHandling"]);o!=null&&r(e,["activityHandling"],o);const s=i(t,["turnCoverage"]);return s!=null&&r(e,["turnCoverage"],s),e}function Hl(t){const e={},n=i(t,["automaticActivityDetection"]);n!=null&&r(e,["automaticActivityDetection"],Jl(n));const o=i(t,["activityHandling"]);o!=null&&r(e,["activityHandling"],o);const s=i(t,["turnCoverage"]);return s!=null&&r(e,["turnCoverage"],s),e}function Wl(t){const e={},n=i(t,["targetTokens"]);return n!=null&&r(e,["targetTokens"],n),e}function Zl(t){const e={},n=i(t,["targetTokens"]);return n!=null&&r(e,["targetTokens"],n),e}function Yl(t){const e={},n=i(t,["triggerTokens"]);n!=null&&r(e,["triggerTokens"],n);const o=i(t,["slidingWindow"]);return o!=null&&r(e,["slidingWindow"],Wl(o)),e}function Kl(t){const e={},n=i(t,["triggerTokens"]);n!=null&&r(e,["triggerTokens"],n);const o=i(t,["slidingWindow"]);return o!=null&&r(e,["slidingWindow"],Zl(o)),e}function zl(t){const e={},n=i(t,["proactiveAudio"]);return n!=null&&r(e,["proactiveAudio"],n),e}function Ol(t){const e={},n=i(t,["proactiveAudio"]);return n!=null&&r(e,["proactiveAudio"],n),e}function Xl(t,e){const n={},o=i(t,["generationConfig"]);e!==void 0&&o!=null&&r(e,["setup","generationConfig"],o);const s=i(t,["responseModalities"]);e!==void 0&&s!=null&&r(e,["setup","generationConfig","responseModalities"],s);const l=i(t,["temperature"]);e!==void 0&&l!=null&&r(e,["setup","generationConfig","temperature"],l);const a=i(t,["topP"]);e!==void 0&&a!=null&&r(e,["setup","generationConfig","topP"],a);const u=i(t,["topK"]);e!==void 0&&u!=null&&r(e,["setup","generationConfig","topK"],u);const f=i(t,["maxOutputTokens"]);e!==void 0&&f!=null&&r(e,["setup","generationConfig","maxOutputTokens"],f);const c=i(t,["mediaResolution"]);e!==void 0&&c!=null&&r(e,["setup","generationConfig","mediaResolution"],c);const d=i(t,["seed"]);e!==void 0&&d!=null&&r(e,["setup","generationConfig","seed"],d);const p=i(t,["speechConfig"]);e!==void 0&&p!=null&&r(e,["setup","generationConfig","speechConfig"],ul(yt(p)));const m=i(t,["enableAffectiveDialog"]);e!==void 0&&m!=null&&r(e,["setup","generationConfig","enableAffectiveDialog"],m);const h=i(t,["systemInstruction"]);e!==void 0&&h!=null&&r(e,["setup","systemInstruction"],Tl(J(h)));const g=i(t,["tools"]);if(e!==void 0&&g!=null){let I=Se(g);Array.isArray(I)&&(I=I.map(U=>Ul(_e(U)))),r(e,["setup","tools"],I)}const y=i(t,["sessionResumption"]);e!==void 0&&y!=null&&r(e,["setup","sessionResumption"],Bl(y));const S=i(t,["inputAudioTranscription"]);e!==void 0&&S!=null&&r(e,["setup","inputAudioTranscription"],Bn());const A=i(t,["outputAudioTranscription"]);e!==void 0&&A!=null&&r(e,["setup","outputAudioTranscription"],Bn());const x=i(t,["realtimeInputConfig"]);e!==void 0&&x!=null&&r(e,["setup","realtimeInputConfig"],bl(x));const M=i(t,["contextWindowCompression"]);e!==void 0&&M!=null&&r(e,["setup","contextWindowCompression"],Yl(M));const P=i(t,["proactivity"]);return e!==void 0&&P!=null&&r(e,["setup","proactivity"],zl(P)),n}function Ql(t,e){const n={},o=i(t,["generationConfig"]);e!==void 0&&o!=null&&r(e,["setup","generationConfig"],o);const s=i(t,["responseModalities"]);e!==void 0&&s!=null&&r(e,["setup","generationConfig","responseModalities"],s);const l=i(t,["temperature"]);e!==void 0&&l!=null&&r(e,["setup","generationConfig","temperature"],l);const a=i(t,["topP"]);e!==void 0&&a!=null&&r(e,["setup","generationConfig","topP"],a);const u=i(t,["topK"]);e!==void 0&&u!=null&&r(e,["setup","generationConfig","topK"],u);const f=i(t,["maxOutputTokens"]);e!==void 0&&f!=null&&r(e,["setup","generationConfig","maxOutputTokens"],f);const c=i(t,["mediaResolution"]);e!==void 0&&c!=null&&r(e,["setup","generationConfig","mediaResolution"],c);const d=i(t,["seed"]);e!==void 0&&d!=null&&r(e,["setup","generationConfig","seed"],d);const p=i(t,["speechConfig"]);e!==void 0&&p!=null&&r(e,["setup","generationConfig","speechConfig"],cl(yt(p)));const m=i(t,["enableAffectiveDialog"]);e!==void 0&&m!=null&&r(e,["setup","generationConfig","enableAffectiveDialog"],m);const h=i(t,["systemInstruction"]);e!==void 0&&h!=null&&r(e,["setup","systemInstruction"],Cl(J(h)));const g=i(t,["tools"]);if(e!==void 0&&g!=null){let I=Se(g);Array.isArray(I)&&(I=I.map(U=>Gl(_e(U)))),r(e,["setup","tools"],I)}const y=i(t,["sessionResumption"]);e!==void 0&&y!=null&&r(e,["setup","sessionResumption"],ql(y));const S=i(t,["inputAudioTranscription"]);e!==void 0&&S!=null&&r(e,["setup","inputAudioTranscription"],qn());const A=i(t,["outputAudioTranscription"]);e!==void 0&&A!=null&&r(e,["setup","outputAudioTranscription"],qn());const x=i(t,["realtimeInputConfig"]);e!==void 0&&x!=null&&r(e,["setup","realtimeInputConfig"],Hl(x));const M=i(t,["contextWindowCompression"]);e!==void 0&&M!=null&&r(e,["setup","contextWindowCompression"],Kl(M));const P=i(t,["proactivity"]);return e!==void 0&&P!=null&&r(e,["setup","proactivity"],Ol(P)),n}function jl(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["setup","model"],L(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],Xl(s,n)),n}function ea(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["setup","model"],L(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],Ql(s,n)),n}function ta(){return{}}function na(){return{}}function oa(){return{}}function ia(){return{}}function ra(t){const e={},n=i(t,["media"]);n!=null&&r(e,["mediaChunks"],ro(n));const o=i(t,["audio"]);o!=null&&r(e,["audio"],Si(o));const s=i(t,["audioStreamEnd"]);s!=null&&r(e,["audioStreamEnd"],s);const l=i(t,["video"]);l!=null&&r(e,["video"],_i(l));const a=i(t,["text"]);return a!=null&&r(e,["text"],a),i(t,["activityStart"])!=null&&r(e,["activityStart"],ta()),i(t,["activityEnd"])!=null&&r(e,["activityEnd"],oa()),e}function sa(t){const e={},n=i(t,["media"]);if(n!=null&&r(e,["mediaChunks"],ro(n)),i(t,["audio"])!==void 0)throw new Error("audio parameter is not supported in Vertex AI.");const o=i(t,["audioStreamEnd"]);if(o!=null&&r(e,["audioStreamEnd"],o),i(t,["video"])!==void 0)throw new Error("video parameter is not supported in Vertex AI.");if(i(t,["text"])!==void 0)throw new Error("text parameter is not supported in Vertex AI.");return i(t,["activityStart"])!=null&&r(e,["activityStart"],na()),i(t,["activityEnd"])!=null&&r(e,["activityEnd"],ia()),e}function To(t){const e={},n=i(t,["text"]);n!=null&&r(e,["text"],n);const o=i(t,["weight"]);return o!=null&&r(e,["weight"],o),e}function la(t){const e={},n=i(t,["weightedPrompts"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>To(s))),r(e,["weightedPrompts"],o)}return e}function Co(t){const e={},n=i(t,["temperature"]);n!=null&&r(e,["temperature"],n);const o=i(t,["topK"]);o!=null&&r(e,["topK"],o);const s=i(t,["seed"]);s!=null&&r(e,["seed"],s);const l=i(t,["guidance"]);l!=null&&r(e,["guidance"],l);const a=i(t,["bpm"]);a!=null&&r(e,["bpm"],a);const u=i(t,["density"]);u!=null&&r(e,["density"],u);const f=i(t,["brightness"]);f!=null&&r(e,["brightness"],f);const c=i(t,["scale"]);c!=null&&r(e,["scale"],c);const d=i(t,["muteBass"]);d!=null&&r(e,["muteBass"],d);const p=i(t,["muteDrums"]);p!=null&&r(e,["muteDrums"],p);const m=i(t,["onlyBassAndDrums"]);return m!=null&&r(e,["onlyBassAndDrums"],m),e}function aa(t){const e={},n=i(t,["musicGenerationConfig"]);return n!=null&&r(e,["musicGenerationConfig"],Co(n)),e}function _o(t){const e={},n=i(t,["model"]);return n!=null&&r(e,["model"],n),e}function So(t){const e={},n=i(t,["weightedPrompts"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>To(s))),r(e,["weightedPrompts"],o)}return e}function ft(t){const e={},n=i(t,["setup"]);n!=null&&r(e,["setup"],_o(n));const o=i(t,["clientContent"]);o!=null&&r(e,["clientContent"],So(o));const s=i(t,["musicGenerationConfig"]);s!=null&&r(e,["musicGenerationConfig"],Co(s));const l=i(t,["playbackControl"]);return l!=null&&r(e,["playbackControl"],l),e}function ua(){return{}}function ca(){return{}}function fa(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function da(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function pa(t){const e={},n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function ma(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["data"]);o!=null&&r(e,["data"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function ha(t){const e={},n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function ga(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["fileUri"]);o!=null&&r(e,["fileUri"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function va(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],fa(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],pa(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],ha(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function ya(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],da(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],ma(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],ga(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Ta(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>va(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Ca(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>ya(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function $n(t){const e={},n=i(t,["text"]);n!=null&&r(e,["text"],n);const o=i(t,["finished"]);return o!=null&&r(e,["finished"],o),e}function Jn(t){const e={},n=i(t,["text"]);n!=null&&r(e,["text"],n);const o=i(t,["finished"]);return o!=null&&r(e,["finished"],o),e}function _a(t){const e={},n=i(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const o=i(t,["urlRetrievalStatus"]);return o!=null&&r(e,["urlRetrievalStatus"],o),e}function Sa(t){const e={},n=i(t,["urlMetadata"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>_a(s))),r(e,["urlMetadata"],o)}return e}function Ea(t){const e={},n=i(t,["modelTurn"]);n!=null&&r(e,["modelTurn"],Ta(n));const o=i(t,["turnComplete"]);o!=null&&r(e,["turnComplete"],o);const s=i(t,["interrupted"]);s!=null&&r(e,["interrupted"],s);const l=i(t,["groundingMetadata"]);l!=null&&r(e,["groundingMetadata"],l);const a=i(t,["generationComplete"]);a!=null&&r(e,["generationComplete"],a);const u=i(t,["inputTranscription"]);u!=null&&r(e,["inputTranscription"],$n(u));const f=i(t,["outputTranscription"]);f!=null&&r(e,["outputTranscription"],$n(f));const c=i(t,["urlContextMetadata"]);return c!=null&&r(e,["urlContextMetadata"],Sa(c)),e}function Aa(t){const e={},n=i(t,["modelTurn"]);n!=null&&r(e,["modelTurn"],Ca(n));const o=i(t,["turnComplete"]);o!=null&&r(e,["turnComplete"],o);const s=i(t,["interrupted"]);s!=null&&r(e,["interrupted"],s);const l=i(t,["groundingMetadata"]);l!=null&&r(e,["groundingMetadata"],l);const a=i(t,["generationComplete"]);a!=null&&r(e,["generationComplete"],a);const u=i(t,["inputTranscription"]);u!=null&&r(e,["inputTranscription"],Jn(u));const f=i(t,["outputTranscription"]);return f!=null&&r(e,["outputTranscription"],Jn(f)),e}function Ma(t){const e={},n=i(t,["id"]);n!=null&&r(e,["id"],n);const o=i(t,["args"]);o!=null&&r(e,["args"],o);const s=i(t,["name"]);return s!=null&&r(e,["name"],s),e}function Ia(t){const e={},n=i(t,["args"]);n!=null&&r(e,["args"],n);const o=i(t,["name"]);return o!=null&&r(e,["name"],o),e}function xa(t){const e={},n=i(t,["functionCalls"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>Ma(s))),r(e,["functionCalls"],o)}return e}function Ra(t){const e={},n=i(t,["functionCalls"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>Ia(s))),r(e,["functionCalls"],o)}return e}function wa(t){const e={},n=i(t,["ids"]);return n!=null&&r(e,["ids"],n),e}function Pa(t){const e={},n=i(t,["ids"]);return n!=null&&r(e,["ids"],n),e}function Ne(t){const e={},n=i(t,["modality"]);n!=null&&r(e,["modality"],n);const o=i(t,["tokenCount"]);return o!=null&&r(e,["tokenCount"],o),e}function ke(t){const e={},n=i(t,["modality"]);n!=null&&r(e,["modality"],n);const o=i(t,["tokenCount"]);return o!=null&&r(e,["tokenCount"],o),e}function Na(t){const e={},n=i(t,["promptTokenCount"]);n!=null&&r(e,["promptTokenCount"],n);const o=i(t,["cachedContentTokenCount"]);o!=null&&r(e,["cachedContentTokenCount"],o);const s=i(t,["responseTokenCount"]);s!=null&&r(e,["responseTokenCount"],s);const l=i(t,["toolUsePromptTokenCount"]);l!=null&&r(e,["toolUsePromptTokenCount"],l);const a=i(t,["thoughtsTokenCount"]);a!=null&&r(e,["thoughtsTokenCount"],a);const u=i(t,["totalTokenCount"]);u!=null&&r(e,["totalTokenCount"],u);const f=i(t,["promptTokensDetails"]);if(f!=null){let m=f;Array.isArray(m)&&(m=m.map(h=>Ne(h))),r(e,["promptTokensDetails"],m)}const c=i(t,["cacheTokensDetails"]);if(c!=null){let m=c;Array.isArray(m)&&(m=m.map(h=>Ne(h))),r(e,["cacheTokensDetails"],m)}const d=i(t,["responseTokensDetails"]);if(d!=null){let m=d;Array.isArray(m)&&(m=m.map(h=>Ne(h))),r(e,["responseTokensDetails"],m)}const p=i(t,["toolUsePromptTokensDetails"]);if(p!=null){let m=p;Array.isArray(m)&&(m=m.map(h=>Ne(h))),r(e,["toolUsePromptTokensDetails"],m)}return e}function ka(t){const e={},n=i(t,["promptTokenCount"]);n!=null&&r(e,["promptTokenCount"],n);const o=i(t,["cachedContentTokenCount"]);o!=null&&r(e,["cachedContentTokenCount"],o);const s=i(t,["candidatesTokenCount"]);s!=null&&r(e,["responseTokenCount"],s);const l=i(t,["toolUsePromptTokenCount"]);l!=null&&r(e,["toolUsePromptTokenCount"],l);const a=i(t,["thoughtsTokenCount"]);a!=null&&r(e,["thoughtsTokenCount"],a);const u=i(t,["totalTokenCount"]);u!=null&&r(e,["totalTokenCount"],u);const f=i(t,["promptTokensDetails"]);if(f!=null){let h=f;Array.isArray(h)&&(h=h.map(g=>ke(g))),r(e,["promptTokensDetails"],h)}const c=i(t,["cacheTokensDetails"]);if(c!=null){let h=c;Array.isArray(h)&&(h=h.map(g=>ke(g))),r(e,["cacheTokensDetails"],h)}const d=i(t,["candidatesTokensDetails"]);if(d!=null){let h=d;Array.isArray(h)&&(h=h.map(g=>ke(g))),r(e,["responseTokensDetails"],h)}const p=i(t,["toolUsePromptTokensDetails"]);if(p!=null){let h=p;Array.isArray(h)&&(h=h.map(g=>ke(g))),r(e,["toolUsePromptTokensDetails"],h)}const m=i(t,["trafficType"]);return m!=null&&r(e,["trafficType"],m),e}function Da(t){const e={},n=i(t,["timeLeft"]);return n!=null&&r(e,["timeLeft"],n),e}function Va(t){const e={},n=i(t,["timeLeft"]);return n!=null&&r(e,["timeLeft"],n),e}function Fa(t){const e={},n=i(t,["newHandle"]);n!=null&&r(e,["newHandle"],n);const o=i(t,["resumable"]);o!=null&&r(e,["resumable"],o);const s=i(t,["lastConsumedClientMessageIndex"]);return s!=null&&r(e,["lastConsumedClientMessageIndex"],s),e}function La(t){const e={},n=i(t,["newHandle"]);n!=null&&r(e,["newHandle"],n);const o=i(t,["resumable"]);o!=null&&r(e,["resumable"],o);const s=i(t,["lastConsumedClientMessageIndex"]);return s!=null&&r(e,["lastConsumedClientMessageIndex"],s),e}function Ua(t){const e={};i(t,["setupComplete"])!=null&&r(e,["setupComplete"],ua());const o=i(t,["serverContent"]);o!=null&&r(e,["serverContent"],Ea(o));const s=i(t,["toolCall"]);s!=null&&r(e,["toolCall"],xa(s));const l=i(t,["toolCallCancellation"]);l!=null&&r(e,["toolCallCancellation"],wa(l));const a=i(t,["usageMetadata"]);a!=null&&r(e,["usageMetadata"],Na(a));const u=i(t,["goAway"]);u!=null&&r(e,["goAway"],Da(u));const f=i(t,["sessionResumptionUpdate"]);return f!=null&&r(e,["sessionResumptionUpdate"],Fa(f)),e}function Ga(t){const e={};i(t,["setupComplete"])!=null&&r(e,["setupComplete"],ca());const o=i(t,["serverContent"]);o!=null&&r(e,["serverContent"],Aa(o));const s=i(t,["toolCall"]);s!=null&&r(e,["toolCall"],Ra(s));const l=i(t,["toolCallCancellation"]);l!=null&&r(e,["toolCallCancellation"],Pa(l));const a=i(t,["usageMetadata"]);a!=null&&r(e,["usageMetadata"],ka(a));const u=i(t,["goAway"]);u!=null&&r(e,["goAway"],Va(u));const f=i(t,["sessionResumptionUpdate"]);return f!=null&&r(e,["sessionResumptionUpdate"],La(f)),e}function Ba(){return{}}function qa(t){const e={},n=i(t,["text"]);n!=null&&r(e,["text"],n);const o=i(t,["weight"]);return o!=null&&r(e,["weight"],o),e}function $a(t){const e={},n=i(t,["weightedPrompts"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>qa(s))),r(e,["weightedPrompts"],o)}return e}function Ja(t){const e={},n=i(t,["temperature"]);n!=null&&r(e,["temperature"],n);const o=i(t,["topK"]);o!=null&&r(e,["topK"],o);const s=i(t,["seed"]);s!=null&&r(e,["seed"],s);const l=i(t,["guidance"]);l!=null&&r(e,["guidance"],l);const a=i(t,["bpm"]);a!=null&&r(e,["bpm"],a);const u=i(t,["density"]);u!=null&&r(e,["density"],u);const f=i(t,["brightness"]);f!=null&&r(e,["brightness"],f);const c=i(t,["scale"]);c!=null&&r(e,["scale"],c);const d=i(t,["muteBass"]);d!=null&&r(e,["muteBass"],d);const p=i(t,["muteDrums"]);p!=null&&r(e,["muteDrums"],p);const m=i(t,["onlyBassAndDrums"]);return m!=null&&r(e,["onlyBassAndDrums"],m),e}function ba(t){const e={},n=i(t,["clientContent"]);n!=null&&r(e,["clientContent"],$a(n));const o=i(t,["musicGenerationConfig"]);return o!=null&&r(e,["musicGenerationConfig"],Ja(o)),e}function Ha(t){const e={},n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);o!=null&&r(e,["mimeType"],o);const s=i(t,["sourceMetadata"]);return s!=null&&r(e,["sourceMetadata"],ba(s)),e}function Wa(t){const e={},n=i(t,["audioChunks"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>Ha(s))),r(e,["audioChunks"],o)}return e}function Za(t){const e={},n=i(t,["text"]);n!=null&&r(e,["text"],n);const o=i(t,["filteredReason"]);return o!=null&&r(e,["filteredReason"],o),e}function Ya(t){const e={};i(t,["setupComplete"])!=null&&r(e,["setupComplete"],Ba());const o=i(t,["serverContent"]);o!=null&&r(e,["serverContent"],Wa(o));const s=i(t,["filteredPrompt"]);return s!=null&&r(e,["filteredPrompt"],Za(s)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Ka(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function za(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Oa(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Xa(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Ka(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],za(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],Oa(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Ye(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>Xa(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Qa(t){const e={},n=i(t,["anyOf"]);n!=null&&r(e,["anyOf"],n);const o=i(t,["default"]);o!=null&&r(e,["default"],o);const s=i(t,["description"]);s!=null&&r(e,["description"],s);const l=i(t,["enum"]);l!=null&&r(e,["enum"],l);const a=i(t,["example"]);a!=null&&r(e,["example"],a);const u=i(t,["format"]);u!=null&&r(e,["format"],u);const f=i(t,["items"]);f!=null&&r(e,["items"],f);const c=i(t,["maxItems"]);c!=null&&r(e,["maxItems"],c);const d=i(t,["maxLength"]);d!=null&&r(e,["maxLength"],d);const p=i(t,["maxProperties"]);p!=null&&r(e,["maxProperties"],p);const m=i(t,["maximum"]);m!=null&&r(e,["maximum"],m);const h=i(t,["minItems"]);h!=null&&r(e,["minItems"],h);const g=i(t,["minLength"]);g!=null&&r(e,["minLength"],g);const y=i(t,["minProperties"]);y!=null&&r(e,["minProperties"],y);const S=i(t,["minimum"]);S!=null&&r(e,["minimum"],S);const A=i(t,["nullable"]);A!=null&&r(e,["nullable"],A);const x=i(t,["pattern"]);x!=null&&r(e,["pattern"],x);const M=i(t,["properties"]);M!=null&&r(e,["properties"],M);const P=i(t,["propertyOrdering"]);P!=null&&r(e,["propertyOrdering"],P);const I=i(t,["required"]);I!=null&&r(e,["required"],I);const U=i(t,["title"]);U!=null&&r(e,["title"],U);const G=i(t,["type"]);return G!=null&&r(e,["type"],G),e}function ja(t){const e={};if(i(t,["method"])!==void 0)throw new Error("method parameter is not supported in Gemini API.");const n=i(t,["category"]);n!=null&&r(e,["category"],n);const o=i(t,["threshold"]);return o!=null&&r(e,["threshold"],o),e}function eu(t){const e={},n=i(t,["behavior"]);n!=null&&r(e,["behavior"],n);const o=i(t,["description"]);o!=null&&r(e,["description"],o);const s=i(t,["name"]);s!=null&&r(e,["name"],s);const l=i(t,["parameters"]);l!=null&&r(e,["parameters"],l);const a=i(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const u=i(t,["response"]);u!=null&&r(e,["response"],u);const f=i(t,["responseJsonSchema"]);return f!=null&&r(e,["responseJsonSchema"],f),e}function tu(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function nu(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],tu(n)),e}function ou(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function iu(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],ou(n)),e}function ru(){return{}}function su(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let u=n;Array.isArray(u)&&(u=u.map(f=>eu(f))),r(e,["functionDeclarations"],u)}if(i(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const o=i(t,["googleSearch"]);o!=null&&r(e,["googleSearch"],nu(o));const s=i(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],iu(s)),i(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(i(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");i(t,["urlContext"])!=null&&r(e,["urlContext"],ru());const a=i(t,["codeExecution"]);return a!=null&&r(e,["codeExecution"],a),e}function lu(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["allowedFunctionNames"]);return o!=null&&r(e,["allowedFunctionNames"],o),e}function au(t){const e={},n=i(t,["latitude"]);n!=null&&r(e,["latitude"],n);const o=i(t,["longitude"]);return o!=null&&r(e,["longitude"],o),e}function uu(t){const e={},n=i(t,["latLng"]);n!=null&&r(e,["latLng"],au(n));const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function cu(t){const e={},n=i(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],lu(n));const o=i(t,["retrievalConfig"]);return o!=null&&r(e,["retrievalConfig"],uu(o)),e}function fu(t){const e={},n=i(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function Eo(t){const e={},n=i(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],fu(n)),e}function du(t){const e={},n=i(t,["speaker"]);n!=null&&r(e,["speaker"],n);const o=i(t,["voiceConfig"]);return o!=null&&r(e,["voiceConfig"],Eo(o)),e}function pu(t){const e={},n=i(t,["speakerVoiceConfigs"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>du(s))),r(e,["speakerVoiceConfigs"],o)}return e}function mu(t){const e={},n=i(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],Eo(n));const o=i(t,["multiSpeakerVoiceConfig"]);o!=null&&r(e,["multiSpeakerVoiceConfig"],pu(o));const s=i(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function hu(t){const e={},n=i(t,["includeThoughts"]);n!=null&&r(e,["includeThoughts"],n);const o=i(t,["thinkingBudget"]);return o!=null&&r(e,["thinkingBudget"],o),e}function gu(t,e,n){const o={},s=i(e,["systemInstruction"]);n!==void 0&&s!=null&&r(n,["systemInstruction"],Ye(J(s)));const l=i(e,["temperature"]);l!=null&&r(o,["temperature"],l);const a=i(e,["topP"]);a!=null&&r(o,["topP"],a);const u=i(e,["topK"]);u!=null&&r(o,["topK"],u);const f=i(e,["candidateCount"]);f!=null&&r(o,["candidateCount"],f);const c=i(e,["maxOutputTokens"]);c!=null&&r(o,["maxOutputTokens"],c);const d=i(e,["stopSequences"]);d!=null&&r(o,["stopSequences"],d);const p=i(e,["responseLogprobs"]);p!=null&&r(o,["responseLogprobs"],p);const m=i(e,["logprobs"]);m!=null&&r(o,["logprobs"],m);const h=i(e,["presencePenalty"]);h!=null&&r(o,["presencePenalty"],h);const g=i(e,["frequencyPenalty"]);g!=null&&r(o,["frequencyPenalty"],g);const y=i(e,["seed"]);y!=null&&r(o,["seed"],y);const S=i(e,["responseMimeType"]);S!=null&&r(o,["responseMimeType"],S);const A=i(e,["responseSchema"]);A!=null&&r(o,["responseSchema"],Qa(we(A)));const x=i(e,["responseJsonSchema"]);if(x!=null&&r(o,["responseJsonSchema"],x),i(e,["routingConfig"])!==void 0)throw new Error("routingConfig parameter is not supported in Gemini API.");if(i(e,["modelSelectionConfig"])!==void 0)throw new Error("modelSelectionConfig parameter is not supported in Gemini API.");const M=i(e,["safetySettings"]);if(n!==void 0&&M!=null){let F=M;Array.isArray(F)&&(F=F.map(Y=>ja(Y))),r(n,["safetySettings"],F)}const P=i(e,["tools"]);if(n!==void 0&&P!=null){let F=Se(P);Array.isArray(F)&&(F=F.map(Y=>su(_e(Y)))),r(n,["tools"],F)}const I=i(e,["toolConfig"]);if(n!==void 0&&I!=null&&r(n,["toolConfig"],cu(I)),i(e,["labels"])!==void 0)throw new Error("labels parameter is not supported in Gemini API.");const U=i(e,["cachedContent"]);n!==void 0&&U!=null&&r(n,["cachedContent"],ee(t,U));const G=i(e,["responseModalities"]);G!=null&&r(o,["responseModalities"],G);const H=i(e,["mediaResolution"]);H!=null&&r(o,["mediaResolution"],H);const N=i(e,["speechConfig"]);if(N!=null&&r(o,["speechConfig"],mu(vt(N))),i(e,["audioTimestamp"])!==void 0)throw new Error("audioTimestamp parameter is not supported in Gemini API.");const B=i(e,["thinkingConfig"]);return B!=null&&r(o,["thinkingConfig"],hu(B)),o}function bn(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["contents"]);if(s!=null){let a=W(s);Array.isArray(a)&&(a=a.map(u=>Ye(u))),r(n,["contents"],a)}const l=i(e,["config"]);return l!=null&&r(n,["generationConfig"],gu(t,l,n)),n}function vu(t,e){const n={},o=i(t,["taskType"]);e!==void 0&&o!=null&&r(e,["requests[]","taskType"],o);const s=i(t,["title"]);e!==void 0&&s!=null&&r(e,["requests[]","title"],s);const l=i(t,["outputDimensionality"]);if(e!==void 0&&l!=null&&r(e,["requests[]","outputDimensionality"],l),i(t,["mimeType"])!==void 0)throw new Error("mimeType parameter is not supported in Gemini API.");if(i(t,["autoTruncate"])!==void 0)throw new Error("autoTruncate parameter is not supported in Gemini API.");return n}function yu(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["contents"]);s!=null&&r(n,["requests[]","content"],lo(t,s));const l=i(e,["config"]);l!=null&&r(n,["config"],vu(l,n));const a=i(e,["model"]);return a!==void 0&&r(n,["requests[]","model"],L(t,a)),n}function Tu(t,e){const n={};if(i(t,["outputGcsUri"])!==void 0)throw new Error("outputGcsUri parameter is not supported in Gemini API.");if(i(t,["negativePrompt"])!==void 0)throw new Error("negativePrompt parameter is not supported in Gemini API.");const o=i(t,["numberOfImages"]);e!==void 0&&o!=null&&r(e,["parameters","sampleCount"],o);const s=i(t,["aspectRatio"]);e!==void 0&&s!=null&&r(e,["parameters","aspectRatio"],s);const l=i(t,["guidanceScale"]);if(e!==void 0&&l!=null&&r(e,["parameters","guidanceScale"],l),i(t,["seed"])!==void 0)throw new Error("seed parameter is not supported in Gemini API.");const a=i(t,["safetyFilterLevel"]);e!==void 0&&a!=null&&r(e,["parameters","safetySetting"],a);const u=i(t,["personGeneration"]);e!==void 0&&u!=null&&r(e,["parameters","personGeneration"],u);const f=i(t,["includeSafetyAttributes"]);e!==void 0&&f!=null&&r(e,["parameters","includeSafetyAttributes"],f);const c=i(t,["includeRaiReason"]);e!==void 0&&c!=null&&r(e,["parameters","includeRaiReason"],c);const d=i(t,["language"]);e!==void 0&&d!=null&&r(e,["parameters","language"],d);const p=i(t,["outputMimeType"]);e!==void 0&&p!=null&&r(e,["parameters","outputOptions","mimeType"],p);const m=i(t,["outputCompressionQuality"]);if(e!==void 0&&m!=null&&r(e,["parameters","outputOptions","compressionQuality"],m),i(t,["addWatermark"])!==void 0)throw new Error("addWatermark parameter is not supported in Gemini API.");if(i(t,["enhancePrompt"])!==void 0)throw new Error("enhancePrompt parameter is not supported in Gemini API.");return n}function Cu(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const l=i(e,["config"]);return l!=null&&r(n,["config"],Tu(l,n)),n}function _u(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","name"],L(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Su(t,e,n){const o={},s=i(e,["pageSize"]);n!==void 0&&s!=null&&r(n,["_query","pageSize"],s);const l=i(e,["pageToken"]);n!==void 0&&l!=null&&r(n,["_query","pageToken"],l);const a=i(e,["filter"]);n!==void 0&&a!=null&&r(n,["_query","filter"],a);const u=i(e,["queryBase"]);return n!==void 0&&u!=null&&r(n,["_url","models_url"],co(t,u)),o}function Eu(t,e){const n={},o=i(e,["config"]);return o!=null&&r(n,["config"],Su(t,o,n)),n}function Au(t,e){const n={},o=i(t,["displayName"]);e!==void 0&&o!=null&&r(e,["displayName"],o);const s=i(t,["description"]);e!==void 0&&s!=null&&r(e,["description"],s);const l=i(t,["defaultCheckpointId"]);return e!==void 0&&l!=null&&r(e,["defaultCheckpointId"],l),n}function Mu(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","name"],L(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],Au(s,n)),n}function Iu(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","name"],L(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function xu(t){const e={};if(i(t,["systemInstruction"])!==void 0)throw new Error("systemInstruction parameter is not supported in Gemini API.");if(i(t,["tools"])!==void 0)throw new Error("tools parameter is not supported in Gemini API.");if(i(t,["generationConfig"])!==void 0)throw new Error("generationConfig parameter is not supported in Gemini API.");return e}function Ru(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["contents"]);if(s!=null){let a=W(s);Array.isArray(a)&&(a=a.map(u=>Ye(u))),r(n,["contents"],a)}const l=i(e,["config"]);return l!=null&&r(n,["config"],xu(l)),n}function wu(t){const e={};if(i(t,["gcsUri"])!==void 0)throw new Error("gcsUri parameter is not supported in Gemini API.");const n=i(t,["imageBytes"]);n!=null&&r(e,["bytesBase64Encoded"],te(n));const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Pu(t,e){const n={},o=i(t,["numberOfVideos"]);if(e!==void 0&&o!=null&&r(e,["parameters","sampleCount"],o),i(t,["outputGcsUri"])!==void 0)throw new Error("outputGcsUri parameter is not supported in Gemini API.");if(i(t,["fps"])!==void 0)throw new Error("fps parameter is not supported in Gemini API.");const s=i(t,["durationSeconds"]);if(e!==void 0&&s!=null&&r(e,["parameters","durationSeconds"],s),i(t,["seed"])!==void 0)throw new Error("seed parameter is not supported in Gemini API.");const l=i(t,["aspectRatio"]);if(e!==void 0&&l!=null&&r(e,["parameters","aspectRatio"],l),i(t,["resolution"])!==void 0)throw new Error("resolution parameter is not supported in Gemini API.");const a=i(t,["personGeneration"]);if(e!==void 0&&a!=null&&r(e,["parameters","personGeneration"],a),i(t,["pubsubTopic"])!==void 0)throw new Error("pubsubTopic parameter is not supported in Gemini API.");const u=i(t,["negativePrompt"]);e!==void 0&&u!=null&&r(e,["parameters","negativePrompt"],u);const f=i(t,["enhancePrompt"]);if(e!==void 0&&f!=null&&r(e,["parameters","enhancePrompt"],f),i(t,["generateAudio"])!==void 0)throw new Error("generateAudio parameter is not supported in Gemini API.");if(i(t,["lastFrame"])!==void 0)throw new Error("lastFrame parameter is not supported in Gemini API.");if(i(t,["compressionQuality"])!==void 0)throw new Error("compressionQuality parameter is not supported in Gemini API.");return n}function Nu(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const l=i(e,["image"]);if(l!=null&&r(n,["instances[0]","image"],wu(l)),i(e,["video"])!==void 0)throw new Error("video parameter is not supported in Gemini API.");const a=i(e,["config"]);return a!=null&&r(n,["config"],Pu(a,n)),n}function ku(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Du(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["data"]);o!=null&&r(e,["data"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Vu(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["fileUri"]);o!=null&&r(e,["fileUri"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Fu(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],ku(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],Du(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],Vu(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Ee(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>Fu(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Lu(t){const e={},n=i(t,["anyOf"]);n!=null&&r(e,["anyOf"],n);const o=i(t,["default"]);o!=null&&r(e,["default"],o);const s=i(t,["description"]);s!=null&&r(e,["description"],s);const l=i(t,["enum"]);l!=null&&r(e,["enum"],l);const a=i(t,["example"]);a!=null&&r(e,["example"],a);const u=i(t,["format"]);u!=null&&r(e,["format"],u);const f=i(t,["items"]);f!=null&&r(e,["items"],f);const c=i(t,["maxItems"]);c!=null&&r(e,["maxItems"],c);const d=i(t,["maxLength"]);d!=null&&r(e,["maxLength"],d);const p=i(t,["maxProperties"]);p!=null&&r(e,["maxProperties"],p);const m=i(t,["maximum"]);m!=null&&r(e,["maximum"],m);const h=i(t,["minItems"]);h!=null&&r(e,["minItems"],h);const g=i(t,["minLength"]);g!=null&&r(e,["minLength"],g);const y=i(t,["minProperties"]);y!=null&&r(e,["minProperties"],y);const S=i(t,["minimum"]);S!=null&&r(e,["minimum"],S);const A=i(t,["nullable"]);A!=null&&r(e,["nullable"],A);const x=i(t,["pattern"]);x!=null&&r(e,["pattern"],x);const M=i(t,["properties"]);M!=null&&r(e,["properties"],M);const P=i(t,["propertyOrdering"]);P!=null&&r(e,["propertyOrdering"],P);const I=i(t,["required"]);I!=null&&r(e,["required"],I);const U=i(t,["title"]);U!=null&&r(e,["title"],U);const G=i(t,["type"]);return G!=null&&r(e,["type"],G),e}function Uu(t){const e={},n=i(t,["featureSelectionPreference"]);return n!=null&&r(e,["featureSelectionPreference"],n),e}function Gu(t){const e={},n=i(t,["method"]);n!=null&&r(e,["method"],n);const o=i(t,["category"]);o!=null&&r(e,["category"],o);const s=i(t,["threshold"]);return s!=null&&r(e,["threshold"],s),e}function Bu(t){const e={};if(i(t,["behavior"])!==void 0)throw new Error("behavior parameter is not supported in Vertex AI.");const n=i(t,["description"]);n!=null&&r(e,["description"],n);const o=i(t,["name"]);o!=null&&r(e,["name"],o);const s=i(t,["parameters"]);s!=null&&r(e,["parameters"],s);const l=i(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const a=i(t,["response"]);a!=null&&r(e,["response"],a);const u=i(t,["responseJsonSchema"]);return u!=null&&r(e,["responseJsonSchema"],u),e}function qu(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function $u(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],qu(n)),e}function Ju(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function bu(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Ju(n)),e}function Hu(){return{}}function Wu(t){const e={},n=i(t,["apiKeyString"]);return n!=null&&r(e,["apiKeyString"],n),e}function Zu(t){const e={},n=i(t,["apiKeyConfig"]);n!=null&&r(e,["apiKeyConfig"],Wu(n));const o=i(t,["authType"]);o!=null&&r(e,["authType"],o);const s=i(t,["googleServiceAccountConfig"]);s!=null&&r(e,["googleServiceAccountConfig"],s);const l=i(t,["httpBasicAuthConfig"]);l!=null&&r(e,["httpBasicAuthConfig"],l);const a=i(t,["oauthConfig"]);a!=null&&r(e,["oauthConfig"],a);const u=i(t,["oidcConfig"]);return u!=null&&r(e,["oidcConfig"],u),e}function Yu(t){const e={},n=i(t,["authConfig"]);return n!=null&&r(e,["authConfig"],Zu(n)),e}function Ku(){return{}}function Ao(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(p=>Bu(p))),r(e,["functionDeclarations"],d)}const o=i(t,["retrieval"]);o!=null&&r(e,["retrieval"],o);const s=i(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],$u(s));const l=i(t,["googleSearchRetrieval"]);l!=null&&r(e,["googleSearchRetrieval"],bu(l)),i(t,["enterpriseWebSearch"])!=null&&r(e,["enterpriseWebSearch"],Hu());const u=i(t,["googleMaps"]);u!=null&&r(e,["googleMaps"],Yu(u)),i(t,["urlContext"])!=null&&r(e,["urlContext"],Ku());const c=i(t,["codeExecution"]);return c!=null&&r(e,["codeExecution"],c),e}function zu(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["allowedFunctionNames"]);return o!=null&&r(e,["allowedFunctionNames"],o),e}function Ou(t){const e={},n=i(t,["latitude"]);n!=null&&r(e,["latitude"],n);const o=i(t,["longitude"]);return o!=null&&r(e,["longitude"],o),e}function Xu(t){const e={},n=i(t,["latLng"]);n!=null&&r(e,["latLng"],Ou(n));const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function Qu(t){const e={},n=i(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],zu(n));const o=i(t,["retrievalConfig"]);return o!=null&&r(e,["retrievalConfig"],Xu(o)),e}function ju(t){const e={},n=i(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function ec(t){const e={},n=i(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],ju(n)),e}function tc(t){const e={},n=i(t,["voiceConfig"]);if(n!=null&&r(e,["voiceConfig"],ec(n)),i(t,["multiSpeakerVoiceConfig"])!==void 0)throw new Error("multiSpeakerVoiceConfig parameter is not supported in Vertex AI.");const o=i(t,["languageCode"]);return o!=null&&r(e,["languageCode"],o),e}function nc(t){const e={},n=i(t,["includeThoughts"]);n!=null&&r(e,["includeThoughts"],n);const o=i(t,["thinkingBudget"]);return o!=null&&r(e,["thinkingBudget"],o),e}function oc(t,e,n){const o={},s=i(e,["systemInstruction"]);n!==void 0&&s!=null&&r(n,["systemInstruction"],Ee(J(s)));const l=i(e,["temperature"]);l!=null&&r(o,["temperature"],l);const a=i(e,["topP"]);a!=null&&r(o,["topP"],a);const u=i(e,["topK"]);u!=null&&r(o,["topK"],u);const f=i(e,["candidateCount"]);f!=null&&r(o,["candidateCount"],f);const c=i(e,["maxOutputTokens"]);c!=null&&r(o,["maxOutputTokens"],c);const d=i(e,["stopSequences"]);d!=null&&r(o,["stopSequences"],d);const p=i(e,["responseLogprobs"]);p!=null&&r(o,["responseLogprobs"],p);const m=i(e,["logprobs"]);m!=null&&r(o,["logprobs"],m);const h=i(e,["presencePenalty"]);h!=null&&r(o,["presencePenalty"],h);const g=i(e,["frequencyPenalty"]);g!=null&&r(o,["frequencyPenalty"],g);const y=i(e,["seed"]);y!=null&&r(o,["seed"],y);const S=i(e,["responseMimeType"]);S!=null&&r(o,["responseMimeType"],S);const A=i(e,["responseSchema"]);A!=null&&r(o,["responseSchema"],Lu(we(A)));const x=i(e,["responseJsonSchema"]);x!=null&&r(o,["responseJsonSchema"],x);const M=i(e,["routingConfig"]);M!=null&&r(o,["routingConfig"],M);const P=i(e,["modelSelectionConfig"]);P!=null&&r(o,["modelConfig"],Uu(P));const I=i(e,["safetySettings"]);if(n!==void 0&&I!=null){let Q=I;Array.isArray(Q)&&(Q=Q.map(ze=>Gu(ze))),r(n,["safetySettings"],Q)}const U=i(e,["tools"]);if(n!==void 0&&U!=null){let Q=Se(U);Array.isArray(Q)&&(Q=Q.map(ze=>Ao(_e(ze)))),r(n,["tools"],Q)}const G=i(e,["toolConfig"]);n!==void 0&&G!=null&&r(n,["toolConfig"],Qu(G));const H=i(e,["labels"]);n!==void 0&&H!=null&&r(n,["labels"],H);const N=i(e,["cachedContent"]);n!==void 0&&N!=null&&r(n,["cachedContent"],ee(t,N));const B=i(e,["responseModalities"]);B!=null&&r(o,["responseModalities"],B);const F=i(e,["mediaResolution"]);F!=null&&r(o,["mediaResolution"],F);const Y=i(e,["speechConfig"]);Y!=null&&r(o,["speechConfig"],tc(vt(Y)));const St=i(e,["audioTimestamp"]);St!=null&&r(o,["audioTimestamp"],St);const Et=i(e,["thinkingConfig"]);return Et!=null&&r(o,["thinkingConfig"],nc(Et)),o}function Hn(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["contents"]);if(s!=null){let a=W(s);Array.isArray(a)&&(a=a.map(u=>Ee(u))),r(n,["contents"],a)}const l=i(e,["config"]);return l!=null&&r(n,["generationConfig"],oc(t,l,n)),n}function ic(t,e){const n={},o=i(t,["taskType"]);e!==void 0&&o!=null&&r(e,["instances[]","task_type"],o);const s=i(t,["title"]);e!==void 0&&s!=null&&r(e,["instances[]","title"],s);const l=i(t,["outputDimensionality"]);e!==void 0&&l!=null&&r(e,["parameters","outputDimensionality"],l);const a=i(t,["mimeType"]);e!==void 0&&a!=null&&r(e,["instances[]","mimeType"],a);const u=i(t,["autoTruncate"]);return e!==void 0&&u!=null&&r(e,["parameters","autoTruncate"],u),n}function rc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["contents"]);s!=null&&r(n,["instances[]","content"],lo(t,s));const l=i(e,["config"]);return l!=null&&r(n,["config"],ic(l,n)),n}function sc(t,e){const n={},o=i(t,["outputGcsUri"]);e!==void 0&&o!=null&&r(e,["parameters","storageUri"],o);const s=i(t,["negativePrompt"]);e!==void 0&&s!=null&&r(e,["parameters","negativePrompt"],s);const l=i(t,["numberOfImages"]);e!==void 0&&l!=null&&r(e,["parameters","sampleCount"],l);const a=i(t,["aspectRatio"]);e!==void 0&&a!=null&&r(e,["parameters","aspectRatio"],a);const u=i(t,["guidanceScale"]);e!==void 0&&u!=null&&r(e,["parameters","guidanceScale"],u);const f=i(t,["seed"]);e!==void 0&&f!=null&&r(e,["parameters","seed"],f);const c=i(t,["safetyFilterLevel"]);e!==void 0&&c!=null&&r(e,["parameters","safetySetting"],c);const d=i(t,["personGeneration"]);e!==void 0&&d!=null&&r(e,["parameters","personGeneration"],d);const p=i(t,["includeSafetyAttributes"]);e!==void 0&&p!=null&&r(e,["parameters","includeSafetyAttributes"],p);const m=i(t,["includeRaiReason"]);e!==void 0&&m!=null&&r(e,["parameters","includeRaiReason"],m);const h=i(t,["language"]);e!==void 0&&h!=null&&r(e,["parameters","language"],h);const g=i(t,["outputMimeType"]);e!==void 0&&g!=null&&r(e,["parameters","outputOptions","mimeType"],g);const y=i(t,["outputCompressionQuality"]);e!==void 0&&y!=null&&r(e,["parameters","outputOptions","compressionQuality"],y);const S=i(t,["addWatermark"]);e!==void 0&&S!=null&&r(e,["parameters","addWatermark"],S);const A=i(t,["enhancePrompt"]);return e!==void 0&&A!=null&&r(e,["parameters","enhancePrompt"],A),n}function lc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const l=i(e,["config"]);return l!=null&&r(n,["config"],sc(l,n)),n}function Ke(t){const e={},n=i(t,["gcsUri"]);n!=null&&r(e,["gcsUri"],n);const o=i(t,["imageBytes"]);o!=null&&r(e,["bytesBase64Encoded"],te(o));const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function ac(t){const e={},n=i(t,["maskMode"]);n!=null&&r(e,["maskMode"],n);const o=i(t,["segmentationClasses"]);o!=null&&r(e,["maskClasses"],o);const s=i(t,["maskDilation"]);return s!=null&&r(e,["dilation"],s),e}function uc(t){const e={},n=i(t,["controlType"]);n!=null&&r(e,["controlType"],n);const o=i(t,["enableControlImageComputation"]);return o!=null&&r(e,["computeControl"],o),e}function cc(t){const e={},n=i(t,["styleDescription"]);return n!=null&&r(e,["styleDescription"],n),e}function fc(t){const e={},n=i(t,["subjectType"]);n!=null&&r(e,["subjectType"],n);const o=i(t,["subjectDescription"]);return o!=null&&r(e,["subjectDescription"],o),e}function dc(t){const e={},n=i(t,["referenceImage"]);n!=null&&r(e,["referenceImage"],Ke(n));const o=i(t,["referenceId"]);o!=null&&r(e,["referenceId"],o);const s=i(t,["referenceType"]);s!=null&&r(e,["referenceType"],s);const l=i(t,["maskImageConfig"]);l!=null&&r(e,["maskImageConfig"],ac(l));const a=i(t,["controlImageConfig"]);a!=null&&r(e,["controlImageConfig"],uc(a));const u=i(t,["styleImageConfig"]);u!=null&&r(e,["styleImageConfig"],cc(u));const f=i(t,["subjectImageConfig"]);return f!=null&&r(e,["subjectImageConfig"],fc(f)),e}function pc(t,e){const n={},o=i(t,["outputGcsUri"]);e!==void 0&&o!=null&&r(e,["parameters","storageUri"],o);const s=i(t,["negativePrompt"]);e!==void 0&&s!=null&&r(e,["parameters","negativePrompt"],s);const l=i(t,["numberOfImages"]);e!==void 0&&l!=null&&r(e,["parameters","sampleCount"],l);const a=i(t,["aspectRatio"]);e!==void 0&&a!=null&&r(e,["parameters","aspectRatio"],a);const u=i(t,["guidanceScale"]);e!==void 0&&u!=null&&r(e,["parameters","guidanceScale"],u);const f=i(t,["seed"]);e!==void 0&&f!=null&&r(e,["parameters","seed"],f);const c=i(t,["safetyFilterLevel"]);e!==void 0&&c!=null&&r(e,["parameters","safetySetting"],c);const d=i(t,["personGeneration"]);e!==void 0&&d!=null&&r(e,["parameters","personGeneration"],d);const p=i(t,["includeSafetyAttributes"]);e!==void 0&&p!=null&&r(e,["parameters","includeSafetyAttributes"],p);const m=i(t,["includeRaiReason"]);e!==void 0&&m!=null&&r(e,["parameters","includeRaiReason"],m);const h=i(t,["language"]);e!==void 0&&h!=null&&r(e,["parameters","language"],h);const g=i(t,["outputMimeType"]);e!==void 0&&g!=null&&r(e,["parameters","outputOptions","mimeType"],g);const y=i(t,["outputCompressionQuality"]);e!==void 0&&y!=null&&r(e,["parameters","outputOptions","compressionQuality"],y);const S=i(t,["editMode"]);e!==void 0&&S!=null&&r(e,["parameters","editMode"],S);const A=i(t,["baseSteps"]);return e!==void 0&&A!=null&&r(e,["parameters","editConfig","baseSteps"],A),n}function mc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const l=i(e,["referenceImages"]);if(l!=null){let u=l;Array.isArray(u)&&(u=u.map(f=>dc(f))),r(n,["instances[0]","referenceImages"],u)}const a=i(e,["config"]);return a!=null&&r(n,["config"],pc(a,n)),n}function hc(t,e){const n={},o=i(t,["includeRaiReason"]);e!==void 0&&o!=null&&r(e,["parameters","includeRaiReason"],o);const s=i(t,["outputMimeType"]);e!==void 0&&s!=null&&r(e,["parameters","outputOptions","mimeType"],s);const l=i(t,["outputCompressionQuality"]);e!==void 0&&l!=null&&r(e,["parameters","outputOptions","compressionQuality"],l);const a=i(t,["enhanceInputImage"]);e!==void 0&&a!=null&&r(e,["parameters","upscaleConfig","enhanceInputImage"],a);const u=i(t,["imagePreservationFactor"]);e!==void 0&&u!=null&&r(e,["parameters","upscaleConfig","imagePreservationFactor"],u);const f=i(t,["numberOfImages"]);e!==void 0&&f!=null&&r(e,["parameters","sampleCount"],f);const c=i(t,["mode"]);return e!==void 0&&c!=null&&r(e,["parameters","mode"],c),n}function gc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["image"]);s!=null&&r(n,["instances[0]","image"],Ke(s));const l=i(e,["upscaleFactor"]);l!=null&&r(n,["parameters","upscaleConfig","upscaleFactor"],l);const a=i(e,["config"]);return a!=null&&r(n,["config"],hc(a,n)),n}function vc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","name"],L(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function yc(t,e,n){const o={},s=i(e,["pageSize"]);n!==void 0&&s!=null&&r(n,["_query","pageSize"],s);const l=i(e,["pageToken"]);n!==void 0&&l!=null&&r(n,["_query","pageToken"],l);const a=i(e,["filter"]);n!==void 0&&a!=null&&r(n,["_query","filter"],a);const u=i(e,["queryBase"]);return n!==void 0&&u!=null&&r(n,["_url","models_url"],co(t,u)),o}function Tc(t,e){const n={},o=i(e,["config"]);return o!=null&&r(n,["config"],yc(t,o,n)),n}function Cc(t,e){const n={},o=i(t,["displayName"]);e!==void 0&&o!=null&&r(e,["displayName"],o);const s=i(t,["description"]);e!==void 0&&s!=null&&r(e,["description"],s);const l=i(t,["defaultCheckpointId"]);return e!==void 0&&l!=null&&r(e,["defaultCheckpointId"],l),n}function _c(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],Cc(s,n)),n}function Sc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","name"],L(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],s),n}function Ec(t,e){const n={},o=i(t,["systemInstruction"]);e!==void 0&&o!=null&&r(e,["systemInstruction"],Ee(J(o)));const s=i(t,["tools"]);if(e!==void 0&&s!=null){let a=s;Array.isArray(a)&&(a=a.map(u=>Ao(u))),r(e,["tools"],a)}const l=i(t,["generationConfig"]);return e!==void 0&&l!=null&&r(e,["generationConfig"],l),n}function Ac(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["contents"]);if(s!=null){let a=W(s);Array.isArray(a)&&(a=a.map(u=>Ee(u))),r(n,["contents"],a)}const l=i(e,["config"]);return l!=null&&r(n,["config"],Ec(l,n)),n}function Mc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["contents"]);if(s!=null){let a=W(s);Array.isArray(a)&&(a=a.map(u=>Ee(u))),r(n,["contents"],a)}const l=i(e,["config"]);return l!=null&&r(n,["config"],l),n}function Ic(t){const e={},n=i(t,["uri"]);n!=null&&r(e,["gcsUri"],n);const o=i(t,["videoBytes"]);o!=null&&r(e,["bytesBase64Encoded"],te(o));const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function xc(t,e){const n={},o=i(t,["numberOfVideos"]);e!==void 0&&o!=null&&r(e,["parameters","sampleCount"],o);const s=i(t,["outputGcsUri"]);e!==void 0&&s!=null&&r(e,["parameters","storageUri"],s);const l=i(t,["fps"]);e!==void 0&&l!=null&&r(e,["parameters","fps"],l);const a=i(t,["durationSeconds"]);e!==void 0&&a!=null&&r(e,["parameters","durationSeconds"],a);const u=i(t,["seed"]);e!==void 0&&u!=null&&r(e,["parameters","seed"],u);const f=i(t,["aspectRatio"]);e!==void 0&&f!=null&&r(e,["parameters","aspectRatio"],f);const c=i(t,["resolution"]);e!==void 0&&c!=null&&r(e,["parameters","resolution"],c);const d=i(t,["personGeneration"]);e!==void 0&&d!=null&&r(e,["parameters","personGeneration"],d);const p=i(t,["pubsubTopic"]);e!==void 0&&p!=null&&r(e,["parameters","pubsubTopic"],p);const m=i(t,["negativePrompt"]);e!==void 0&&m!=null&&r(e,["parameters","negativePrompt"],m);const h=i(t,["enhancePrompt"]);e!==void 0&&h!=null&&r(e,["parameters","enhancePrompt"],h);const g=i(t,["generateAudio"]);e!==void 0&&g!=null&&r(e,["parameters","generateAudio"],g);const y=i(t,["lastFrame"]);e!==void 0&&y!=null&&r(e,["instances[0]","lastFrame"],Ke(y));const S=i(t,["compressionQuality"]);return e!==void 0&&S!=null&&r(e,["parameters","compressionQuality"],S),n}function Rc(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["_url","model"],L(t,o));const s=i(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const l=i(e,["image"]);l!=null&&r(n,["instances[0]","image"],Ke(l));const a=i(e,["video"]);a!=null&&r(n,["instances[0]","video"],Ic(a));const u=i(e,["config"]);return u!=null&&r(n,["config"],xc(u,n)),n}function wc(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Pc(t){const e={},n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Nc(t){const e={},n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function kc(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],wc(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],Pc(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],Nc(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function Dc(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>kc(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Vc(t){const e={},n=i(t,["citationSources"]);return n!=null&&r(e,["citations"],n),e}function Fc(t){const e={},n=i(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const o=i(t,["urlRetrievalStatus"]);return o!=null&&r(e,["urlRetrievalStatus"],o),e}function Lc(t){const e={},n=i(t,["urlMetadata"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>Fc(s))),r(e,["urlMetadata"],o)}return e}function Uc(t){const e={},n=i(t,["content"]);n!=null&&r(e,["content"],Dc(n));const o=i(t,["citationMetadata"]);o!=null&&r(e,["citationMetadata"],Vc(o));const s=i(t,["tokenCount"]);s!=null&&r(e,["tokenCount"],s);const l=i(t,["finishReason"]);l!=null&&r(e,["finishReason"],l);const a=i(t,["urlContextMetadata"]);a!=null&&r(e,["urlContextMetadata"],Lc(a));const u=i(t,["avgLogprobs"]);u!=null&&r(e,["avgLogprobs"],u);const f=i(t,["groundingMetadata"]);f!=null&&r(e,["groundingMetadata"],f);const c=i(t,["index"]);c!=null&&r(e,["index"],c);const d=i(t,["logprobsResult"]);d!=null&&r(e,["logprobsResult"],d);const p=i(t,["safetyRatings"]);return p!=null&&r(e,["safetyRatings"],p),e}function Wn(t){const e={},n=i(t,["candidates"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(u=>Uc(u))),r(e,["candidates"],a)}const o=i(t,["modelVersion"]);o!=null&&r(e,["modelVersion"],o);const s=i(t,["promptFeedback"]);s!=null&&r(e,["promptFeedback"],s);const l=i(t,["usageMetadata"]);return l!=null&&r(e,["usageMetadata"],l),e}function Gc(t){const e={},n=i(t,["values"]);return n!=null&&r(e,["values"],n),e}function Bc(){return{}}function qc(t){const e={},n=i(t,["embeddings"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>Gc(l))),r(e,["embeddings"],s)}return i(t,["metadata"])!=null&&r(e,["metadata"],Bc()),e}function $c(t){const e={},n=i(t,["bytesBase64Encoded"]);n!=null&&r(e,["imageBytes"],te(n));const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function Mo(t){const e={},n=i(t,["safetyAttributes","categories"]);n!=null&&r(e,["categories"],n);const o=i(t,["safetyAttributes","scores"]);o!=null&&r(e,["scores"],o);const s=i(t,["contentType"]);return s!=null&&r(e,["contentType"],s),e}function Jc(t){const e={},n=i(t,["_self"]);n!=null&&r(e,["image"],$c(n));const o=i(t,["raiFilteredReason"]);o!=null&&r(e,["raiFilteredReason"],o);const s=i(t,["_self"]);return s!=null&&r(e,["safetyAttributes"],Mo(s)),e}function bc(t){const e={},n=i(t,["predictions"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>Jc(l))),r(e,["generatedImages"],s)}const o=i(t,["positivePromptSafetyAttributes"]);return o!=null&&r(e,["positivePromptSafetyAttributes"],Mo(o)),e}function Hc(t){const e={},n=i(t,["baseModel"]);n!=null&&r(e,["baseModel"],n);const o=i(t,["createTime"]);o!=null&&r(e,["createTime"],o);const s=i(t,["updateTime"]);return s!=null&&r(e,["updateTime"],s),e}function dt(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["description"]);s!=null&&r(e,["description"],s);const l=i(t,["version"]);l!=null&&r(e,["version"],l);const a=i(t,["_self"]);a!=null&&r(e,["tunedModelInfo"],Hc(a));const u=i(t,["inputTokenLimit"]);u!=null&&r(e,["inputTokenLimit"],u);const f=i(t,["outputTokenLimit"]);f!=null&&r(e,["outputTokenLimit"],f);const c=i(t,["supportedGenerationMethods"]);return c!=null&&r(e,["supportedActions"],c),e}function Wc(t,e){const n={},o=i(e,["nextPageToken"]);o!=null&&r(n,["nextPageToken"],o);const s=i(e,["_self"]);if(s!=null){let l=fo(s);Array.isArray(l)&&(l=l.map(a=>dt(a))),r(n,["models"],l)}return n}function Zc(){return{}}function Yc(t){const e={},n=i(t,["totalTokens"]);n!=null&&r(e,["totalTokens"],n);const o=i(t,["cachedContentTokenCount"]);return o!=null&&r(e,["cachedContentTokenCount"],o),e}function Kc(t){const e={},n=i(t,["video","uri"]);n!=null&&r(e,["uri"],n);const o=i(t,["video","encodedVideo"]);o!=null&&r(e,["videoBytes"],te(o));const s=i(t,["encoding"]);return s!=null&&r(e,["mimeType"],s),e}function zc(t){const e={},n=i(t,["_self"]);return n!=null&&r(e,["video"],Kc(n)),e}function Oc(t){const e={},n=i(t,["generatedSamples"]);if(n!=null){let l=n;Array.isArray(l)&&(l=l.map(a=>zc(a))),r(e,["generatedVideos"],l)}const o=i(t,["raiMediaFilteredCount"]);o!=null&&r(e,["raiMediaFilteredCount"],o);const s=i(t,["raiMediaFilteredReasons"]);return s!=null&&r(e,["raiMediaFilteredReasons"],s),e}function Xc(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["metadata"]);o!=null&&r(e,["metadata"],o);const s=i(t,["done"]);s!=null&&r(e,["done"],s);const l=i(t,["error"]);l!=null&&r(e,["error"],l);const a=i(t,["response","generateVideoResponse"]);return a!=null&&r(e,["response"],Oc(a)),e}function Qc(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function jc(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["data"]);o!=null&&r(e,["data"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function ef(t){const e={},n=i(t,["displayName"]);n!=null&&r(e,["displayName"],n);const o=i(t,["fileUri"]);o!=null&&r(e,["fileUri"],o);const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function tf(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Qc(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],jc(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],ef(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function nf(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>tf(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function of(t){const e={},n=i(t,["citations"]);return n!=null&&r(e,["citations"],n),e}function rf(t){const e={},n=i(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const o=i(t,["urlRetrievalStatus"]);return o!=null&&r(e,["urlRetrievalStatus"],o),e}function sf(t){const e={},n=i(t,["urlMetadata"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>rf(s))),r(e,["urlMetadata"],o)}return e}function lf(t){const e={},n=i(t,["content"]);n!=null&&r(e,["content"],nf(n));const o=i(t,["citationMetadata"]);o!=null&&r(e,["citationMetadata"],of(o));const s=i(t,["finishMessage"]);s!=null&&r(e,["finishMessage"],s);const l=i(t,["finishReason"]);l!=null&&r(e,["finishReason"],l);const a=i(t,["urlContextMetadata"]);a!=null&&r(e,["urlContextMetadata"],sf(a));const u=i(t,["avgLogprobs"]);u!=null&&r(e,["avgLogprobs"],u);const f=i(t,["groundingMetadata"]);f!=null&&r(e,["groundingMetadata"],f);const c=i(t,["index"]);c!=null&&r(e,["index"],c);const d=i(t,["logprobsResult"]);d!=null&&r(e,["logprobsResult"],d);const p=i(t,["safetyRatings"]);return p!=null&&r(e,["safetyRatings"],p),e}function Zn(t){const e={},n=i(t,["candidates"]);if(n!=null){let f=n;Array.isArray(f)&&(f=f.map(c=>lf(c))),r(e,["candidates"],f)}const o=i(t,["createTime"]);o!=null&&r(e,["createTime"],o);const s=i(t,["responseId"]);s!=null&&r(e,["responseId"],s);const l=i(t,["modelVersion"]);l!=null&&r(e,["modelVersion"],l);const a=i(t,["promptFeedback"]);a!=null&&r(e,["promptFeedback"],a);const u=i(t,["usageMetadata"]);return u!=null&&r(e,["usageMetadata"],u),e}function af(t){const e={},n=i(t,["truncated"]);n!=null&&r(e,["truncated"],n);const o=i(t,["token_count"]);return o!=null&&r(e,["tokenCount"],o),e}function uf(t){const e={},n=i(t,["values"]);n!=null&&r(e,["values"],n);const o=i(t,["statistics"]);return o!=null&&r(e,["statistics"],af(o)),e}function cf(t){const e={},n=i(t,["billableCharacterCount"]);return n!=null&&r(e,["billableCharacterCount"],n),e}function ff(t){const e={},n=i(t,["predictions[]","embeddings"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>uf(l))),r(e,["embeddings"],s)}const o=i(t,["metadata"]);return o!=null&&r(e,["metadata"],cf(o)),e}function df(t){const e={},n=i(t,["gcsUri"]);n!=null&&r(e,["gcsUri"],n);const o=i(t,["bytesBase64Encoded"]);o!=null&&r(e,["imageBytes"],te(o));const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Io(t){const e={},n=i(t,["safetyAttributes","categories"]);n!=null&&r(e,["categories"],n);const o=i(t,["safetyAttributes","scores"]);o!=null&&r(e,["scores"],o);const s=i(t,["contentType"]);return s!=null&&r(e,["contentType"],s),e}function Tt(t){const e={},n=i(t,["_self"]);n!=null&&r(e,["image"],df(n));const o=i(t,["raiFilteredReason"]);o!=null&&r(e,["raiFilteredReason"],o);const s=i(t,["_self"]);s!=null&&r(e,["safetyAttributes"],Io(s));const l=i(t,["prompt"]);return l!=null&&r(e,["enhancedPrompt"],l),e}function pf(t){const e={},n=i(t,["predictions"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>Tt(l))),r(e,["generatedImages"],s)}const o=i(t,["positivePromptSafetyAttributes"]);return o!=null&&r(e,["positivePromptSafetyAttributes"],Io(o)),e}function mf(t){const e={},n=i(t,["predictions"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>Tt(s))),r(e,["generatedImages"],o)}return e}function hf(t){const e={},n=i(t,["predictions"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>Tt(s))),r(e,["generatedImages"],o)}return e}function gf(t){const e={},n=i(t,["endpoint"]);n!=null&&r(e,["name"],n);const o=i(t,["deployedModelId"]);return o!=null&&r(e,["deployedModelId"],o),e}function vf(t){const e={},n=i(t,["labels","google-vertex-llm-tuning-base-model-id"]);n!=null&&r(e,["baseModel"],n);const o=i(t,["createTime"]);o!=null&&r(e,["createTime"],o);const s=i(t,["updateTime"]);return s!=null&&r(e,["updateTime"],s),e}function yf(t){const e={},n=i(t,["checkpointId"]);n!=null&&r(e,["checkpointId"],n);const o=i(t,["epoch"]);o!=null&&r(e,["epoch"],o);const s=i(t,["step"]);return s!=null&&r(e,["step"],s),e}function pt(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["displayName"]);o!=null&&r(e,["displayName"],o);const s=i(t,["description"]);s!=null&&r(e,["description"],s);const l=i(t,["versionId"]);l!=null&&r(e,["version"],l);const a=i(t,["deployedModels"]);if(a!=null){let p=a;Array.isArray(p)&&(p=p.map(m=>gf(m))),r(e,["endpoints"],p)}const u=i(t,["labels"]);u!=null&&r(e,["labels"],u);const f=i(t,["_self"]);f!=null&&r(e,["tunedModelInfo"],vf(f));const c=i(t,["defaultCheckpointId"]);c!=null&&r(e,["defaultCheckpointId"],c);const d=i(t,["checkpoints"]);if(d!=null){let p=d;Array.isArray(p)&&(p=p.map(m=>yf(m))),r(e,["checkpoints"],p)}return e}function Tf(t,e){const n={},o=i(e,["nextPageToken"]);o!=null&&r(n,["nextPageToken"],o);const s=i(e,["_self"]);if(s!=null){let l=fo(s);Array.isArray(l)&&(l=l.map(a=>pt(a))),r(n,["models"],l)}return n}function Cf(){return{}}function _f(t){const e={},n=i(t,["totalTokens"]);return n!=null&&r(e,["totalTokens"],n),e}function Sf(t){const e={},n=i(t,["tokensInfo"]);return n!=null&&r(e,["tokensInfo"],n),e}function Ef(t){const e={},n=i(t,["gcsUri"]);n!=null&&r(e,["uri"],n);const o=i(t,["bytesBase64Encoded"]);o!=null&&r(e,["videoBytes"],te(o));const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Af(t){const e={},n=i(t,["_self"]);return n!=null&&r(e,["video"],Ef(n)),e}function Mf(t){const e={},n=i(t,["videos"]);if(n!=null){let l=n;Array.isArray(l)&&(l=l.map(a=>Af(a))),r(e,["generatedVideos"],l)}const o=i(t,["raiMediaFilteredCount"]);o!=null&&r(e,["raiMediaFilteredCount"],o);const s=i(t,["raiMediaFilteredReasons"]);return s!=null&&r(e,["raiMediaFilteredReasons"],s),e}function If(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["metadata"]);o!=null&&r(e,["metadata"],o);const s=i(t,["done"]);s!=null&&r(e,["done"],s);const l=i(t,["error"]);l!=null&&r(e,["error"],l);const a=i(t,["response"]);return a!=null&&r(e,["response"],Mf(a)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const xf="Content-Type",Rf="X-Server-Timeout",wf="User-Agent",mt="x-goog-api-client",Pf="1.7.0",Nf=`google-genai-sdk/${Pf}`,kf="v1beta1",Df="v1beta",Yn=/^data: (.*)(?:\n\n|\r\r|\r\n\r\n)/;class Vf{constructor(e){var n,o;this.clientOptions=Object.assign(Object.assign({},e),{project:e.project,location:e.location,apiKey:e.apiKey,vertexai:e.vertexai});const s={};this.clientOptions.vertexai?(s.apiVersion=(n=this.clientOptions.apiVersion)!==null&&n!==void 0?n:kf,s.baseUrl=this.baseUrlFromProjectLocation(),this.normalizeAuthParameters()):(s.apiVersion=(o=this.clientOptions.apiVersion)!==null&&o!==void 0?o:Df,s.baseUrl="https://generativelanguage.googleapis.com/"),s.headers=this.getDefaultHeaders(),this.clientOptions.httpOptions=s,e.httpOptions&&(this.clientOptions.httpOptions=this.patchHttpOptions(s,e.httpOptions))}baseUrlFromProjectLocation(){return this.clientOptions.project&&this.clientOptions.location&&this.clientOptions.location!=="global"?`https://${this.clientOptions.location}-aiplatform.googleapis.com/`:"https://aiplatform.googleapis.com/"}normalizeAuthParameters(){if(this.clientOptions.project&&this.clientOptions.location){this.clientOptions.apiKey=void 0;return}this.clientOptions.project=void 0,this.clientOptions.location=void 0}isVertexAI(){var e;return(e=this.clientOptions.vertexai)!==null&&e!==void 0?e:!1}getProject(){return this.clientOptions.project}getLocation(){return this.clientOptions.location}getApiVersion(){if(this.clientOptions.httpOptions&&this.clientOptions.httpOptions.apiVersion!==void 0)return this.clientOptions.httpOptions.apiVersion;throw new Error("API version is not set.")}getBaseUrl(){if(this.clientOptions.httpOptions&&this.clientOptions.httpOptions.baseUrl!==void 0)return this.clientOptions.httpOptions.baseUrl;throw new Error("Base URL is not set.")}getRequestUrl(){return this.getRequestUrlInternal(this.clientOptions.httpOptions)}getHeaders(){if(this.clientOptions.httpOptions&&this.clientOptions.httpOptions.headers!==void 0)return this.clientOptions.httpOptions.headers;throw new Error("Headers are not set.")}getRequestUrlInternal(e){if(!e||e.baseUrl===void 0||e.apiVersion===void 0)throw new Error("HTTP options are not correctly set.");const o=[e.baseUrl.endsWith("/")?e.baseUrl.slice(0,-1):e.baseUrl];return e.apiVersion&&e.apiVersion!==""&&o.push(e.apiVersion),o.join("/")}getBaseResourcePath(){return`projects/${this.clientOptions.project}/locations/${this.clientOptions.location}`}getApiKey(){return this.clientOptions.apiKey}getWebsocketBaseUrl(){const e=this.getBaseUrl(),n=new URL(e);return n.protocol=n.protocol=="http:"?"ws":"wss",n.toString()}setBaseUrl(e){if(this.clientOptions.httpOptions)this.clientOptions.httpOptions.baseUrl=e;else throw new Error("HTTP options are not correctly set.")}constructUrl(e,n,o){const s=[this.getRequestUrlInternal(n)];return o&&s.push(this.getBaseResourcePath()),e!==""&&s.push(e),new URL(`${s.join("/")}`)}shouldPrependVertexProjectPath(e){return!(this.clientOptions.apiKey||!this.clientOptions.vertexai||e.path.startsWith("projects/")||e.httpMethod==="GET"&&e.path.startsWith("publishers/google/models"))}async request(e){let n=this.clientOptions.httpOptions;e.httpOptions&&(n=this.patchHttpOptions(this.clientOptions.httpOptions,e.httpOptions));const o=this.shouldPrependVertexProjectPath(e),s=this.constructUrl(e.path,n,o);if(e.queryParams)for(const[a,u]of Object.entries(e.queryParams))s.searchParams.append(a,String(u));let l={};if(e.httpMethod==="GET"){if(e.body&&e.body!=="{}")throw new Error("Request body should be empty for GET request, but got non empty request body")}else l.body=e.body;return l=await this.includeExtraHttpOptionsToRequestInit(l,n,e.abortSignal),this.unaryApiCall(s,l,e.httpMethod)}patchHttpOptions(e,n){const o=JSON.parse(JSON.stringify(e));for(const[s,l]of Object.entries(n))typeof l=="object"?o[s]=Object.assign(Object.assign({},o[s]),l):l!==void 0&&(o[s]=l);return o}async requestStream(e){let n=this.clientOptions.httpOptions;e.httpOptions&&(n=this.patchHttpOptions(this.clientOptions.httpOptions,e.httpOptions));const o=this.shouldPrependVertexProjectPath(e),s=this.constructUrl(e.path,n,o);(!s.searchParams.has("alt")||s.searchParams.get("alt")!=="sse")&&s.searchParams.set("alt","sse");let l={};return l.body=e.body,l=await this.includeExtraHttpOptionsToRequestInit(l,n,e.abortSignal),this.streamApiCall(s,l,e.httpMethod)}async includeExtraHttpOptionsToRequestInit(e,n,o){if(n&&n.timeout||o){const s=new AbortController,l=s.signal;n.timeout&&n?.timeout>0&&setTimeout(()=>s.abort(),n.timeout),o&&o.addEventListener("abort",()=>{s.abort()}),e.signal=l}return n&&n.extraBody!==null&&Ff(e,n.extraBody),e.headers=await this.getHeadersInternal(n),e}async unaryApiCall(e,n,o){return this.apiCall(e.toString(),Object.assign(Object.assign({},n),{method:o})).then(async s=>(await Kn(s),new st(s))).catch(s=>{throw s instanceof Error?s:new Error(JSON.stringify(s))})}async streamApiCall(e,n,o){return this.apiCall(e.toString(),Object.assign(Object.assign({},n),{method:o})).then(async s=>(await Kn(s),this.processStreamResponse(s))).catch(s=>{throw s instanceof Error?s:new Error(JSON.stringify(s))})}processStreamResponse(e){var n;return he(this,arguments,function*(){const s=(n=e?.body)===null||n===void 0?void 0:n.getReader(),l=new TextDecoder("utf-8");if(!s)throw new Error("Response body is empty");try{let a="";for(;;){const{done:u,value:f}=yield q(s.read());if(u){if(a.trim().length>0)throw new Error("Incomplete JSON segment at the end");break}const c=l.decode(f);try{const p=JSON.parse(c);if("error"in p){const m=JSON.parse(JSON.stringify(p.error)),h=m.status,g=m.code,y=`got status: ${h}. ${JSON.stringify(p)}`;if(g>=400&&g<600)throw new Ze({message:y,status:g})}}catch(p){if(p.name==="ApiError")throw p}a+=c;let d=a.match(Yn);for(;d;){const p=d[1];try{const m=new Response(p,{headers:e?.headers,status:e?.status,statusText:e?.statusText});yield yield q(new st(m)),a=a.slice(d[0].length),d=a.match(Yn)}catch(m){throw new Error(`exception parsing stream chunk ${p}. ${m}`)}}}}finally{s.releaseLock()}})}async apiCall(e,n){return fetch(e,n).catch(o=>{throw new Error(`exception ${o} sending request`)})}getDefaultHeaders(){const e={},n=Nf+" "+this.clientOptions.userAgentExtra;return e[wf]=n,e[mt]=n,e[xf]="application/json",e}async getHeadersInternal(e){const n=new Headers;if(e&&e.headers){for(const[o,s]of Object.entries(e.headers))n.append(o,s);e.timeout&&e.timeout>0&&n.append(Rf,String(Math.ceil(e.timeout/1e3)))}return await this.clientOptions.auth.addAuthHeaders(n),n}async uploadFile(e,n){var o;const s={};n!=null&&(s.mimeType=n.mimeType,s.name=n.name,s.displayName=n.displayName),s.name&&!s.name.startsWith("files/")&&(s.name=`files/${s.name}`);const l=this.clientOptions.uploader,a=await l.stat(e);s.sizeBytes=String(a.size);const u=(o=n?.mimeType)!==null&&o!==void 0?o:a.type;if(u===void 0||u==="")throw new Error("Can not determine mimeType. Please provide mimeType in the config.");s.mimeType=u;const f=await this.fetchUploadUrl(s,n);return l.upload(e,f,this)}async downloadFile(e){await this.clientOptions.downloader.download(e,this)}async fetchUploadUrl(e,n){var o;let s={};n?.httpOptions?s=n.httpOptions:s={apiVersion:"",headers:{"Content-Type":"application/json","X-Goog-Upload-Protocol":"resumable","X-Goog-Upload-Command":"start","X-Goog-Upload-Header-Content-Length":`${e.sizeBytes}`,"X-Goog-Upload-Header-Content-Type":`${e.mimeType}`}};const l={file:e},a=await this.request({path:E("upload/v1beta/files",l._url),body:JSON.stringify(l),httpMethod:"POST",httpOptions:s});if(!a||!a?.headers)throw new Error("Server did not return an HttpResponse or the returned HttpResponse did not have headers.");const u=(o=a?.headers)===null||o===void 0?void 0:o["x-goog-upload-url"];if(u===void 0)throw new Error("Failed to get upload url. Server did not return the x-google-upload-url in the headers");return u}}async function Kn(t){var e;if(t===void 0)throw new Error("response is undefined");if(!t.ok){const n=t.status;let o;!((e=t.headers.get("content-type"))===null||e===void 0)&&e.includes("application/json")?o=await t.json():o={error:{message:await t.text(),code:t.status,status:t.statusText}};const s=JSON.stringify(o);throw n>=400&&n<600?new Ze({message:s,status:n}):new Error(s)}}function Ff(t,e){if(!e||Object.keys(e).length===0)return;if(t.body instanceof Blob){console.warn("includeExtraBodyToRequestInit: extraBody provided but current request body is a Blob. extraBody will be ignored as merging is not supported for Blob bodies.");return}let n={};if(typeof t.body=="string"&&t.body.length>0)try{const l=JSON.parse(t.body);if(typeof l=="object"&&l!==null&&!Array.isArray(l))n=l;else{console.warn("includeExtraBodyToRequestInit: Original request body is valid JSON but not a non-array object. Skip applying extraBody to the request body.");return}}catch{console.warn("includeExtraBodyToRequestInit: Original request body is not valid JSON. Skip applying extraBody to the request body.");return}function o(l,a){const u=Object.assign({},l);for(const f in a)if(Object.prototype.hasOwnProperty.call(a,f)){const c=a[f],d=u[f];c&&typeof c=="object"&&!Array.isArray(c)&&d&&typeof d=="object"&&!Array.isArray(d)?u[f]=o(d,c):(d&&c&&typeof d!=typeof c&&console.warn(`includeExtraBodyToRequestInit:deepMerge: Type mismatch for key "${f}". Original type: ${typeof d}, New type: ${typeof c}. Overwriting.`),u[f]=c)}return u}const s=o(n,e);t.body=JSON.stringify(s)}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Lf="mcp_used/unknown";function xo(t){for(const e of t)if(Ct(e)||typeof e=="object"&&"inputSchema"in e)return!0;return!1}function Ro(t){var e;const n=(e=t[mt])!==null&&e!==void 0?e:"";t[mt]=(n+` ${Lf}`).trimStart()}function Uf(t){var e,n,o;return(o=(n=(e=t.config)===null||e===void 0?void 0:e.tools)===null||n===void 0?void 0:n.some(s=>Ct(s)))!==null&&o!==void 0?o:!1}function Gf(t){var e,n,o;return(o=(n=(e=t.config)===null||e===void 0?void 0:e.tools)===null||n===void 0?void 0:n.some(s=>!Ct(s)))!==null&&o!==void 0?o:!1}function Ct(t){return t!==null&&typeof t=="object"&&t instanceof _t}function Bf(t,e=100){return he(this,arguments,function*(){let o,s=0;for(;s<e;){const l=yield q(t.listTools({cursor:o}));for(const a of l.tools)yield yield q(a),s++;if(!l.nextCursor)break;o=l.nextCursor}})}class _t{constructor(e=[],n){this.mcpTools=[],this.functionNameToMcpClient={},this.mcpClients=e,this.config=n}static create(e,n){return new _t(e,n)}async initialize(){var e,n,o,s;if(this.mcpTools.length>0)return;const l={},a=[];for(const d of this.mcpClients)try{for(var u=!0,f=(n=void 0,xe(Bf(d))),c;c=await f.next(),e=c.done,!e;u=!0){s=c.value,u=!1;const p=s;a.push(p);const m=p.name;if(l[m])throw new Error(`Duplicate function name ${m} found in MCP tools. Please ensure function names are unique.`);l[m]=d}}catch(p){n={error:p}}finally{try{!u&&!e&&(o=f.return)&&await o.call(f)}finally{if(n)throw n.error}}this.mcpTools=a,this.functionNameToMcpClient=l}async tool(){return await this.initialize(),Di(this.mcpTools,this.config)}async callTool(e){await this.initialize();const n=[];for(const o of e)if(o.name in this.functionNameToMcpClient){const s=this.functionNameToMcpClient[o.name];let l;this.config.timeout&&(l={timeout:this.config.timeout});const a=await s.callTool({name:o.name,arguments:o.args},void 0,l);n.push({functionResponse:{name:o.name,response:a.isError?{error:a}:a}})}return n}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */async function qf(t,e,n){const o=new Ci;let s;n.data instanceof Blob?s=JSON.parse(await n.data.text()):s=JSON.parse(n.data);const l=Ya(s);Object.assign(o,l),e(o)}class $f{constructor(e,n,o){this.apiClient=e,this.auth=n,this.webSocketFactory=o}async connect(e){var n,o;if(this.apiClient.isVertexAI())throw new Error("Live music is not supported for Vertex AI.");console.warn("Live music generation is experimental and may change in future versions.");const s=this.apiClient.getWebsocketBaseUrl(),l=this.apiClient.getApiVersion(),a=Hf(this.apiClient.getDefaultHeaders()),u=this.apiClient.getApiKey(),f=`${s}/ws/google.ai.generativelanguage.${l}.GenerativeService.BidiGenerateMusic?key=${u}`;let c=()=>{};const d=new Promise(M=>{c=M}),p=e.callbacks,m=function(){c({})},h=this.apiClient,g={onopen:m,onmessage:M=>{qf(h,p.onmessage,M)},onerror:(n=p?.onerror)!==null&&n!==void 0?n:function(M){},onclose:(o=p?.onclose)!==null&&o!==void 0?o:function(M){}},y=this.webSocketFactory.create(f,bf(a),g);y.connect(),await d;const S=L(this.apiClient,e.model),A=_o({model:S}),x=ft({setup:A});return y.send(JSON.stringify(x)),new Jf(y,this.apiClient)}}class Jf{constructor(e,n){this.conn=e,this.apiClient=n}async setWeightedPrompts(e){if(!e.weightedPrompts||Object.keys(e.weightedPrompts).length===0)throw new Error("Weighted prompts must be set and contain at least one entry.");const n=la(e),o=So(n);this.conn.send(JSON.stringify({clientContent:o}))}async setMusicGenerationConfig(e){e.musicGenerationConfig||(e.musicGenerationConfig={});const n=aa(e),o=ft(n);this.conn.send(JSON.stringify(o))}sendPlaybackControl(e){const n=ft({playbackControl:e});this.conn.send(JSON.stringify(n))}play(){this.sendPlaybackControl(pe.PLAY)}pause(){this.sendPlaybackControl(pe.PAUSE)}stop(){this.sendPlaybackControl(pe.STOP)}resetContext(){this.sendPlaybackControl(pe.RESET_CONTEXT)}close(){this.conn.close()}}function bf(t){const e={};return t.forEach((n,o)=>{e[o]=n}),e}function Hf(t){const e=new Headers;for(const[n,o]of Object.entries(t))e.append(n,o);return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Wf="FunctionResponse request must have an `id` field from the response of a ToolCall.FunctionalCalls in Google AI.";async function Zf(t,e,n){const o=new Ti;let s;n.data instanceof Blob?s=await n.data.text():n.data instanceof ArrayBuffer?s=new TextDecoder().decode(n.data):s=n.data;const l=JSON.parse(s);if(t.isVertexAI()){const a=Ga(l);Object.assign(o,a)}else{const a=Ua(l);Object.assign(o,a)}e(o)}class Yf{constructor(e,n,o){this.apiClient=e,this.auth=n,this.webSocketFactory=o,this.music=new $f(this.apiClient,this.auth,this.webSocketFactory)}async connect(e){var n,o,s,l,a,u;const f=this.apiClient.getWebsocketBaseUrl(),c=this.apiClient.getApiVersion();let d;const p=this.apiClient.getDefaultHeaders();e.config&&e.config.tools&&xo(e.config.tools)&&Ro(p);const m=Xf(p);if(this.apiClient.isVertexAI())d=`${f}/ws/google.cloud.aiplatform.${c}.LlmBidiService/BidiGenerateContent`,await this.auth.addAuthHeaders(m);else{const N=this.apiClient.getApiKey();let B="BidiGenerateContent",F="key";N?.startsWith("auth_tokens/")&&(console.warn("Warning: Ephemeral token support is experimental and may change in future versions."),B="BidiGenerateContentConstrained",F="access_token"),d=`${f}/ws/google.ai.generativelanguage.${c}.GenerativeService.${B}?${F}=${N}`}let h=()=>{};const g=new Promise(N=>{h=N}),y=e.callbacks,S=function(){var N;(N=y?.onopen)===null||N===void 0||N.call(y),h({})},A=this.apiClient,x={onopen:S,onmessage:N=>{Zf(A,y.onmessage,N)},onerror:(n=y?.onerror)!==null&&n!==void 0?n:function(N){},onclose:(o=y?.onclose)!==null&&o!==void 0?o:function(N){}},M=this.webSocketFactory.create(d,Of(m),x);M.connect(),await g;let P=L(this.apiClient,e.model);if(this.apiClient.isVertexAI()&&P.startsWith("publishers/")){const N=this.apiClient.getProject(),B=this.apiClient.getLocation();P=`projects/${N}/locations/${B}/`+P}let I={};this.apiClient.isVertexAI()&&((s=e.config)===null||s===void 0?void 0:s.responseModalities)===void 0&&(e.config===void 0?e.config={responseModalities:[Je.AUDIO]}:e.config.responseModalities=[Je.AUDIO]),!((l=e.config)===null||l===void 0)&&l.generationConfig&&console.warn("Setting `LiveConnectConfig.generation_config` is deprecated, please set the fields on `LiveConnectConfig` directly. This will become an error in a future version (not before Q3 2025).");const U=(u=(a=e.config)===null||a===void 0?void 0:a.tools)!==null&&u!==void 0?u:[],G=[];for(const N of U)if(this.isCallableTool(N)){const B=N;G.push(await B.tool())}else G.push(N);G.length>0&&(e.config.tools=G);const H={model:P,config:e.config,callbacks:e.callbacks};return this.apiClient.isVertexAI()?I=ea(this.apiClient,H):I=jl(this.apiClient,H),delete I.config,M.send(JSON.stringify(I)),new zf(M,this.apiClient)}isCallableTool(e){return"callTool"in e&&typeof e.callTool=="function"}}const Kf={turnComplete:!0};class zf{constructor(e,n){this.conn=e,this.apiClient=n}tLiveClientContent(e,n){if(n.turns!==null&&n.turns!==void 0){let o=[];try{o=W(n.turns),e.isVertexAI()?o=o.map(s=>Ee(s)):o=o.map(s=>Ye(s))}catch{throw new Error(`Failed to parse client content "turns", type: '${typeof n.turns}'`)}return{clientContent:{turns:o,turnComplete:n.turnComplete}}}return{clientContent:{turnComplete:n.turnComplete}}}tLiveClienttToolResponse(e,n){let o=[];if(n.functionResponses==null)throw new Error("functionResponses is required.");if(Array.isArray(n.functionResponses)?o=n.functionResponses:o=[n.functionResponses],o.length===0)throw new Error("functionResponses is required.");for(const l of o){if(typeof l!="object"||l===null||!("name"in l)||!("response"in l))throw new Error(`Could not parse function response, type '${typeof l}'.`);if(!e.isVertexAI()&&!("id"in l))throw new Error(Wf)}return{toolResponse:{functionResponses:o}}}sendClientContent(e){e=Object.assign(Object.assign({},Kf),e);const n=this.tLiveClientContent(this.apiClient,e);this.conn.send(JSON.stringify(n))}sendRealtimeInput(e){let n={};this.apiClient.isVertexAI()?n={realtimeInput:sa(e)}:n={realtimeInput:ra(e)},this.conn.send(JSON.stringify(n))}sendToolResponse(e){if(e.functionResponses==null)throw new Error("Tool response parameters are required.");const n=this.tLiveClienttToolResponse(this.apiClient,e);this.conn.send(JSON.stringify(n))}close(){this.conn.close()}}function Of(t){const e={};return t.forEach((n,o)=>{e[o]=n}),e}function Xf(t){const e=new Headers;for(const[n,o]of Object.entries(t))e.append(n,o);return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const zn=10;function On(t){var e,n,o;if(!((e=t?.automaticFunctionCalling)===null||e===void 0)&&e.disable)return!0;let s=!1;for(const a of(n=t?.tools)!==null&&n!==void 0?n:[])if(Fe(a)){s=!0;break}if(!s)return!0;const l=(o=t?.automaticFunctionCalling)===null||o===void 0?void 0:o.maximumRemoteCalls;return l&&(l<0||!Number.isInteger(l))||l==0?(console.warn("Invalid maximumRemoteCalls value provided for automatic function calling. Disabled automatic function calling. Please provide a valid integer value greater than 0. maximumRemoteCalls provided:",l),!0):!1}function Fe(t){return"callTool"in t&&typeof t.callTool=="function"}function Xn(t){var e;return!(!((e=t?.automaticFunctionCalling)===null||e===void 0)&&e.ignoreCallHistory)}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class Qf extends ce{constructor(e){super(),this.apiClient=e,this.generateContent=async n=>{var o,s,l,a,u;const f=await this.processParamsForMcpUsage(n);if(!Uf(n)||On(n.config))return await this.generateContentInternal(f);if(Gf(n))throw new Error("Automatic function calling with CallableTools and Tools is not yet supported.");let c,d;const p=W(f.contents),m=(l=(s=(o=f.config)===null||o===void 0?void 0:o.automaticFunctionCalling)===null||s===void 0?void 0:s.maximumRemoteCalls)!==null&&l!==void 0?l:zn;let h=0;for(;h<m&&(c=await this.generateContentInternal(f),!(!c.functionCalls||c.functionCalls.length===0));){const g=c.candidates[0].content,y=[];for(const S of(u=(a=n.config)===null||a===void 0?void 0:a.tools)!==null&&u!==void 0?u:[])if(Fe(S)){const x=await S.callTool(c.functionCalls);y.push(...x)}h++,d={role:"user",parts:y},f.contents=W(f.contents),f.contents.push(g),f.contents.push(d),Xn(f.config)&&(p.push(g),p.push(d))}return Xn(f.config)&&(c.automaticFunctionCallingHistory=p),c},this.generateContentStream=async n=>{if(On(n.config)){const o=await this.processParamsForMcpUsage(n);return await this.generateContentStreamInternal(o)}else return await this.processAfcStream(n)},this.generateImages=async n=>await this.generateImagesInternal(n).then(o=>{var s;let l;const a=[];if(o?.generatedImages)for(const f of o.generatedImages)f&&f?.safetyAttributes&&((s=f?.safetyAttributes)===null||s===void 0?void 0:s.contentType)==="Positive Prompt"?l=f?.safetyAttributes:a.push(f);let u;return l?u={generatedImages:a,positivePromptSafetyAttributes:l}:u={generatedImages:a},u}),this.list=async n=>{var o;const a={config:Object.assign(Object.assign({},{queryBase:!0}),n?.config)};if(this.apiClient.isVertexAI()&&!a.config.queryBase){if(!((o=a.config)===null||o===void 0)&&o.filter)throw new Error("Filtering tuned models list for Vertex AI is not currently supported");a.config.filter="labels.tune-type:*"}return new Pe(ue.PAGED_ITEM_MODELS,u=>this.listInternal(u),await this.listInternal(a),a)},this.editImage=async n=>{const o={model:n.model,prompt:n.prompt,referenceImages:[],config:n.config};return n.referenceImages&&n.referenceImages&&(o.referenceImages=n.referenceImages.map(s=>s.toReferenceImageAPI())),await this.editImageInternal(o)},this.upscaleImage=async n=>{let o={numberOfImages:1,mode:"upscale"};n.config&&(o=Object.assign(Object.assign({},o),n.config));const s={model:n.model,image:n.image,upscaleFactor:n.upscaleFactor,config:o};return await this.upscaleImageInternal(s)}}async processParamsForMcpUsage(e){var n,o,s;const l=(n=e.config)===null||n===void 0?void 0:n.tools;if(!l)return e;const a=await Promise.all(l.map(async f=>Fe(f)?await f.tool():f)),u={model:e.model,contents:e.contents,config:Object.assign(Object.assign({},e.config),{tools:a})};if(u.config.tools=a,e.config&&e.config.tools&&xo(e.config.tools)){const f=(s=(o=e.config.httpOptions)===null||o===void 0?void 0:o.headers)!==null&&s!==void 0?s:{};let c=Object.assign({},f);Object.keys(c).length===0&&(c=this.apiClient.getDefaultHeaders()),Ro(c),u.config.httpOptions=Object.assign(Object.assign({},e.config.httpOptions),{headers:c})}return u}async initAfcToolsMap(e){var n,o,s;const l=new Map;for(const a of(o=(n=e.config)===null||n===void 0?void 0:n.tools)!==null&&o!==void 0?o:[])if(Fe(a)){const u=a,f=await u.tool();for(const c of(s=f.functionDeclarations)!==null&&s!==void 0?s:[]){if(!c.name)throw new Error("Function declaration name is required.");if(l.has(c.name))throw new Error(`Duplicate tool declaration name: ${c.name}`);l.set(c.name,u)}}return l}async processAfcStream(e){var n,o,s;const l=(s=(o=(n=e.config)===null||n===void 0?void 0:n.automaticFunctionCalling)===null||o===void 0?void 0:o.maximumRemoteCalls)!==null&&s!==void 0?s:zn;let a=!1,u=0;const f=await this.initAfcToolsMap(e);return function(c,d,p){var m,h;return he(this,arguments,function*(){for(var g,y,S,A;u<l;){a&&(u++,a=!1);const I=yield q(c.processParamsForMcpUsage(p)),U=yield q(c.generateContentStreamInternal(I)),G=[],H=[];try{for(var x=!0,M=(y=void 0,xe(U)),P;P=yield q(M.next()),g=P.done,!g;x=!0){A=P.value,x=!1;const N=A;if(yield yield q(N),N.candidates&&(!((m=N.candidates[0])===null||m===void 0)&&m.content)){H.push(N.candidates[0].content);for(const B of(h=N.candidates[0].content.parts)!==null&&h!==void 0?h:[])if(u<l&&B.functionCall){if(!B.functionCall.name)throw new Error("Function call name was not returned by the model.");if(d.has(B.functionCall.name)){const F=yield q(d.get(B.functionCall.name).callTool([B.functionCall]));G.push(...F)}else throw new Error(`Automatic function calling was requested, but not all the tools the model used implement the CallableTool interface. Available tools: ${d.keys()}, mising tool: ${B.functionCall.name}`)}}}}catch(N){y={error:N}}finally{try{!x&&!g&&(S=M.return)&&(yield q(S.call(M)))}finally{if(y)throw y.error}}if(G.length>0){a=!0;const N=new Ae;N.candidates=[{content:{role:"user",parts:G}}],yield yield q(N);const B=[];B.push(...H),B.push({role:"user",parts:G});const F=W(p.contents).concat(B);p.contents=F}else break}})}(this,f,e)}async generateContentInternal(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=Hn(this.apiClient,e);return u=E("{model}:generateContent",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>{const p=Zn(d),m=new Ae;return Object.assign(m,p),m})}else{const c=bn(this.apiClient,e);return u=E("{model}:generateContent",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>{const p=Wn(d),m=new Ae;return Object.assign(m,p),m})}}async generateContentStreamInternal(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=Hn(this.apiClient,e);return u=E("{model}:streamGenerateContent?alt=sse",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.requestStream({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}),a.then(function(p){return he(this,arguments,function*(){var m,h,g,y;try{for(var S=!0,A=xe(p),x;x=yield q(A.next()),m=x.done,!m;S=!0){y=x.value,S=!1;const P=Zn(yield q(y.json())),I=new Ae;Object.assign(I,P),yield yield q(I)}}catch(M){h={error:M}}finally{try{!S&&!m&&(g=A.return)&&(yield q(g.call(A)))}finally{if(h)throw h.error}}})})}else{const c=bn(this.apiClient,e);return u=E("{model}:streamGenerateContent?alt=sse",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.requestStream({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}),a.then(function(p){return he(this,arguments,function*(){var m,h,g,y;try{for(var S=!0,A=xe(p),x;x=yield q(A.next()),m=x.done,!m;S=!0){y=x.value,S=!1;const P=Wn(yield q(y.json())),I=new Ae;Object.assign(I,P),yield yield q(I)}}catch(M){h={error:M}}finally{try{!S&&!m&&(g=A.return)&&(yield q(g.call(A)))}finally{if(h)throw h.error}}})})}}async embedContent(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=rc(this.apiClient,e);return u=E("{model}:predict",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>{const p=ff(d),m=new Sn;return Object.assign(m,p),m})}else{const c=yu(this.apiClient,e);return u=E("{model}:batchEmbedContents",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>{const p=qc(d),m=new Sn;return Object.assign(m,p),m})}}async generateImagesInternal(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=lc(this.apiClient,e);return u=E("{model}:predict",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>{const p=pf(d),m=new En;return Object.assign(m,p),m})}else{const c=Cu(this.apiClient,e);return u=E("{model}:predict",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>{const p=bc(d),m=new En;return Object.assign(m,p),m})}}async editImageInternal(e){var n,o;let s,l="",a={};if(this.apiClient.isVertexAI()){const u=mc(this.apiClient,e);return l=E("{model}:predict",u._url),a=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:l,queryParams:a,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),s.then(f=>{const c=mf(f),d=new pi;return Object.assign(d,c),d})}else throw new Error("This method is only supported by the Vertex AI.")}async upscaleImageInternal(e){var n,o;let s,l="",a={};if(this.apiClient.isVertexAI()){const u=gc(this.apiClient,e);return l=E("{model}:predict",u._url),a=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:l,queryParams:a,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),s.then(f=>{const c=hf(f),d=new mi;return Object.assign(d,c),d})}else throw new Error("This method is only supported by the Vertex AI.")}async get(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=vc(this.apiClient,e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>pt(d))}else{const c=_u(this.apiClient,e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>dt(d))}}async listInternal(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=Tc(this.apiClient,e);return u=E("{models_url}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>{const p=Tf(this.apiClient,d),m=new An;return Object.assign(m,p),m})}else{const c=Eu(this.apiClient,e);return u=E("{models_url}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>{const p=Wc(this.apiClient,d),m=new An;return Object.assign(m,p),m})}}async update(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=_c(this.apiClient,e);return u=E("{model}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"PATCH",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>pt(d))}else{const c=Mu(this.apiClient,e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"PATCH",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>dt(d))}}async delete(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=Sc(this.apiClient,e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(()=>{const d=Cf(),p=new Mn;return Object.assign(p,d),p})}else{const c=Iu(this.apiClient,e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(()=>{const d=Zc(),p=new Mn;return Object.assign(p,d),p})}}async countTokens(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=Ac(this.apiClient,e);return u=E("{model}:countTokens",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>{const p=_f(d),m=new In;return Object.assign(m,p),m})}else{const c=Ru(this.apiClient,e);return u=E("{model}:countTokens",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>{const p=Yc(d),m=new In;return Object.assign(m,p),m})}}async computeTokens(e){var n,o;let s,l="",a={};if(this.apiClient.isVertexAI()){const u=Mc(this.apiClient,e);return l=E("{model}:computeTokens",u._url),a=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:l,queryParams:a,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),s.then(f=>{const c=Sf(f),d=new hi;return Object.assign(d,c),d})}else throw new Error("This method is only supported by the Vertex AI.")}async generateVideos(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=Rc(this.apiClient,e);return u=E("{model}:predictLongRunning",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>If(d))}else{const c=Nu(this.apiClient,e);return u=E("{model}:predictLongRunning",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>Xc(d))}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function jf(t){const e={},n=i(t,["operationName"]);n!=null&&r(e,["_url","operationName"],n);const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function ed(t){const e={},n=i(t,["operationName"]);n!=null&&r(e,["_url","operationName"],n);const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function td(t){const e={},n=i(t,["operationName"]);n!=null&&r(e,["operationName"],n);const o=i(t,["resourceName"]);o!=null&&r(e,["_url","resourceName"],o);const s=i(t,["config"]);return s!=null&&r(e,["config"],s),e}function nd(t){const e={},n=i(t,["video","uri"]);n!=null&&r(e,["uri"],n);const o=i(t,["video","encodedVideo"]);o!=null&&r(e,["videoBytes"],te(o));const s=i(t,["encoding"]);return s!=null&&r(e,["mimeType"],s),e}function od(t){const e={},n=i(t,["_self"]);return n!=null&&r(e,["video"],nd(n)),e}function id(t){const e={},n=i(t,["generatedSamples"]);if(n!=null){let l=n;Array.isArray(l)&&(l=l.map(a=>od(a))),r(e,["generatedVideos"],l)}const o=i(t,["raiMediaFilteredCount"]);o!=null&&r(e,["raiMediaFilteredCount"],o);const s=i(t,["raiMediaFilteredReasons"]);return s!=null&&r(e,["raiMediaFilteredReasons"],s),e}function rd(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["metadata"]);o!=null&&r(e,["metadata"],o);const s=i(t,["done"]);s!=null&&r(e,["done"],s);const l=i(t,["error"]);l!=null&&r(e,["error"],l);const a=i(t,["response","generateVideoResponse"]);return a!=null&&r(e,["response"],id(a)),e}function sd(t){const e={},n=i(t,["gcsUri"]);n!=null&&r(e,["uri"],n);const o=i(t,["bytesBase64Encoded"]);o!=null&&r(e,["videoBytes"],te(o));const s=i(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function ld(t){const e={},n=i(t,["_self"]);return n!=null&&r(e,["video"],sd(n)),e}function ad(t){const e={},n=i(t,["videos"]);if(n!=null){let l=n;Array.isArray(l)&&(l=l.map(a=>ld(a))),r(e,["generatedVideos"],l)}const o=i(t,["raiMediaFilteredCount"]);o!=null&&r(e,["raiMediaFilteredCount"],o);const s=i(t,["raiMediaFilteredReasons"]);return s!=null&&r(e,["raiMediaFilteredReasons"],s),e}function Qn(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["metadata"]);o!=null&&r(e,["metadata"],o);const s=i(t,["done"]);s!=null&&r(e,["done"],s);const l=i(t,["error"]);l!=null&&r(e,["error"],l);const a=i(t,["response"]);return a!=null&&r(e,["response"],ad(a)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class ud extends ce{constructor(e){super(),this.apiClient=e}async getVideosOperation(e){const n=e.operation,o=e.config;if(n.name===void 0||n.name==="")throw new Error("Operation name is required.");if(this.apiClient.isVertexAI()){const s=n.name.split("/operations/")[0];let l;return o&&"httpOptions"in o&&(l=o.httpOptions),this.fetchPredictVideosOperationInternal({operationName:n.name,resourceName:s,config:{httpOptions:l}})}else return this.getVideosOperationInternal({operationName:n.name,config:o})}async getVideosOperationInternal(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=ed(e);return u=E("{operationName}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>Qn(d))}else{const c=jf(e);return u=E("{operationName}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>rd(d))}}async fetchPredictVideosOperationInternal(e){var n,o;let s,l="",a={};if(this.apiClient.isVertexAI()){const u=td(e);return l=E("{resourceName}:fetchPredictOperation",u._url),a=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:l,queryParams:a,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),s.then(f=>Qn(f))}else throw new Error("This method is only supported by the Vertex AI.")}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function cd(t){const e={},n=i(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function wo(t){const e={},n=i(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],cd(n)),e}function fd(t){const e={},n=i(t,["speaker"]);n!=null&&r(e,["speaker"],n);const o=i(t,["voiceConfig"]);return o!=null&&r(e,["voiceConfig"],wo(o)),e}function dd(t){const e={},n=i(t,["speakerVoiceConfigs"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>fd(s))),r(e,["speakerVoiceConfigs"],o)}return e}function pd(t){const e={},n=i(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],wo(n));const o=i(t,["multiSpeakerVoiceConfig"]);o!=null&&r(e,["multiSpeakerVoiceConfig"],dd(o));const s=i(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function md(t){const e={},n=i(t,["fps"]);n!=null&&r(e,["fps"],n);const o=i(t,["endOffset"]);o!=null&&r(e,["endOffset"],o);const s=i(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function hd(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["data"]);n!=null&&r(e,["data"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function gd(t){const e={};if(i(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=i(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const o=i(t,["mimeType"]);return o!=null&&r(e,["mimeType"],o),e}function vd(t){const e={},n=i(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],md(n));const o=i(t,["thought"]);o!=null&&r(e,["thought"],o);const s=i(t,["inlineData"]);s!=null&&r(e,["inlineData"],hd(s));const l=i(t,["fileData"]);l!=null&&r(e,["fileData"],gd(l));const a=i(t,["thoughtSignature"]);a!=null&&r(e,["thoughtSignature"],a);const u=i(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=i(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=i(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const d=i(t,["functionResponse"]);d!=null&&r(e,["functionResponse"],d);const p=i(t,["text"]);return p!=null&&r(e,["text"],p),e}function yd(t){const e={},n=i(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(l=>vd(l))),r(e,["parts"],s)}const o=i(t,["role"]);return o!=null&&r(e,["role"],o),e}function Td(t){const e={},n=i(t,["behavior"]);n!=null&&r(e,["behavior"],n);const o=i(t,["description"]);o!=null&&r(e,["description"],o);const s=i(t,["name"]);s!=null&&r(e,["name"],s);const l=i(t,["parameters"]);l!=null&&r(e,["parameters"],l);const a=i(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const u=i(t,["response"]);u!=null&&r(e,["response"],u);const f=i(t,["responseJsonSchema"]);return f!=null&&r(e,["responseJsonSchema"],f),e}function Cd(t){const e={},n=i(t,["startTime"]);n!=null&&r(e,["startTime"],n);const o=i(t,["endTime"]);return o!=null&&r(e,["endTime"],o),e}function _d(t){const e={},n=i(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Cd(n)),e}function Sd(t){const e={},n=i(t,["mode"]);n!=null&&r(e,["mode"],n);const o=i(t,["dynamicThreshold"]);return o!=null&&r(e,["dynamicThreshold"],o),e}function Ed(t){const e={},n=i(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Sd(n)),e}function Ad(){return{}}function Md(t){const e={},n=i(t,["functionDeclarations"]);if(n!=null){let u=n;Array.isArray(u)&&(u=u.map(f=>Td(f))),r(e,["functionDeclarations"],u)}if(i(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const o=i(t,["googleSearch"]);o!=null&&r(e,["googleSearch"],_d(o));const s=i(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],Ed(s)),i(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(i(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");i(t,["urlContext"])!=null&&r(e,["urlContext"],Ad());const a=i(t,["codeExecution"]);return a!=null&&r(e,["codeExecution"],a),e}function Id(t){const e={},n=i(t,["handle"]);if(n!=null&&r(e,["handle"],n),i(t,["transparent"])!==void 0)throw new Error("transparent parameter is not supported in Gemini API.");return e}function jn(){return{}}function xd(t){const e={},n=i(t,["disabled"]);n!=null&&r(e,["disabled"],n);const o=i(t,["startOfSpeechSensitivity"]);o!=null&&r(e,["startOfSpeechSensitivity"],o);const s=i(t,["endOfSpeechSensitivity"]);s!=null&&r(e,["endOfSpeechSensitivity"],s);const l=i(t,["prefixPaddingMs"]);l!=null&&r(e,["prefixPaddingMs"],l);const a=i(t,["silenceDurationMs"]);return a!=null&&r(e,["silenceDurationMs"],a),e}function Rd(t){const e={},n=i(t,["automaticActivityDetection"]);n!=null&&r(e,["automaticActivityDetection"],xd(n));const o=i(t,["activityHandling"]);o!=null&&r(e,["activityHandling"],o);const s=i(t,["turnCoverage"]);return s!=null&&r(e,["turnCoverage"],s),e}function wd(t){const e={},n=i(t,["targetTokens"]);return n!=null&&r(e,["targetTokens"],n),e}function Pd(t){const e={},n=i(t,["triggerTokens"]);n!=null&&r(e,["triggerTokens"],n);const o=i(t,["slidingWindow"]);return o!=null&&r(e,["slidingWindow"],wd(o)),e}function Nd(t){const e={},n=i(t,["proactiveAudio"]);return n!=null&&r(e,["proactiveAudio"],n),e}function kd(t,e){const n={},o=i(t,["generationConfig"]);e!==void 0&&o!=null&&r(e,["setup","generationConfig"],o);const s=i(t,["responseModalities"]);e!==void 0&&s!=null&&r(e,["setup","generationConfig","responseModalities"],s);const l=i(t,["temperature"]);e!==void 0&&l!=null&&r(e,["setup","generationConfig","temperature"],l);const a=i(t,["topP"]);e!==void 0&&a!=null&&r(e,["setup","generationConfig","topP"],a);const u=i(t,["topK"]);e!==void 0&&u!=null&&r(e,["setup","generationConfig","topK"],u);const f=i(t,["maxOutputTokens"]);e!==void 0&&f!=null&&r(e,["setup","generationConfig","maxOutputTokens"],f);const c=i(t,["mediaResolution"]);e!==void 0&&c!=null&&r(e,["setup","generationConfig","mediaResolution"],c);const d=i(t,["seed"]);e!==void 0&&d!=null&&r(e,["setup","generationConfig","seed"],d);const p=i(t,["speechConfig"]);e!==void 0&&p!=null&&r(e,["setup","generationConfig","speechConfig"],pd(yt(p)));const m=i(t,["enableAffectiveDialog"]);e!==void 0&&m!=null&&r(e,["setup","generationConfig","enableAffectiveDialog"],m);const h=i(t,["systemInstruction"]);e!==void 0&&h!=null&&r(e,["setup","systemInstruction"],yd(J(h)));const g=i(t,["tools"]);if(e!==void 0&&g!=null){let I=Se(g);Array.isArray(I)&&(I=I.map(U=>Md(_e(U)))),r(e,["setup","tools"],I)}const y=i(t,["sessionResumption"]);e!==void 0&&y!=null&&r(e,["setup","sessionResumption"],Id(y));const S=i(t,["inputAudioTranscription"]);e!==void 0&&S!=null&&r(e,["setup","inputAudioTranscription"],jn());const A=i(t,["outputAudioTranscription"]);e!==void 0&&A!=null&&r(e,["setup","outputAudioTranscription"],jn());const x=i(t,["realtimeInputConfig"]);e!==void 0&&x!=null&&r(e,["setup","realtimeInputConfig"],Rd(x));const M=i(t,["contextWindowCompression"]);e!==void 0&&M!=null&&r(e,["setup","contextWindowCompression"],Pd(M));const P=i(t,["proactivity"]);return e!==void 0&&P!=null&&r(e,["setup","proactivity"],Nd(P)),n}function Dd(t,e){const n={},o=i(e,["model"]);o!=null&&r(n,["setup","model"],L(t,o));const s=i(e,["config"]);return s!=null&&r(n,["config"],kd(s,n)),n}function Vd(t,e,n){const o={},s=i(e,["expireTime"]);n!==void 0&&s!=null&&r(n,["expireTime"],s);const l=i(e,["newSessionExpireTime"]);n!==void 0&&l!=null&&r(n,["newSessionExpireTime"],l);const a=i(e,["uses"]);n!==void 0&&a!=null&&r(n,["uses"],a);const u=i(e,["liveConnectConstraints"]);n!==void 0&&u!=null&&r(n,["bidiGenerateContentSetup"],Dd(t,u));const f=i(e,["lockAdditionalFields"]);return n!==void 0&&f!=null&&r(n,["fieldMask"],f),o}function Fd(t,e){const n={},o=i(e,["config"]);return o!=null&&r(n,["config"],Vd(t,o,n)),n}function Ld(t){const e={},n=i(t,["name"]);return n!=null&&r(e,["name"],n),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Ud(t){const e=[];for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)){const o=t[n];if(typeof o=="object"&&o!=null&&Object.keys(o).length>0){const s=Object.keys(o).map(l=>`${n}.${l}`);e.push(...s)}else e.push(n)}return e.join(",")}function Gd(t,e){let n=null;const o=t.bidiGenerateContentSetup;if(typeof o=="object"&&o!==null&&"setup"in o){const l=o.setup;typeof l=="object"&&l!==null?(t.bidiGenerateContentSetup=l,n=l):delete t.bidiGenerateContentSetup}else o!==void 0&&delete t.bidiGenerateContentSetup;const s=t.fieldMask;if(n){const l=Ud(n);if(Array.isArray(e?.lockAdditionalFields)&&e?.lockAdditionalFields.length===0)l?t.fieldMask=l:delete t.fieldMask;else if(e?.lockAdditionalFields&&e.lockAdditionalFields.length>0&&s!==null&&Array.isArray(s)&&s.length>0){const a=["temperature","topK","topP","maxOutputTokens","responseModalities","seed","speechConfig"];let u=[];s.length>0&&(u=s.map(c=>a.includes(c)?`generationConfig.${c}`:c));const f=[];l&&f.push(l),u.length>0&&f.push(...u),f.length>0?t.fieldMask=f.join(","):delete t.fieldMask}else delete t.fieldMask}else s!==null&&Array.isArray(s)&&s.length>0?t.fieldMask=s.join(","):delete t.fieldMask;return t}class Bd extends ce{constructor(e){super(),this.apiClient=e}async create(e){var n,o;let s,l="",a={};if(this.apiClient.isVertexAI())throw new Error("The client.tokens.create method is only supported by the Gemini Developer API.");{const u=Fd(this.apiClient,e);l=E("auth_tokens",u._url),a=u._query,delete u.config,delete u._url,delete u._query;const f=Gd(u,e.config);return s=this.apiClient.request({path:l,queryParams:a,body:JSON.stringify(f),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(c=>c.json()),s.then(c=>Ld(c))}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function qd(t){const e={},n=i(t,["name"]);n!=null&&r(e,["_url","name"],n);const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function $d(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);e!==void 0&&s!=null&&r(e,["_query","pageToken"],s);const l=i(t,["filter"]);return e!==void 0&&l!=null&&r(e,["_query","filter"],l),n}function Jd(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],$d(n,e)),e}function bd(t){const e={},n=i(t,["textInput"]);n!=null&&r(e,["textInput"],n);const o=i(t,["output"]);return o!=null&&r(e,["output"],o),e}function Hd(t){const e={};if(i(t,["gcsUri"])!==void 0)throw new Error("gcsUri parameter is not supported in Gemini API.");const n=i(t,["examples"]);if(n!=null){let o=n;Array.isArray(o)&&(o=o.map(s=>bd(s))),r(e,["examples","examples"],o)}return e}function Wd(t,e){const n={};if(i(t,["validationDataset"])!==void 0)throw new Error("validationDataset parameter is not supported in Gemini API.");const o=i(t,["tunedModelDisplayName"]);if(e!==void 0&&o!=null&&r(e,["displayName"],o),i(t,["description"])!==void 0)throw new Error("description parameter is not supported in Gemini API.");const s=i(t,["epochCount"]);e!==void 0&&s!=null&&r(e,["tuningTask","hyperparameters","epochCount"],s);const l=i(t,["learningRateMultiplier"]);if(l!=null&&r(n,["tuningTask","hyperparameters","learningRateMultiplier"],l),i(t,["exportLastCheckpointOnly"])!==void 0)throw new Error("exportLastCheckpointOnly parameter is not supported in Gemini API.");if(i(t,["adapterSize"])!==void 0)throw new Error("adapterSize parameter is not supported in Gemini API.");const a=i(t,["batchSize"]);e!==void 0&&a!=null&&r(e,["tuningTask","hyperparameters","batchSize"],a);const u=i(t,["learningRate"]);return e!==void 0&&u!=null&&r(e,["tuningTask","hyperparameters","learningRate"],u),n}function Zd(t){const e={},n=i(t,["baseModel"]);n!=null&&r(e,["baseModel"],n);const o=i(t,["trainingDataset"]);o!=null&&r(e,["tuningTask","trainingData"],Hd(o));const s=i(t,["config"]);return s!=null&&r(e,["config"],Wd(s,e)),e}function Yd(t){const e={},n=i(t,["name"]);n!=null&&r(e,["_url","name"],n);const o=i(t,["config"]);return o!=null&&r(e,["config"],o),e}function Kd(t,e){const n={},o=i(t,["pageSize"]);e!==void 0&&o!=null&&r(e,["_query","pageSize"],o);const s=i(t,["pageToken"]);e!==void 0&&s!=null&&r(e,["_query","pageToken"],s);const l=i(t,["filter"]);return e!==void 0&&l!=null&&r(e,["_query","filter"],l),n}function zd(t){const e={},n=i(t,["config"]);return n!=null&&r(e,["config"],Kd(n,e)),e}function Od(t,e){const n={},o=i(t,["gcsUri"]);if(e!==void 0&&o!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],o),i(t,["examples"])!==void 0)throw new Error("examples parameter is not supported in Vertex AI.");return n}function Xd(t){const e={},n=i(t,["gcsUri"]);return n!=null&&r(e,["validationDatasetUri"],n),e}function Qd(t,e){const n={},o=i(t,["validationDataset"]);e!==void 0&&o!=null&&r(e,["supervisedTuningSpec"],Xd(o));const s=i(t,["tunedModelDisplayName"]);e!==void 0&&s!=null&&r(e,["tunedModelDisplayName"],s);const l=i(t,["description"]);e!==void 0&&l!=null&&r(e,["description"],l);const a=i(t,["epochCount"]);e!==void 0&&a!=null&&r(e,["supervisedTuningSpec","hyperParameters","epochCount"],a);const u=i(t,["learningRateMultiplier"]);e!==void 0&&u!=null&&r(e,["supervisedTuningSpec","hyperParameters","learningRateMultiplier"],u);const f=i(t,["exportLastCheckpointOnly"]);e!==void 0&&f!=null&&r(e,["supervisedTuningSpec","exportLastCheckpointOnly"],f);const c=i(t,["adapterSize"]);if(e!==void 0&&c!=null&&r(e,["supervisedTuningSpec","hyperParameters","adapterSize"],c),i(t,["batchSize"])!==void 0)throw new Error("batchSize parameter is not supported in Vertex AI.");if(i(t,["learningRate"])!==void 0)throw new Error("learningRate parameter is not supported in Vertex AI.");return n}function jd(t){const e={},n=i(t,["baseModel"]);n!=null&&r(e,["baseModel"],n);const o=i(t,["trainingDataset"]);o!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],Od(o,e));const s=i(t,["config"]);return s!=null&&r(e,["config"],Qd(s,e)),e}function ep(t){const e={},n=i(t,["name"]);n!=null&&r(e,["model"],n);const o=i(t,["name"]);return o!=null&&r(e,["endpoint"],o),e}function Po(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["state"]);o!=null&&r(e,["state"],ao(o));const s=i(t,["createTime"]);s!=null&&r(e,["createTime"],s);const l=i(t,["tuningTask","startTime"]);l!=null&&r(e,["startTime"],l);const a=i(t,["tuningTask","completeTime"]);a!=null&&r(e,["endTime"],a);const u=i(t,["updateTime"]);u!=null&&r(e,["updateTime"],u);const f=i(t,["description"]);f!=null&&r(e,["description"],f);const c=i(t,["baseModel"]);c!=null&&r(e,["baseModel"],c);const d=i(t,["_self"]);d!=null&&r(e,["tunedModel"],ep(d));const p=i(t,["distillationSpec"]);p!=null&&r(e,["distillationSpec"],p);const m=i(t,["experiment"]);m!=null&&r(e,["experiment"],m);const h=i(t,["labels"]);h!=null&&r(e,["labels"],h);const g=i(t,["pipelineJob"]);g!=null&&r(e,["pipelineJob"],g);const y=i(t,["serviceAccount"]);y!=null&&r(e,["serviceAccount"],y);const S=i(t,["tunedModelDisplayName"]);return S!=null&&r(e,["tunedModelDisplayName"],S),e}function tp(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["tunedModels"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(l=>Po(l))),r(e,["tuningJobs"],s)}return e}function np(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["metadata"]);o!=null&&r(e,["metadata"],o);const s=i(t,["done"]);s!=null&&r(e,["done"],s);const l=i(t,["error"]);return l!=null&&r(e,["error"],l),e}function op(t){const e={},n=i(t,["checkpointId"]);n!=null&&r(e,["checkpointId"],n);const o=i(t,["epoch"]);o!=null&&r(e,["epoch"],o);const s=i(t,["step"]);s!=null&&r(e,["step"],s);const l=i(t,["endpoint"]);return l!=null&&r(e,["endpoint"],l),e}function ip(t){const e={},n=i(t,["model"]);n!=null&&r(e,["model"],n);const o=i(t,["endpoint"]);o!=null&&r(e,["endpoint"],o);const s=i(t,["checkpoints"]);if(s!=null){let l=s;Array.isArray(l)&&(l=l.map(a=>op(a))),r(e,["checkpoints"],l)}return e}function ht(t){const e={},n=i(t,["name"]);n!=null&&r(e,["name"],n);const o=i(t,["state"]);o!=null&&r(e,["state"],ao(o));const s=i(t,["createTime"]);s!=null&&r(e,["createTime"],s);const l=i(t,["startTime"]);l!=null&&r(e,["startTime"],l);const a=i(t,["endTime"]);a!=null&&r(e,["endTime"],a);const u=i(t,["updateTime"]);u!=null&&r(e,["updateTime"],u);const f=i(t,["error"]);f!=null&&r(e,["error"],f);const c=i(t,["description"]);c!=null&&r(e,["description"],c);const d=i(t,["baseModel"]);d!=null&&r(e,["baseModel"],d);const p=i(t,["tunedModel"]);p!=null&&r(e,["tunedModel"],ip(p));const m=i(t,["supervisedTuningSpec"]);m!=null&&r(e,["supervisedTuningSpec"],m);const h=i(t,["tuningDataStats"]);h!=null&&r(e,["tuningDataStats"],h);const g=i(t,["encryptionSpec"]);g!=null&&r(e,["encryptionSpec"],g);const y=i(t,["partnerModelTuningSpec"]);y!=null&&r(e,["partnerModelTuningSpec"],y);const S=i(t,["distillationSpec"]);S!=null&&r(e,["distillationSpec"],S);const A=i(t,["experiment"]);A!=null&&r(e,["experiment"],A);const x=i(t,["labels"]);x!=null&&r(e,["labels"],x);const M=i(t,["pipelineJob"]);M!=null&&r(e,["pipelineJob"],M);const P=i(t,["serviceAccount"]);P!=null&&r(e,["serviceAccount"],P);const I=i(t,["tunedModelDisplayName"]);return I!=null&&r(e,["tunedModelDisplayName"],I),e}function rp(t){const e={},n=i(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const o=i(t,["tuningJobs"]);if(o!=null){let s=o;Array.isArray(s)&&(s=s.map(l=>ht(l))),r(e,["tuningJobs"],s)}return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class sp extends ce{constructor(e){super(),this.apiClient=e,this.get=async n=>await this.getInternal(n),this.list=async(n={})=>new Pe(ue.PAGED_ITEM_TUNING_JOBS,o=>this.listInternal(o),await this.listInternal(n),n),this.tune=async n=>{if(this.apiClient.isVertexAI())return await this.tuneInternal(n);{const o=await this.tuneMldevInternal(n);let s="";return o.metadata!==void 0&&o.metadata.tunedModel!==void 0?s=o.metadata.tunedModel:o.name!==void 0&&o.name.includes("/operations/")&&(s=o.name.split("/operations/")[0]),{name:s,state:rt.JOB_STATE_QUEUED}}}}async getInternal(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=Yd(e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>ht(d))}else{const c=qd(e);return u=E("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>Po(d))}}async listInternal(e){var n,o,s,l;let a,u="",f={};if(this.apiClient.isVertexAI()){const c=zd(e);return u=E("tuningJobs",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(d=>d.json()),a.then(d=>{const p=rp(d),m=new xn;return Object.assign(m,p),m})}else{const c=Jd(e);return u=E("tunedModels",c._url),f=c._query,delete c.config,delete c._url,delete c._query,a=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(l=e.config)===null||l===void 0?void 0:l.abortSignal}).then(d=>d.json()),a.then(d=>{const p=tp(d),m=new xn;return Object.assign(m,p),m})}}async tuneInternal(e){var n,o;let s,l="",a={};if(this.apiClient.isVertexAI()){const u=jd(e);return l=E("tuningJobs",u._url),a=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:l,queryParams:a,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),s.then(f=>ht(f))}else throw new Error("This method is only supported by the Vertex AI.")}async tuneMldevInternal(e){var n,o;let s,l="",a={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=Zd(e);return l=E("tunedModels",u._url),a=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:l,queryParams:a,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(o=e.config)===null||o===void 0?void 0:o.abortSignal}).then(f=>f.json()),s.then(f=>np(f))}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class lp{async download(e,n){throw new Error("Download to file is not supported in the browser, please use a browser compliant download like an <a> tag.")}}const ap=1024*1024*8,up=3,cp=1e3,fp=2,Xe="x-goog-upload-status";async function dp(t,e,n){var o,s,l;let a=0,u=0,f=new st(new Response),c="upload";for(a=t.size;u<a;){const p=Math.min(ap,a-u),m=t.slice(u,u+p);u+p>=a&&(c+=", finalize");let h=0,g=cp;for(;h<up&&(f=await n.request({path:"",body:m,httpMethod:"POST",httpOptions:{apiVersion:"",baseUrl:e,headers:{"X-Goog-Upload-Command":c,"X-Goog-Upload-Offset":String(u),"Content-Length":String(p)}}}),!(!((o=f?.headers)===null||o===void 0)&&o[Xe]));)h++,await mp(g),g=g*fp;if(u+=p,((s=f?.headers)===null||s===void 0?void 0:s[Xe])!=="active")break;if(a<=u)throw new Error("All content has been uploaded, but the upload status is not finalized.")}const d=await f?.json();if(((l=f?.headers)===null||l===void 0?void 0:l[Xe])!=="final")throw new Error("Failed to upload file: Upload status is not finalized.");return d.file}async function pp(t){return{size:t.size,type:t.type}}function mp(t){return new Promise(e=>setTimeout(e,t))}class hp{async upload(e,n,o){if(typeof e=="string")throw new Error("File path is not supported in browser uploader.");return await dp(e,n,o)}async stat(e){if(typeof e=="string")throw new Error("File path is not supported in browser uploader.");return await pp(e)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class gp{create(e,n,o){return new vp(e,n,o)}}class vp{constructor(e,n,o){this.url=e,this.headers=n,this.callbacks=o}connect(){this.ws=new WebSocket(this.url),this.ws.onopen=this.callbacks.onopen,this.ws.onerror=this.callbacks.onerror,this.ws.onclose=this.callbacks.onclose,this.ws.onmessage=this.callbacks.onmessage}send(e){if(this.ws===void 0)throw new Error("WebSocket is not connected");this.ws.send(e)}close(){if(this.ws===void 0)throw new Error("WebSocket is not connected");this.ws.close()}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const eo="x-goog-api-key";class yp{constructor(e){this.apiKey=e}async addAuthHeaders(e){if(e.get(eo)===null){if(this.apiKey.startsWith("auth_tokens/"))throw new Error("Ephemeral tokens are only supported by the live API.");if(!this.apiKey)throw new Error("API key is missing. Please provide a valid API key.");e.append(eo,this.apiKey)}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Tp="gl-node/";class Cp{constructor(e){var n;if(e.apiKey==null)throw new Error("An API Key must be set when running in a browser");if(e.project||e.location)throw new Error("Vertex AI project based authentication is not supported on browser runtimes. Please do not provide a project or location.");this.vertexai=(n=e.vertexai)!==null&&n!==void 0?n:!1,this.apiKey=e.apiKey;const o=di(e,void 0,void 0);o&&(e.httpOptions?e.httpOptions.baseUrl=o:e.httpOptions={baseUrl:o}),this.apiVersion=e.apiVersion;const s=new yp(this.apiKey);this.apiClient=new Vf({auth:s,apiVersion:this.apiVersion,apiKey:this.apiKey,vertexai:this.vertexai,httpOptions:e.httpOptions,userAgentExtra:Tp+"web",uploader:new hp,downloader:new lp}),this.models=new Qf(this.apiClient),this.live=new Yf(this.apiClient,s,new gp),this.batches=new qr(this.apiClient),this.chats=new Hs(this.models,this.apiClient),this.caches=new $s(this.apiClient),this.files=new ol(this.apiClient),this.operations=new ud(this.apiClient),this.authTokens=new Bd(this.apiClient),this.tunings=new sp(this.apiClient)}}export{Cp as G};
