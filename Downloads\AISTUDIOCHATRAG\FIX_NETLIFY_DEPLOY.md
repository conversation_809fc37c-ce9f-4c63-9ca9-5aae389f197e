# 🔧 Correção do Erro de Deploy no Netlify

## ❌ Problema Identificado

O erro `npm error Missing script: "build"` ocorre porque o Netlify está executando o comando no diretório errado (`C:\Users\<USER>\Users\italo\Downloads\AISTUDIOCHATRAG`).

## ✅ Soluções (3 Opções)

### Opção 1: Deploy Manual Correto (RECOMENDADO)

```bash
# 1. Navegar para o diretório correto
cd C:\Users\<USER>\Downloads\AISTUDIOCHATRAG

# 2. Verificar se está no diretório certo
pwd
ls package.json  # Deve mostrar o arquivo

# 3. Instalar dependências
npm install

# 4. Fazer build local
npm run build

# 5. Deploy manual
netlify deploy --dir=dist

# 6. Se tudo estiver OK, deploy para produção
netlify deploy --prod --dir=dist
```

### Opção 2: Conectar Repositório GitHub (AUTOMÁTICO)

1. **Fazer push do código para GitHub:**
   ```bash
   cd C:\Users\<USER>\Downloads\AISTUDIOCHATRAG
   git init
   git add .
   git commit -m "Sistema da Vereadora Rafaela - Deploy inicial"
   git branch -M main
   git remote add origin https://github.com/SEU_USUARIO/vereadora-rafaela.git
   git push -u origin main
   ```

2. **No painel do Netlify:**
   - Clique em "New site from Git"
   - Conecte seu repositório GitHub
   - Configure:
     ```
     Base directory: (deixar vazio)
     Build command: npm run build
     Publish directory: dist
     ```

3. **Configurar variáveis de ambiente no Netlify:**
   - Site settings → Environment variables
   - Adicionar:
     ```
     VITE_GEMINI_API_KEY=sua_chave_gemini
     VITE_SUPABASE_URL=https://seu-projeto.supabase.co
     VITE_SUPABASE_ANON_KEY=sua_chave_supabase
     VITE_SUPABASE_SERVICE_ROLE_KEY=sua_chave_service
     NODE_ENV=production
     ```

### Opção 3: Drag & Drop (MAIS SIMPLES)

```bash
# 1. Navegar para o diretório correto
cd C:\Users\<USER>\Downloads\AISTUDIOCHATRAG

# 2. Fazer build
npm install
npm run build

# 3. No painel do Netlify:
# - Clique em "Deploy manually"
# - Arraste a pasta "dist" para o Netlify
```

## 🔧 Verificações Antes do Deploy

### 1. Verificar Estrutura do Projeto
```bash
cd C:\Users\<USER>\Downloads\AISTUDIOCHATRAG

# Verificar arquivos essenciais
ls package.json      # ✅ Deve existir
ls netlify.toml      # ✅ Deve existir
ls index.html        # ✅ Deve existir
ls vite.config.ts    # ✅ Deve existir
```

### 2. Testar Build Local
```bash
# Limpar cache
npm run clean

# Instalar dependências
npm install

# Fazer build
npm run build

# Verificar se dist foi criado
ls dist/             # ✅ Deve conter index.html e assets/

# Testar preview local
npm run preview      # ✅ Deve abrir em http://localhost:4173
```

### 3. Verificar Scripts no package.json
```bash
npm run              # Deve mostrar todos os scripts disponíveis
```

## 🚀 Deploy Passo a Passo (MÉTODO RECOMENDADO)

### Passo 1: Preparar Ambiente
```bash
# Abrir PowerShell como Administrador
cd C:\Users\<USER>\Downloads\AISTUDIOCHATRAG

# Verificar Node.js
node --version       # Deve ser 16+ 
npm --version        # Deve estar instalado

# Verificar Netlify CLI
netlify --version    # Se não estiver: npm install -g netlify-cli
netlify status       # Se não logado: netlify login
```

### Passo 2: Build e Teste
```bash
# Limpar tudo
rm -rf dist node_modules/.vite

# Instalar dependências
npm ci

# Build para produção
npm run build

# Verificar build
ls dist/index.html   # ✅ Deve existir
```

### Passo 3: Deploy
```bash
# Deploy de teste
netlify deploy --dir=dist

# Testar a URL de preview fornecida
# Se tudo OK, deploy para produção:
netlify deploy --prod --dir=dist
```

## 🔍 Troubleshooting

### Se ainda der erro "Missing script: build"
```bash
# Verificar se está no diretório certo
pwd
# Deve mostrar: C:\Users\<USER>\Downloads\AISTUDIOCHATRAG

# Verificar conteúdo do package.json
cat package.json | grep -A 10 '"scripts"'
# Deve mostrar o script "build": "vite build"
```

### Se der erro de dependências
```bash
# Deletar node_modules e reinstalar
rm -rf node_modules package-lock.json
npm install
```

### Se der erro de permissões
```bash
# Executar PowerShell como Administrador
# Ou usar:
npm config set fund false
npm config set audit false
```

## 📋 Checklist Final

Antes de fazer deploy, verificar:

- [ ] Está no diretório correto (`Downloads\AISTUDIOCHATRAG`)
- [ ] `package.json` existe e tem script "build"
- [ ] `npm install` executou sem erros
- [ ] `npm run build` cria pasta `dist/`
- [ ] `dist/index.html` existe
- [ ] Variáveis de ambiente configuradas no Netlify
- [ ] Netlify CLI instalado e logado

## 🎯 Comando Rápido (Copy & Paste)

```bash
cd C:\Users\<USER>\Downloads\AISTUDIOCHATRAG && npm install && npm run build && netlify deploy --dir=dist
```

Se tudo funcionar, então:
```bash
netlify deploy --prod --dir=dist
```

## 📞 Se Ainda Não Funcionar

1. **Verificar logs completos:**
   ```bash
   npm run build --verbose
   ```

2. **Testar com Vite diretamente:**
   ```bash
   npx vite build
   ```

3. **Usar método drag & drop no Netlify**

4. **Criar issue no GitHub com logs completos**

---

**🎯 O problema principal é executar os comandos do diretório correto!**
