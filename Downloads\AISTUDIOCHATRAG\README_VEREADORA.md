# Sistema RAG Avançado - Vereadora Rafaela de Nilda

Sistema completo de atendimento automatizado para o gabinete da Vereadora Rafaela de Nilda de Parnamirim/RN, utilizando tecnologia RAG (Retrieval-Augmented Generation) avançada com pipeline completo de IA.

## 🎯 Visão Geral

Este sistema implementa uma solução completa de atendimento ao cidadão com a persona da Vereadora Rafaela de Nilda, incluindo:

- **Pipeline RAG Avançado** com chunking inteligente, embeddings multi-modais e retrieval híbrido
- **Persona Autêntica** da vereadora com conhecimento específico sobre Parnamirim/RN
- **Monitoramento Completo** com métricas de performance e qualidade
- **Integração WhatsApp** para atendimento direto aos cidadãos
- **Sistema de Feedback** para melhoria contínua

## 🚀 Características Principais

### Pipeline RAG Avançado
- **Ingestão Inteligente**: Suporte a PDF, DOCX, TXT, XLSX, CSV, imagens e APIs
- **Chunking Semântico**: Segmentação inteligente com sobreposição contextual
- **Embeddings Multi-Modal**: Vetorização de texto, código e multimídia
- **Retrieval Híbrido**: Busca vetorial + BM25 + temporal + grafo
- **Reranking Inteligente**: Cross-encoder e LLM scoring
- **Otimização de Contexto**: Compressão e sumarização seletiva

### Persona da Vereadora
- **Identidade Completa**: Rafaela de Nilda, Vereadora de Parnamirim/RN
- **Conhecimento Local**: Informações específicas sobre o município
- **Tom Profissional**: Linguagem acolhedora e próxima ao cidadão
- **Áreas de Atuação**: Assistência social, educação, saúde, meio ambiente

### Monitoramento e Observabilidade
- **Métricas de Performance**: Tempo de resposta, latência, throughput
- **Métricas de Qualidade**: Relevância, satisfação, precisão
- **Sistema de Alertas**: Notificações automáticas de problemas
- **Dashboard Completo**: Visualização em tempo real

## 🛠️ Tecnologias

### Frontend
- **React 19** + TypeScript + Vite
- **Tailwind CSS** para estilização
- **Componentes Personalizados** para a vereadora

### Backend & IA
- **Google Gemini 2.5 Flash** como LLM principal
- **Supabase** com pgvector para armazenamento vetorial
- **Node.js + Express** para APIs
- **WPPConnect** para integração WhatsApp

### Pipeline RAG
- **ChromaDB/FAISS** para índices vetoriais
- **BM25** para busca textual
- **Cross-encoders** para reranking
- **Algoritmos de fusão** (RRF, ponderada)

## 📋 Configuração

### 1. Variáveis de Ambiente

```env
# APIs Principais
VITE_GEMINI_API_KEY=sua_chave_gemini_api
VITE_SUPABASE_URL=sua_url_supabase
VITE_SUPABASE_ANON_KEY=sua_chave_supabase_anonima
VITE_SUPABASE_SERVICE_ROLE_KEY=sua_chave_supabase_service

# Configurações RAG
RAG_MAX_CHUNKS=10
RAG_CHUNK_SIZE=1000
RAG_CHUNK_OVERLAP=200
RAG_RERANKING_ENABLED=true

# Monitoramento
MONITORING_ENABLED=true
FEEDBACK_ENABLED=true
```

### 2. Banco de Dados

Execute os scripts SQL para criar as tabelas:

```bash
# Tabelas principais
psql -f database/schema.sql

# Extensões vetoriais
CREATE EXTENSION IF NOT EXISTS vector;
```

### 3. Instalação

```bash
# Frontend
npm install

# Backend
cd backend && npm install

# Dependências RAG
npm install @google/genai @supabase/supabase-js pdfjs-dist
```

## 🚀 Execução

### Desenvolvimento
```bash
# Frontend (porta 5173)
npm run dev

# Backend (porta 3001)
cd backend && npm run dev

# Testes do sistema
node test-vereadora-system.js
```

### Produção
```bash
# Build do frontend
npm run build

# Deploy do backend
npm run start:prod

# Monitoramento
npm run monitor
```

## 📊 Uso do Sistema

### 1. Interface Principal
- **Header Personalizado**: Informações da vereadora e status do sistema
- **Chat Inteligente**: Conversação com persona autêntica
- **Gestão de Documentos**: Upload e processamento de arquivos
- **Dashboard de Métricas**: Monitoramento em tempo real

### 2. Funcionalidades RAG
- **Upload de Documentos**: Processamento automático com chunking
- **Consultas Inteligentes**: Respostas baseadas em documentos oficiais
- **Citação de Fontes**: Referências automáticas aos documentos
- **Feedback Contínuo**: Sistema de avaliação das respostas

### 3. Integração WhatsApp
- **Atendimento Automatizado**: Bot com persona da vereadora
- **Escalação Inteligente**: Direcionamento para atendimento humano
- **Histórico Completo**: Registro de todas as interações

## 🔧 Configuração da Persona

A persona da Vereadora Rafaela está configurada em `config/vereadora.ts`:

```typescript
export const VEREADORA_CONFIG = {
  pessoal: {
    nome: "Rafaela de Nilda",
    cargo: "Vereadora",
    municipio: "Parnamirim",
    estado: "Rio Grande do Norte"
  },
  atuacao: {
    areas_prioritarias: [
      "Assistência Social e Direitos Humanos",
      "Educação e Cultura",
      "Saúde Pública",
      // ...
    ]
  }
  // ...
};
```

## 📈 Monitoramento

### Métricas Coletadas
- **Performance**: Tempo de resposta, latência, throughput
- **Qualidade**: Relevância, satisfação, precisão
- **Uso**: Consultas, usuários, sessões
- **Erros**: Taxa de erro, tipos de falha

### Dashboard
Acesse a aba de monitoramento para visualizar:
- Status do sistema em tempo real
- Métricas de performance
- Alertas ativos
- Estatísticas de uso

## 🧪 Testes

### Testes Automatizados
```bash
# Teste completo do sistema
node test-vereadora-system.js

# Validação específica
npm run test:rag
npm run test:monitoring
npm run test:persona
```

### Validação Manual
1. **Upload de Documentos**: Teste com PDFs, DOCX, etc.
2. **Consultas RAG**: Perguntas sobre documentos carregados
3. **Persona**: Verificar tom e conhecimento da vereadora
4. **WhatsApp**: Teste de integração completa

## 📁 Estrutura do Projeto

```
├── components/
│   ├── VereadoraHeader.tsx      # Header personalizado
│   ├── MonitoringDashboard.tsx  # Dashboard de métricas
│   └── ...
├── services/
│   ├── vereadoraRAGService.ts   # Serviço principal integrado
│   ├── ragPipeline.ts           # Pipeline RAG avançado
│   ├── ingestionService.ts      # Ingestão de documentos
│   ├── vectorStore.ts           # Armazenamento vetorial
│   ├── rerankingService.ts      # Reranking inteligente
│   ├── monitoringService.ts     # Monitoramento e métricas
│   └── ...
├── config/
│   └── vereadora.ts             # Configuração da persona
├── backend/
│   ├── server.js                # Servidor principal
│   └── services/                # Serviços backend
├── database/
│   ├── schema.sql               # Schema principal
│   └── monitoring_schema.sql    # Schema de monitoramento
└── test-vereadora-system.js     # Testes automatizados
```

## 🎯 Exemplos de Uso

### Consultas Típicas dos Cidadãos
- "Como posso solicitar uma consulta médica no posto de saúde?"
- "Quais são os projetos da vereadora para a educação?"
- "Como funciona o programa de assistência social?"
- "Onde posso denunciar problemas de saneamento?"
- "Quais são os horários de atendimento do gabinete?"

### Respostas Personalizadas
O sistema responde com a persona da Vereadora Rafaela, incluindo:
- Conhecimento específico sobre Parnamirim
- Orientações práticas sobre serviços públicos
- Informações sobre projetos e iniciativas
- Encaminhamentos adequados quando necessário

## 🔄 Fluxo de Processamento

1. **Recepção da Consulta**: Via chat ou WhatsApp
2. **Análise Semântica**: Compreensão da intenção do usuário
3. **Retrieval Híbrido**: Busca em documentos relevantes
4. **Reranking**: Priorização dos melhores resultados
5. **Geração de Resposta**: Com persona da vereadora
6. **Monitoramento**: Coleta de métricas e feedback

## 🤝 Contribuição

1. Fork o repositório
2. Crie uma branch: `git checkout -b feature/nova-funcionalidade`
3. Commit: `git commit -m 'Adiciona nova funcionalidade'`
4. Push: `git push origin feature/nova-funcionalidade`
5. Abra um Pull Request

## 📄 Licença

MIT License - veja o arquivo LICENSE para detalhes.

## 🆘 Suporte

Para suporte técnico ou dúvidas sobre implementação:
- Abra uma issue no GitHub
- Consulte a documentação técnica
- Execute os testes automatizados

---

**Desenvolvido para o Gabinete da Vereadora Rafaela de Nilda - Parnamirim/RN**

*Sistema de atendimento automatizado com tecnologia RAG avançada para melhor servir aos cidadãos de Parnamirim.*
