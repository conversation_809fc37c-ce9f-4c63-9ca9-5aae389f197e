import{r as Id,a as Pd,b as X0}from"./vendor-DrD-X_hS.js";import{_ as Q0,c as eh}from"./supabase-zuWrSlgC.js";import{G as Lr}from"./gemini-DcgyraGI.js";import{_ as Z0,a as K0}from"./pdf-BCx2EAgV.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))r(d);new MutationObserver(d=>{for(const m of d)if(m.type==="childList")for(const g of m.addedNodes)g.tagName==="LINK"&&g.rel==="modulepreload"&&r(g)}).observe(document,{childList:!0,subtree:!0});function c(d){const m={};return d.integrity&&(m.integrity=d.integrity),d.referrerPolicy&&(m.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?m.credentials="include":d.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function r(d){if(d.ep)return;d.ep=!0;const m=c(d);fetch(d.href,m)}})();var Er={exports:{}},Cl={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ld;function J0(){if(Ld)return Cl;Ld=1;var u=Symbol.for("react.transitional.element"),s=Symbol.for("react.fragment");function c(r,d,m){var g=null;if(m!==void 0&&(g=""+m),d.key!==void 0&&(g=""+d.key),"key"in d){m={};for(var y in d)y!=="key"&&(m[y]=d[y])}else m=d;return d=m.ref,{$$typeof:u,type:r,key:g,ref:d!==void 0?d:null,props:m}}return Cl.Fragment=s,Cl.jsx=c,Cl.jsxs=c,Cl}var Vd;function $0(){return Vd||(Vd=1,Er.exports=J0()),Er.exports}var f=$0(),k=Id();const Dr=Pd(k);var Ar={exports:{}},Ml={},Nr={exports:{}},Tr={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gd;function W0(){return Gd||(Gd=1,function(u){function s(T,z){var H=T.length;T.push(z);e:for(;0<H;){var P=H-1>>>1,ue=T[P];if(0<d(ue,z))T[P]=z,T[H]=ue,H=P;else break e}}function c(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var z=T[0],H=T.pop();if(H!==z){T[0]=H;e:for(var P=0,ue=T.length,ce=ue>>>1;P<ce;){var re=2*(P+1)-1,Z=T[re],pe=re+1,Be=T[pe];if(0>d(Z,H))pe<ue&&0>d(Be,Z)?(T[P]=Be,T[pe]=H,P=pe):(T[P]=Z,T[re]=H,P=re);else if(pe<ue&&0>d(Be,H))T[P]=Be,T[pe]=H,P=pe;else break e}}return z}function d(T,z){var H=T.sortIndex-z.sortIndex;return H!==0?H:T.id-z.id}if(u.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var m=performance;u.unstable_now=function(){return m.now()}}else{var g=Date,y=g.now();u.unstable_now=function(){return g.now()-y}}var b=[],M=[],C=1,R=null,O=3,Q=!1,X=!1,D=!1,B=!1,J=typeof setTimeout=="function"?setTimeout:null,ie=typeof clearTimeout=="function"?clearTimeout:null,I=typeof setImmediate<"u"?setImmediate:null;function be(T){for(var z=c(M);z!==null;){if(z.callback===null)r(M);else if(z.startTime<=T)r(M),z.sortIndex=z.expirationTime,s(b,z);else break;z=c(M)}}function He(T){if(D=!1,be(T),!X)if(c(b)!==null)X=!0,te||(te=!0,Le());else{var z=c(M);z!==null&&pt(He,z.startTime-T)}}var te=!1,q=-1,ne=5,ve=-1;function St(){return B?!0:!(u.unstable_now()-ve<ne)}function Ye(){if(B=!1,te){var T=u.unstable_now();ve=T;var z=!0;try{e:{X=!1,D&&(D=!1,ie(q),q=-1),Q=!0;var H=O;try{t:{for(be(T),R=c(b);R!==null&&!(R.expirationTime>T&&St());){var P=R.callback;if(typeof P=="function"){R.callback=null,O=R.priorityLevel;var ue=P(R.expirationTime<=T);if(T=u.unstable_now(),typeof ue=="function"){R.callback=ue,be(T),z=!0;break t}R===c(b)&&r(b),be(T)}else r(b);R=c(b)}if(R!==null)z=!0;else{var ce=c(M);ce!==null&&pt(He,ce.startTime-T),z=!1}}break e}finally{R=null,O=H,Q=!1}z=void 0}}finally{z?Le():te=!1}}}var Le;if(typeof I=="function")Le=function(){I(Ye)};else if(typeof MessageChannel<"u"){var Ea=new MessageChannel,Wt=Ea.port2;Ea.port1.onmessage=Ye,Le=function(){Wt.postMessage(null)}}else Le=function(){J(Ye,0)};function pt(T,z){q=J(function(){T(u.unstable_now())},z)}u.unstable_IdlePriority=5,u.unstable_ImmediatePriority=1,u.unstable_LowPriority=4,u.unstable_NormalPriority=3,u.unstable_Profiling=null,u.unstable_UserBlockingPriority=2,u.unstable_cancelCallback=function(T){T.callback=null},u.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):ne=0<T?Math.floor(1e3/T):5},u.unstable_getCurrentPriorityLevel=function(){return O},u.unstable_next=function(T){switch(O){case 1:case 2:case 3:var z=3;break;default:z=O}var H=O;O=z;try{return T()}finally{O=H}},u.unstable_requestPaint=function(){B=!0},u.unstable_runWithPriority=function(T,z){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var H=O;O=T;try{return z()}finally{O=H}},u.unstable_scheduleCallback=function(T,z,H){var P=u.unstable_now();switch(typeof H=="object"&&H!==null?(H=H.delay,H=typeof H=="number"&&0<H?P+H:P):H=P,T){case 1:var ue=-1;break;case 2:ue=250;break;case 5:ue=1073741823;break;case 4:ue=1e4;break;default:ue=5e3}return ue=H+ue,T={id:C++,callback:z,priorityLevel:T,startTime:H,expirationTime:ue,sortIndex:-1},H>P?(T.sortIndex=H,s(M,T),c(b)===null&&T===c(M)&&(D?(ie(q),q=-1):D=!0,pt(He,H-P))):(T.sortIndex=ue,s(b,T),X||Q||(X=!0,te||(te=!0,Le()))),T},u.unstable_shouldYield=St,u.unstable_wrapCallback=function(T){var z=O;return function(){var H=O;O=z;try{return T.apply(this,arguments)}finally{O=H}}}}(Tr)),Tr}var Yd;function F0(){return Yd||(Yd=1,Nr.exports=W0()),Nr.exports}var Xd;function I0(){if(Xd)return Ml;Xd=1;/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var u=F0(),s=Id(),c=X0();function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function m(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function g(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function y(e){if(m(e)!==e)throw Error(r(188))}function b(e){var t=e.alternate;if(!t){if(t=m(e),t===null)throw Error(r(188));return t!==e?null:e}for(var a=e,n=t;;){var l=a.return;if(l===null)break;var i=l.alternate;if(i===null){if(n=l.return,n!==null){a=n;continue}break}if(l.child===i.child){for(i=l.child;i;){if(i===a)return y(l),e;if(i===n)return y(l),t;i=i.sibling}throw Error(r(188))}if(a.return!==n.return)a=l,n=i;else{for(var o=!1,h=l.child;h;){if(h===a){o=!0,a=l,n=i;break}if(h===n){o=!0,n=l,a=i;break}h=h.sibling}if(!o){for(h=i.child;h;){if(h===a){o=!0,a=i,n=l;break}if(h===n){o=!0,n=i,a=l;break}h=h.sibling}if(!o)throw Error(r(189))}}if(a.alternate!==n)throw Error(r(190))}if(a.tag!==3)throw Error(r(188));return a.stateNode.current===a?e:t}function M(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=M(e),t!==null)return t;e=e.sibling}return null}var C=Object.assign,R=Symbol.for("react.element"),O=Symbol.for("react.transitional.element"),Q=Symbol.for("react.portal"),X=Symbol.for("react.fragment"),D=Symbol.for("react.strict_mode"),B=Symbol.for("react.profiler"),J=Symbol.for("react.provider"),ie=Symbol.for("react.consumer"),I=Symbol.for("react.context"),be=Symbol.for("react.forward_ref"),He=Symbol.for("react.suspense"),te=Symbol.for("react.suspense_list"),q=Symbol.for("react.memo"),ne=Symbol.for("react.lazy"),ve=Symbol.for("react.activity"),St=Symbol.for("react.memo_cache_sentinel"),Ye=Symbol.iterator;function Le(e){return e===null||typeof e!="object"?null:(e=Ye&&e[Ye]||e["@@iterator"],typeof e=="function"?e:null)}var Ea=Symbol.for("react.client.reference");function Wt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Ea?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case X:return"Fragment";case B:return"Profiler";case D:return"StrictMode";case He:return"Suspense";case te:return"SuspenseList";case ve:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case Q:return"Portal";case I:return(e.displayName||"Context")+".Provider";case ie:return(e._context.displayName||"Context")+".Consumer";case be:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case q:return t=e.displayName||null,t!==null?t:Wt(e.type)||"Memo";case ne:t=e._payload,e=e._init;try{return Wt(e(t))}catch{}}return null}var pt=Array.isArray,T=s.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,z=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,H={pending:!1,data:null,method:null,action:null},P=[],ue=-1;function ce(e){return{current:e}}function re(e){0>ue||(e.current=P[ue],P[ue]=null,ue--)}function Z(e,t){ue++,P[ue]=e.current,e.current=t}var pe=ce(null),Be=ce(null),Et=ce(null),Va=ce(null);function st(e,t){switch(Z(Et,t),Z(Be,e),Z(pe,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?md(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=md(t),e=gd(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}re(pe),Z(pe,e)}function Rt(){re(pe),re(Be),re(Et)}function ci(e){e.memoizedState!==null&&Z(Va,e);var t=pe.current,a=gd(t,e.type);t!==a&&(Z(Be,e),Z(pe,a))}function Bl(e){Be.current===e&&(re(pe),re(Be)),Va.current===e&&(re(Va),Nl._currentValue=H)}var ri=Object.prototype.hasOwnProperty,ui=u.unstable_scheduleCallback,oi=u.unstable_cancelCallback,Eh=u.unstable_shouldYield,Ah=u.unstable_requestPaint,At=u.unstable_now,Nh=u.unstable_getCurrentPriorityLevel,Zr=u.unstable_ImmediatePriority,Kr=u.unstable_UserBlockingPriority,Ul=u.unstable_NormalPriority,Th=u.unstable_LowPriority,Jr=u.unstable_IdlePriority,_h=u.log,jh=u.unstable_setDisableYieldValue,Dn=null,We=null;function Ft(e){if(typeof _h=="function"&&jh(e),We&&typeof We.setStrictMode=="function")try{We.setStrictMode(Dn,e)}catch{}}var Fe=Math.clz32?Math.clz32:Mh,wh=Math.log,Ch=Math.LN2;function Mh(e){return e>>>=0,e===0?32:31-(wh(e)/Ch|0)|0}var kl=256,ql=4194304;function Aa(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Hl(e,t,a){var n=e.pendingLanes;if(n===0)return 0;var l=0,i=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var h=n&134217727;return h!==0?(n=h&~i,n!==0?l=Aa(n):(o&=h,o!==0?l=Aa(o):a||(a=h&~e,a!==0&&(l=Aa(a))))):(h=n&~i,h!==0?l=Aa(h):o!==0?l=Aa(o):a||(a=n&~e,a!==0&&(l=Aa(a)))),l===0?0:t!==0&&t!==l&&(t&i)===0&&(i=l&-l,a=t&-t,i>=a||i===32&&(a&4194048)!==0)?t:l}function Rn(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function zh(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function $r(){var e=kl;return kl<<=1,(kl&4194048)===0&&(kl=256),e}function Wr(){var e=ql;return ql<<=1,(ql&62914560)===0&&(ql=4194304),e}function fi(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function On(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Dh(e,t,a,n,l,i){var o=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var h=e.entanglements,p=e.expirationTimes,E=e.hiddenUpdates;for(a=o&~a;0<a;){var _=31-Fe(a),w=1<<_;h[_]=0,p[_]=-1;var A=E[_];if(A!==null)for(E[_]=null,_=0;_<A.length;_++){var N=A[_];N!==null&&(N.lane&=-536870913)}a&=~w}n!==0&&Fr(e,n,0),i!==0&&l===0&&e.tag!==0&&(e.suspendedLanes|=i&~(o&~t))}function Fr(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var n=31-Fe(t);e.entangledLanes|=t,e.entanglements[n]=e.entanglements[n]|1073741824|a&4194090}function Ir(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var n=31-Fe(a),l=1<<n;l&t|e[n]&t&&(e[n]|=t),a&=~l}}function di(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function hi(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Pr(){var e=z.p;return e!==0?e:(e=window.event,e===void 0?32:Od(e.type))}function Rh(e,t){var a=z.p;try{return z.p=e,t()}finally{z.p=a}}var It=Math.random().toString(36).slice(2),Ve="__reactFiber$"+It,Qe="__reactProps$"+It,Ga="__reactContainer$"+It,mi="__reactEvents$"+It,Oh="__reactListeners$"+It,Bh="__reactHandles$"+It,eu="__reactResources$"+It,Bn="__reactMarker$"+It;function gi(e){delete e[Ve],delete e[Qe],delete e[mi],delete e[Oh],delete e[Bh]}function Ya(e){var t=e[Ve];if(t)return t;for(var a=e.parentNode;a;){if(t=a[Ga]||a[Ve]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=xd(e);e!==null;){if(a=e[Ve])return a;e=xd(e)}return t}e=a,a=e.parentNode}return null}function Xa(e){if(e=e[Ve]||e[Ga]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function Un(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(r(33))}function Qa(e){var t=e[eu];return t||(t=e[eu]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function ze(e){e[Bn]=!0}var tu=new Set,au={};function Na(e,t){Za(e,t),Za(e+"Capture",t)}function Za(e,t){for(au[e]=t,e=0;e<t.length;e++)tu.add(t[e])}var Uh=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),nu={},lu={};function kh(e){return ri.call(lu,e)?!0:ri.call(nu,e)?!1:Uh.test(e)?lu[e]=!0:(nu[e]=!0,!1)}function Ll(e,t,a){if(kh(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var n=t.toLowerCase().slice(0,5);if(n!=="data-"&&n!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function Vl(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Ot(e,t,a,n){if(n===null)e.removeAttribute(a);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+n)}}var pi,su;function Ka(e){if(pi===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);pi=t&&t[1]||"",su=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+pi+e+su}var yi=!1;function vi(e,t){if(!e||yi)return"";yi=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var n={DetermineComponentFrameRoot:function(){try{if(t){var w=function(){throw Error()};if(Object.defineProperty(w.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(w,[])}catch(N){var A=N}Reflect.construct(e,[],w)}else{try{w.call()}catch(N){A=N}e.call(w.prototype)}}else{try{throw Error()}catch(N){A=N}(w=e())&&typeof w.catch=="function"&&w.catch(function(){})}}catch(N){if(N&&A&&typeof N.stack=="string")return[N.stack,A.stack]}return[null,null]}};n.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var l=Object.getOwnPropertyDescriptor(n.DetermineComponentFrameRoot,"name");l&&l.configurable&&Object.defineProperty(n.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var i=n.DetermineComponentFrameRoot(),o=i[0],h=i[1];if(o&&h){var p=o.split(`
`),E=h.split(`
`);for(l=n=0;n<p.length&&!p[n].includes("DetermineComponentFrameRoot");)n++;for(;l<E.length&&!E[l].includes("DetermineComponentFrameRoot");)l++;if(n===p.length||l===E.length)for(n=p.length-1,l=E.length-1;1<=n&&0<=l&&p[n]!==E[l];)l--;for(;1<=n&&0<=l;n--,l--)if(p[n]!==E[l]){if(n!==1||l!==1)do if(n--,l--,0>l||p[n]!==E[l]){var _=`
`+p[n].replace(" at new "," at ");return e.displayName&&_.includes("<anonymous>")&&(_=_.replace("<anonymous>",e.displayName)),_}while(1<=n&&0<=l);break}}}finally{yi=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?Ka(a):""}function qh(e){switch(e.tag){case 26:case 27:case 5:return Ka(e.type);case 16:return Ka("Lazy");case 13:return Ka("Suspense");case 19:return Ka("SuspenseList");case 0:case 15:return vi(e.type,!1);case 11:return vi(e.type.render,!1);case 1:return vi(e.type,!0);case 31:return Ka("Activity");default:return""}}function iu(e){try{var t="";do t+=qh(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function it(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function cu(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Hh(e){var t=cu(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var l=a.get,i=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(o){n=""+o,i.call(this,o)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return n},setValue:function(o){n=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Gl(e){e._valueTracker||(e._valueTracker=Hh(e))}function ru(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),n="";return e&&(n=cu(e)?e.checked?"true":"false":e.value),e=n,e!==a?(t.setValue(e),!0):!1}function Yl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Lh=/[\n"\\]/g;function ct(e){return e.replace(Lh,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function xi(e,t,a,n,l,i,o,h){e.name="",o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?e.type=o:e.removeAttribute("type"),t!=null?o==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+it(t)):e.value!==""+it(t)&&(e.value=""+it(t)):o!=="submit"&&o!=="reset"||e.removeAttribute("value"),t!=null?bi(e,o,it(t)):a!=null?bi(e,o,it(a)):n!=null&&e.removeAttribute("value"),l==null&&i!=null&&(e.defaultChecked=!!i),l!=null&&(e.checked=l&&typeof l!="function"&&typeof l!="symbol"),h!=null&&typeof h!="function"&&typeof h!="symbol"&&typeof h!="boolean"?e.name=""+it(h):e.removeAttribute("name")}function uu(e,t,a,n,l,i,o,h){if(i!=null&&typeof i!="function"&&typeof i!="symbol"&&typeof i!="boolean"&&(e.type=i),t!=null||a!=null){if(!(i!=="submit"&&i!=="reset"||t!=null))return;a=a!=null?""+it(a):"",t=t!=null?""+it(t):a,h||t===e.value||(e.value=t),e.defaultValue=t}n=n??l,n=typeof n!="function"&&typeof n!="symbol"&&!!n,e.checked=h?e.checked:!!n,e.defaultChecked=!!n,o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(e.name=o)}function bi(e,t,a){t==="number"&&Yl(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function Ja(e,t,a,n){if(e=e.options,t){t={};for(var l=0;l<a.length;l++)t["$"+a[l]]=!0;for(a=0;a<e.length;a++)l=t.hasOwnProperty("$"+e[a].value),e[a].selected!==l&&(e[a].selected=l),l&&n&&(e[a].defaultSelected=!0)}else{for(a=""+it(a),t=null,l=0;l<e.length;l++){if(e[l].value===a){e[l].selected=!0,n&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function ou(e,t,a){if(t!=null&&(t=""+it(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+it(a):""}function fu(e,t,a,n){if(t==null){if(n!=null){if(a!=null)throw Error(r(92));if(pt(n)){if(1<n.length)throw Error(r(93));n=n[0]}a=n}a==null&&(a=""),t=a}a=it(t),e.defaultValue=a,n=e.textContent,n===a&&n!==""&&n!==null&&(e.value=n)}function $a(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var Vh=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function du(e,t,a){var n=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?n?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":n?e.setProperty(t,a):typeof a!="number"||a===0||Vh.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function hu(e,t,a){if(t!=null&&typeof t!="object")throw Error(r(62));if(e=e.style,a!=null){for(var n in a)!a.hasOwnProperty(n)||t!=null&&t.hasOwnProperty(n)||(n.indexOf("--")===0?e.setProperty(n,""):n==="float"?e.cssFloat="":e[n]="");for(var l in t)n=t[l],t.hasOwnProperty(l)&&a[l]!==n&&du(e,l,n)}else for(var i in t)t.hasOwnProperty(i)&&du(e,i,t[i])}function Si(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Gh=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Yh=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Xl(e){return Yh.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Ei=null;function Ai(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Wa=null,Fa=null;function mu(e){var t=Xa(e);if(t&&(e=t.stateNode)){var a=e[Qe]||null;e:switch(e=t.stateNode,t.type){case"input":if(xi(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+ct(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var n=a[t];if(n!==e&&n.form===e.form){var l=n[Qe]||null;if(!l)throw Error(r(90));xi(n,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name)}}for(t=0;t<a.length;t++)n=a[t],n.form===e.form&&ru(n)}break e;case"textarea":ou(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&Ja(e,!!a.multiple,t,!1)}}}var Ni=!1;function gu(e,t,a){if(Ni)return e(t,a);Ni=!0;try{var n=e(t);return n}finally{if(Ni=!1,(Wa!==null||Fa!==null)&&(Cs(),Wa&&(t=Wa,e=Fa,Fa=Wa=null,mu(t),e)))for(t=0;t<e.length;t++)mu(e[t])}}function kn(e,t){var a=e.stateNode;if(a===null)return null;var n=a[Qe]||null;if(n===null)return null;a=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(r(231,t,typeof a));return a}var Bt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Ti=!1;if(Bt)try{var qn={};Object.defineProperty(qn,"passive",{get:function(){Ti=!0}}),window.addEventListener("test",qn,qn),window.removeEventListener("test",qn,qn)}catch{Ti=!1}var Pt=null,_i=null,Ql=null;function pu(){if(Ql)return Ql;var e,t=_i,a=t.length,n,l="value"in Pt?Pt.value:Pt.textContent,i=l.length;for(e=0;e<a&&t[e]===l[e];e++);var o=a-e;for(n=1;n<=o&&t[a-n]===l[i-n];n++);return Ql=l.slice(e,1<n?1-n:void 0)}function Zl(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Kl(){return!0}function yu(){return!1}function Ze(e){function t(a,n,l,i,o){this._reactName=a,this._targetInst=l,this.type=n,this.nativeEvent=i,this.target=o,this.currentTarget=null;for(var h in e)e.hasOwnProperty(h)&&(a=e[h],this[h]=a?a(i):i[h]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Kl:yu,this.isPropagationStopped=yu,this}return C(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Kl)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Kl)},persist:function(){},isPersistent:Kl}),t}var Ta={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Jl=Ze(Ta),Hn=C({},Ta,{view:0,detail:0}),Xh=Ze(Hn),ji,wi,Ln,$l=C({},Hn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Mi,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Ln&&(Ln&&e.type==="mousemove"?(ji=e.screenX-Ln.screenX,wi=e.screenY-Ln.screenY):wi=ji=0,Ln=e),ji)},movementY:function(e){return"movementY"in e?e.movementY:wi}}),vu=Ze($l),Qh=C({},$l,{dataTransfer:0}),Zh=Ze(Qh),Kh=C({},Hn,{relatedTarget:0}),Ci=Ze(Kh),Jh=C({},Ta,{animationName:0,elapsedTime:0,pseudoElement:0}),$h=Ze(Jh),Wh=C({},Ta,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Fh=Ze(Wh),Ih=C({},Ta,{data:0}),xu=Ze(Ih),Ph={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},em={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},tm={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function am(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=tm[e])?!!t[e]:!1}function Mi(){return am}var nm=C({},Hn,{key:function(e){if(e.key){var t=Ph[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Zl(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?em[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Mi,charCode:function(e){return e.type==="keypress"?Zl(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Zl(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),lm=Ze(nm),sm=C({},$l,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),bu=Ze(sm),im=C({},Hn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Mi}),cm=Ze(im),rm=C({},Ta,{propertyName:0,elapsedTime:0,pseudoElement:0}),um=Ze(rm),om=C({},$l,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),fm=Ze(om),dm=C({},Ta,{newState:0,oldState:0}),hm=Ze(dm),mm=[9,13,27,32],zi=Bt&&"CompositionEvent"in window,Vn=null;Bt&&"documentMode"in document&&(Vn=document.documentMode);var gm=Bt&&"TextEvent"in window&&!Vn,Su=Bt&&(!zi||Vn&&8<Vn&&11>=Vn),Eu=" ",Au=!1;function Nu(e,t){switch(e){case"keyup":return mm.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Tu(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Ia=!1;function pm(e,t){switch(e){case"compositionend":return Tu(t);case"keypress":return t.which!==32?null:(Au=!0,Eu);case"textInput":return e=t.data,e===Eu&&Au?null:e;default:return null}}function ym(e,t){if(Ia)return e==="compositionend"||!zi&&Nu(e,t)?(e=pu(),Ql=_i=Pt=null,Ia=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Su&&t.locale!=="ko"?null:t.data;default:return null}}var vm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function _u(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!vm[e.type]:t==="textarea"}function ju(e,t,a,n){Wa?Fa?Fa.push(n):Fa=[n]:Wa=n,t=Bs(t,"onChange"),0<t.length&&(a=new Jl("onChange","change",null,a,n),e.push({event:a,listeners:t}))}var Gn=null,Yn=null;function xm(e){ud(e,0)}function Wl(e){var t=Un(e);if(ru(t))return e}function wu(e,t){if(e==="change")return t}var Cu=!1;if(Bt){var Di;if(Bt){var Ri="oninput"in document;if(!Ri){var Mu=document.createElement("div");Mu.setAttribute("oninput","return;"),Ri=typeof Mu.oninput=="function"}Di=Ri}else Di=!1;Cu=Di&&(!document.documentMode||9<document.documentMode)}function zu(){Gn&&(Gn.detachEvent("onpropertychange",Du),Yn=Gn=null)}function Du(e){if(e.propertyName==="value"&&Wl(Yn)){var t=[];ju(t,Yn,e,Ai(e)),gu(xm,t)}}function bm(e,t,a){e==="focusin"?(zu(),Gn=t,Yn=a,Gn.attachEvent("onpropertychange",Du)):e==="focusout"&&zu()}function Sm(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Wl(Yn)}function Em(e,t){if(e==="click")return Wl(t)}function Am(e,t){if(e==="input"||e==="change")return Wl(t)}function Nm(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ie=typeof Object.is=="function"?Object.is:Nm;function Xn(e,t){if(Ie(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),n=Object.keys(t);if(a.length!==n.length)return!1;for(n=0;n<a.length;n++){var l=a[n];if(!ri.call(t,l)||!Ie(e[l],t[l]))return!1}return!0}function Ru(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ou(e,t){var a=Ru(e);e=0;for(var n;a;){if(a.nodeType===3){if(n=e+a.textContent.length,e<=t&&n>=t)return{node:a,offset:t-e};e=n}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=Ru(a)}}function Bu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Bu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Uu(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=Yl(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=Yl(e.document)}return t}function Oi(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var Tm=Bt&&"documentMode"in document&&11>=document.documentMode,Pa=null,Bi=null,Qn=null,Ui=!1;function ku(e,t,a){var n=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Ui||Pa==null||Pa!==Yl(n)||(n=Pa,"selectionStart"in n&&Oi(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),Qn&&Xn(Qn,n)||(Qn=n,n=Bs(Bi,"onSelect"),0<n.length&&(t=new Jl("onSelect","select",null,t,a),e.push({event:t,listeners:n}),t.target=Pa)))}function _a(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var en={animationend:_a("Animation","AnimationEnd"),animationiteration:_a("Animation","AnimationIteration"),animationstart:_a("Animation","AnimationStart"),transitionrun:_a("Transition","TransitionRun"),transitionstart:_a("Transition","TransitionStart"),transitioncancel:_a("Transition","TransitionCancel"),transitionend:_a("Transition","TransitionEnd")},ki={},qu={};Bt&&(qu=document.createElement("div").style,"AnimationEvent"in window||(delete en.animationend.animation,delete en.animationiteration.animation,delete en.animationstart.animation),"TransitionEvent"in window||delete en.transitionend.transition);function ja(e){if(ki[e])return ki[e];if(!en[e])return e;var t=en[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in qu)return ki[e]=t[a];return e}var Hu=ja("animationend"),Lu=ja("animationiteration"),Vu=ja("animationstart"),_m=ja("transitionrun"),jm=ja("transitionstart"),wm=ja("transitioncancel"),Gu=ja("transitionend"),Yu=new Map,qi="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");qi.push("scrollEnd");function yt(e,t){Yu.set(e,t),Na(t,[e])}var Xu=new WeakMap;function rt(e,t){if(typeof e=="object"&&e!==null){var a=Xu.get(e);return a!==void 0?a:(t={value:e,source:t,stack:iu(t)},Xu.set(e,t),t)}return{value:e,source:t,stack:iu(t)}}var ut=[],tn=0,Hi=0;function Fl(){for(var e=tn,t=Hi=tn=0;t<e;){var a=ut[t];ut[t++]=null;var n=ut[t];ut[t++]=null;var l=ut[t];ut[t++]=null;var i=ut[t];if(ut[t++]=null,n!==null&&l!==null){var o=n.pending;o===null?l.next=l:(l.next=o.next,o.next=l),n.pending=l}i!==0&&Qu(a,l,i)}}function Il(e,t,a,n){ut[tn++]=e,ut[tn++]=t,ut[tn++]=a,ut[tn++]=n,Hi|=n,e.lanes|=n,e=e.alternate,e!==null&&(e.lanes|=n)}function Li(e,t,a,n){return Il(e,t,a,n),Pl(e)}function an(e,t){return Il(e,null,null,t),Pl(e)}function Qu(e,t,a){e.lanes|=a;var n=e.alternate;n!==null&&(n.lanes|=a);for(var l=!1,i=e.return;i!==null;)i.childLanes|=a,n=i.alternate,n!==null&&(n.childLanes|=a),i.tag===22&&(e=i.stateNode,e===null||e._visibility&1||(l=!0)),e=i,i=i.return;return e.tag===3?(i=e.stateNode,l&&t!==null&&(l=31-Fe(a),e=i.hiddenUpdates,n=e[l],n===null?e[l]=[t]:n.push(t),t.lane=a|536870912),i):null}function Pl(e){if(50<pl)throw pl=0,Zc=null,Error(r(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var nn={};function Cm(e,t,a,n){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Pe(e,t,a,n){return new Cm(e,t,a,n)}function Vi(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ut(e,t){var a=e.alternate;return a===null?(a=Pe(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function Zu(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function es(e,t,a,n,l,i){var o=0;if(n=e,typeof e=="function")Vi(e)&&(o=1);else if(typeof e=="string")o=z0(e,a,pe.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ve:return e=Pe(31,a,t,l),e.elementType=ve,e.lanes=i,e;case X:return wa(a.children,l,i,t);case D:o=8,l|=24;break;case B:return e=Pe(12,a,t,l|2),e.elementType=B,e.lanes=i,e;case He:return e=Pe(13,a,t,l),e.elementType=He,e.lanes=i,e;case te:return e=Pe(19,a,t,l),e.elementType=te,e.lanes=i,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case J:case I:o=10;break e;case ie:o=9;break e;case be:o=11;break e;case q:o=14;break e;case ne:o=16,n=null;break e}o=29,a=Error(r(130,e===null?"null":typeof e,"")),n=null}return t=Pe(o,a,t,l),t.elementType=e,t.type=n,t.lanes=i,t}function wa(e,t,a,n){return e=Pe(7,e,n,t),e.lanes=a,e}function Gi(e,t,a){return e=Pe(6,e,null,t),e.lanes=a,e}function Yi(e,t,a){return t=Pe(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var ln=[],sn=0,ts=null,as=0,ot=[],ft=0,Ca=null,kt=1,qt="";function Ma(e,t){ln[sn++]=as,ln[sn++]=ts,ts=e,as=t}function Ku(e,t,a){ot[ft++]=kt,ot[ft++]=qt,ot[ft++]=Ca,Ca=e;var n=kt;e=qt;var l=32-Fe(n)-1;n&=~(1<<l),a+=1;var i=32-Fe(t)+l;if(30<i){var o=l-l%5;i=(n&(1<<o)-1).toString(32),n>>=o,l-=o,kt=1<<32-Fe(t)+l|a<<l|n,qt=i+e}else kt=1<<i|a<<l|n,qt=e}function Xi(e){e.return!==null&&(Ma(e,1),Ku(e,1,0))}function Qi(e){for(;e===ts;)ts=ln[--sn],ln[sn]=null,as=ln[--sn],ln[sn]=null;for(;e===Ca;)Ca=ot[--ft],ot[ft]=null,qt=ot[--ft],ot[ft]=null,kt=ot[--ft],ot[ft]=null}var Xe=null,Ee=null,se=!1,za=null,Nt=!1,Zi=Error(r(519));function Da(e){var t=Error(r(418,""));throw Jn(rt(t,e)),Zi}function Ju(e){var t=e.stateNode,a=e.type,n=e.memoizedProps;switch(t[Ve]=e,t[Qe]=n,a){case"dialog":F("cancel",t),F("close",t);break;case"iframe":case"object":case"embed":F("load",t);break;case"video":case"audio":for(a=0;a<vl.length;a++)F(vl[a],t);break;case"source":F("error",t);break;case"img":case"image":case"link":F("error",t),F("load",t);break;case"details":F("toggle",t);break;case"input":F("invalid",t),uu(t,n.value,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name,!0),Gl(t);break;case"select":F("invalid",t);break;case"textarea":F("invalid",t),fu(t,n.value,n.defaultValue,n.children),Gl(t)}a=n.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||n.suppressHydrationWarning===!0||hd(t.textContent,a)?(n.popover!=null&&(F("beforetoggle",t),F("toggle",t)),n.onScroll!=null&&F("scroll",t),n.onScrollEnd!=null&&F("scrollend",t),n.onClick!=null&&(t.onclick=Us),t=!0):t=!1,t||Da(e)}function $u(e){for(Xe=e.return;Xe;)switch(Xe.tag){case 5:case 13:Nt=!1;return;case 27:case 3:Nt=!0;return;default:Xe=Xe.return}}function Zn(e){if(e!==Xe)return!1;if(!se)return $u(e),se=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||rr(e.type,e.memoizedProps)),a=!a),a&&Ee&&Da(e),$u(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(r(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){Ee=xt(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}Ee=null}}else t===27?(t=Ee,ga(e.type)?(e=dr,dr=null,Ee=e):Ee=t):Ee=Xe?xt(e.stateNode.nextSibling):null;return!0}function Kn(){Ee=Xe=null,se=!1}function Wu(){var e=za;return e!==null&&($e===null?$e=e:$e.push.apply($e,e),za=null),e}function Jn(e){za===null?za=[e]:za.push(e)}var Ki=ce(null),Ra=null,Ht=null;function ea(e,t,a){Z(Ki,t._currentValue),t._currentValue=a}function Lt(e){e._currentValue=Ki.current,re(Ki)}function Ji(e,t,a){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===a)break;e=e.return}}function $i(e,t,a,n){var l=e.child;for(l!==null&&(l.return=e);l!==null;){var i=l.dependencies;if(i!==null){var o=l.child;i=i.firstContext;e:for(;i!==null;){var h=i;i=l;for(var p=0;p<t.length;p++)if(h.context===t[p]){i.lanes|=a,h=i.alternate,h!==null&&(h.lanes|=a),Ji(i.return,a,e),n||(o=null);break e}i=h.next}}else if(l.tag===18){if(o=l.return,o===null)throw Error(r(341));o.lanes|=a,i=o.alternate,i!==null&&(i.lanes|=a),Ji(o,a,e),o=null}else o=l.child;if(o!==null)o.return=l;else for(o=l;o!==null;){if(o===e){o=null;break}if(l=o.sibling,l!==null){l.return=o.return,o=l;break}o=o.return}l=o}}function $n(e,t,a,n){e=null;for(var l=t,i=!1;l!==null;){if(!i){if((l.flags&524288)!==0)i=!0;else if((l.flags&262144)!==0)break}if(l.tag===10){var o=l.alternate;if(o===null)throw Error(r(387));if(o=o.memoizedProps,o!==null){var h=l.type;Ie(l.pendingProps.value,o.value)||(e!==null?e.push(h):e=[h])}}else if(l===Va.current){if(o=l.alternate,o===null)throw Error(r(387));o.memoizedState.memoizedState!==l.memoizedState.memoizedState&&(e!==null?e.push(Nl):e=[Nl])}l=l.return}e!==null&&$i(t,e,a,n),t.flags|=262144}function ns(e){for(e=e.firstContext;e!==null;){if(!Ie(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Oa(e){Ra=e,Ht=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Ge(e){return Fu(Ra,e)}function ls(e,t){return Ra===null&&Oa(e),Fu(e,t)}function Fu(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Ht===null){if(e===null)throw Error(r(308));Ht=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Ht=Ht.next=t;return a}var Mm=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},zm=u.unstable_scheduleCallback,Dm=u.unstable_NormalPriority,Ce={$$typeof:I,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Wi(){return{controller:new Mm,data:new Map,refCount:0}}function Wn(e){e.refCount--,e.refCount===0&&zm(Dm,function(){e.controller.abort()})}var Fn=null,Fi=0,cn=0,rn=null;function Rm(e,t){if(Fn===null){var a=Fn=[];Fi=0,cn=Pc(),rn={status:"pending",value:void 0,then:function(n){a.push(n)}}}return Fi++,t.then(Iu,Iu),t}function Iu(){if(--Fi===0&&Fn!==null){rn!==null&&(rn.status="fulfilled");var e=Fn;Fn=null,cn=0,rn=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Om(e,t){var a=[],n={status:"pending",value:null,reason:null,then:function(l){a.push(l)}};return e.then(function(){n.status="fulfilled",n.value=t;for(var l=0;l<a.length;l++)(0,a[l])(t)},function(l){for(n.status="rejected",n.reason=l,l=0;l<a.length;l++)(0,a[l])(void 0)}),n}var Pu=T.S;T.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&Rm(e,t),Pu!==null&&Pu(e,t)};var Ba=ce(null);function Ii(){var e=Ba.current;return e!==null?e:ye.pooledCache}function ss(e,t){t===null?Z(Ba,Ba.current):Z(Ba,t.pool)}function eo(){var e=Ii();return e===null?null:{parent:Ce._currentValue,pool:e}}var In=Error(r(460)),to=Error(r(474)),is=Error(r(542)),Pi={then:function(){}};function ao(e){return e=e.status,e==="fulfilled"||e==="rejected"}function cs(){}function no(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(cs,cs),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,so(e),e;default:if(typeof t.status=="string")t.then(cs,cs);else{if(e=ye,e!==null&&100<e.shellSuspendCounter)throw Error(r(482));e=t,e.status="pending",e.then(function(n){if(t.status==="pending"){var l=t;l.status="fulfilled",l.value=n}},function(n){if(t.status==="pending"){var l=t;l.status="rejected",l.reason=n}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,so(e),e}throw Pn=t,In}}var Pn=null;function lo(){if(Pn===null)throw Error(r(459));var e=Pn;return Pn=null,e}function so(e){if(e===In||e===is)throw Error(r(483))}var ta=!1;function ec(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function tc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function aa(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function na(e,t,a){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,(oe&2)!==0){var l=n.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),n.pending=t,t=Pl(e),Qu(e,null,a),t}return Il(e,n,t,a),Pl(e)}function el(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var n=t.lanes;n&=e.pendingLanes,a|=n,t.lanes=a,Ir(e,a)}}function ac(e,t){var a=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,a===n)){var l=null,i=null;if(a=a.firstBaseUpdate,a!==null){do{var o={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};i===null?l=i=o:i=i.next=o,a=a.next}while(a!==null);i===null?l=i=t:i=i.next=t}else l=i=t;a={baseState:n.baseState,firstBaseUpdate:l,lastBaseUpdate:i,shared:n.shared,callbacks:n.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var nc=!1;function tl(){if(nc){var e=rn;if(e!==null)throw e}}function al(e,t,a,n){nc=!1;var l=e.updateQueue;ta=!1;var i=l.firstBaseUpdate,o=l.lastBaseUpdate,h=l.shared.pending;if(h!==null){l.shared.pending=null;var p=h,E=p.next;p.next=null,o===null?i=E:o.next=E,o=p;var _=e.alternate;_!==null&&(_=_.updateQueue,h=_.lastBaseUpdate,h!==o&&(h===null?_.firstBaseUpdate=E:h.next=E,_.lastBaseUpdate=p))}if(i!==null){var w=l.baseState;o=0,_=E=p=null,h=i;do{var A=h.lane&-536870913,N=A!==h.lane;if(N?(ae&A)===A:(n&A)===A){A!==0&&A===cn&&(nc=!0),_!==null&&(_=_.next={lane:0,tag:h.tag,payload:h.payload,callback:null,next:null});e:{var Y=e,V=h;A=t;var me=a;switch(V.tag){case 1:if(Y=V.payload,typeof Y=="function"){w=Y.call(me,w,A);break e}w=Y;break e;case 3:Y.flags=Y.flags&-65537|128;case 0:if(Y=V.payload,A=typeof Y=="function"?Y.call(me,w,A):Y,A==null)break e;w=C({},w,A);break e;case 2:ta=!0}}A=h.callback,A!==null&&(e.flags|=64,N&&(e.flags|=8192),N=l.callbacks,N===null?l.callbacks=[A]:N.push(A))}else N={lane:A,tag:h.tag,payload:h.payload,callback:h.callback,next:null},_===null?(E=_=N,p=w):_=_.next=N,o|=A;if(h=h.next,h===null){if(h=l.shared.pending,h===null)break;N=h,h=N.next,N.next=null,l.lastBaseUpdate=N,l.shared.pending=null}}while(!0);_===null&&(p=w),l.baseState=p,l.firstBaseUpdate=E,l.lastBaseUpdate=_,i===null&&(l.shared.lanes=0),fa|=o,e.lanes=o,e.memoizedState=w}}function io(e,t){if(typeof e!="function")throw Error(r(191,e));e.call(t)}function co(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)io(a[e],t)}var un=ce(null),rs=ce(0);function ro(e,t){e=Kt,Z(rs,e),Z(un,t),Kt=e|t.baseLanes}function lc(){Z(rs,Kt),Z(un,un.current)}function sc(){Kt=rs.current,re(un),re(rs)}var la=0,K=null,de=null,_e=null,us=!1,on=!1,Ua=!1,os=0,nl=0,fn=null,Bm=0;function Ne(){throw Error(r(321))}function ic(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!Ie(e[a],t[a]))return!1;return!0}function cc(e,t,a,n,l,i){return la=i,K=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,T.H=e===null||e.memoizedState===null?Zo:Ko,Ua=!1,i=a(n,l),Ua=!1,on&&(i=oo(t,a,n,l)),uo(e),i}function uo(e){T.H=ps;var t=de!==null&&de.next!==null;if(la=0,_e=de=K=null,us=!1,nl=0,fn=null,t)throw Error(r(300));e===null||De||(e=e.dependencies,e!==null&&ns(e)&&(De=!0))}function oo(e,t,a,n){K=e;var l=0;do{if(on&&(fn=null),nl=0,on=!1,25<=l)throw Error(r(301));if(l+=1,_e=de=null,e.updateQueue!=null){var i=e.updateQueue;i.lastEffect=null,i.events=null,i.stores=null,i.memoCache!=null&&(i.memoCache.index=0)}T.H=Gm,i=t(a,n)}while(on);return i}function Um(){var e=T.H,t=e.useState()[0];return t=typeof t.then=="function"?ll(t):t,e=e.useState()[0],(de!==null?de.memoizedState:null)!==e&&(K.flags|=1024),t}function rc(){var e=os!==0;return os=0,e}function uc(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function oc(e){if(us){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}us=!1}la=0,_e=de=K=null,on=!1,nl=os=0,fn=null}function Ke(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return _e===null?K.memoizedState=_e=e:_e=_e.next=e,_e}function je(){if(de===null){var e=K.alternate;e=e!==null?e.memoizedState:null}else e=de.next;var t=_e===null?K.memoizedState:_e.next;if(t!==null)_e=t,de=e;else{if(e===null)throw K.alternate===null?Error(r(467)):Error(r(310));de=e,e={memoizedState:de.memoizedState,baseState:de.baseState,baseQueue:de.baseQueue,queue:de.queue,next:null},_e===null?K.memoizedState=_e=e:_e=_e.next=e}return _e}function fc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ll(e){var t=nl;return nl+=1,fn===null&&(fn=[]),e=no(fn,e,t),t=K,(_e===null?t.memoizedState:_e.next)===null&&(t=t.alternate,T.H=t===null||t.memoizedState===null?Zo:Ko),e}function fs(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ll(e);if(e.$$typeof===I)return Ge(e)}throw Error(r(438,String(e)))}function dc(e){var t=null,a=K.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var n=K.alternate;n!==null&&(n=n.updateQueue,n!==null&&(n=n.memoCache,n!=null&&(t={data:n.data.map(function(l){return l.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=fc(),K.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),n=0;n<e;n++)a[n]=St;return t.index++,a}function Vt(e,t){return typeof t=="function"?t(e):t}function ds(e){var t=je();return hc(t,de,e)}function hc(e,t,a){var n=e.queue;if(n===null)throw Error(r(311));n.lastRenderedReducer=a;var l=e.baseQueue,i=n.pending;if(i!==null){if(l!==null){var o=l.next;l.next=i.next,i.next=o}t.baseQueue=l=i,n.pending=null}if(i=e.baseState,l===null)e.memoizedState=i;else{t=l.next;var h=o=null,p=null,E=t,_=!1;do{var w=E.lane&-536870913;if(w!==E.lane?(ae&w)===w:(la&w)===w){var A=E.revertLane;if(A===0)p!==null&&(p=p.next={lane:0,revertLane:0,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null}),w===cn&&(_=!0);else if((la&A)===A){E=E.next,A===cn&&(_=!0);continue}else w={lane:0,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},p===null?(h=p=w,o=i):p=p.next=w,K.lanes|=A,fa|=A;w=E.action,Ua&&a(i,w),i=E.hasEagerState?E.eagerState:a(i,w)}else A={lane:w,revertLane:E.revertLane,action:E.action,hasEagerState:E.hasEagerState,eagerState:E.eagerState,next:null},p===null?(h=p=A,o=i):p=p.next=A,K.lanes|=w,fa|=w;E=E.next}while(E!==null&&E!==t);if(p===null?o=i:p.next=h,!Ie(i,e.memoizedState)&&(De=!0,_&&(a=rn,a!==null)))throw a;e.memoizedState=i,e.baseState=o,e.baseQueue=p,n.lastRenderedState=i}return l===null&&(n.lanes=0),[e.memoizedState,n.dispatch]}function mc(e){var t=je(),a=t.queue;if(a===null)throw Error(r(311));a.lastRenderedReducer=e;var n=a.dispatch,l=a.pending,i=t.memoizedState;if(l!==null){a.pending=null;var o=l=l.next;do i=e(i,o.action),o=o.next;while(o!==l);Ie(i,t.memoizedState)||(De=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),a.lastRenderedState=i}return[i,n]}function fo(e,t,a){var n=K,l=je(),i=se;if(i){if(a===void 0)throw Error(r(407));a=a()}else a=t();var o=!Ie((de||l).memoizedState,a);o&&(l.memoizedState=a,De=!0),l=l.queue;var h=go.bind(null,n,l,e);if(sl(2048,8,h,[e]),l.getSnapshot!==t||o||_e!==null&&_e.memoizedState.tag&1){if(n.flags|=2048,dn(9,hs(),mo.bind(null,n,l,a,t),null),ye===null)throw Error(r(349));i||(la&124)!==0||ho(n,t,a)}return a}function ho(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=K.updateQueue,t===null?(t=fc(),K.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function mo(e,t,a,n){t.value=a,t.getSnapshot=n,po(t)&&yo(e)}function go(e,t,a){return a(function(){po(t)&&yo(e)})}function po(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!Ie(e,a)}catch{return!0}}function yo(e){var t=an(e,2);t!==null&&lt(t,e,2)}function gc(e){var t=Ke();if(typeof e=="function"){var a=e;if(e=a(),Ua){Ft(!0);try{a()}finally{Ft(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vt,lastRenderedState:e},t}function vo(e,t,a,n){return e.baseState=a,hc(e,de,typeof n=="function"?n:Vt)}function km(e,t,a,n,l){if(gs(e))throw Error(r(485));if(e=t.action,e!==null){var i={payload:l,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(o){i.listeners.push(o)}};T.T!==null?a(!0):i.isTransition=!1,n(i),a=t.pending,a===null?(i.next=t.pending=i,xo(t,i)):(i.next=a.next,t.pending=a.next=i)}}function xo(e,t){var a=t.action,n=t.payload,l=e.state;if(t.isTransition){var i=T.T,o={};T.T=o;try{var h=a(l,n),p=T.S;p!==null&&p(o,h),bo(e,t,h)}catch(E){pc(e,t,E)}finally{T.T=i}}else try{i=a(l,n),bo(e,t,i)}catch(E){pc(e,t,E)}}function bo(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(n){So(e,t,n)},function(n){return pc(e,t,n)}):So(e,t,a)}function So(e,t,a){t.status="fulfilled",t.value=a,Eo(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,xo(e,a)))}function pc(e,t,a){var n=e.pending;if(e.pending=null,n!==null){n=n.next;do t.status="rejected",t.reason=a,Eo(t),t=t.next;while(t!==n)}e.action=null}function Eo(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Ao(e,t){return t}function No(e,t){if(se){var a=ye.formState;if(a!==null){e:{var n=K;if(se){if(Ee){t:{for(var l=Ee,i=Nt;l.nodeType!==8;){if(!i){l=null;break t}if(l=xt(l.nextSibling),l===null){l=null;break t}}i=l.data,l=i==="F!"||i==="F"?l:null}if(l){Ee=xt(l.nextSibling),n=l.data==="F!";break e}}Da(n)}n=!1}n&&(t=a[0])}}return a=Ke(),a.memoizedState=a.baseState=t,n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ao,lastRenderedState:t},a.queue=n,a=Yo.bind(null,K,n),n.dispatch=a,n=gc(!1),i=Sc.bind(null,K,!1,n.queue),n=Ke(),l={state:t,dispatch:null,action:e,pending:null},n.queue=l,a=km.bind(null,K,l,i,a),l.dispatch=a,n.memoizedState=e,[t,a,!1]}function To(e){var t=je();return _o(t,de,e)}function _o(e,t,a){if(t=hc(e,t,Ao)[0],e=ds(Vt)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var n=ll(t)}catch(o){throw o===In?is:o}else n=t;t=je();var l=t.queue,i=l.dispatch;return a!==t.memoizedState&&(K.flags|=2048,dn(9,hs(),qm.bind(null,l,a),null)),[n,i,e]}function qm(e,t){e.action=t}function jo(e){var t=je(),a=de;if(a!==null)return _o(t,a,e);je(),t=t.memoizedState,a=je();var n=a.queue.dispatch;return a.memoizedState=e,[t,n,!1]}function dn(e,t,a,n){return e={tag:e,create:a,deps:n,inst:t,next:null},t=K.updateQueue,t===null&&(t=fc(),K.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(n=a.next,a.next=e,e.next=n,t.lastEffect=e),e}function hs(){return{destroy:void 0,resource:void 0}}function wo(){return je().memoizedState}function ms(e,t,a,n){var l=Ke();n=n===void 0?null:n,K.flags|=e,l.memoizedState=dn(1|t,hs(),a,n)}function sl(e,t,a,n){var l=je();n=n===void 0?null:n;var i=l.memoizedState.inst;de!==null&&n!==null&&ic(n,de.memoizedState.deps)?l.memoizedState=dn(t,i,a,n):(K.flags|=e,l.memoizedState=dn(1|t,i,a,n))}function Co(e,t){ms(8390656,8,e,t)}function Mo(e,t){sl(2048,8,e,t)}function zo(e,t){return sl(4,2,e,t)}function Do(e,t){return sl(4,4,e,t)}function Ro(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Oo(e,t,a){a=a!=null?a.concat([e]):null,sl(4,4,Ro.bind(null,t,e),a)}function yc(){}function Bo(e,t){var a=je();t=t===void 0?null:t;var n=a.memoizedState;return t!==null&&ic(t,n[1])?n[0]:(a.memoizedState=[e,t],e)}function Uo(e,t){var a=je();t=t===void 0?null:t;var n=a.memoizedState;if(t!==null&&ic(t,n[1]))return n[0];if(n=e(),Ua){Ft(!0);try{e()}finally{Ft(!1)}}return a.memoizedState=[n,t],n}function vc(e,t,a){return a===void 0||(la&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=Lf(),K.lanes|=e,fa|=e,a)}function ko(e,t,a,n){return Ie(a,t)?a:un.current!==null?(e=vc(e,a,n),Ie(e,t)||(De=!0),e):(la&42)===0?(De=!0,e.memoizedState=a):(e=Lf(),K.lanes|=e,fa|=e,t)}function qo(e,t,a,n,l){var i=z.p;z.p=i!==0&&8>i?i:8;var o=T.T,h={};T.T=h,Sc(e,!1,t,a);try{var p=l(),E=T.S;if(E!==null&&E(h,p),p!==null&&typeof p=="object"&&typeof p.then=="function"){var _=Om(p,n);il(e,t,_,nt(e))}else il(e,t,n,nt(e))}catch(w){il(e,t,{then:function(){},status:"rejected",reason:w},nt())}finally{z.p=i,T.T=o}}function Hm(){}function xc(e,t,a,n){if(e.tag!==5)throw Error(r(476));var l=Ho(e).queue;qo(e,l,t,H,a===null?Hm:function(){return Lo(e),a(n)})}function Ho(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:H,baseState:H,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vt,lastRenderedState:H},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vt,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Lo(e){var t=Ho(e).next.queue;il(e,t,{},nt())}function bc(){return Ge(Nl)}function Vo(){return je().memoizedState}function Go(){return je().memoizedState}function Lm(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=nt();e=aa(a);var n=na(t,e,a);n!==null&&(lt(n,t,a),el(n,t,a)),t={cache:Wi()},e.payload=t;return}t=t.return}}function Vm(e,t,a){var n=nt();a={lane:n,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},gs(e)?Xo(t,a):(a=Li(e,t,a,n),a!==null&&(lt(a,e,n),Qo(a,t,n)))}function Yo(e,t,a){var n=nt();il(e,t,a,n)}function il(e,t,a,n){var l={lane:n,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(gs(e))Xo(t,l);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var o=t.lastRenderedState,h=i(o,a);if(l.hasEagerState=!0,l.eagerState=h,Ie(h,o))return Il(e,t,l,0),ye===null&&Fl(),!1}catch{}finally{}if(a=Li(e,t,l,n),a!==null)return lt(a,e,n),Qo(a,t,n),!0}return!1}function Sc(e,t,a,n){if(n={lane:2,revertLane:Pc(),action:n,hasEagerState:!1,eagerState:null,next:null},gs(e)){if(t)throw Error(r(479))}else t=Li(e,a,n,2),t!==null&&lt(t,e,2)}function gs(e){var t=e.alternate;return e===K||t!==null&&t===K}function Xo(e,t){on=us=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function Qo(e,t,a){if((a&4194048)!==0){var n=t.lanes;n&=e.pendingLanes,a|=n,t.lanes=a,Ir(e,a)}}var ps={readContext:Ge,use:fs,useCallback:Ne,useContext:Ne,useEffect:Ne,useImperativeHandle:Ne,useLayoutEffect:Ne,useInsertionEffect:Ne,useMemo:Ne,useReducer:Ne,useRef:Ne,useState:Ne,useDebugValue:Ne,useDeferredValue:Ne,useTransition:Ne,useSyncExternalStore:Ne,useId:Ne,useHostTransitionStatus:Ne,useFormState:Ne,useActionState:Ne,useOptimistic:Ne,useMemoCache:Ne,useCacheRefresh:Ne},Zo={readContext:Ge,use:fs,useCallback:function(e,t){return Ke().memoizedState=[e,t===void 0?null:t],e},useContext:Ge,useEffect:Co,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,ms(4194308,4,Ro.bind(null,t,e),a)},useLayoutEffect:function(e,t){return ms(4194308,4,e,t)},useInsertionEffect:function(e,t){ms(4,2,e,t)},useMemo:function(e,t){var a=Ke();t=t===void 0?null:t;var n=e();if(Ua){Ft(!0);try{e()}finally{Ft(!1)}}return a.memoizedState=[n,t],n},useReducer:function(e,t,a){var n=Ke();if(a!==void 0){var l=a(t);if(Ua){Ft(!0);try{a(t)}finally{Ft(!1)}}}else l=t;return n.memoizedState=n.baseState=l,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:l},n.queue=e,e=e.dispatch=Vm.bind(null,K,e),[n.memoizedState,e]},useRef:function(e){var t=Ke();return e={current:e},t.memoizedState=e},useState:function(e){e=gc(e);var t=e.queue,a=Yo.bind(null,K,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:yc,useDeferredValue:function(e,t){var a=Ke();return vc(a,e,t)},useTransition:function(){var e=gc(!1);return e=qo.bind(null,K,e.queue,!0,!1),Ke().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var n=K,l=Ke();if(se){if(a===void 0)throw Error(r(407));a=a()}else{if(a=t(),ye===null)throw Error(r(349));(ae&124)!==0||ho(n,t,a)}l.memoizedState=a;var i={value:a,getSnapshot:t};return l.queue=i,Co(go.bind(null,n,i,e),[e]),n.flags|=2048,dn(9,hs(),mo.bind(null,n,i,a,t),null),a},useId:function(){var e=Ke(),t=ye.identifierPrefix;if(se){var a=qt,n=kt;a=(n&~(1<<32-Fe(n)-1)).toString(32)+a,t="«"+t+"R"+a,a=os++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=Bm++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:bc,useFormState:No,useActionState:No,useOptimistic:function(e){var t=Ke();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=Sc.bind(null,K,!0,a),a.dispatch=t,[e,t]},useMemoCache:dc,useCacheRefresh:function(){return Ke().memoizedState=Lm.bind(null,K)}},Ko={readContext:Ge,use:fs,useCallback:Bo,useContext:Ge,useEffect:Mo,useImperativeHandle:Oo,useInsertionEffect:zo,useLayoutEffect:Do,useMemo:Uo,useReducer:ds,useRef:wo,useState:function(){return ds(Vt)},useDebugValue:yc,useDeferredValue:function(e,t){var a=je();return ko(a,de.memoizedState,e,t)},useTransition:function(){var e=ds(Vt)[0],t=je().memoizedState;return[typeof e=="boolean"?e:ll(e),t]},useSyncExternalStore:fo,useId:Vo,useHostTransitionStatus:bc,useFormState:To,useActionState:To,useOptimistic:function(e,t){var a=je();return vo(a,de,e,t)},useMemoCache:dc,useCacheRefresh:Go},Gm={readContext:Ge,use:fs,useCallback:Bo,useContext:Ge,useEffect:Mo,useImperativeHandle:Oo,useInsertionEffect:zo,useLayoutEffect:Do,useMemo:Uo,useReducer:mc,useRef:wo,useState:function(){return mc(Vt)},useDebugValue:yc,useDeferredValue:function(e,t){var a=je();return de===null?vc(a,e,t):ko(a,de.memoizedState,e,t)},useTransition:function(){var e=mc(Vt)[0],t=je().memoizedState;return[typeof e=="boolean"?e:ll(e),t]},useSyncExternalStore:fo,useId:Vo,useHostTransitionStatus:bc,useFormState:jo,useActionState:jo,useOptimistic:function(e,t){var a=je();return de!==null?vo(a,de,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:dc,useCacheRefresh:Go},hn=null,cl=0;function ys(e){var t=cl;return cl+=1,hn===null&&(hn=[]),no(hn,e,t)}function rl(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function vs(e,t){throw t.$$typeof===R?Error(r(525)):(e=Object.prototype.toString.call(t),Error(r(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Jo(e){var t=e._init;return t(e._payload)}function $o(e){function t(x,v){if(e){var S=x.deletions;S===null?(x.deletions=[v],x.flags|=16):S.push(v)}}function a(x,v){if(!e)return null;for(;v!==null;)t(x,v),v=v.sibling;return null}function n(x){for(var v=new Map;x!==null;)x.key!==null?v.set(x.key,x):v.set(x.index,x),x=x.sibling;return v}function l(x,v){return x=Ut(x,v),x.index=0,x.sibling=null,x}function i(x,v,S){return x.index=S,e?(S=x.alternate,S!==null?(S=S.index,S<v?(x.flags|=67108866,v):S):(x.flags|=67108866,v)):(x.flags|=1048576,v)}function o(x){return e&&x.alternate===null&&(x.flags|=67108866),x}function h(x,v,S,j){return v===null||v.tag!==6?(v=Gi(S,x.mode,j),v.return=x,v):(v=l(v,S),v.return=x,v)}function p(x,v,S,j){var U=S.type;return U===X?_(x,v,S.props.children,j,S.key):v!==null&&(v.elementType===U||typeof U=="object"&&U!==null&&U.$$typeof===ne&&Jo(U)===v.type)?(v=l(v,S.props),rl(v,S),v.return=x,v):(v=es(S.type,S.key,S.props,null,x.mode,j),rl(v,S),v.return=x,v)}function E(x,v,S,j){return v===null||v.tag!==4||v.stateNode.containerInfo!==S.containerInfo||v.stateNode.implementation!==S.implementation?(v=Yi(S,x.mode,j),v.return=x,v):(v=l(v,S.children||[]),v.return=x,v)}function _(x,v,S,j,U){return v===null||v.tag!==7?(v=wa(S,x.mode,j,U),v.return=x,v):(v=l(v,S),v.return=x,v)}function w(x,v,S){if(typeof v=="string"&&v!==""||typeof v=="number"||typeof v=="bigint")return v=Gi(""+v,x.mode,S),v.return=x,v;if(typeof v=="object"&&v!==null){switch(v.$$typeof){case O:return S=es(v.type,v.key,v.props,null,x.mode,S),rl(S,v),S.return=x,S;case Q:return v=Yi(v,x.mode,S),v.return=x,v;case ne:var j=v._init;return v=j(v._payload),w(x,v,S)}if(pt(v)||Le(v))return v=wa(v,x.mode,S,null),v.return=x,v;if(typeof v.then=="function")return w(x,ys(v),S);if(v.$$typeof===I)return w(x,ls(x,v),S);vs(x,v)}return null}function A(x,v,S,j){var U=v!==null?v.key:null;if(typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint")return U!==null?null:h(x,v,""+S,j);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case O:return S.key===U?p(x,v,S,j):null;case Q:return S.key===U?E(x,v,S,j):null;case ne:return U=S._init,S=U(S._payload),A(x,v,S,j)}if(pt(S)||Le(S))return U!==null?null:_(x,v,S,j,null);if(typeof S.then=="function")return A(x,v,ys(S),j);if(S.$$typeof===I)return A(x,v,ls(x,S),j);vs(x,S)}return null}function N(x,v,S,j,U){if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return x=x.get(S)||null,h(v,x,""+j,U);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case O:return x=x.get(j.key===null?S:j.key)||null,p(v,x,j,U);case Q:return x=x.get(j.key===null?S:j.key)||null,E(v,x,j,U);case ne:var $=j._init;return j=$(j._payload),N(x,v,S,j,U)}if(pt(j)||Le(j))return x=x.get(S)||null,_(v,x,j,U,null);if(typeof j.then=="function")return N(x,v,S,ys(j),U);if(j.$$typeof===I)return N(x,v,S,ls(v,j),U);vs(v,j)}return null}function Y(x,v,S,j){for(var U=null,$=null,L=v,G=v=0,Oe=null;L!==null&&G<S.length;G++){L.index>G?(Oe=L,L=null):Oe=L.sibling;var le=A(x,L,S[G],j);if(le===null){L===null&&(L=Oe);break}e&&L&&le.alternate===null&&t(x,L),v=i(le,v,G),$===null?U=le:$.sibling=le,$=le,L=Oe}if(G===S.length)return a(x,L),se&&Ma(x,G),U;if(L===null){for(;G<S.length;G++)L=w(x,S[G],j),L!==null&&(v=i(L,v,G),$===null?U=L:$.sibling=L,$=L);return se&&Ma(x,G),U}for(L=n(L);G<S.length;G++)Oe=N(L,x,G,S[G],j),Oe!==null&&(e&&Oe.alternate!==null&&L.delete(Oe.key===null?G:Oe.key),v=i(Oe,v,G),$===null?U=Oe:$.sibling=Oe,$=Oe);return e&&L.forEach(function(ba){return t(x,ba)}),se&&Ma(x,G),U}function V(x,v,S,j){if(S==null)throw Error(r(151));for(var U=null,$=null,L=v,G=v=0,Oe=null,le=S.next();L!==null&&!le.done;G++,le=S.next()){L.index>G?(Oe=L,L=null):Oe=L.sibling;var ba=A(x,L,le.value,j);if(ba===null){L===null&&(L=Oe);break}e&&L&&ba.alternate===null&&t(x,L),v=i(ba,v,G),$===null?U=ba:$.sibling=ba,$=ba,L=Oe}if(le.done)return a(x,L),se&&Ma(x,G),U;if(L===null){for(;!le.done;G++,le=S.next())le=w(x,le.value,j),le!==null&&(v=i(le,v,G),$===null?U=le:$.sibling=le,$=le);return se&&Ma(x,G),U}for(L=n(L);!le.done;G++,le=S.next())le=N(L,x,G,le.value,j),le!==null&&(e&&le.alternate!==null&&L.delete(le.key===null?G:le.key),v=i(le,v,G),$===null?U=le:$.sibling=le,$=le);return e&&L.forEach(function(Y0){return t(x,Y0)}),se&&Ma(x,G),U}function me(x,v,S,j){if(typeof S=="object"&&S!==null&&S.type===X&&S.key===null&&(S=S.props.children),typeof S=="object"&&S!==null){switch(S.$$typeof){case O:e:{for(var U=S.key;v!==null;){if(v.key===U){if(U=S.type,U===X){if(v.tag===7){a(x,v.sibling),j=l(v,S.props.children),j.return=x,x=j;break e}}else if(v.elementType===U||typeof U=="object"&&U!==null&&U.$$typeof===ne&&Jo(U)===v.type){a(x,v.sibling),j=l(v,S.props),rl(j,S),j.return=x,x=j;break e}a(x,v);break}else t(x,v);v=v.sibling}S.type===X?(j=wa(S.props.children,x.mode,j,S.key),j.return=x,x=j):(j=es(S.type,S.key,S.props,null,x.mode,j),rl(j,S),j.return=x,x=j)}return o(x);case Q:e:{for(U=S.key;v!==null;){if(v.key===U)if(v.tag===4&&v.stateNode.containerInfo===S.containerInfo&&v.stateNode.implementation===S.implementation){a(x,v.sibling),j=l(v,S.children||[]),j.return=x,x=j;break e}else{a(x,v);break}else t(x,v);v=v.sibling}j=Yi(S,x.mode,j),j.return=x,x=j}return o(x);case ne:return U=S._init,S=U(S._payload),me(x,v,S,j)}if(pt(S))return Y(x,v,S,j);if(Le(S)){if(U=Le(S),typeof U!="function")throw Error(r(150));return S=U.call(S),V(x,v,S,j)}if(typeof S.then=="function")return me(x,v,ys(S),j);if(S.$$typeof===I)return me(x,v,ls(x,S),j);vs(x,S)}return typeof S=="string"&&S!==""||typeof S=="number"||typeof S=="bigint"?(S=""+S,v!==null&&v.tag===6?(a(x,v.sibling),j=l(v,S),j.return=x,x=j):(a(x,v),j=Gi(S,x.mode,j),j.return=x,x=j),o(x)):a(x,v)}return function(x,v,S,j){try{cl=0;var U=me(x,v,S,j);return hn=null,U}catch(L){if(L===In||L===is)throw L;var $=Pe(29,L,null,x.mode);return $.lanes=j,$.return=x,$}finally{}}}var mn=$o(!0),Wo=$o(!1),dt=ce(null),Tt=null;function sa(e){var t=e.alternate;Z(Me,Me.current&1),Z(dt,e),Tt===null&&(t===null||un.current!==null||t.memoizedState!==null)&&(Tt=e)}function Fo(e){if(e.tag===22){if(Z(Me,Me.current),Z(dt,e),Tt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Tt=e)}}else ia()}function ia(){Z(Me,Me.current),Z(dt,dt.current)}function Gt(e){re(dt),Tt===e&&(Tt=null),re(Me)}var Me=ce(0);function xs(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||fr(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Ec(e,t,a,n){t=e.memoizedState,a=a(n,t),a=a==null?t:C({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var Ac={enqueueSetState:function(e,t,a){e=e._reactInternals;var n=nt(),l=aa(n);l.payload=t,a!=null&&(l.callback=a),t=na(e,l,n),t!==null&&(lt(t,e,n),el(t,e,n))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var n=nt(),l=aa(n);l.tag=1,l.payload=t,a!=null&&(l.callback=a),t=na(e,l,n),t!==null&&(lt(t,e,n),el(t,e,n))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=nt(),n=aa(a);n.tag=2,t!=null&&(n.callback=t),t=na(e,n,a),t!==null&&(lt(t,e,a),el(t,e,a))}};function Io(e,t,a,n,l,i,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,i,o):t.prototype&&t.prototype.isPureReactComponent?!Xn(a,n)||!Xn(l,i):!0}function Po(e,t,a,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,n),t.state!==e&&Ac.enqueueReplaceState(t,t.state,null)}function ka(e,t){var a=t;if("ref"in t){a={};for(var n in t)n!=="ref"&&(a[n]=t[n])}if(e=e.defaultProps){a===t&&(a=C({},a));for(var l in e)a[l]===void 0&&(a[l]=e[l])}return a}var bs=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function ef(e){bs(e)}function tf(e){console.error(e)}function af(e){bs(e)}function Ss(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function nf(e,t,a){try{var n=e.onCaughtError;n(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(l){setTimeout(function(){throw l})}}function Nc(e,t,a){return a=aa(a),a.tag=3,a.payload={element:null},a.callback=function(){Ss(e,t)},a}function lf(e){return e=aa(e),e.tag=3,e}function sf(e,t,a,n){var l=a.type.getDerivedStateFromError;if(typeof l=="function"){var i=n.value;e.payload=function(){return l(i)},e.callback=function(){nf(t,a,n)}}var o=a.stateNode;o!==null&&typeof o.componentDidCatch=="function"&&(e.callback=function(){nf(t,a,n),typeof l!="function"&&(da===null?da=new Set([this]):da.add(this));var h=n.stack;this.componentDidCatch(n.value,{componentStack:h!==null?h:""})})}function Ym(e,t,a,n,l){if(a.flags|=32768,n!==null&&typeof n=="object"&&typeof n.then=="function"){if(t=a.alternate,t!==null&&$n(t,a,l,!0),a=dt.current,a!==null){switch(a.tag){case 13:return Tt===null?Jc():a.alternate===null&&Ae===0&&(Ae=3),a.flags&=-257,a.flags|=65536,a.lanes=l,n===Pi?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([n]):t.add(n),Wc(e,n,l)),!1;case 22:return a.flags|=65536,n===Pi?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([n])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([n]):a.add(n)),Wc(e,n,l)),!1}throw Error(r(435,a.tag))}return Wc(e,n,l),Jc(),!1}if(se)return t=dt.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=l,n!==Zi&&(e=Error(r(422),{cause:n}),Jn(rt(e,a)))):(n!==Zi&&(t=Error(r(423),{cause:n}),Jn(rt(t,a))),e=e.current.alternate,e.flags|=65536,l&=-l,e.lanes|=l,n=rt(n,a),l=Nc(e.stateNode,n,l),ac(e,l),Ae!==4&&(Ae=2)),!1;var i=Error(r(520),{cause:n});if(i=rt(i,a),gl===null?gl=[i]:gl.push(i),Ae!==4&&(Ae=2),t===null)return!0;n=rt(n,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=l&-l,a.lanes|=e,e=Nc(a.stateNode,n,e),ac(a,e),!1;case 1:if(t=a.type,i=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||i!==null&&typeof i.componentDidCatch=="function"&&(da===null||!da.has(i))))return a.flags|=65536,l&=-l,a.lanes|=l,l=lf(l),sf(l,e,a,n),ac(a,l),!1}a=a.return}while(a!==null);return!1}var cf=Error(r(461)),De=!1;function Ue(e,t,a,n){t.child=e===null?Wo(t,null,a,n):mn(t,e.child,a,n)}function rf(e,t,a,n,l){a=a.render;var i=t.ref;if("ref"in n){var o={};for(var h in n)h!=="ref"&&(o[h]=n[h])}else o=n;return Oa(t),n=cc(e,t,a,o,i,l),h=rc(),e!==null&&!De?(uc(e,t,l),Yt(e,t,l)):(se&&h&&Xi(t),t.flags|=1,Ue(e,t,n,l),t.child)}function uf(e,t,a,n,l){if(e===null){var i=a.type;return typeof i=="function"&&!Vi(i)&&i.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=i,of(e,t,i,n,l)):(e=es(a.type,null,n,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!Dc(e,l)){var o=i.memoizedProps;if(a=a.compare,a=a!==null?a:Xn,a(o,n)&&e.ref===t.ref)return Yt(e,t,l)}return t.flags|=1,e=Ut(i,n),e.ref=t.ref,e.return=t,t.child=e}function of(e,t,a,n,l){if(e!==null){var i=e.memoizedProps;if(Xn(i,n)&&e.ref===t.ref)if(De=!1,t.pendingProps=n=i,Dc(e,l))(e.flags&131072)!==0&&(De=!0);else return t.lanes=e.lanes,Yt(e,t,l)}return Tc(e,t,a,n,l)}function ff(e,t,a){var n=t.pendingProps,l=n.children,i=e!==null?e.memoizedState:null;if(n.mode==="hidden"){if((t.flags&128)!==0){if(n=i!==null?i.baseLanes|a:a,e!==null){for(l=t.child=e.child,i=0;l!==null;)i=i|l.lanes|l.childLanes,l=l.sibling;t.childLanes=i&~n}else t.childLanes=0,t.child=null;return df(e,t,n,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&ss(t,i!==null?i.cachePool:null),i!==null?ro(t,i):lc(),Fo(t);else return t.lanes=t.childLanes=536870912,df(e,t,i!==null?i.baseLanes|a:a,a)}else i!==null?(ss(t,i.cachePool),ro(t,i),ia(),t.memoizedState=null):(e!==null&&ss(t,null),lc(),ia());return Ue(e,t,l,a),t.child}function df(e,t,a,n){var l=Ii();return l=l===null?null:{parent:Ce._currentValue,pool:l},t.memoizedState={baseLanes:a,cachePool:l},e!==null&&ss(t,null),lc(),Fo(t),e!==null&&$n(e,t,n,!0),null}function Es(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(r(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function Tc(e,t,a,n,l){return Oa(t),a=cc(e,t,a,n,void 0,l),n=rc(),e!==null&&!De?(uc(e,t,l),Yt(e,t,l)):(se&&n&&Xi(t),t.flags|=1,Ue(e,t,a,l),t.child)}function hf(e,t,a,n,l,i){return Oa(t),t.updateQueue=null,a=oo(t,n,a,l),uo(e),n=rc(),e!==null&&!De?(uc(e,t,i),Yt(e,t,i)):(se&&n&&Xi(t),t.flags|=1,Ue(e,t,a,i),t.child)}function mf(e,t,a,n,l){if(Oa(t),t.stateNode===null){var i=nn,o=a.contextType;typeof o=="object"&&o!==null&&(i=Ge(o)),i=new a(n,i),t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,i.updater=Ac,t.stateNode=i,i._reactInternals=t,i=t.stateNode,i.props=n,i.state=t.memoizedState,i.refs={},ec(t),o=a.contextType,i.context=typeof o=="object"&&o!==null?Ge(o):nn,i.state=t.memoizedState,o=a.getDerivedStateFromProps,typeof o=="function"&&(Ec(t,a,o,n),i.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(o=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),o!==i.state&&Ac.enqueueReplaceState(i,i.state,null),al(t,n,i,l),tl(),i.state=t.memoizedState),typeof i.componentDidMount=="function"&&(t.flags|=4194308),n=!0}else if(e===null){i=t.stateNode;var h=t.memoizedProps,p=ka(a,h);i.props=p;var E=i.context,_=a.contextType;o=nn,typeof _=="object"&&_!==null&&(o=Ge(_));var w=a.getDerivedStateFromProps;_=typeof w=="function"||typeof i.getSnapshotBeforeUpdate=="function",h=t.pendingProps!==h,_||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(h||E!==o)&&Po(t,i,n,o),ta=!1;var A=t.memoizedState;i.state=A,al(t,n,i,l),tl(),E=t.memoizedState,h||A!==E||ta?(typeof w=="function"&&(Ec(t,a,w,n),E=t.memoizedState),(p=ta||Io(t,a,p,n,A,E,o))?(_||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=E),i.props=n,i.state=E,i.context=o,n=p):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{i=t.stateNode,tc(e,t),o=t.memoizedProps,_=ka(a,o),i.props=_,w=t.pendingProps,A=i.context,E=a.contextType,p=nn,typeof E=="object"&&E!==null&&(p=Ge(E)),h=a.getDerivedStateFromProps,(E=typeof h=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(o!==w||A!==p)&&Po(t,i,n,p),ta=!1,A=t.memoizedState,i.state=A,al(t,n,i,l),tl();var N=t.memoizedState;o!==w||A!==N||ta||e!==null&&e.dependencies!==null&&ns(e.dependencies)?(typeof h=="function"&&(Ec(t,a,h,n),N=t.memoizedState),(_=ta||Io(t,a,_,n,A,N,p)||e!==null&&e.dependencies!==null&&ns(e.dependencies))?(E||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(n,N,p),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(n,N,p)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||o===e.memoizedProps&&A===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&A===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=N),i.props=n,i.state=N,i.context=p,n=_):(typeof i.componentDidUpdate!="function"||o===e.memoizedProps&&A===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&A===e.memoizedState||(t.flags|=1024),n=!1)}return i=n,Es(e,t),n=(t.flags&128)!==0,i||n?(i=t.stateNode,a=n&&typeof a.getDerivedStateFromError!="function"?null:i.render(),t.flags|=1,e!==null&&n?(t.child=mn(t,e.child,null,l),t.child=mn(t,null,a,l)):Ue(e,t,a,l),t.memoizedState=i.state,e=t.child):e=Yt(e,t,l),e}function gf(e,t,a,n){return Kn(),t.flags|=256,Ue(e,t,a,n),t.child}var _c={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function jc(e){return{baseLanes:e,cachePool:eo()}}function wc(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=ht),e}function pf(e,t,a){var n=t.pendingProps,l=!1,i=(t.flags&128)!==0,o;if((o=i)||(o=e!==null&&e.memoizedState===null?!1:(Me.current&2)!==0),o&&(l=!0,t.flags&=-129),o=(t.flags&32)!==0,t.flags&=-33,e===null){if(se){if(l?sa(t):ia(),se){var h=Ee,p;if(p=h){e:{for(p=h,h=Nt;p.nodeType!==8;){if(!h){h=null;break e}if(p=xt(p.nextSibling),p===null){h=null;break e}}h=p}h!==null?(t.memoizedState={dehydrated:h,treeContext:Ca!==null?{id:kt,overflow:qt}:null,retryLane:536870912,hydrationErrors:null},p=Pe(18,null,null,0),p.stateNode=h,p.return=t,t.child=p,Xe=t,Ee=null,p=!0):p=!1}p||Da(t)}if(h=t.memoizedState,h!==null&&(h=h.dehydrated,h!==null))return fr(h)?t.lanes=32:t.lanes=536870912,null;Gt(t)}return h=n.children,n=n.fallback,l?(ia(),l=t.mode,h=As({mode:"hidden",children:h},l),n=wa(n,l,a,null),h.return=t,n.return=t,h.sibling=n,t.child=h,l=t.child,l.memoizedState=jc(a),l.childLanes=wc(e,o,a),t.memoizedState=_c,n):(sa(t),Cc(t,h))}if(p=e.memoizedState,p!==null&&(h=p.dehydrated,h!==null)){if(i)t.flags&256?(sa(t),t.flags&=-257,t=Mc(e,t,a)):t.memoizedState!==null?(ia(),t.child=e.child,t.flags|=128,t=null):(ia(),l=n.fallback,h=t.mode,n=As({mode:"visible",children:n.children},h),l=wa(l,h,a,null),l.flags|=2,n.return=t,l.return=t,n.sibling=l,t.child=n,mn(t,e.child,null,a),n=t.child,n.memoizedState=jc(a),n.childLanes=wc(e,o,a),t.memoizedState=_c,t=l);else if(sa(t),fr(h)){if(o=h.nextSibling&&h.nextSibling.dataset,o)var E=o.dgst;o=E,n=Error(r(419)),n.stack="",n.digest=o,Jn({value:n,source:null,stack:null}),t=Mc(e,t,a)}else if(De||$n(e,t,a,!1),o=(a&e.childLanes)!==0,De||o){if(o=ye,o!==null&&(n=a&-a,n=(n&42)!==0?1:di(n),n=(n&(o.suspendedLanes|a))!==0?0:n,n!==0&&n!==p.retryLane))throw p.retryLane=n,an(e,n),lt(o,e,n),cf;h.data==="$?"||Jc(),t=Mc(e,t,a)}else h.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=p.treeContext,Ee=xt(h.nextSibling),Xe=t,se=!0,za=null,Nt=!1,e!==null&&(ot[ft++]=kt,ot[ft++]=qt,ot[ft++]=Ca,kt=e.id,qt=e.overflow,Ca=t),t=Cc(t,n.children),t.flags|=4096);return t}return l?(ia(),l=n.fallback,h=t.mode,p=e.child,E=p.sibling,n=Ut(p,{mode:"hidden",children:n.children}),n.subtreeFlags=p.subtreeFlags&65011712,E!==null?l=Ut(E,l):(l=wa(l,h,a,null),l.flags|=2),l.return=t,n.return=t,n.sibling=l,t.child=n,n=l,l=t.child,h=e.child.memoizedState,h===null?h=jc(a):(p=h.cachePool,p!==null?(E=Ce._currentValue,p=p.parent!==E?{parent:E,pool:E}:p):p=eo(),h={baseLanes:h.baseLanes|a,cachePool:p}),l.memoizedState=h,l.childLanes=wc(e,o,a),t.memoizedState=_c,n):(sa(t),a=e.child,e=a.sibling,a=Ut(a,{mode:"visible",children:n.children}),a.return=t,a.sibling=null,e!==null&&(o=t.deletions,o===null?(t.deletions=[e],t.flags|=16):o.push(e)),t.child=a,t.memoizedState=null,a)}function Cc(e,t){return t=As({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function As(e,t){return e=Pe(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Mc(e,t,a){return mn(t,e.child,null,a),e=Cc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function yf(e,t,a){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),Ji(e.return,t,a)}function zc(e,t,a,n,l){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:a,tailMode:l}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=n,i.tail=a,i.tailMode=l)}function vf(e,t,a){var n=t.pendingProps,l=n.revealOrder,i=n.tail;if(Ue(e,t,n.children,a),n=Me.current,(n&2)!==0)n=n&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&yf(e,a,t);else if(e.tag===19)yf(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}switch(Z(Me,n),l){case"forwards":for(a=t.child,l=null;a!==null;)e=a.alternate,e!==null&&xs(e)===null&&(l=a),a=a.sibling;a=l,a===null?(l=t.child,t.child=null):(l=a.sibling,a.sibling=null),zc(t,!1,l,a,i);break;case"backwards":for(a=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&xs(e)===null){t.child=l;break}e=l.sibling,l.sibling=a,a=l,l=e}zc(t,!0,a,null,i);break;case"together":zc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Yt(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),fa|=t.lanes,(a&t.childLanes)===0)if(e!==null){if($n(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(r(153));if(t.child!==null){for(e=t.child,a=Ut(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Ut(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function Dc(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&ns(e)))}function Xm(e,t,a){switch(t.tag){case 3:st(t,t.stateNode.containerInfo),ea(t,Ce,e.memoizedState.cache),Kn();break;case 27:case 5:ci(t);break;case 4:st(t,t.stateNode.containerInfo);break;case 10:ea(t,t.type,t.memoizedProps.value);break;case 13:var n=t.memoizedState;if(n!==null)return n.dehydrated!==null?(sa(t),t.flags|=128,null):(a&t.child.childLanes)!==0?pf(e,t,a):(sa(t),e=Yt(e,t,a),e!==null?e.sibling:null);sa(t);break;case 19:var l=(e.flags&128)!==0;if(n=(a&t.childLanes)!==0,n||($n(e,t,a,!1),n=(a&t.childLanes)!==0),l){if(n)return vf(e,t,a);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),Z(Me,Me.current),n)break;return null;case 22:case 23:return t.lanes=0,ff(e,t,a);case 24:ea(t,Ce,e.memoizedState.cache)}return Yt(e,t,a)}function xf(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)De=!0;else{if(!Dc(e,a)&&(t.flags&128)===0)return De=!1,Xm(e,t,a);De=(e.flags&131072)!==0}else De=!1,se&&(t.flags&1048576)!==0&&Ku(t,as,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var n=t.elementType,l=n._init;if(n=l(n._payload),t.type=n,typeof n=="function")Vi(n)?(e=ka(n,e),t.tag=1,t=mf(null,t,n,e,a)):(t.tag=0,t=Tc(null,t,n,e,a));else{if(n!=null){if(l=n.$$typeof,l===be){t.tag=11,t=rf(null,t,n,e,a);break e}else if(l===q){t.tag=14,t=uf(null,t,n,e,a);break e}}throw t=Wt(n)||n,Error(r(306,t,""))}}return t;case 0:return Tc(e,t,t.type,t.pendingProps,a);case 1:return n=t.type,l=ka(n,t.pendingProps),mf(e,t,n,l,a);case 3:e:{if(st(t,t.stateNode.containerInfo),e===null)throw Error(r(387));n=t.pendingProps;var i=t.memoizedState;l=i.element,tc(e,t),al(t,n,null,a);var o=t.memoizedState;if(n=o.cache,ea(t,Ce,n),n!==i.cache&&$i(t,[Ce],a,!0),tl(),n=o.element,i.isDehydrated)if(i={element:n,isDehydrated:!1,cache:o.cache},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){t=gf(e,t,n,a);break e}else if(n!==l){l=rt(Error(r(424)),t),Jn(l),t=gf(e,t,n,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Ee=xt(e.firstChild),Xe=t,se=!0,za=null,Nt=!0,a=Wo(t,null,n,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(Kn(),n===l){t=Yt(e,t,a);break e}Ue(e,t,n,a)}t=t.child}return t;case 26:return Es(e,t),e===null?(a=Ad(t.type,null,t.pendingProps,null))?t.memoizedState=a:se||(a=t.type,e=t.pendingProps,n=ks(Et.current).createElement(a),n[Ve]=t,n[Qe]=e,qe(n,a,e),ze(n),t.stateNode=n):t.memoizedState=Ad(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return ci(t),e===null&&se&&(n=t.stateNode=bd(t.type,t.pendingProps,Et.current),Xe=t,Nt=!0,l=Ee,ga(t.type)?(dr=l,Ee=xt(n.firstChild)):Ee=l),Ue(e,t,t.pendingProps.children,a),Es(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&se&&((l=n=Ee)&&(n=v0(n,t.type,t.pendingProps,Nt),n!==null?(t.stateNode=n,Xe=t,Ee=xt(n.firstChild),Nt=!1,l=!0):l=!1),l||Da(t)),ci(t),l=t.type,i=t.pendingProps,o=e!==null?e.memoizedProps:null,n=i.children,rr(l,i)?n=null:o!==null&&rr(l,o)&&(t.flags|=32),t.memoizedState!==null&&(l=cc(e,t,Um,null,null,a),Nl._currentValue=l),Es(e,t),Ue(e,t,n,a),t.child;case 6:return e===null&&se&&((e=a=Ee)&&(a=x0(a,t.pendingProps,Nt),a!==null?(t.stateNode=a,Xe=t,Ee=null,e=!0):e=!1),e||Da(t)),null;case 13:return pf(e,t,a);case 4:return st(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=mn(t,null,n,a):Ue(e,t,n,a),t.child;case 11:return rf(e,t,t.type,t.pendingProps,a);case 7:return Ue(e,t,t.pendingProps,a),t.child;case 8:return Ue(e,t,t.pendingProps.children,a),t.child;case 12:return Ue(e,t,t.pendingProps.children,a),t.child;case 10:return n=t.pendingProps,ea(t,t.type,n.value),Ue(e,t,n.children,a),t.child;case 9:return l=t.type._context,n=t.pendingProps.children,Oa(t),l=Ge(l),n=n(l),t.flags|=1,Ue(e,t,n,a),t.child;case 14:return uf(e,t,t.type,t.pendingProps,a);case 15:return of(e,t,t.type,t.pendingProps,a);case 19:return vf(e,t,a);case 31:return n=t.pendingProps,a=t.mode,n={mode:n.mode,children:n.children},e===null?(a=As(n,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Ut(e.child,n),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return ff(e,t,a);case 24:return Oa(t),n=Ge(Ce),e===null?(l=Ii(),l===null&&(l=ye,i=Wi(),l.pooledCache=i,i.refCount++,i!==null&&(l.pooledCacheLanes|=a),l=i),t.memoizedState={parent:n,cache:l},ec(t),ea(t,Ce,l)):((e.lanes&a)!==0&&(tc(e,t),al(t,null,null,a),tl()),l=e.memoizedState,i=t.memoizedState,l.parent!==n?(l={parent:n,cache:n},t.memoizedState=l,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=l),ea(t,Ce,n)):(n=i.cache,ea(t,Ce,n),n!==l.cache&&$i(t,[Ce],a,!0))),Ue(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function Xt(e){e.flags|=4}function bf(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!wd(t)){if(t=dt.current,t!==null&&((ae&4194048)===ae?Tt!==null:(ae&62914560)!==ae&&(ae&536870912)===0||t!==Tt))throw Pn=Pi,to;e.flags|=8192}}function Ns(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Wr():536870912,e.lanes|=t,vn|=t)}function ul(e,t){if(!se)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var n=null;a!==null;)a.alternate!==null&&(n=a),a=a.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function Se(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,n=0;if(t)for(var l=e.child;l!==null;)a|=l.lanes|l.childLanes,n|=l.subtreeFlags&65011712,n|=l.flags&65011712,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)a|=l.lanes|l.childLanes,n|=l.subtreeFlags,n|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=n,e.childLanes=a,t}function Qm(e,t,a){var n=t.pendingProps;switch(Qi(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Se(t),null;case 1:return Se(t),null;case 3:return a=t.stateNode,n=null,e!==null&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),Lt(Ce),Rt(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(Zn(t)?Xt(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Wu())),Se(t),null;case 26:return a=t.memoizedState,e===null?(Xt(t),a!==null?(Se(t),bf(t,a)):(Se(t),t.flags&=-16777217)):a?a!==e.memoizedState?(Xt(t),Se(t),bf(t,a)):(Se(t),t.flags&=-16777217):(e.memoizedProps!==n&&Xt(t),Se(t),t.flags&=-16777217),null;case 27:Bl(t),a=Et.current;var l=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==n&&Xt(t);else{if(!n){if(t.stateNode===null)throw Error(r(166));return Se(t),null}e=pe.current,Zn(t)?Ju(t):(e=bd(l,n,a),t.stateNode=e,Xt(t))}return Se(t),null;case 5:if(Bl(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==n&&Xt(t);else{if(!n){if(t.stateNode===null)throw Error(r(166));return Se(t),null}if(e=pe.current,Zn(t))Ju(t);else{switch(l=ks(Et.current),e){case 1:e=l.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=l.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=l.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=l.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=l.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof n.is=="string"?l.createElement("select",{is:n.is}):l.createElement("select"),n.multiple?e.multiple=!0:n.size&&(e.size=n.size);break;default:e=typeof n.is=="string"?l.createElement(a,{is:n.is}):l.createElement(a)}}e[Ve]=t,e[Qe]=n;e:for(l=t.child;l!==null;){if(l.tag===5||l.tag===6)e.appendChild(l.stateNode);else if(l.tag!==4&&l.tag!==27&&l.child!==null){l.child.return=l,l=l.child;continue}if(l===t)break e;for(;l.sibling===null;){if(l.return===null||l.return===t)break e;l=l.return}l.sibling.return=l.return,l=l.sibling}t.stateNode=e;e:switch(qe(e,a,n),a){case"button":case"input":case"select":case"textarea":e=!!n.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Xt(t)}}return Se(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==n&&Xt(t);else{if(typeof n!="string"&&t.stateNode===null)throw Error(r(166));if(e=Et.current,Zn(t)){if(e=t.stateNode,a=t.memoizedProps,n=null,l=Xe,l!==null)switch(l.tag){case 27:case 5:n=l.memoizedProps}e[Ve]=t,e=!!(e.nodeValue===a||n!==null&&n.suppressHydrationWarning===!0||hd(e.nodeValue,a)),e||Da(t)}else e=ks(e).createTextNode(n),e[Ve]=t,t.stateNode=e}return Se(t),null;case 13:if(n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(l=Zn(t),n!==null&&n.dehydrated!==null){if(e===null){if(!l)throw Error(r(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(r(317));l[Ve]=t}else Kn(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Se(t),l=!1}else l=Wu(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=l),l=!0;if(!l)return t.flags&256?(Gt(t),t):(Gt(t),null)}if(Gt(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=n!==null,e=e!==null&&e.memoizedState!==null,a){n=t.child,l=null,n.alternate!==null&&n.alternate.memoizedState!==null&&n.alternate.memoizedState.cachePool!==null&&(l=n.alternate.memoizedState.cachePool.pool);var i=null;n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(i=n.memoizedState.cachePool.pool),i!==l&&(n.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),Ns(t,t.updateQueue),Se(t),null;case 4:return Rt(),e===null&&nr(t.stateNode.containerInfo),Se(t),null;case 10:return Lt(t.type),Se(t),null;case 19:if(re(Me),l=t.memoizedState,l===null)return Se(t),null;if(n=(t.flags&128)!==0,i=l.rendering,i===null)if(n)ul(l,!1);else{if(Ae!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(i=xs(e),i!==null){for(t.flags|=128,ul(l,!1),e=i.updateQueue,t.updateQueue=e,Ns(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)Zu(a,e),a=a.sibling;return Z(Me,Me.current&1|2),t.child}e=e.sibling}l.tail!==null&&At()>js&&(t.flags|=128,n=!0,ul(l,!1),t.lanes=4194304)}else{if(!n)if(e=xs(i),e!==null){if(t.flags|=128,n=!0,e=e.updateQueue,t.updateQueue=e,Ns(t,e),ul(l,!0),l.tail===null&&l.tailMode==="hidden"&&!i.alternate&&!se)return Se(t),null}else 2*At()-l.renderingStartTime>js&&a!==536870912&&(t.flags|=128,n=!0,ul(l,!1),t.lanes=4194304);l.isBackwards?(i.sibling=t.child,t.child=i):(e=l.last,e!==null?e.sibling=i:t.child=i,l.last=i)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=At(),t.sibling=null,e=Me.current,Z(Me,n?e&1|2:e&1),t):(Se(t),null);case 22:case 23:return Gt(t),sc(),n=t.memoizedState!==null,e!==null?e.memoizedState!==null!==n&&(t.flags|=8192):n&&(t.flags|=8192),n?(a&536870912)!==0&&(t.flags&128)===0&&(Se(t),t.subtreeFlags&6&&(t.flags|=8192)):Se(t),a=t.updateQueue,a!==null&&Ns(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),n=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(n=t.memoizedState.cachePool.pool),n!==a&&(t.flags|=2048),e!==null&&re(Ba),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Lt(Ce),Se(t),null;case 25:return null;case 30:return null}throw Error(r(156,t.tag))}function Zm(e,t){switch(Qi(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Lt(Ce),Rt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return Bl(t),null;case 13:if(Gt(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(r(340));Kn()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return re(Me),null;case 4:return Rt(),null;case 10:return Lt(t.type),null;case 22:case 23:return Gt(t),sc(),e!==null&&re(Ba),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Lt(Ce),null;case 25:return null;default:return null}}function Sf(e,t){switch(Qi(t),t.tag){case 3:Lt(Ce),Rt();break;case 26:case 27:case 5:Bl(t);break;case 4:Rt();break;case 13:Gt(t);break;case 19:re(Me);break;case 10:Lt(t.type);break;case 22:case 23:Gt(t),sc(),e!==null&&re(Ba);break;case 24:Lt(Ce)}}function ol(e,t){try{var a=t.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var l=n.next;a=l;do{if((a.tag&e)===e){n=void 0;var i=a.create,o=a.inst;n=i(),o.destroy=n}a=a.next}while(a!==l)}}catch(h){ge(t,t.return,h)}}function ca(e,t,a){try{var n=t.updateQueue,l=n!==null?n.lastEffect:null;if(l!==null){var i=l.next;n=i;do{if((n.tag&e)===e){var o=n.inst,h=o.destroy;if(h!==void 0){o.destroy=void 0,l=t;var p=a,E=h;try{E()}catch(_){ge(l,p,_)}}}n=n.next}while(n!==i)}}catch(_){ge(t,t.return,_)}}function Ef(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{co(t,a)}catch(n){ge(e,e.return,n)}}}function Af(e,t,a){a.props=ka(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(n){ge(e,t,n)}}function fl(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var n=e.stateNode;break;case 30:n=e.stateNode;break;default:n=e.stateNode}typeof a=="function"?e.refCleanup=a(n):a.current=n}}catch(l){ge(e,t,l)}}function _t(e,t){var a=e.ref,n=e.refCleanup;if(a!==null)if(typeof n=="function")try{n()}catch(l){ge(e,t,l)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(l){ge(e,t,l)}else a.current=null}function Nf(e){var t=e.type,a=e.memoizedProps,n=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break e;case"img":a.src?n.src=a.src:a.srcSet&&(n.srcset=a.srcSet)}}catch(l){ge(e,e.return,l)}}function Rc(e,t,a){try{var n=e.stateNode;h0(n,e.type,a,t),n[Qe]=t}catch(l){ge(e,e.return,l)}}function Tf(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&ga(e.type)||e.tag===4}function Oc(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Tf(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&ga(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Bc(e,t,a){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=Us));else if(n!==4&&(n===27&&ga(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(Bc(e,t,a),e=e.sibling;e!==null;)Bc(e,t,a),e=e.sibling}function Ts(e,t,a){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(n!==4&&(n===27&&ga(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(Ts(e,t,a),e=e.sibling;e!==null;)Ts(e,t,a),e=e.sibling}function _f(e){var t=e.stateNode,a=e.memoizedProps;try{for(var n=e.type,l=t.attributes;l.length;)t.removeAttributeNode(l[0]);qe(t,n,a),t[Ve]=e,t[Qe]=a}catch(i){ge(e,e.return,i)}}var Qt=!1,Te=!1,Uc=!1,jf=typeof WeakSet=="function"?WeakSet:Set,Re=null;function Km(e,t){if(e=e.containerInfo,ir=Ys,e=Uu(e),Oi(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var n=a.getSelection&&a.getSelection();if(n&&n.rangeCount!==0){a=n.anchorNode;var l=n.anchorOffset,i=n.focusNode;n=n.focusOffset;try{a.nodeType,i.nodeType}catch{a=null;break e}var o=0,h=-1,p=-1,E=0,_=0,w=e,A=null;t:for(;;){for(var N;w!==a||l!==0&&w.nodeType!==3||(h=o+l),w!==i||n!==0&&w.nodeType!==3||(p=o+n),w.nodeType===3&&(o+=w.nodeValue.length),(N=w.firstChild)!==null;)A=w,w=N;for(;;){if(w===e)break t;if(A===a&&++E===l&&(h=o),A===i&&++_===n&&(p=o),(N=w.nextSibling)!==null)break;w=A,A=w.parentNode}w=N}a=h===-1||p===-1?null:{start:h,end:p}}else a=null}a=a||{start:0,end:0}}else a=null;for(cr={focusedElem:e,selectionRange:a},Ys=!1,Re=t;Re!==null;)if(t=Re,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Re=e;else for(;Re!==null;){switch(t=Re,i=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&i!==null){e=void 0,a=t,l=i.memoizedProps,i=i.memoizedState,n=a.stateNode;try{var Y=ka(a.type,l,a.elementType===a.type);e=n.getSnapshotBeforeUpdate(Y,i),n.__reactInternalSnapshotBeforeUpdate=e}catch(V){ge(a,a.return,V)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)or(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":or(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(r(163))}if(e=t.sibling,e!==null){e.return=t.return,Re=e;break}Re=t.return}}function wf(e,t,a){var n=a.flags;switch(a.tag){case 0:case 11:case 15:ra(e,a),n&4&&ol(5,a);break;case 1:if(ra(e,a),n&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(o){ge(a,a.return,o)}else{var l=ka(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(l,t,e.__reactInternalSnapshotBeforeUpdate)}catch(o){ge(a,a.return,o)}}n&64&&Ef(a),n&512&&fl(a,a.return);break;case 3:if(ra(e,a),n&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{co(e,t)}catch(o){ge(a,a.return,o)}}break;case 27:t===null&&n&4&&_f(a);case 26:case 5:ra(e,a),t===null&&n&4&&Nf(a),n&512&&fl(a,a.return);break;case 12:ra(e,a);break;case 13:ra(e,a),n&4&&zf(e,a),n&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=a0.bind(null,a),b0(e,a))));break;case 22:if(n=a.memoizedState!==null||Qt,!n){t=t!==null&&t.memoizedState!==null||Te,l=Qt;var i=Te;Qt=n,(Te=t)&&!i?ua(e,a,(a.subtreeFlags&8772)!==0):ra(e,a),Qt=l,Te=i}break;case 30:break;default:ra(e,a)}}function Cf(e){var t=e.alternate;t!==null&&(e.alternate=null,Cf(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&gi(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var xe=null,Je=!1;function Zt(e,t,a){for(a=a.child;a!==null;)Mf(e,t,a),a=a.sibling}function Mf(e,t,a){if(We&&typeof We.onCommitFiberUnmount=="function")try{We.onCommitFiberUnmount(Dn,a)}catch{}switch(a.tag){case 26:Te||_t(a,t),Zt(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Te||_t(a,t);var n=xe,l=Je;ga(a.type)&&(xe=a.stateNode,Je=!1),Zt(e,t,a),bl(a.stateNode),xe=n,Je=l;break;case 5:Te||_t(a,t);case 6:if(n=xe,l=Je,xe=null,Zt(e,t,a),xe=n,Je=l,xe!==null)if(Je)try{(xe.nodeType===9?xe.body:xe.nodeName==="HTML"?xe.ownerDocument.body:xe).removeChild(a.stateNode)}catch(i){ge(a,t,i)}else try{xe.removeChild(a.stateNode)}catch(i){ge(a,t,i)}break;case 18:xe!==null&&(Je?(e=xe,vd(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),wl(e)):vd(xe,a.stateNode));break;case 4:n=xe,l=Je,xe=a.stateNode.containerInfo,Je=!0,Zt(e,t,a),xe=n,Je=l;break;case 0:case 11:case 14:case 15:Te||ca(2,a,t),Te||ca(4,a,t),Zt(e,t,a);break;case 1:Te||(_t(a,t),n=a.stateNode,typeof n.componentWillUnmount=="function"&&Af(a,t,n)),Zt(e,t,a);break;case 21:Zt(e,t,a);break;case 22:Te=(n=Te)||a.memoizedState!==null,Zt(e,t,a),Te=n;break;default:Zt(e,t,a)}}function zf(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{wl(e)}catch(a){ge(t,t.return,a)}}function Jm(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new jf),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new jf),t;default:throw Error(r(435,e.tag))}}function kc(e,t){var a=Jm(e);t.forEach(function(n){var l=n0.bind(null,e,n);a.has(n)||(a.add(n),n.then(l,l))})}function et(e,t){var a=t.deletions;if(a!==null)for(var n=0;n<a.length;n++){var l=a[n],i=e,o=t,h=o;e:for(;h!==null;){switch(h.tag){case 27:if(ga(h.type)){xe=h.stateNode,Je=!1;break e}break;case 5:xe=h.stateNode,Je=!1;break e;case 3:case 4:xe=h.stateNode.containerInfo,Je=!0;break e}h=h.return}if(xe===null)throw Error(r(160));Mf(i,o,l),xe=null,Je=!1,i=l.alternate,i!==null&&(i.return=null),l.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Df(t,e),t=t.sibling}var vt=null;function Df(e,t){var a=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:et(t,e),tt(e),n&4&&(ca(3,e,e.return),ol(3,e),ca(5,e,e.return));break;case 1:et(t,e),tt(e),n&512&&(Te||a===null||_t(a,a.return)),n&64&&Qt&&(e=e.updateQueue,e!==null&&(n=e.callbacks,n!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?n:a.concat(n))));break;case 26:var l=vt;if(et(t,e),tt(e),n&512&&(Te||a===null||_t(a,a.return)),n&4){var i=a!==null?a.memoizedState:null;if(n=e.memoizedState,a===null)if(n===null)if(e.stateNode===null){e:{n=e.type,a=e.memoizedProps,l=l.ownerDocument||l;t:switch(n){case"title":i=l.getElementsByTagName("title")[0],(!i||i[Bn]||i[Ve]||i.namespaceURI==="http://www.w3.org/2000/svg"||i.hasAttribute("itemprop"))&&(i=l.createElement(n),l.head.insertBefore(i,l.querySelector("head > title"))),qe(i,n,a),i[Ve]=e,ze(i),n=i;break e;case"link":var o=_d("link","href",l).get(n+(a.href||""));if(o){for(var h=0;h<o.length;h++)if(i=o[h],i.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&i.getAttribute("rel")===(a.rel==null?null:a.rel)&&i.getAttribute("title")===(a.title==null?null:a.title)&&i.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){o.splice(h,1);break t}}i=l.createElement(n),qe(i,n,a),l.head.appendChild(i);break;case"meta":if(o=_d("meta","content",l).get(n+(a.content||""))){for(h=0;h<o.length;h++)if(i=o[h],i.getAttribute("content")===(a.content==null?null:""+a.content)&&i.getAttribute("name")===(a.name==null?null:a.name)&&i.getAttribute("property")===(a.property==null?null:a.property)&&i.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&i.getAttribute("charset")===(a.charSet==null?null:a.charSet)){o.splice(h,1);break t}}i=l.createElement(n),qe(i,n,a),l.head.appendChild(i);break;default:throw Error(r(468,n))}i[Ve]=e,ze(i),n=i}e.stateNode=n}else jd(l,e.type,e.stateNode);else e.stateNode=Td(l,n,e.memoizedProps);else i!==n?(i===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):i.count--,n===null?jd(l,e.type,e.stateNode):Td(l,n,e.memoizedProps)):n===null&&e.stateNode!==null&&Rc(e,e.memoizedProps,a.memoizedProps)}break;case 27:et(t,e),tt(e),n&512&&(Te||a===null||_t(a,a.return)),a!==null&&n&4&&Rc(e,e.memoizedProps,a.memoizedProps);break;case 5:if(et(t,e),tt(e),n&512&&(Te||a===null||_t(a,a.return)),e.flags&32){l=e.stateNode;try{$a(l,"")}catch(N){ge(e,e.return,N)}}n&4&&e.stateNode!=null&&(l=e.memoizedProps,Rc(e,l,a!==null?a.memoizedProps:l)),n&1024&&(Uc=!0);break;case 6:if(et(t,e),tt(e),n&4){if(e.stateNode===null)throw Error(r(162));n=e.memoizedProps,a=e.stateNode;try{a.nodeValue=n}catch(N){ge(e,e.return,N)}}break;case 3:if(Ls=null,l=vt,vt=qs(t.containerInfo),et(t,e),vt=l,tt(e),n&4&&a!==null&&a.memoizedState.isDehydrated)try{wl(t.containerInfo)}catch(N){ge(e,e.return,N)}Uc&&(Uc=!1,Rf(e));break;case 4:n=vt,vt=qs(e.stateNode.containerInfo),et(t,e),tt(e),vt=n;break;case 12:et(t,e),tt(e);break;case 13:et(t,e),tt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Yc=At()),n&4&&(n=e.updateQueue,n!==null&&(e.updateQueue=null,kc(e,n)));break;case 22:l=e.memoizedState!==null;var p=a!==null&&a.memoizedState!==null,E=Qt,_=Te;if(Qt=E||l,Te=_||p,et(t,e),Te=_,Qt=E,tt(e),n&8192)e:for(t=e.stateNode,t._visibility=l?t._visibility&-2:t._visibility|1,l&&(a===null||p||Qt||Te||qa(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){p=a=t;try{if(i=p.stateNode,l)o=i.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none";else{h=p.stateNode;var w=p.memoizedProps.style,A=w!=null&&w.hasOwnProperty("display")?w.display:null;h.style.display=A==null||typeof A=="boolean"?"":(""+A).trim()}}catch(N){ge(p,p.return,N)}}}else if(t.tag===6){if(a===null){p=t;try{p.stateNode.nodeValue=l?"":p.memoizedProps}catch(N){ge(p,p.return,N)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}n&4&&(n=e.updateQueue,n!==null&&(a=n.retryQueue,a!==null&&(n.retryQueue=null,kc(e,a))));break;case 19:et(t,e),tt(e),n&4&&(n=e.updateQueue,n!==null&&(e.updateQueue=null,kc(e,n)));break;case 30:break;case 21:break;default:et(t,e),tt(e)}}function tt(e){var t=e.flags;if(t&2){try{for(var a,n=e.return;n!==null;){if(Tf(n)){a=n;break}n=n.return}if(a==null)throw Error(r(160));switch(a.tag){case 27:var l=a.stateNode,i=Oc(e);Ts(e,i,l);break;case 5:var o=a.stateNode;a.flags&32&&($a(o,""),a.flags&=-33);var h=Oc(e);Ts(e,h,o);break;case 3:case 4:var p=a.stateNode.containerInfo,E=Oc(e);Bc(e,E,p);break;default:throw Error(r(161))}}catch(_){ge(e,e.return,_)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Rf(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;Rf(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function ra(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)wf(e,t.alternate,t),t=t.sibling}function qa(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ca(4,t,t.return),qa(t);break;case 1:_t(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&Af(t,t.return,a),qa(t);break;case 27:bl(t.stateNode);case 26:case 5:_t(t,t.return),qa(t);break;case 22:t.memoizedState===null&&qa(t);break;case 30:qa(t);break;default:qa(t)}e=e.sibling}}function ua(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var n=t.alternate,l=e,i=t,o=i.flags;switch(i.tag){case 0:case 11:case 15:ua(l,i,a),ol(4,i);break;case 1:if(ua(l,i,a),n=i,l=n.stateNode,typeof l.componentDidMount=="function")try{l.componentDidMount()}catch(E){ge(n,n.return,E)}if(n=i,l=n.updateQueue,l!==null){var h=n.stateNode;try{var p=l.shared.hiddenCallbacks;if(p!==null)for(l.shared.hiddenCallbacks=null,l=0;l<p.length;l++)io(p[l],h)}catch(E){ge(n,n.return,E)}}a&&o&64&&Ef(i),fl(i,i.return);break;case 27:_f(i);case 26:case 5:ua(l,i,a),a&&n===null&&o&4&&Nf(i),fl(i,i.return);break;case 12:ua(l,i,a);break;case 13:ua(l,i,a),a&&o&4&&zf(l,i);break;case 22:i.memoizedState===null&&ua(l,i,a),fl(i,i.return);break;case 30:break;default:ua(l,i,a)}t=t.sibling}}function qc(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Wn(a))}function Hc(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Wn(e))}function jt(e,t,a,n){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Of(e,t,a,n),t=t.sibling}function Of(e,t,a,n){var l=t.flags;switch(t.tag){case 0:case 11:case 15:jt(e,t,a,n),l&2048&&ol(9,t);break;case 1:jt(e,t,a,n);break;case 3:jt(e,t,a,n),l&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Wn(e)));break;case 12:if(l&2048){jt(e,t,a,n),e=t.stateNode;try{var i=t.memoizedProps,o=i.id,h=i.onPostCommit;typeof h=="function"&&h(o,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(p){ge(t,t.return,p)}}else jt(e,t,a,n);break;case 13:jt(e,t,a,n);break;case 23:break;case 22:i=t.stateNode,o=t.alternate,t.memoizedState!==null?i._visibility&2?jt(e,t,a,n):dl(e,t):i._visibility&2?jt(e,t,a,n):(i._visibility|=2,gn(e,t,a,n,(t.subtreeFlags&10256)!==0)),l&2048&&qc(o,t);break;case 24:jt(e,t,a,n),l&2048&&Hc(t.alternate,t);break;default:jt(e,t,a,n)}}function gn(e,t,a,n,l){for(l=l&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var i=e,o=t,h=a,p=n,E=o.flags;switch(o.tag){case 0:case 11:case 15:gn(i,o,h,p,l),ol(8,o);break;case 23:break;case 22:var _=o.stateNode;o.memoizedState!==null?_._visibility&2?gn(i,o,h,p,l):dl(i,o):(_._visibility|=2,gn(i,o,h,p,l)),l&&E&2048&&qc(o.alternate,o);break;case 24:gn(i,o,h,p,l),l&&E&2048&&Hc(o.alternate,o);break;default:gn(i,o,h,p,l)}t=t.sibling}}function dl(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,n=t,l=n.flags;switch(n.tag){case 22:dl(a,n),l&2048&&qc(n.alternate,n);break;case 24:dl(a,n),l&2048&&Hc(n.alternate,n);break;default:dl(a,n)}t=t.sibling}}var hl=8192;function pn(e){if(e.subtreeFlags&hl)for(e=e.child;e!==null;)Bf(e),e=e.sibling}function Bf(e){switch(e.tag){case 26:pn(e),e.flags&hl&&e.memoizedState!==null&&R0(vt,e.memoizedState,e.memoizedProps);break;case 5:pn(e);break;case 3:case 4:var t=vt;vt=qs(e.stateNode.containerInfo),pn(e),vt=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=hl,hl=16777216,pn(e),hl=t):pn(e));break;default:pn(e)}}function Uf(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function ml(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var n=t[a];Re=n,qf(n,e)}Uf(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)kf(e),e=e.sibling}function kf(e){switch(e.tag){case 0:case 11:case 15:ml(e),e.flags&2048&&ca(9,e,e.return);break;case 3:ml(e);break;case 12:ml(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,_s(e)):ml(e);break;default:ml(e)}}function _s(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var n=t[a];Re=n,qf(n,e)}Uf(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:ca(8,t,t.return),_s(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,_s(t));break;default:_s(t)}e=e.sibling}}function qf(e,t){for(;Re!==null;){var a=Re;switch(a.tag){case 0:case 11:case 15:ca(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var n=a.memoizedState.cachePool.pool;n!=null&&n.refCount++}break;case 24:Wn(a.memoizedState.cache)}if(n=a.child,n!==null)n.return=a,Re=n;else e:for(a=e;Re!==null;){n=Re;var l=n.sibling,i=n.return;if(Cf(n),n===a){Re=null;break e}if(l!==null){l.return=i,Re=l;break e}Re=i}}}var $m={getCacheForType:function(e){var t=Ge(Ce),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},Wm=typeof WeakMap=="function"?WeakMap:Map,oe=0,ye=null,W=null,ae=0,fe=0,at=null,oa=!1,yn=!1,Lc=!1,Kt=0,Ae=0,fa=0,Ha=0,Vc=0,ht=0,vn=0,gl=null,$e=null,Gc=!1,Yc=0,js=1/0,ws=null,da=null,ke=0,ha=null,xn=null,bn=0,Xc=0,Qc=null,Hf=null,pl=0,Zc=null;function nt(){if((oe&2)!==0&&ae!==0)return ae&-ae;if(T.T!==null){var e=cn;return e!==0?e:Pc()}return Pr()}function Lf(){ht===0&&(ht=(ae&536870912)===0||se?$r():536870912);var e=dt.current;return e!==null&&(e.flags|=32),ht}function lt(e,t,a){(e===ye&&(fe===2||fe===9)||e.cancelPendingCommit!==null)&&(Sn(e,0),ma(e,ae,ht,!1)),On(e,a),((oe&2)===0||e!==ye)&&(e===ye&&((oe&2)===0&&(Ha|=a),Ae===4&&ma(e,ae,ht,!1)),wt(e))}function Vf(e,t,a){if((oe&6)!==0)throw Error(r(327));var n=!a&&(t&124)===0&&(t&e.expiredLanes)===0||Rn(e,t),l=n?Pm(e,t):$c(e,t,!0),i=n;do{if(l===0){yn&&!n&&ma(e,t,0,!1);break}else{if(a=e.current.alternate,i&&!Fm(a)){l=$c(e,t,!1),i=!1;continue}if(l===2){if(i=t,e.errorRecoveryDisabledLanes&i)var o=0;else o=e.pendingLanes&-536870913,o=o!==0?o:o&536870912?536870912:0;if(o!==0){t=o;e:{var h=e;l=gl;var p=h.current.memoizedState.isDehydrated;if(p&&(Sn(h,o).flags|=256),o=$c(h,o,!1),o!==2){if(Lc&&!p){h.errorRecoveryDisabledLanes|=i,Ha|=i,l=4;break e}i=$e,$e=l,i!==null&&($e===null?$e=i:$e.push.apply($e,i))}l=o}if(i=!1,l!==2)continue}}if(l===1){Sn(e,0),ma(e,t,0,!0);break}e:{switch(n=e,i=l,i){case 0:case 1:throw Error(r(345));case 4:if((t&4194048)!==t)break;case 6:ma(n,t,ht,!oa);break e;case 2:$e=null;break;case 3:case 5:break;default:throw Error(r(329))}if((t&62914560)===t&&(l=Yc+300-At(),10<l)){if(ma(n,t,ht,!oa),Hl(n,0,!0)!==0)break e;n.timeoutHandle=pd(Gf.bind(null,n,a,$e,ws,Gc,t,ht,Ha,vn,oa,i,2,-0,0),l);break e}Gf(n,a,$e,ws,Gc,t,ht,Ha,vn,oa,i,0,-0,0)}}break}while(!0);wt(e)}function Gf(e,t,a,n,l,i,o,h,p,E,_,w,A,N){if(e.timeoutHandle=-1,w=t.subtreeFlags,(w&8192||(w&16785408)===16785408)&&(Al={stylesheets:null,count:0,unsuspend:D0},Bf(t),w=O0(),w!==null)){e.cancelPendingCommit=w($f.bind(null,e,t,i,a,n,l,o,h,p,_,1,A,N)),ma(e,i,o,!E);return}$f(e,t,i,a,n,l,o,h,p)}function Fm(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var n=0;n<a.length;n++){var l=a[n],i=l.getSnapshot;l=l.value;try{if(!Ie(i(),l))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function ma(e,t,a,n){t&=~Vc,t&=~Ha,e.suspendedLanes|=t,e.pingedLanes&=~t,n&&(e.warmLanes|=t),n=e.expirationTimes;for(var l=t;0<l;){var i=31-Fe(l),o=1<<i;n[i]=-1,l&=~o}a!==0&&Fr(e,a,t)}function Cs(){return(oe&6)===0?(yl(0),!1):!0}function Kc(){if(W!==null){if(fe===0)var e=W.return;else e=W,Ht=Ra=null,oc(e),hn=null,cl=0,e=W;for(;e!==null;)Sf(e.alternate,e),e=e.return;W=null}}function Sn(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,g0(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Kc(),ye=e,W=a=Ut(e.current,null),ae=t,fe=0,at=null,oa=!1,yn=Rn(e,t),Lc=!1,vn=ht=Vc=Ha=fa=Ae=0,$e=gl=null,Gc=!1,(t&8)!==0&&(t|=t&32);var n=e.entangledLanes;if(n!==0)for(e=e.entanglements,n&=t;0<n;){var l=31-Fe(n),i=1<<l;t|=e[l],n&=~i}return Kt=t,Fl(),a}function Yf(e,t){K=null,T.H=ps,t===In||t===is?(t=lo(),fe=3):t===to?(t=lo(),fe=4):fe=t===cf?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,at=t,W===null&&(Ae=1,Ss(e,rt(t,e.current)))}function Xf(){var e=T.H;return T.H=ps,e===null?ps:e}function Qf(){var e=T.A;return T.A=$m,e}function Jc(){Ae=4,oa||(ae&4194048)!==ae&&dt.current!==null||(yn=!0),(fa&134217727)===0&&(Ha&134217727)===0||ye===null||ma(ye,ae,ht,!1)}function $c(e,t,a){var n=oe;oe|=2;var l=Xf(),i=Qf();(ye!==e||ae!==t)&&(ws=null,Sn(e,t)),t=!1;var o=Ae;e:do try{if(fe!==0&&W!==null){var h=W,p=at;switch(fe){case 8:Kc(),o=6;break e;case 3:case 2:case 9:case 6:dt.current===null&&(t=!0);var E=fe;if(fe=0,at=null,En(e,h,p,E),a&&yn){o=0;break e}break;default:E=fe,fe=0,at=null,En(e,h,p,E)}}Im(),o=Ae;break}catch(_){Yf(e,_)}while(!0);return t&&e.shellSuspendCounter++,Ht=Ra=null,oe=n,T.H=l,T.A=i,W===null&&(ye=null,ae=0,Fl()),o}function Im(){for(;W!==null;)Zf(W)}function Pm(e,t){var a=oe;oe|=2;var n=Xf(),l=Qf();ye!==e||ae!==t?(ws=null,js=At()+500,Sn(e,t)):yn=Rn(e,t);e:do try{if(fe!==0&&W!==null){t=W;var i=at;t:switch(fe){case 1:fe=0,at=null,En(e,t,i,1);break;case 2:case 9:if(ao(i)){fe=0,at=null,Kf(t);break}t=function(){fe!==2&&fe!==9||ye!==e||(fe=7),wt(e)},i.then(t,t);break e;case 3:fe=7;break e;case 4:fe=5;break e;case 7:ao(i)?(fe=0,at=null,Kf(t)):(fe=0,at=null,En(e,t,i,7));break;case 5:var o=null;switch(W.tag){case 26:o=W.memoizedState;case 5:case 27:var h=W;if(!o||wd(o)){fe=0,at=null;var p=h.sibling;if(p!==null)W=p;else{var E=h.return;E!==null?(W=E,Ms(E)):W=null}break t}}fe=0,at=null,En(e,t,i,5);break;case 6:fe=0,at=null,En(e,t,i,6);break;case 8:Kc(),Ae=6;break e;default:throw Error(r(462))}}e0();break}catch(_){Yf(e,_)}while(!0);return Ht=Ra=null,T.H=n,T.A=l,oe=a,W!==null?0:(ye=null,ae=0,Fl(),Ae)}function e0(){for(;W!==null&&!Eh();)Zf(W)}function Zf(e){var t=xf(e.alternate,e,Kt);e.memoizedProps=e.pendingProps,t===null?Ms(e):W=t}function Kf(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=hf(a,t,t.pendingProps,t.type,void 0,ae);break;case 11:t=hf(a,t,t.pendingProps,t.type.render,t.ref,ae);break;case 5:oc(t);default:Sf(a,t),t=W=Zu(t,Kt),t=xf(a,t,Kt)}e.memoizedProps=e.pendingProps,t===null?Ms(e):W=t}function En(e,t,a,n){Ht=Ra=null,oc(t),hn=null,cl=0;var l=t.return;try{if(Ym(e,l,t,a,ae)){Ae=1,Ss(e,rt(a,e.current)),W=null;return}}catch(i){if(l!==null)throw W=l,i;Ae=1,Ss(e,rt(a,e.current)),W=null;return}t.flags&32768?(se||n===1?e=!0:yn||(ae&536870912)!==0?e=!1:(oa=e=!0,(n===2||n===9||n===3||n===6)&&(n=dt.current,n!==null&&n.tag===13&&(n.flags|=16384))),Jf(t,e)):Ms(t)}function Ms(e){var t=e;do{if((t.flags&32768)!==0){Jf(t,oa);return}e=t.return;var a=Qm(t.alternate,t,Kt);if(a!==null){W=a;return}if(t=t.sibling,t!==null){W=t;return}W=t=e}while(t!==null);Ae===0&&(Ae=5)}function Jf(e,t){do{var a=Zm(e.alternate,e);if(a!==null){a.flags&=32767,W=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){W=e;return}W=e=a}while(e!==null);Ae=6,W=null}function $f(e,t,a,n,l,i,o,h,p){e.cancelPendingCommit=null;do zs();while(ke!==0);if((oe&6)!==0)throw Error(r(327));if(t!==null){if(t===e.current)throw Error(r(177));if(i=t.lanes|t.childLanes,i|=Hi,Dh(e,a,i,o,h,p),e===ye&&(W=ye=null,ae=0),xn=t,ha=e,bn=a,Xc=i,Qc=l,Hf=n,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,l0(Ul,function(){return ed(),null})):(e.callbackNode=null,e.callbackPriority=0),n=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||n){n=T.T,T.T=null,l=z.p,z.p=2,o=oe,oe|=4;try{Km(e,t,a)}finally{oe=o,z.p=l,T.T=n}}ke=1,Wf(),Ff(),If()}}function Wf(){if(ke===1){ke=0;var e=ha,t=xn,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=T.T,T.T=null;var n=z.p;z.p=2;var l=oe;oe|=4;try{Df(t,e);var i=cr,o=Uu(e.containerInfo),h=i.focusedElem,p=i.selectionRange;if(o!==h&&h&&h.ownerDocument&&Bu(h.ownerDocument.documentElement,h)){if(p!==null&&Oi(h)){var E=p.start,_=p.end;if(_===void 0&&(_=E),"selectionStart"in h)h.selectionStart=E,h.selectionEnd=Math.min(_,h.value.length);else{var w=h.ownerDocument||document,A=w&&w.defaultView||window;if(A.getSelection){var N=A.getSelection(),Y=h.textContent.length,V=Math.min(p.start,Y),me=p.end===void 0?V:Math.min(p.end,Y);!N.extend&&V>me&&(o=me,me=V,V=o);var x=Ou(h,V),v=Ou(h,me);if(x&&v&&(N.rangeCount!==1||N.anchorNode!==x.node||N.anchorOffset!==x.offset||N.focusNode!==v.node||N.focusOffset!==v.offset)){var S=w.createRange();S.setStart(x.node,x.offset),N.removeAllRanges(),V>me?(N.addRange(S),N.extend(v.node,v.offset)):(S.setEnd(v.node,v.offset),N.addRange(S))}}}}for(w=[],N=h;N=N.parentNode;)N.nodeType===1&&w.push({element:N,left:N.scrollLeft,top:N.scrollTop});for(typeof h.focus=="function"&&h.focus(),h=0;h<w.length;h++){var j=w[h];j.element.scrollLeft=j.left,j.element.scrollTop=j.top}}Ys=!!ir,cr=ir=null}finally{oe=l,z.p=n,T.T=a}}e.current=t,ke=2}}function Ff(){if(ke===2){ke=0;var e=ha,t=xn,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=T.T,T.T=null;var n=z.p;z.p=2;var l=oe;oe|=4;try{wf(e,t.alternate,t)}finally{oe=l,z.p=n,T.T=a}}ke=3}}function If(){if(ke===4||ke===3){ke=0,Ah();var e=ha,t=xn,a=bn,n=Hf;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?ke=5:(ke=0,xn=ha=null,Pf(e,e.pendingLanes));var l=e.pendingLanes;if(l===0&&(da=null),hi(a),t=t.stateNode,We&&typeof We.onCommitFiberRoot=="function")try{We.onCommitFiberRoot(Dn,t,void 0,(t.current.flags&128)===128)}catch{}if(n!==null){t=T.T,l=z.p,z.p=2,T.T=null;try{for(var i=e.onRecoverableError,o=0;o<n.length;o++){var h=n[o];i(h.value,{componentStack:h.stack})}}finally{T.T=t,z.p=l}}(bn&3)!==0&&zs(),wt(e),l=e.pendingLanes,(a&4194090)!==0&&(l&42)!==0?e===Zc?pl++:(pl=0,Zc=e):pl=0,yl(0)}}function Pf(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Wn(t)))}function zs(e){return Wf(),Ff(),If(),ed()}function ed(){if(ke!==5)return!1;var e=ha,t=Xc;Xc=0;var a=hi(bn),n=T.T,l=z.p;try{z.p=32>a?32:a,T.T=null,a=Qc,Qc=null;var i=ha,o=bn;if(ke=0,xn=ha=null,bn=0,(oe&6)!==0)throw Error(r(331));var h=oe;if(oe|=4,kf(i.current),Of(i,i.current,o,a),oe=h,yl(0,!1),We&&typeof We.onPostCommitFiberRoot=="function")try{We.onPostCommitFiberRoot(Dn,i)}catch{}return!0}finally{z.p=l,T.T=n,Pf(e,t)}}function td(e,t,a){t=rt(a,t),t=Nc(e.stateNode,t,2),e=na(e,t,2),e!==null&&(On(e,2),wt(e))}function ge(e,t,a){if(e.tag===3)td(e,e,a);else for(;t!==null;){if(t.tag===3){td(t,e,a);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(da===null||!da.has(n))){e=rt(a,e),a=lf(2),n=na(t,a,2),n!==null&&(sf(a,n,t,e),On(n,2),wt(n));break}}t=t.return}}function Wc(e,t,a){var n=e.pingCache;if(n===null){n=e.pingCache=new Wm;var l=new Set;n.set(t,l)}else l=n.get(t),l===void 0&&(l=new Set,n.set(t,l));l.has(a)||(Lc=!0,l.add(a),e=t0.bind(null,e,t,a),t.then(e,e))}function t0(e,t,a){var n=e.pingCache;n!==null&&n.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,ye===e&&(ae&a)===a&&(Ae===4||Ae===3&&(ae&62914560)===ae&&300>At()-Yc?(oe&2)===0&&Sn(e,0):Vc|=a,vn===ae&&(vn=0)),wt(e)}function ad(e,t){t===0&&(t=Wr()),e=an(e,t),e!==null&&(On(e,t),wt(e))}function a0(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),ad(e,a)}function n0(e,t){var a=0;switch(e.tag){case 13:var n=e.stateNode,l=e.memoizedState;l!==null&&(a=l.retryLane);break;case 19:n=e.stateNode;break;case 22:n=e.stateNode._retryCache;break;default:throw Error(r(314))}n!==null&&n.delete(t),ad(e,a)}function l0(e,t){return ui(e,t)}var Ds=null,An=null,Fc=!1,Rs=!1,Ic=!1,La=0;function wt(e){e!==An&&e.next===null&&(An===null?Ds=An=e:An=An.next=e),Rs=!0,Fc||(Fc=!0,i0())}function yl(e,t){if(!Ic&&Rs){Ic=!0;do for(var a=!1,n=Ds;n!==null;){if(e!==0){var l=n.pendingLanes;if(l===0)var i=0;else{var o=n.suspendedLanes,h=n.pingedLanes;i=(1<<31-Fe(42|e)+1)-1,i&=l&~(o&~h),i=i&201326741?i&201326741|1:i?i|2:0}i!==0&&(a=!0,id(n,i))}else i=ae,i=Hl(n,n===ye?i:0,n.cancelPendingCommit!==null||n.timeoutHandle!==-1),(i&3)===0||Rn(n,i)||(a=!0,id(n,i));n=n.next}while(a);Ic=!1}}function s0(){nd()}function nd(){Rs=Fc=!1;var e=0;La!==0&&(m0()&&(e=La),La=0);for(var t=At(),a=null,n=Ds;n!==null;){var l=n.next,i=ld(n,t);i===0?(n.next=null,a===null?Ds=l:a.next=l,l===null&&(An=a)):(a=n,(e!==0||(i&3)!==0)&&(Rs=!0)),n=l}yl(e)}function ld(e,t){for(var a=e.suspendedLanes,n=e.pingedLanes,l=e.expirationTimes,i=e.pendingLanes&-62914561;0<i;){var o=31-Fe(i),h=1<<o,p=l[o];p===-1?((h&a)===0||(h&n)!==0)&&(l[o]=zh(h,t)):p<=t&&(e.expiredLanes|=h),i&=~h}if(t=ye,a=ae,a=Hl(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),n=e.callbackNode,a===0||e===t&&(fe===2||fe===9)||e.cancelPendingCommit!==null)return n!==null&&n!==null&&oi(n),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||Rn(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(n!==null&&oi(n),hi(a)){case 2:case 8:a=Kr;break;case 32:a=Ul;break;case 268435456:a=Jr;break;default:a=Ul}return n=sd.bind(null,e),a=ui(a,n),e.callbackPriority=t,e.callbackNode=a,t}return n!==null&&n!==null&&oi(n),e.callbackPriority=2,e.callbackNode=null,2}function sd(e,t){if(ke!==0&&ke!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(zs()&&e.callbackNode!==a)return null;var n=ae;return n=Hl(e,e===ye?n:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),n===0?null:(Vf(e,n,t),ld(e,At()),e.callbackNode!=null&&e.callbackNode===a?sd.bind(null,e):null)}function id(e,t){if(zs())return null;Vf(e,t,!0)}function i0(){p0(function(){(oe&6)!==0?ui(Zr,s0):nd()})}function Pc(){return La===0&&(La=$r()),La}function cd(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Xl(""+e)}function rd(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function c0(e,t,a,n,l){if(t==="submit"&&a&&a.stateNode===l){var i=cd((l[Qe]||null).action),o=n.submitter;o&&(t=(t=o[Qe]||null)?cd(t.formAction):o.getAttribute("formAction"),t!==null&&(i=t,o=null));var h=new Jl("action","action",null,n,l);e.push({event:h,listeners:[{instance:null,listener:function(){if(n.defaultPrevented){if(La!==0){var p=o?rd(l,o):new FormData(l);xc(a,{pending:!0,data:p,method:l.method,action:i},null,p)}}else typeof i=="function"&&(h.preventDefault(),p=o?rd(l,o):new FormData(l),xc(a,{pending:!0,data:p,method:l.method,action:i},i,p))},currentTarget:l}]})}}for(var er=0;er<qi.length;er++){var tr=qi[er],r0=tr.toLowerCase(),u0=tr[0].toUpperCase()+tr.slice(1);yt(r0,"on"+u0)}yt(Hu,"onAnimationEnd"),yt(Lu,"onAnimationIteration"),yt(Vu,"onAnimationStart"),yt("dblclick","onDoubleClick"),yt("focusin","onFocus"),yt("focusout","onBlur"),yt(_m,"onTransitionRun"),yt(jm,"onTransitionStart"),yt(wm,"onTransitionCancel"),yt(Gu,"onTransitionEnd"),Za("onMouseEnter",["mouseout","mouseover"]),Za("onMouseLeave",["mouseout","mouseover"]),Za("onPointerEnter",["pointerout","pointerover"]),Za("onPointerLeave",["pointerout","pointerover"]),Na("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Na("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Na("onBeforeInput",["compositionend","keypress","textInput","paste"]),Na("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Na("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Na("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var vl="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),o0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(vl));function ud(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var n=e[a],l=n.event;n=n.listeners;e:{var i=void 0;if(t)for(var o=n.length-1;0<=o;o--){var h=n[o],p=h.instance,E=h.currentTarget;if(h=h.listener,p!==i&&l.isPropagationStopped())break e;i=h,l.currentTarget=E;try{i(l)}catch(_){bs(_)}l.currentTarget=null,i=p}else for(o=0;o<n.length;o++){if(h=n[o],p=h.instance,E=h.currentTarget,h=h.listener,p!==i&&l.isPropagationStopped())break e;i=h,l.currentTarget=E;try{i(l)}catch(_){bs(_)}l.currentTarget=null,i=p}}}}function F(e,t){var a=t[mi];a===void 0&&(a=t[mi]=new Set);var n=e+"__bubble";a.has(n)||(od(t,e,2,!1),a.add(n))}function ar(e,t,a){var n=0;t&&(n|=4),od(a,e,n,t)}var Os="_reactListening"+Math.random().toString(36).slice(2);function nr(e){if(!e[Os]){e[Os]=!0,tu.forEach(function(a){a!=="selectionchange"&&(o0.has(a)||ar(a,!1,e),ar(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Os]||(t[Os]=!0,ar("selectionchange",!1,t))}}function od(e,t,a,n){switch(Od(t)){case 2:var l=k0;break;case 8:l=q0;break;default:l=yr}a=l.bind(null,t,a,e),l=void 0,!Ti||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),n?l!==void 0?e.addEventListener(t,a,{capture:!0,passive:l}):e.addEventListener(t,a,!0):l!==void 0?e.addEventListener(t,a,{passive:l}):e.addEventListener(t,a,!1)}function lr(e,t,a,n,l){var i=n;if((t&1)===0&&(t&2)===0&&n!==null)e:for(;;){if(n===null)return;var o=n.tag;if(o===3||o===4){var h=n.stateNode.containerInfo;if(h===l)break;if(o===4)for(o=n.return;o!==null;){var p=o.tag;if((p===3||p===4)&&o.stateNode.containerInfo===l)return;o=o.return}for(;h!==null;){if(o=Ya(h),o===null)return;if(p=o.tag,p===5||p===6||p===26||p===27){n=i=o;continue e}h=h.parentNode}}n=n.return}gu(function(){var E=i,_=Ai(a),w=[];e:{var A=Yu.get(e);if(A!==void 0){var N=Jl,Y=e;switch(e){case"keypress":if(Zl(a)===0)break e;case"keydown":case"keyup":N=lm;break;case"focusin":Y="focus",N=Ci;break;case"focusout":Y="blur",N=Ci;break;case"beforeblur":case"afterblur":N=Ci;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":N=vu;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":N=Zh;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":N=cm;break;case Hu:case Lu:case Vu:N=$h;break;case Gu:N=um;break;case"scroll":case"scrollend":N=Xh;break;case"wheel":N=fm;break;case"copy":case"cut":case"paste":N=Fh;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":N=bu;break;case"toggle":case"beforetoggle":N=hm}var V=(t&4)!==0,me=!V&&(e==="scroll"||e==="scrollend"),x=V?A!==null?A+"Capture":null:A;V=[];for(var v=E,S;v!==null;){var j=v;if(S=j.stateNode,j=j.tag,j!==5&&j!==26&&j!==27||S===null||x===null||(j=kn(v,x),j!=null&&V.push(xl(v,j,S))),me)break;v=v.return}0<V.length&&(A=new N(A,Y,null,a,_),w.push({event:A,listeners:V}))}}if((t&7)===0){e:{if(A=e==="mouseover"||e==="pointerover",N=e==="mouseout"||e==="pointerout",A&&a!==Ei&&(Y=a.relatedTarget||a.fromElement)&&(Ya(Y)||Y[Ga]))break e;if((N||A)&&(A=_.window===_?_:(A=_.ownerDocument)?A.defaultView||A.parentWindow:window,N?(Y=a.relatedTarget||a.toElement,N=E,Y=Y?Ya(Y):null,Y!==null&&(me=m(Y),V=Y.tag,Y!==me||V!==5&&V!==27&&V!==6)&&(Y=null)):(N=null,Y=E),N!==Y)){if(V=vu,j="onMouseLeave",x="onMouseEnter",v="mouse",(e==="pointerout"||e==="pointerover")&&(V=bu,j="onPointerLeave",x="onPointerEnter",v="pointer"),me=N==null?A:Un(N),S=Y==null?A:Un(Y),A=new V(j,v+"leave",N,a,_),A.target=me,A.relatedTarget=S,j=null,Ya(_)===E&&(V=new V(x,v+"enter",Y,a,_),V.target=S,V.relatedTarget=me,j=V),me=j,N&&Y)t:{for(V=N,x=Y,v=0,S=V;S;S=Nn(S))v++;for(S=0,j=x;j;j=Nn(j))S++;for(;0<v-S;)V=Nn(V),v--;for(;0<S-v;)x=Nn(x),S--;for(;v--;){if(V===x||x!==null&&V===x.alternate)break t;V=Nn(V),x=Nn(x)}V=null}else V=null;N!==null&&fd(w,A,N,V,!1),Y!==null&&me!==null&&fd(w,me,Y,V,!0)}}e:{if(A=E?Un(E):window,N=A.nodeName&&A.nodeName.toLowerCase(),N==="select"||N==="input"&&A.type==="file")var U=wu;else if(_u(A))if(Cu)U=Am;else{U=Sm;var $=bm}else N=A.nodeName,!N||N.toLowerCase()!=="input"||A.type!=="checkbox"&&A.type!=="radio"?E&&Si(E.elementType)&&(U=wu):U=Em;if(U&&(U=U(e,E))){ju(w,U,a,_);break e}$&&$(e,A,E),e==="focusout"&&E&&A.type==="number"&&E.memoizedProps.value!=null&&bi(A,"number",A.value)}switch($=E?Un(E):window,e){case"focusin":(_u($)||$.contentEditable==="true")&&(Pa=$,Bi=E,Qn=null);break;case"focusout":Qn=Bi=Pa=null;break;case"mousedown":Ui=!0;break;case"contextmenu":case"mouseup":case"dragend":Ui=!1,ku(w,a,_);break;case"selectionchange":if(Tm)break;case"keydown":case"keyup":ku(w,a,_)}var L;if(zi)e:{switch(e){case"compositionstart":var G="onCompositionStart";break e;case"compositionend":G="onCompositionEnd";break e;case"compositionupdate":G="onCompositionUpdate";break e}G=void 0}else Ia?Nu(e,a)&&(G="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(G="onCompositionStart");G&&(Su&&a.locale!=="ko"&&(Ia||G!=="onCompositionStart"?G==="onCompositionEnd"&&Ia&&(L=pu()):(Pt=_,_i="value"in Pt?Pt.value:Pt.textContent,Ia=!0)),$=Bs(E,G),0<$.length&&(G=new xu(G,e,null,a,_),w.push({event:G,listeners:$}),L?G.data=L:(L=Tu(a),L!==null&&(G.data=L)))),(L=gm?pm(e,a):ym(e,a))&&(G=Bs(E,"onBeforeInput"),0<G.length&&($=new xu("onBeforeInput","beforeinput",null,a,_),w.push({event:$,listeners:G}),$.data=L)),c0(w,e,E,a,_)}ud(w,t)})}function xl(e,t,a){return{instance:e,listener:t,currentTarget:a}}function Bs(e,t){for(var a=t+"Capture",n=[];e!==null;){var l=e,i=l.stateNode;if(l=l.tag,l!==5&&l!==26&&l!==27||i===null||(l=kn(e,a),l!=null&&n.unshift(xl(e,l,i)),l=kn(e,t),l!=null&&n.push(xl(e,l,i))),e.tag===3)return n;e=e.return}return[]}function Nn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function fd(e,t,a,n,l){for(var i=t._reactName,o=[];a!==null&&a!==n;){var h=a,p=h.alternate,E=h.stateNode;if(h=h.tag,p!==null&&p===n)break;h!==5&&h!==26&&h!==27||E===null||(p=E,l?(E=kn(a,i),E!=null&&o.unshift(xl(a,E,p))):l||(E=kn(a,i),E!=null&&o.push(xl(a,E,p)))),a=a.return}o.length!==0&&e.push({event:t,listeners:o})}var f0=/\r\n?/g,d0=/\u0000|\uFFFD/g;function dd(e){return(typeof e=="string"?e:""+e).replace(f0,`
`).replace(d0,"")}function hd(e,t){return t=dd(t),dd(e)===t}function Us(){}function he(e,t,a,n,l,i){switch(a){case"children":typeof n=="string"?t==="body"||t==="textarea"&&n===""||$a(e,n):(typeof n=="number"||typeof n=="bigint")&&t!=="body"&&$a(e,""+n);break;case"className":Vl(e,"class",n);break;case"tabIndex":Vl(e,"tabindex",n);break;case"dir":case"role":case"viewBox":case"width":case"height":Vl(e,a,n);break;case"style":hu(e,n,i);break;case"data":if(t!=="object"){Vl(e,"data",n);break}case"src":case"href":if(n===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(n==null||typeof n=="function"||typeof n=="symbol"||typeof n=="boolean"){e.removeAttribute(a);break}n=Xl(""+n),e.setAttribute(a,n);break;case"action":case"formAction":if(typeof n=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof i=="function"&&(a==="formAction"?(t!=="input"&&he(e,t,"name",l.name,l,null),he(e,t,"formEncType",l.formEncType,l,null),he(e,t,"formMethod",l.formMethod,l,null),he(e,t,"formTarget",l.formTarget,l,null)):(he(e,t,"encType",l.encType,l,null),he(e,t,"method",l.method,l,null),he(e,t,"target",l.target,l,null)));if(n==null||typeof n=="symbol"||typeof n=="boolean"){e.removeAttribute(a);break}n=Xl(""+n),e.setAttribute(a,n);break;case"onClick":n!=null&&(e.onclick=Us);break;case"onScroll":n!=null&&F("scroll",e);break;case"onScrollEnd":n!=null&&F("scrollend",e);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(r(61));if(a=n.__html,a!=null){if(l.children!=null)throw Error(r(60));e.innerHTML=a}}break;case"multiple":e.multiple=n&&typeof n!="function"&&typeof n!="symbol";break;case"muted":e.muted=n&&typeof n!="function"&&typeof n!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(n==null||typeof n=="function"||typeof n=="boolean"||typeof n=="symbol"){e.removeAttribute("xlink:href");break}a=Xl(""+n),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":n!=null&&typeof n!="function"&&typeof n!="symbol"?e.setAttribute(a,""+n):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":n&&typeof n!="function"&&typeof n!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":n===!0?e.setAttribute(a,""):n!==!1&&n!=null&&typeof n!="function"&&typeof n!="symbol"?e.setAttribute(a,n):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":n!=null&&typeof n!="function"&&typeof n!="symbol"&&!isNaN(n)&&1<=n?e.setAttribute(a,n):e.removeAttribute(a);break;case"rowSpan":case"start":n==null||typeof n=="function"||typeof n=="symbol"||isNaN(n)?e.removeAttribute(a):e.setAttribute(a,n);break;case"popover":F("beforetoggle",e),F("toggle",e),Ll(e,"popover",n);break;case"xlinkActuate":Ot(e,"http://www.w3.org/1999/xlink","xlink:actuate",n);break;case"xlinkArcrole":Ot(e,"http://www.w3.org/1999/xlink","xlink:arcrole",n);break;case"xlinkRole":Ot(e,"http://www.w3.org/1999/xlink","xlink:role",n);break;case"xlinkShow":Ot(e,"http://www.w3.org/1999/xlink","xlink:show",n);break;case"xlinkTitle":Ot(e,"http://www.w3.org/1999/xlink","xlink:title",n);break;case"xlinkType":Ot(e,"http://www.w3.org/1999/xlink","xlink:type",n);break;case"xmlBase":Ot(e,"http://www.w3.org/XML/1998/namespace","xml:base",n);break;case"xmlLang":Ot(e,"http://www.w3.org/XML/1998/namespace","xml:lang",n);break;case"xmlSpace":Ot(e,"http://www.w3.org/XML/1998/namespace","xml:space",n);break;case"is":Ll(e,"is",n);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Gh.get(a)||a,Ll(e,a,n))}}function sr(e,t,a,n,l,i){switch(a){case"style":hu(e,n,i);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(r(61));if(a=n.__html,a!=null){if(l.children!=null)throw Error(r(60));e.innerHTML=a}}break;case"children":typeof n=="string"?$a(e,n):(typeof n=="number"||typeof n=="bigint")&&$a(e,""+n);break;case"onScroll":n!=null&&F("scroll",e);break;case"onScrollEnd":n!=null&&F("scrollend",e);break;case"onClick":n!=null&&(e.onclick=Us);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!au.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(l=a.endsWith("Capture"),t=a.slice(2,l?a.length-7:void 0),i=e[Qe]||null,i=i!=null?i[a]:null,typeof i=="function"&&e.removeEventListener(t,i,l),typeof n=="function")){typeof i!="function"&&i!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,n,l);break e}a in e?e[a]=n:n===!0?e.setAttribute(a,""):Ll(e,a,n)}}}function qe(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":F("error",e),F("load",e);var n=!1,l=!1,i;for(i in a)if(a.hasOwnProperty(i)){var o=a[i];if(o!=null)switch(i){case"src":n=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:he(e,t,i,o,a,null)}}l&&he(e,t,"srcSet",a.srcSet,a,null),n&&he(e,t,"src",a.src,a,null);return;case"input":F("invalid",e);var h=i=o=l=null,p=null,E=null;for(n in a)if(a.hasOwnProperty(n)){var _=a[n];if(_!=null)switch(n){case"name":l=_;break;case"type":o=_;break;case"checked":p=_;break;case"defaultChecked":E=_;break;case"value":i=_;break;case"defaultValue":h=_;break;case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(r(137,t));break;default:he(e,t,n,_,a,null)}}uu(e,i,h,p,E,o,l,!1),Gl(e);return;case"select":F("invalid",e),n=o=i=null;for(l in a)if(a.hasOwnProperty(l)&&(h=a[l],h!=null))switch(l){case"value":i=h;break;case"defaultValue":o=h;break;case"multiple":n=h;default:he(e,t,l,h,a,null)}t=i,a=o,e.multiple=!!n,t!=null?Ja(e,!!n,t,!1):a!=null&&Ja(e,!!n,a,!0);return;case"textarea":F("invalid",e),i=l=n=null;for(o in a)if(a.hasOwnProperty(o)&&(h=a[o],h!=null))switch(o){case"value":n=h;break;case"defaultValue":l=h;break;case"children":i=h;break;case"dangerouslySetInnerHTML":if(h!=null)throw Error(r(91));break;default:he(e,t,o,h,a,null)}fu(e,n,l,i),Gl(e);return;case"option":for(p in a)if(a.hasOwnProperty(p)&&(n=a[p],n!=null))switch(p){case"selected":e.selected=n&&typeof n!="function"&&typeof n!="symbol";break;default:he(e,t,p,n,a,null)}return;case"dialog":F("beforetoggle",e),F("toggle",e),F("cancel",e),F("close",e);break;case"iframe":case"object":F("load",e);break;case"video":case"audio":for(n=0;n<vl.length;n++)F(vl[n],e);break;case"image":F("error",e),F("load",e);break;case"details":F("toggle",e);break;case"embed":case"source":case"link":F("error",e),F("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(E in a)if(a.hasOwnProperty(E)&&(n=a[E],n!=null))switch(E){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:he(e,t,E,n,a,null)}return;default:if(Si(t)){for(_ in a)a.hasOwnProperty(_)&&(n=a[_],n!==void 0&&sr(e,t,_,n,a,void 0));return}}for(h in a)a.hasOwnProperty(h)&&(n=a[h],n!=null&&he(e,t,h,n,a,null))}function h0(e,t,a,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var l=null,i=null,o=null,h=null,p=null,E=null,_=null;for(N in a){var w=a[N];if(a.hasOwnProperty(N)&&w!=null)switch(N){case"checked":break;case"value":break;case"defaultValue":p=w;default:n.hasOwnProperty(N)||he(e,t,N,null,n,w)}}for(var A in n){var N=n[A];if(w=a[A],n.hasOwnProperty(A)&&(N!=null||w!=null))switch(A){case"type":i=N;break;case"name":l=N;break;case"checked":E=N;break;case"defaultChecked":_=N;break;case"value":o=N;break;case"defaultValue":h=N;break;case"children":case"dangerouslySetInnerHTML":if(N!=null)throw Error(r(137,t));break;default:N!==w&&he(e,t,A,N,n,w)}}xi(e,o,h,p,E,_,i,l);return;case"select":N=o=h=A=null;for(i in a)if(p=a[i],a.hasOwnProperty(i)&&p!=null)switch(i){case"value":break;case"multiple":N=p;default:n.hasOwnProperty(i)||he(e,t,i,null,n,p)}for(l in n)if(i=n[l],p=a[l],n.hasOwnProperty(l)&&(i!=null||p!=null))switch(l){case"value":A=i;break;case"defaultValue":h=i;break;case"multiple":o=i;default:i!==p&&he(e,t,l,i,n,p)}t=h,a=o,n=N,A!=null?Ja(e,!!a,A,!1):!!n!=!!a&&(t!=null?Ja(e,!!a,t,!0):Ja(e,!!a,a?[]:"",!1));return;case"textarea":N=A=null;for(h in a)if(l=a[h],a.hasOwnProperty(h)&&l!=null&&!n.hasOwnProperty(h))switch(h){case"value":break;case"children":break;default:he(e,t,h,null,n,l)}for(o in n)if(l=n[o],i=a[o],n.hasOwnProperty(o)&&(l!=null||i!=null))switch(o){case"value":A=l;break;case"defaultValue":N=l;break;case"children":break;case"dangerouslySetInnerHTML":if(l!=null)throw Error(r(91));break;default:l!==i&&he(e,t,o,l,n,i)}ou(e,A,N);return;case"option":for(var Y in a)if(A=a[Y],a.hasOwnProperty(Y)&&A!=null&&!n.hasOwnProperty(Y))switch(Y){case"selected":e.selected=!1;break;default:he(e,t,Y,null,n,A)}for(p in n)if(A=n[p],N=a[p],n.hasOwnProperty(p)&&A!==N&&(A!=null||N!=null))switch(p){case"selected":e.selected=A&&typeof A!="function"&&typeof A!="symbol";break;default:he(e,t,p,A,n,N)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var V in a)A=a[V],a.hasOwnProperty(V)&&A!=null&&!n.hasOwnProperty(V)&&he(e,t,V,null,n,A);for(E in n)if(A=n[E],N=a[E],n.hasOwnProperty(E)&&A!==N&&(A!=null||N!=null))switch(E){case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(r(137,t));break;default:he(e,t,E,A,n,N)}return;default:if(Si(t)){for(var me in a)A=a[me],a.hasOwnProperty(me)&&A!==void 0&&!n.hasOwnProperty(me)&&sr(e,t,me,void 0,n,A);for(_ in n)A=n[_],N=a[_],!n.hasOwnProperty(_)||A===N||A===void 0&&N===void 0||sr(e,t,_,A,n,N);return}}for(var x in a)A=a[x],a.hasOwnProperty(x)&&A!=null&&!n.hasOwnProperty(x)&&he(e,t,x,null,n,A);for(w in n)A=n[w],N=a[w],!n.hasOwnProperty(w)||A===N||A==null&&N==null||he(e,t,w,A,n,N)}var ir=null,cr=null;function ks(e){return e.nodeType===9?e:e.ownerDocument}function md(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function gd(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function rr(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var ur=null;function m0(){var e=window.event;return e&&e.type==="popstate"?e===ur?!1:(ur=e,!0):(ur=null,!1)}var pd=typeof setTimeout=="function"?setTimeout:void 0,g0=typeof clearTimeout=="function"?clearTimeout:void 0,yd=typeof Promise=="function"?Promise:void 0,p0=typeof queueMicrotask=="function"?queueMicrotask:typeof yd<"u"?function(e){return yd.resolve(null).then(e).catch(y0)}:pd;function y0(e){setTimeout(function(){throw e})}function ga(e){return e==="head"}function vd(e,t){var a=t,n=0,l=0;do{var i=a.nextSibling;if(e.removeChild(a),i&&i.nodeType===8)if(a=i.data,a==="/$"){if(0<n&&8>n){a=n;var o=e.ownerDocument;if(a&1&&bl(o.documentElement),a&2&&bl(o.body),a&4)for(a=o.head,bl(a),o=a.firstChild;o;){var h=o.nextSibling,p=o.nodeName;o[Bn]||p==="SCRIPT"||p==="STYLE"||p==="LINK"&&o.rel.toLowerCase()==="stylesheet"||a.removeChild(o),o=h}}if(l===0){e.removeChild(i),wl(t);return}l--}else a==="$"||a==="$?"||a==="$!"?l++:n=a.charCodeAt(0)-48;else n=0;a=i}while(a);wl(t)}function or(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":or(a),gi(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function v0(e,t,a,n){for(;e.nodeType===1;){var l=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!n&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(n){if(!e[Bn])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(i=e.getAttribute("rel"),i==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(i!==l.rel||e.getAttribute("href")!==(l.href==null||l.href===""?null:l.href)||e.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin)||e.getAttribute("title")!==(l.title==null?null:l.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(i=e.getAttribute("src"),(i!==(l.src==null?null:l.src)||e.getAttribute("type")!==(l.type==null?null:l.type)||e.getAttribute("crossorigin")!==(l.crossOrigin==null?null:l.crossOrigin))&&i&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var i=l.name==null?null:""+l.name;if(l.type==="hidden"&&e.getAttribute("name")===i)return e}else return e;if(e=xt(e.nextSibling),e===null)break}return null}function x0(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=xt(e.nextSibling),e===null))return null;return e}function fr(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function b0(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var n=function(){t(),a.removeEventListener("DOMContentLoaded",n)};a.addEventListener("DOMContentLoaded",n),e._reactRetry=n}}function xt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var dr=null;function xd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function bd(e,t,a){switch(t=ks(a),e){case"html":if(e=t.documentElement,!e)throw Error(r(452));return e;case"head":if(e=t.head,!e)throw Error(r(453));return e;case"body":if(e=t.body,!e)throw Error(r(454));return e;default:throw Error(r(451))}}function bl(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);gi(e)}var mt=new Map,Sd=new Set;function qs(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Jt=z.d;z.d={f:S0,r:E0,D:A0,C:N0,L:T0,m:_0,X:w0,S:j0,M:C0};function S0(){var e=Jt.f(),t=Cs();return e||t}function E0(e){var t=Xa(e);t!==null&&t.tag===5&&t.type==="form"?Lo(t):Jt.r(e)}var Tn=typeof document>"u"?null:document;function Ed(e,t,a){var n=Tn;if(n&&typeof t=="string"&&t){var l=ct(t);l='link[rel="'+e+'"][href="'+l+'"]',typeof a=="string"&&(l+='[crossorigin="'+a+'"]'),Sd.has(l)||(Sd.add(l),e={rel:e,crossOrigin:a,href:t},n.querySelector(l)===null&&(t=n.createElement("link"),qe(t,"link",e),ze(t),n.head.appendChild(t)))}}function A0(e){Jt.D(e),Ed("dns-prefetch",e,null)}function N0(e,t){Jt.C(e,t),Ed("preconnect",e,t)}function T0(e,t,a){Jt.L(e,t,a);var n=Tn;if(n&&e&&t){var l='link[rel="preload"][as="'+ct(t)+'"]';t==="image"&&a&&a.imageSrcSet?(l+='[imagesrcset="'+ct(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(l+='[imagesizes="'+ct(a.imageSizes)+'"]')):l+='[href="'+ct(e)+'"]';var i=l;switch(t){case"style":i=_n(e);break;case"script":i=jn(e)}mt.has(i)||(e=C({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),mt.set(i,e),n.querySelector(l)!==null||t==="style"&&n.querySelector(Sl(i))||t==="script"&&n.querySelector(El(i))||(t=n.createElement("link"),qe(t,"link",e),ze(t),n.head.appendChild(t)))}}function _0(e,t){Jt.m(e,t);var a=Tn;if(a&&e){var n=t&&typeof t.as=="string"?t.as:"script",l='link[rel="modulepreload"][as="'+ct(n)+'"][href="'+ct(e)+'"]',i=l;switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":i=jn(e)}if(!mt.has(i)&&(e=C({rel:"modulepreload",href:e},t),mt.set(i,e),a.querySelector(l)===null)){switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(El(i)))return}n=a.createElement("link"),qe(n,"link",e),ze(n),a.head.appendChild(n)}}}function j0(e,t,a){Jt.S(e,t,a);var n=Tn;if(n&&e){var l=Qa(n).hoistableStyles,i=_n(e);t=t||"default";var o=l.get(i);if(!o){var h={loading:0,preload:null};if(o=n.querySelector(Sl(i)))h.loading=5;else{e=C({rel:"stylesheet",href:e,"data-precedence":t},a),(a=mt.get(i))&&hr(e,a);var p=o=n.createElement("link");ze(p),qe(p,"link",e),p._p=new Promise(function(E,_){p.onload=E,p.onerror=_}),p.addEventListener("load",function(){h.loading|=1}),p.addEventListener("error",function(){h.loading|=2}),h.loading|=4,Hs(o,t,n)}o={type:"stylesheet",instance:o,count:1,state:h},l.set(i,o)}}}function w0(e,t){Jt.X(e,t);var a=Tn;if(a&&e){var n=Qa(a).hoistableScripts,l=jn(e),i=n.get(l);i||(i=a.querySelector(El(l)),i||(e=C({src:e,async:!0},t),(t=mt.get(l))&&mr(e,t),i=a.createElement("script"),ze(i),qe(i,"link",e),a.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},n.set(l,i))}}function C0(e,t){Jt.M(e,t);var a=Tn;if(a&&e){var n=Qa(a).hoistableScripts,l=jn(e),i=n.get(l);i||(i=a.querySelector(El(l)),i||(e=C({src:e,async:!0,type:"module"},t),(t=mt.get(l))&&mr(e,t),i=a.createElement("script"),ze(i),qe(i,"link",e),a.head.appendChild(i)),i={type:"script",instance:i,count:1,state:null},n.set(l,i))}}function Ad(e,t,a,n){var l=(l=Et.current)?qs(l):null;if(!l)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=_n(a.href),a=Qa(l).hoistableStyles,n=a.get(t),n||(n={type:"style",instance:null,count:0,state:null},a.set(t,n)),n):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=_n(a.href);var i=Qa(l).hoistableStyles,o=i.get(e);if(o||(l=l.ownerDocument||l,o={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},i.set(e,o),(i=l.querySelector(Sl(e)))&&!i._p&&(o.instance=i,o.state.loading=5),mt.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},mt.set(e,a),i||M0(l,e,a,o.state))),t&&n===null)throw Error(r(528,""));return o}if(t&&n!==null)throw Error(r(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=jn(a),a=Qa(l).hoistableScripts,n=a.get(t),n||(n={type:"script",instance:null,count:0,state:null},a.set(t,n)),n):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function _n(e){return'href="'+ct(e)+'"'}function Sl(e){return'link[rel="stylesheet"]['+e+"]"}function Nd(e){return C({},e,{"data-precedence":e.precedence,precedence:null})}function M0(e,t,a,n){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?n.loading=1:(t=e.createElement("link"),n.preload=t,t.addEventListener("load",function(){return n.loading|=1}),t.addEventListener("error",function(){return n.loading|=2}),qe(t,"link",a),ze(t),e.head.appendChild(t))}function jn(e){return'[src="'+ct(e)+'"]'}function El(e){return"script[async]"+e}function Td(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var n=e.querySelector('style[data-href~="'+ct(a.href)+'"]');if(n)return t.instance=n,ze(n),n;var l=C({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return n=(e.ownerDocument||e).createElement("style"),ze(n),qe(n,"style",l),Hs(n,a.precedence,e),t.instance=n;case"stylesheet":l=_n(a.href);var i=e.querySelector(Sl(l));if(i)return t.state.loading|=4,t.instance=i,ze(i),i;n=Nd(a),(l=mt.get(l))&&hr(n,l),i=(e.ownerDocument||e).createElement("link"),ze(i);var o=i;return o._p=new Promise(function(h,p){o.onload=h,o.onerror=p}),qe(i,"link",n),t.state.loading|=4,Hs(i,a.precedence,e),t.instance=i;case"script":return i=jn(a.src),(l=e.querySelector(El(i)))?(t.instance=l,ze(l),l):(n=a,(l=mt.get(i))&&(n=C({},a),mr(n,l)),e=e.ownerDocument||e,l=e.createElement("script"),ze(l),qe(l,"link",n),e.head.appendChild(l),t.instance=l);case"void":return null;default:throw Error(r(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(n=t.instance,t.state.loading|=4,Hs(n,a.precedence,e));return t.instance}function Hs(e,t,a){for(var n=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),l=n.length?n[n.length-1]:null,i=l,o=0;o<n.length;o++){var h=n[o];if(h.dataset.precedence===t)i=h;else if(i!==l)break}i?i.parentNode.insertBefore(e,i.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function hr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function mr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Ls=null;function _d(e,t,a){if(Ls===null){var n=new Map,l=Ls=new Map;l.set(a,n)}else l=Ls,n=l.get(a),n||(n=new Map,l.set(a,n));if(n.has(e))return n;for(n.set(e,null),a=a.getElementsByTagName(e),l=0;l<a.length;l++){var i=a[l];if(!(i[Bn]||i[Ve]||e==="link"&&i.getAttribute("rel")==="stylesheet")&&i.namespaceURI!=="http://www.w3.org/2000/svg"){var o=i.getAttribute(t)||"";o=e+o;var h=n.get(o);h?h.push(i):n.set(o,[i])}}return n}function jd(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function z0(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function wd(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Al=null;function D0(){}function R0(e,t,a){if(Al===null)throw Error(r(475));var n=Al;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var l=_n(a.href),i=e.querySelector(Sl(l));if(i){e=i._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(n.count++,n=Vs.bind(n),e.then(n,n)),t.state.loading|=4,t.instance=i,ze(i);return}i=e.ownerDocument||e,a=Nd(a),(l=mt.get(l))&&hr(a,l),i=i.createElement("link"),ze(i);var o=i;o._p=new Promise(function(h,p){o.onload=h,o.onerror=p}),qe(i,"link",a),t.instance=i}n.stylesheets===null&&(n.stylesheets=new Map),n.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(n.count++,t=Vs.bind(n),e.addEventListener("load",t),e.addEventListener("error",t))}}function O0(){if(Al===null)throw Error(r(475));var e=Al;return e.stylesheets&&e.count===0&&gr(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&gr(e,e.stylesheets),e.unsuspend){var n=e.unsuspend;e.unsuspend=null,n()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Vs(){if(this.count--,this.count===0){if(this.stylesheets)gr(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Gs=null;function gr(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Gs=new Map,t.forEach(B0,e),Gs=null,Vs.call(e))}function B0(e,t){if(!(t.state.loading&4)){var a=Gs.get(e);if(a)var n=a.get(null);else{a=new Map,Gs.set(e,a);for(var l=e.querySelectorAll("link[data-precedence],style[data-precedence]"),i=0;i<l.length;i++){var o=l[i];(o.nodeName==="LINK"||o.getAttribute("media")!=="not all")&&(a.set(o.dataset.precedence,o),n=o)}n&&a.set(null,n)}l=t.instance,o=l.getAttribute("data-precedence"),i=a.get(o)||n,i===n&&a.set(null,l),a.set(o,l),this.count++,n=Vs.bind(this),l.addEventListener("load",n),l.addEventListener("error",n),i?i.parentNode.insertBefore(l,i.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(l,e.firstChild)),t.state.loading|=4}}var Nl={$$typeof:I,Provider:null,Consumer:null,_currentValue:H,_currentValue2:H,_threadCount:0};function U0(e,t,a,n,l,i,o,h){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=fi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=fi(0),this.hiddenUpdates=fi(null),this.identifierPrefix=n,this.onUncaughtError=l,this.onCaughtError=i,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=h,this.incompleteTransitions=new Map}function Cd(e,t,a,n,l,i,o,h,p,E,_,w){return e=new U0(e,t,a,o,h,p,E,w),t=1,i===!0&&(t|=24),i=Pe(3,null,null,t),e.current=i,i.stateNode=e,t=Wi(),t.refCount++,e.pooledCache=t,t.refCount++,i.memoizedState={element:n,isDehydrated:a,cache:t},ec(i),e}function Md(e){return e?(e=nn,e):nn}function zd(e,t,a,n,l,i){l=Md(l),n.context===null?n.context=l:n.pendingContext=l,n=aa(t),n.payload={element:a},i=i===void 0?null:i,i!==null&&(n.callback=i),a=na(e,n,t),a!==null&&(lt(a,e,t),el(a,e,t))}function Dd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function pr(e,t){Dd(e,t),(e=e.alternate)&&Dd(e,t)}function Rd(e){if(e.tag===13){var t=an(e,67108864);t!==null&&lt(t,e,67108864),pr(e,67108864)}}var Ys=!0;function k0(e,t,a,n){var l=T.T;T.T=null;var i=z.p;try{z.p=2,yr(e,t,a,n)}finally{z.p=i,T.T=l}}function q0(e,t,a,n){var l=T.T;T.T=null;var i=z.p;try{z.p=8,yr(e,t,a,n)}finally{z.p=i,T.T=l}}function yr(e,t,a,n){if(Ys){var l=vr(n);if(l===null)lr(e,t,n,Xs,a),Bd(e,n);else if(L0(l,e,t,a,n))n.stopPropagation();else if(Bd(e,n),t&4&&-1<H0.indexOf(e)){for(;l!==null;){var i=Xa(l);if(i!==null)switch(i.tag){case 3:if(i=i.stateNode,i.current.memoizedState.isDehydrated){var o=Aa(i.pendingLanes);if(o!==0){var h=i;for(h.pendingLanes|=2,h.entangledLanes|=2;o;){var p=1<<31-Fe(o);h.entanglements[1]|=p,o&=~p}wt(i),(oe&6)===0&&(js=At()+500,yl(0))}}break;case 13:h=an(i,2),h!==null&&lt(h,i,2),Cs(),pr(i,2)}if(i=vr(n),i===null&&lr(e,t,n,Xs,a),i===l)break;l=i}l!==null&&n.stopPropagation()}else lr(e,t,n,null,a)}}function vr(e){return e=Ai(e),xr(e)}var Xs=null;function xr(e){if(Xs=null,e=Ya(e),e!==null){var t=m(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=g(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Xs=e,null}function Od(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Nh()){case Zr:return 2;case Kr:return 8;case Ul:case Th:return 32;case Jr:return 268435456;default:return 32}default:return 32}}var br=!1,pa=null,ya=null,va=null,Tl=new Map,_l=new Map,xa=[],H0="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Bd(e,t){switch(e){case"focusin":case"focusout":pa=null;break;case"dragenter":case"dragleave":ya=null;break;case"mouseover":case"mouseout":va=null;break;case"pointerover":case"pointerout":Tl.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":_l.delete(t.pointerId)}}function jl(e,t,a,n,l,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:a,eventSystemFlags:n,nativeEvent:i,targetContainers:[l]},t!==null&&(t=Xa(t),t!==null&&Rd(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function L0(e,t,a,n,l){switch(t){case"focusin":return pa=jl(pa,e,t,a,n,l),!0;case"dragenter":return ya=jl(ya,e,t,a,n,l),!0;case"mouseover":return va=jl(va,e,t,a,n,l),!0;case"pointerover":var i=l.pointerId;return Tl.set(i,jl(Tl.get(i)||null,e,t,a,n,l)),!0;case"gotpointercapture":return i=l.pointerId,_l.set(i,jl(_l.get(i)||null,e,t,a,n,l)),!0}return!1}function Ud(e){var t=Ya(e.target);if(t!==null){var a=m(t);if(a!==null){if(t=a.tag,t===13){if(t=g(a),t!==null){e.blockedOn=t,Rh(e.priority,function(){if(a.tag===13){var n=nt();n=di(n);var l=an(a,n);l!==null&&lt(l,a,n),pr(a,n)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Qs(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=vr(e.nativeEvent);if(a===null){a=e.nativeEvent;var n=new a.constructor(a.type,a);Ei=n,a.target.dispatchEvent(n),Ei=null}else return t=Xa(a),t!==null&&Rd(t),e.blockedOn=a,!1;t.shift()}return!0}function kd(e,t,a){Qs(e)&&a.delete(t)}function V0(){br=!1,pa!==null&&Qs(pa)&&(pa=null),ya!==null&&Qs(ya)&&(ya=null),va!==null&&Qs(va)&&(va=null),Tl.forEach(kd),_l.forEach(kd)}function Zs(e,t){e.blockedOn===t&&(e.blockedOn=null,br||(br=!0,u.unstable_scheduleCallback(u.unstable_NormalPriority,V0)))}var Ks=null;function qd(e){Ks!==e&&(Ks=e,u.unstable_scheduleCallback(u.unstable_NormalPriority,function(){Ks===e&&(Ks=null);for(var t=0;t<e.length;t+=3){var a=e[t],n=e[t+1],l=e[t+2];if(typeof n!="function"){if(xr(n||a)===null)continue;break}var i=Xa(a);i!==null&&(e.splice(t,3),t-=3,xc(i,{pending:!0,data:l,method:a.method,action:n},n,l))}}))}function wl(e){function t(p){return Zs(p,e)}pa!==null&&Zs(pa,e),ya!==null&&Zs(ya,e),va!==null&&Zs(va,e),Tl.forEach(t),_l.forEach(t);for(var a=0;a<xa.length;a++){var n=xa[a];n.blockedOn===e&&(n.blockedOn=null)}for(;0<xa.length&&(a=xa[0],a.blockedOn===null);)Ud(a),a.blockedOn===null&&xa.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(n=0;n<a.length;n+=3){var l=a[n],i=a[n+1],o=l[Qe]||null;if(typeof i=="function")o||qd(a);else if(o){var h=null;if(i&&i.hasAttribute("formAction")){if(l=i,o=i[Qe]||null)h=o.formAction;else if(xr(l)!==null)continue}else h=o.action;typeof h=="function"?a[n+1]=h:(a.splice(n,3),n-=3),qd(a)}}}function Sr(e){this._internalRoot=e}Js.prototype.render=Sr.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(r(409));var a=t.current,n=nt();zd(a,n,e,t,null,null)},Js.prototype.unmount=Sr.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;zd(e.current,2,null,e,null,null),Cs(),t[Ga]=null}};function Js(e){this._internalRoot=e}Js.prototype.unstable_scheduleHydration=function(e){if(e){var t=Pr();e={blockedOn:null,target:e,priority:t};for(var a=0;a<xa.length&&t!==0&&t<xa[a].priority;a++);xa.splice(a,0,e),a===0&&Ud(e)}};var Hd=s.version;if(Hd!=="19.1.0")throw Error(r(527,Hd,"19.1.0"));z.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(r(188)):(e=Object.keys(e).join(","),Error(r(268,e)));return e=b(t),e=e!==null?M(e):null,e=e===null?null:e.stateNode,e};var G0={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:T,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var $s=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!$s.isDisabled&&$s.supportsFiber)try{Dn=$s.inject(G0),We=$s}catch{}}return Ml.createRoot=function(e,t){if(!d(e))throw Error(r(299));var a=!1,n="",l=ef,i=tf,o=af,h=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onUncaughtError!==void 0&&(l=t.onUncaughtError),t.onCaughtError!==void 0&&(i=t.onCaughtError),t.onRecoverableError!==void 0&&(o=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(h=t.unstable_transitionCallbacks)),t=Cd(e,1,!1,null,null,a,n,l,i,o,h,null),e[Ga]=t.current,nr(e),new Sr(t)},Ml.hydrateRoot=function(e,t,a){if(!d(e))throw Error(r(299));var n=!1,l="",i=ef,o=tf,h=af,p=null,E=null;return a!=null&&(a.unstable_strictMode===!0&&(n=!0),a.identifierPrefix!==void 0&&(l=a.identifierPrefix),a.onUncaughtError!==void 0&&(i=a.onUncaughtError),a.onCaughtError!==void 0&&(o=a.onCaughtError),a.onRecoverableError!==void 0&&(h=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(p=a.unstable_transitionCallbacks),a.formState!==void 0&&(E=a.formState)),t=Cd(e,1,!0,t,a??null,n,l,i,o,h,p,E),t.context=Md(null),a=t.current,n=nt(),n=di(n),l=aa(n),l.callback=null,na(a,l,n),a=n,t.current.lanes=a,On(t,a),wt(t),e[Ga]=t.current,nr(e),new Js(t)},Ml.version="19.1.0",Ml}var Qd;function P0(){if(Qd)return Ar.exports;Qd=1;function u(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(u)}catch(s){console.error(s)}}return u(),Ar.exports=I0(),Ar.exports}var eg=P0();const tg=Pd(eg),ag=({sources:u})=>{if(!u||u.length===0)return null;const s=u.filter(c=>c.web&&c.web.uri&&c.web.title||c.retrievedContext&&c.retrievedContext.uri&&c.retrievedContext.title);return s.length===0?null:f.jsxs("div",{className:"mt-2 pt-2 border-t border-neutral-500",children:[f.jsx("h4",{className:"text-xs font-semibold text-gray-300 mb-1",children:"Sources:"}),f.jsx("ul",{className:"list-disc list-inside space-y-1",children:s.map((c,r)=>{const d=c.web||c.retrievedContext;if(!d||!d.uri||!d.title)return null;const m=d.title.length>80?d.title.substring(0,77)+"...":d.title;return f.jsx("li",{className:"text-xs",children:f.jsx("a",{href:d.uri,target:"_blank",rel:"noopener noreferrer",className:"text-teal-400 hover:text-teal-300 hover:underline",title:d.title,children:m})},r)})})]})},ng=({message:u})=>{const s=u.sender==="user",c=u.sender==="bot",r=u.sender==="system",d="max-w-xl p-3 rounded-xl shadow-md break-words",m="bg-teal-600 text-white self-end",g="bg-neutral-600 text-white self-start",y="bg-yellow-600 bg-opacity-50 text-yellow-100 self-center text-xs italic p-2 text-center mx-auto w-full md:w-3/4";let b=d;s?b+=` ${m}`:c?b+=` ${g}`:r&&(b+=` ${y}`);const M=C=>{let O=(Q=>Q.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;"))(C);return O=O.replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>"),O=O.replace(/\*(.*?)\*/g,"<em>$1</em>"),O=O.replace(/`(.*?)`/g,'<code class="bg-gray-700 px-1 rounded text-sm">$1</code>'),O=O.replace(/```([\s\S]*?)```/g,'<pre class="bg-gray-800 p-2 rounded text-sm my-1 overflow-x-auto whitespace-pre-wrap">$1</pre>'),O=O.replace(/\n/g,"<br />"),{__html:O}};return f.jsxs("div",{className:`flex flex-col ${s?"items-end":r?"items-center":"items-start"}`,children:[f.jsxs("div",{className:b,children:[f.jsx("p",{dangerouslySetInnerHTML:M(u.text)}),u.sources&&u.sources.length>0&&c&&f.jsx(ag,{sources:u.sources})]}),!r&&f.jsx("span",{className:"text-xs text-gray-400 mt-1 px-1",children:u.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]})},lg=()=>f.jsxs("div",{className:"flex items-center space-x-2 p-2 bg-neutral-600 rounded-lg shadow",children:[f.jsx("div",{className:"w-5 h-5 border-2 border-t-teal-400 border-r-teal-400 border-b-transparent border-l-transparent rounded-full animate-spin"}),f.jsx("span",{className:"text-sm text-gray-300",children:"Thinking..."})]}),sg=({messages:u,onSendMessage:s,isLoading:c,apiKeyLoaded:r})=>{const[d,m]=k.useState(""),g=k.useRef(null),[y,b]=k.useState(!1),[M,C]=k.useState(!1),R=k.useRef(null),O=k.useRef([]),Q=()=>{g.current?.scrollIntoView({behavior:"smooth"})};k.useEffect(Q,[u]);const X=B=>{B.preventDefault(),d.trim()&&!c&&r&&!y&&(s({text:d}),m(""))},D=async()=>{if(r)if(y)R.current&&R.current.state==="recording"&&R.current.stop(),b(!1),C(!0);else try{const B=await navigator.mediaDevices.getUserMedia({audio:!0});R.current=new MediaRecorder(B),O.current=[],R.current.ondataavailable=J=>{O.current.push(J.data)},R.current.onstop=async()=>{const J=O.current.length>0&&O.current[0].type?O.current[0].type:"audio/webm",ie=new Blob(O.current,{type:J}),I=new FileReader;I.readAsDataURL(ie),I.onloadend=()=>{const be=I.result?.toString().split(",")[1];be?(s({text:d,audio:{mimeType:ie.type||J,data:be}}),m("")):(console.error("Failed to convert audio to base64"),alert("Error: Could not process recorded audio. Please try again.")),C(!1)},I.onerror=()=>{console.error("FileReader error during audio conversion."),alert("Error: Could not read recorded audio. Please try again."),C(!1)},B.getTracks().forEach(be=>be.stop())},R.current.start(),b(!0),C(!1)}catch(B){console.error("Error accessing microphone:",B),alert("Could not access microphone. Please ensure permission is granted and try again."),b(!1),C(!1)}};return f.jsxs("div",{className:"flex flex-col h-full",children:[f.jsxs("div",{className:"flex-1 overflow-y-auto p-6 space-y-4",children:[u.length===0?f.jsxs("div",{className:"flex flex-col items-center justify-center h-full text-center",children:[f.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mb-6",children:f.jsx("svg",{className:"w-8 h-8 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),f.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"Bem-vindo ao Gemini Chat"}),f.jsx("p",{className:"text-gray-600 mb-8 max-w-md",children:"Inicie uma conversa com IA. Envie documentos para contexto ou habilite a busca web para informações em tempo real."}),f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 max-w-lg",children:[f.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 text-center",children:[f.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-lg flex items-center justify-center mx-auto mb-2",children:f.jsx("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),f.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-1",children:"Chat"}),f.jsx("p",{className:"text-xs text-gray-600",children:"Pergunte qualquer coisa"})]}),f.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 text-center",children:[f.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-lg flex items-center justify-center mx-auto mb-2",children:f.jsx("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),f.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-1",children:"Documentos"}),f.jsx("p",{className:"text-xs text-gray-600",children:"Envie para contexto"})]}),f.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 text-center",children:[f.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-lg flex items-center justify-center mx-auto mb-2",children:f.jsx("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),f.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-1",children:"Busca"}),f.jsx("p",{className:"text-xs text-gray-600",children:"Busca na web"})]})]})]}):u.map(B=>f.jsx(ng,{message:B},B.id)),(c||M)&&f.jsx("div",{className:"flex justify-start",children:f.jsx("div",{className:"bg-gray-100 border border-gray-200 p-4 rounded-2xl max-w-xs",children:f.jsxs("div",{className:"flex items-center space-x-3",children:[f.jsx(lg,{}),f.jsx("span",{className:"text-sm text-gray-700",children:M?"Processando áudio...":"Pensando..."})]})})}),f.jsx("div",{ref:g})]}),f.jsx("div",{className:"p-6 bg-white border-t border-gray-200",children:f.jsxs("form",{onSubmit:X,className:"max-w-4xl mx-auto",children:[f.jsxs("div",{className:"flex items-end space-x-3",children:[f.jsxs("div",{className:"flex-1 relative",children:[f.jsx("input",{type:"text",value:d,onChange:B=>m(B.target.value),placeholder:r?y?"Gravando... (texto acompanhará o áudio)":"Digite sua mensagem...":"Chave API não carregada. Não é possível enviar mensagens.",className:"w-full p-4 pr-12 rounded-xl bg-gray-50 border border-gray-300 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-gray-900 focus:border-gray-900 focus:outline-none transition-all duration-200",disabled:c||!r||y||M}),d&&f.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:f.jsx("span",{className:"text-xs text-gray-400",children:d.length})})]}),f.jsx("button",{type:"button",onClick:D,disabled:c||!r||M,className:`p-4 w-14 h-14 flex items-center justify-center rounded-xl text-white transition-all duration-200 hover:scale-105 active:scale-95
                          ${y?"bg-red-600 hover:bg-red-700":"bg-gray-600 hover:bg-gray-700"}
                          disabled:bg-gray-400 disabled:cursor-not-allowed disabled:transform-none`,title:y?"Parar gravação":"Iniciar gravação de voz",children:M?f.jsxs("svg",{className:"animate-spin h-6 w-6 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[f.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),f.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):y?f.jsx("svg",{className:"w-6 h-6",fill:"currentColor",viewBox:"0 0 20 20",children:f.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a2 2 0 114 0v4a2 2 0 11-4 0V7z",clipRule:"evenodd"})}):f.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"})})}),f.jsx("button",{type:"submit",disabled:!d.trim()||c||!r||y||M,className:"p-4 w-14 h-14 flex items-center justify-center rounded-xl bg-gray-900 hover:bg-gray-800 text-white transition-all duration-200 hover:scale-105 active:scale-95 disabled:bg-gray-400 disabled:cursor-not-allowed disabled:transform-none",title:"Enviar mensagem",children:f.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})})]}),(y||M)&&f.jsx("div",{className:"mt-3 flex items-center justify-center",children:f.jsxs("div",{className:"flex items-center space-x-2 px-3 py-1 bg-gray-100 rounded-full border border-gray-200",children:[y&&f.jsxs(f.Fragment,{children:[f.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),f.jsx("span",{className:"text-xs text-red-600",children:"Gravando..."})]}),M&&f.jsxs(f.Fragment,{children:[f.jsx("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"}),f.jsx("span",{className:"text-xs text-blue-600",children:"Processando áudio..."})]})]})})]})})]})},ig=({onDocumentLoad:u})=>{const[s,c]=k.useState(""),[r,d]=k.useState(null),[m,g]=k.useState(!1),[y,b]=k.useState(null),M=k.useCallback(async O=>{const Q=O.target.files?.[0];if(Q){g(!0),d(null),c(""),b(`Processing ${Q.name}...`);try{if(Q.type==="application/pdf"){const X=await Q0(()=>import("./pdf.min-CHfrD03w.js"),[]);X.GlobalWorkerOptions.workerSrc="https://esm.sh/pdfjs-dist@4.10.38/build/pdf.worker.min.mjs";const D=await Q.arrayBuffer(),B=await X.getDocument(D).promise;let J="";for(let ie=1;ie<=B.numPages;ie++){const be=await(await B.getPage(ie)).getTextContent();J+=be.items.map(He=>He.str).join(" ")+`
`}c(J),d(Q.name),u(J),b(`${Q.name} loaded successfully.`)}else if(Q.type==="text/plain"){const X=new FileReader;X.onload=D=>{const B=D.target?.result;c(B),d(Q.name),u(B),b(`${Q.name} loaded successfully.`)},X.readAsText(Q)}else alert("Unsupported file type. Please upload a .txt or .pdf file."),b(null)}catch(X){console.error("Error processing file:",X),alert(`Error processing file: ${X instanceof Error?X.message:"Unknown error"}`),b(`Error loading ${Q.name}.`)}finally{g(!1)}}},[u]),C=k.useCallback(()=>{s.trim()?(u(s),d("Pasted Content"),b("Pasted content loaded.")):alert("Please paste some content before loading.")},[s,u]),R=k.useCallback(()=>{c(""),d(null),u(""),b("Document context cleared.");const O=document.getElementById("file-upload");O&&(O.value="")},[u]);return f.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-4",children:[f.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[f.jsx("div",{className:"w-8 h-8 bg-gray-200 rounded-lg flex items-center justify-center",children:f.jsx("svg",{className:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),f.jsxs("div",{children:[f.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Documentos"}),f.jsx("p",{className:"text-xs text-gray-600",children:"Envie ou cole conteúdo para contexto"})]})]}),f.jsxs("div",{className:"space-y-3",children:[f.jsx("label",{htmlFor:"file-upload",className:"block text-sm font-medium text-gray-700",children:"Enviar Documento"}),f.jsxs("div",{className:"relative",children:[f.jsx("input",{id:"file-upload",type:"file",accept:".txt,.pdf",onChange:M,disabled:m,className:`block w-full text-sm text-gray-700 bg-white border border-gray-300 rounded-lg p-3
                       file:mr-4 file:py-2 file:px-4
                       file:rounded-lg file:border-0
                       file:text-sm file:font-medium
                       file:bg-gray-900 file:text-white
                       hover:file:bg-gray-800
                       file:transition-all file:duration-200
                       disabled:opacity-50 disabled:cursor-not-allowed
                       focus:ring-2 focus:ring-gray-900 focus:border-gray-900 focus:outline-none`}),m&&f.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:f.jsx("div",{className:"w-5 h-5 border-2 border-gray-900 border-t-transparent rounded-full animate-spin"})})]}),f.jsx("p",{className:"text-xs text-gray-500",children:"Suporta arquivos .txt e .pdf até 10MB"})]}),f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"absolute inset-0 flex items-center",children:f.jsx("div",{className:"w-full border-t border-gray-300"})}),f.jsx("div",{className:"relative flex justify-center text-xs",children:f.jsx("span",{className:"bg-gray-50 px-3 text-gray-500",children:"OR"})})]}),f.jsxs("div",{className:"space-y-3",children:[f.jsx("label",{htmlFor:"text-area-input",className:"block text-sm font-medium text-gray-700",children:"Colar Conteúdo de Texto"}),f.jsx("textarea",{id:"text-area-input",rows:6,value:s,onChange:O=>c(O.target.value),placeholder:"Cole o texto do seu documento aqui...",disabled:m,className:"w-full p-2 border border-neutral-500 rounded-md bg-neutral-700 text-white placeholder-gray-400 focus:ring-2 focus:ring-teal-500 focus:border-teal-500 disabled:opacity-50"}),f.jsx("button",{onClick:C,className:"mt-2 w-full bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded-md transition-colors disabled:bg-gray-500 disabled:opacity-50",disabled:!s.trim()||m,children:"Load Pasted Text"})]}),m&&y&&f.jsxs("div",{className:"mt-2 text-sm text-yellow-400 flex items-center",children:[f.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-yellow-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[f.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),f.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),y]}),!m&&r&&y&&f.jsxs("div",{className:`mt-2 text-sm ${y?.includes("Error")?"text-red-400":"text-green-400"}`,children:[f.jsx("i",{className:`fas ${y?.includes("Error")?"fa-exclamation-circle":"fa-check-circle"} mr-1`}),y.startsWith("Loaded:")||y.startsWith("Pasted content loaded.")||y.startsWith("Error loading")?y:`Loaded: ${r}`]}),!m&&!r&&y&&f.jsxs("div",{className:`mt-2 text-sm ${y?.includes("Error")?"text-red-400":"text-sky-300"}`,children:[f.jsx("i",{className:`fas ${y?.includes("Error")?"fa-exclamation-circle":"fa-info-circle"} mr-1`}),y]}),(s||r)&&f.jsxs("button",{onClick:R,disabled:m,className:"mt-2 w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-md transition-colors disabled:bg-gray-500 disabled:opacity-50",children:[f.jsx("i",{className:"fas fa-times-circle mr-1"})," Clear Document Context"]})]})},cg=({currentDocument:u,onDocumentSelect:s,onDocumentDelete:c})=>{const[r,d]=k.useState([]),[m,g]=k.useState("");k.useEffect(()=>{const D=localStorage.getItem("gemini-chat-documents");if(D)try{const B=JSON.parse(D);d(B.map(J=>({...J,uploadedAt:new Date(J.uploadedAt)})))}catch(B){console.error("Erro ao carregar documentos:",B)}},[]);const y=D=>{localStorage.setItem("gemini-chat-documents",JSON.stringify(D)),d(D)},b=(D,B,J,ie)=>{const I={id:Date.now().toString(),name:D,type:J,size:ie,content:B,uploadedAt:new Date,isActive:!1},be=[...r,I];return y(be),I.id},M=D=>{const B=r.find(J=>J.id===D);if(B){const J=r.map(ie=>({...ie,isActive:ie.id===D}));y(J),s(B.content,B.name)}},C=D=>{const B=r.filter(J=>J.id!==D);y(B),c(D)},R=r.filter(D=>D.name.toLowerCase().includes(m.toLowerCase())),O=D=>{if(D===0)return"0 Bytes";const B=1024,J=["Bytes","KB","MB","GB"],ie=Math.floor(Math.log(D)/Math.log(B));return parseFloat((D/Math.pow(B,ie)).toFixed(2))+" "+J[ie]},Q=D=>D.toLocaleDateString("pt-BR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),X=D=>{switch(D){case"pdf":return f.jsx("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"})});case"txt":return f.jsx("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})});case"paste":return f.jsx("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})});default:return f.jsx("svg",{className:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}};return Dr.useImperativeHandle(Dr.createRef(),()=>({addDocument:b})),f.jsxs("div",{className:"p-6 space-y-6",children:[f.jsx("div",{className:"flex items-center justify-between",children:f.jsxs("div",{children:[f.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Gerenciar Documentos"}),f.jsxs("p",{className:"text-sm text-gray-600",children:[r.length," documento",r.length!==1?"s":""," armazenado",r.length!==1?"s":""]})]})}),f.jsxs("div",{className:"relative",children:[f.jsx("svg",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),f.jsx("input",{type:"text",placeholder:"Buscar documentos...",value:m,onChange:D=>g(D.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-gray-900 focus:border-gray-900 focus:outline-none"})]}),f.jsx("div",{className:"space-y-3",children:R.length===0?f.jsxs("div",{className:"text-center py-12",children:[f.jsx("svg",{className:"mx-auto w-12 h-12 text-gray-400 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),f.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Nenhum documento encontrado"}),f.jsx("p",{className:"text-gray-600",children:m?"Tente uma busca diferente":"Envie documentos na aba Chat para começar"})]}):R.map(D=>f.jsx("div",{className:`
                border rounded-lg p-4 transition-all duration-200 hover:shadow-md
                ${D.isActive?"border-gray-900 bg-gray-50":"border-gray-200 bg-white"}
              `,children:f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsxs("div",{className:"flex items-center space-x-3 flex-1 min-w-0",children:[X(D.type),f.jsxs("div",{className:"flex-1 min-w-0",children:[f.jsx("h3",{className:"text-sm font-medium text-gray-900 truncate",children:D.name}),f.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-500 mt-1",children:[f.jsx("span",{children:O(D.size)}),f.jsx("span",{children:Q(D.uploadedAt)}),D.isActive&&f.jsx("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Ativo"})]})]})]}),f.jsxs("div",{className:"flex items-center space-x-2",children:[!D.isActive&&f.jsx("button",{onClick:()=>M(D.id),className:"px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors",children:"Ativar"}),f.jsx("button",{onClick:()=>C(D.id),className:"p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors",title:"Excluir documento",children:f.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]})},D.id))})]})},rg=({tabs:u,activeTab:s,onTabChange:c})=>f.jsx("div",{className:"border-b border-gray-200 bg-white",children:f.jsx("nav",{className:"flex space-x-8 px-6","aria-label":"Abas",children:u.map(r=>f.jsxs("button",{onClick:()=>c(r.id),className:`
              flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200
              ${s===r.id?"border-gray-900 text-gray-900":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}
            `,"aria-current":s===r.id?"page":void 0,children:[f.jsx("span",{className:"w-5 h-5",children:r.icon}),f.jsx("span",{children:r.label})]},r.id))})}),Dt=Object.create(null);Dt.open="0";Dt.close="1";Dt.ping="2";Dt.pong="3";Dt.message="4";Dt.upgrade="5";Dt.noop="6";const Ps=Object.create(null);Object.keys(Dt).forEach(u=>{Ps[Dt[u]]=u});const Rr={type:"error",data:"parser error"},th=typeof Blob=="function"||typeof Blob<"u"&&Object.prototype.toString.call(Blob)==="[object BlobConstructor]",ah=typeof ArrayBuffer=="function",nh=u=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(u):u&&u.buffer instanceof ArrayBuffer,Vr=({type:u,data:s},c,r)=>th&&s instanceof Blob?c?r(s):Zd(s,r):ah&&(s instanceof ArrayBuffer||nh(s))?c?r(s):Zd(new Blob([s]),r):r(Dt[u]+(s||"")),Zd=(u,s)=>{const c=new FileReader;return c.onload=function(){const r=c.result.split(",")[1];s("b"+(r||""))},c.readAsDataURL(u)};function Kd(u){return u instanceof Uint8Array?u:u instanceof ArrayBuffer?new Uint8Array(u):new Uint8Array(u.buffer,u.byteOffset,u.byteLength)}let _r;function ug(u,s){if(th&&u.data instanceof Blob)return u.data.arrayBuffer().then(Kd).then(s);if(ah&&(u.data instanceof ArrayBuffer||nh(u.data)))return s(Kd(u.data));Vr(u,!1,c=>{_r||(_r=new TextEncoder),s(_r.encode(c))})}const Jd="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Dl=typeof Uint8Array>"u"?[]:new Uint8Array(256);for(let u=0;u<Jd.length;u++)Dl[Jd.charCodeAt(u)]=u;const og=u=>{let s=u.length*.75,c=u.length,r,d=0,m,g,y,b;u[u.length-1]==="="&&(s--,u[u.length-2]==="="&&s--);const M=new ArrayBuffer(s),C=new Uint8Array(M);for(r=0;r<c;r+=4)m=Dl[u.charCodeAt(r)],g=Dl[u.charCodeAt(r+1)],y=Dl[u.charCodeAt(r+2)],b=Dl[u.charCodeAt(r+3)],C[d++]=m<<2|g>>4,C[d++]=(g&15)<<4|y>>2,C[d++]=(y&3)<<6|b&63;return M},fg=typeof ArrayBuffer=="function",Gr=(u,s)=>{if(typeof u!="string")return{type:"message",data:lh(u,s)};const c=u.charAt(0);return c==="b"?{type:"message",data:dg(u.substring(1),s)}:Ps[c]?u.length>1?{type:Ps[c],data:u.substring(1)}:{type:Ps[c]}:Rr},dg=(u,s)=>{if(fg){const c=og(u);return lh(c,s)}else return{base64:!0,data:u}},lh=(u,s)=>{switch(s){case"blob":return u instanceof Blob?u:new Blob([u]);case"arraybuffer":default:return u instanceof ArrayBuffer?u:u.buffer}},sh="",hg=(u,s)=>{const c=u.length,r=new Array(c);let d=0;u.forEach((m,g)=>{Vr(m,!1,y=>{r[g]=y,++d===c&&s(r.join(sh))})})},mg=(u,s)=>{const c=u.split(sh),r=[];for(let d=0;d<c.length;d++){const m=Gr(c[d],s);if(r.push(m),m.type==="error")break}return r};function gg(){return new TransformStream({transform(u,s){ug(u,c=>{const r=c.length;let d;if(r<126)d=new Uint8Array(1),new DataView(d.buffer).setUint8(0,r);else if(r<65536){d=new Uint8Array(3);const m=new DataView(d.buffer);m.setUint8(0,126),m.setUint16(1,r)}else{d=new Uint8Array(9);const m=new DataView(d.buffer);m.setUint8(0,127),m.setBigUint64(1,BigInt(r))}u.data&&typeof u.data!="string"&&(d[0]|=128),s.enqueue(d),s.enqueue(c)})}})}let jr;function Ws(u){return u.reduce((s,c)=>s+c.length,0)}function Fs(u,s){if(u[0].length===s)return u.shift();const c=new Uint8Array(s);let r=0;for(let d=0;d<s;d++)c[d]=u[0][r++],r===u[0].length&&(u.shift(),r=0);return u.length&&r<u[0].length&&(u[0]=u[0].slice(r)),c}function pg(u,s){jr||(jr=new TextDecoder);const c=[];let r=0,d=-1,m=!1;return new TransformStream({transform(g,y){for(c.push(g);;){if(r===0){if(Ws(c)<1)break;const b=Fs(c,1);m=(b[0]&128)===128,d=b[0]&127,d<126?r=3:d===126?r=1:r=2}else if(r===1){if(Ws(c)<2)break;const b=Fs(c,2);d=new DataView(b.buffer,b.byteOffset,b.length).getUint16(0),r=3}else if(r===2){if(Ws(c)<8)break;const b=Fs(c,8),M=new DataView(b.buffer,b.byteOffset,b.length),C=M.getUint32(0);if(C>Math.pow(2,21)-1){y.enqueue(Rr);break}d=C*Math.pow(2,32)+M.getUint32(4),r=3}else{if(Ws(c)<d)break;const b=Fs(c,d);y.enqueue(Gr(m?b:jr.decode(b),s)),r=0}if(d===0||d>u){y.enqueue(Rr);break}}}})}const ih=4;function we(u){if(u)return yg(u)}function yg(u){for(var s in we.prototype)u[s]=we.prototype[s];return u}we.prototype.on=we.prototype.addEventListener=function(u,s){return this._callbacks=this._callbacks||{},(this._callbacks["$"+u]=this._callbacks["$"+u]||[]).push(s),this};we.prototype.once=function(u,s){function c(){this.off(u,c),s.apply(this,arguments)}return c.fn=s,this.on(u,c),this};we.prototype.off=we.prototype.removeListener=we.prototype.removeAllListeners=we.prototype.removeEventListener=function(u,s){if(this._callbacks=this._callbacks||{},arguments.length==0)return this._callbacks={},this;var c=this._callbacks["$"+u];if(!c)return this;if(arguments.length==1)return delete this._callbacks["$"+u],this;for(var r,d=0;d<c.length;d++)if(r=c[d],r===s||r.fn===s){c.splice(d,1);break}return c.length===0&&delete this._callbacks["$"+u],this};we.prototype.emit=function(u){this._callbacks=this._callbacks||{};for(var s=new Array(arguments.length-1),c=this._callbacks["$"+u],r=1;r<arguments.length;r++)s[r-1]=arguments[r];if(c){c=c.slice(0);for(var r=0,d=c.length;r<d;++r)c[r].apply(this,s)}return this};we.prototype.emitReserved=we.prototype.emit;we.prototype.listeners=function(u){return this._callbacks=this._callbacks||{},this._callbacks["$"+u]||[]};we.prototype.hasListeners=function(u){return!!this.listeners(u).length};const si=typeof Promise=="function"&&typeof Promise.resolve=="function"?s=>Promise.resolve().then(s):(s,c)=>c(s,0),gt=typeof self<"u"?self:typeof window<"u"?window:Function("return this")(),vg="arraybuffer";function ch(u,...s){return s.reduce((c,r)=>(u.hasOwnProperty(r)&&(c[r]=u[r]),c),{})}const xg=gt.setTimeout,bg=gt.clearTimeout;function ii(u,s){s.useNativeTimers?(u.setTimeoutFn=xg.bind(gt),u.clearTimeoutFn=bg.bind(gt)):(u.setTimeoutFn=gt.setTimeout.bind(gt),u.clearTimeoutFn=gt.clearTimeout.bind(gt))}const Sg=1.33;function Eg(u){return typeof u=="string"?Ag(u):Math.ceil((u.byteLength||u.size)*Sg)}function Ag(u){let s=0,c=0;for(let r=0,d=u.length;r<d;r++)s=u.charCodeAt(r),s<128?c+=1:s<2048?c+=2:s<55296||s>=57344?c+=3:(r++,c+=4);return c}function rh(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}function Ng(u){let s="";for(let c in u)u.hasOwnProperty(c)&&(s.length&&(s+="&"),s+=encodeURIComponent(c)+"="+encodeURIComponent(u[c]));return s}function Tg(u){let s={},c=u.split("&");for(let r=0,d=c.length;r<d;r++){let m=c[r].split("=");s[decodeURIComponent(m[0])]=decodeURIComponent(m[1])}return s}class _g extends Error{constructor(s,c,r){super(s),this.description=c,this.context=r,this.type="TransportError"}}class Yr extends we{constructor(s){super(),this.writable=!1,ii(this,s),this.opts=s,this.query=s.query,this.socket=s.socket,this.supportsBinary=!s.forceBase64}onError(s,c,r){return super.emitReserved("error",new _g(s,c,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return(this.readyState==="opening"||this.readyState==="open")&&(this.doClose(),this.onClose()),this}send(s){this.readyState==="open"&&this.write(s)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(s){const c=Gr(s,this.socket.binaryType);this.onPacket(c)}onPacket(s){super.emitReserved("packet",s)}onClose(s){this.readyState="closed",super.emitReserved("close",s)}pause(s){}createUri(s,c={}){return s+"://"+this._hostname()+this._port()+this.opts.path+this._query(c)}_hostname(){const s=this.opts.hostname;return s.indexOf(":")===-1?s:"["+s+"]"}_port(){return this.opts.port&&(this.opts.secure&&+(this.opts.port!==443)||!this.opts.secure&&Number(this.opts.port)!==80)?":"+this.opts.port:""}_query(s){const c=Ng(s);return c.length?"?"+c:""}}class jg extends Yr{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(s){this.readyState="pausing";const c=()=>{this.readyState="paused",s()};if(this._polling||!this.writable){let r=0;this._polling&&(r++,this.once("pollComplete",function(){--r||c()})),this.writable||(r++,this.once("drain",function(){--r||c()}))}else c()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(s){const c=r=>{if(this.readyState==="opening"&&r.type==="open"&&this.onOpen(),r.type==="close")return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(r)};mg(s,this.socket.binaryType).forEach(c),this.readyState!=="closed"&&(this._polling=!1,this.emitReserved("pollComplete"),this.readyState==="open"&&this._poll())}doClose(){const s=()=>{this.write([{type:"close"}])};this.readyState==="open"?s():this.once("open",s)}write(s){this.writable=!1,hg(s,c=>{this.doWrite(c,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const s=this.opts.secure?"https":"http",c=this.query||{};return this.opts.timestampRequests!==!1&&(c[this.opts.timestampParam]=rh()),!this.supportsBinary&&!c.sid&&(c.b64=1),this.createUri(s,c)}}let uh=!1;try{uh=typeof XMLHttpRequest<"u"&&"withCredentials"in new XMLHttpRequest}catch{}const wg=uh;function Cg(){}class Mg extends jg{constructor(s){if(super(s),typeof location<"u"){const c=location.protocol==="https:";let r=location.port;r||(r=c?"443":"80"),this.xd=typeof location<"u"&&s.hostname!==location.hostname||r!==s.port}}doWrite(s,c){const r=this.request({method:"POST",data:s});r.on("success",c),r.on("error",(d,m)=>{this.onError("xhr post error",d,m)})}doPoll(){const s=this.request();s.on("data",this.onData.bind(this)),s.on("error",(c,r)=>{this.onError("xhr poll error",c,r)}),this.pollXhr=s}}class zt extends we{constructor(s,c,r){super(),this.createRequest=s,ii(this,r),this._opts=r,this._method=r.method||"GET",this._uri=c,this._data=r.data!==void 0?r.data:null,this._create()}_create(){var s;const c=ch(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");c.xdomain=!!this._opts.xd;const r=this._xhr=this.createRequest(c);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0);for(let d in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(d)&&r.setRequestHeader(d,this._opts.extraHeaders[d])}}catch{}if(this._method==="POST")try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch{}try{r.setRequestHeader("Accept","*/*")}catch{}(s=this._opts.cookieJar)===null||s===void 0||s.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var d;r.readyState===3&&((d=this._opts.cookieJar)===null||d===void 0||d.parseCookies(r.getResponseHeader("set-cookie"))),r.readyState===4&&(r.status===200||r.status===1223?this._onLoad():this.setTimeoutFn(()=>{this._onError(typeof r.status=="number"?r.status:0)},0))},r.send(this._data)}catch(d){this.setTimeoutFn(()=>{this._onError(d)},0);return}typeof document<"u"&&(this._index=zt.requestsCount++,zt.requests[this._index]=this)}_onError(s){this.emitReserved("error",s,this._xhr),this._cleanup(!0)}_cleanup(s){if(!(typeof this._xhr>"u"||this._xhr===null)){if(this._xhr.onreadystatechange=Cg,s)try{this._xhr.abort()}catch{}typeof document<"u"&&delete zt.requests[this._index],this._xhr=null}}_onLoad(){const s=this._xhr.responseText;s!==null&&(this.emitReserved("data",s),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}zt.requestsCount=0;zt.requests={};if(typeof document<"u"){if(typeof attachEvent=="function")attachEvent("onunload",$d);else if(typeof addEventListener=="function"){const u="onpagehide"in gt?"pagehide":"unload";addEventListener(u,$d,!1)}}function $d(){for(let u in zt.requests)zt.requests.hasOwnProperty(u)&&zt.requests[u].abort()}const zg=function(){const u=oh({xdomain:!1});return u&&u.responseType!==null}();class Dg extends Mg{constructor(s){super(s);const c=s&&s.forceBase64;this.supportsBinary=zg&&!c}request(s={}){return Object.assign(s,{xd:this.xd},this.opts),new zt(oh,this.uri(),s)}}function oh(u){const s=u.xdomain;try{if(typeof XMLHttpRequest<"u"&&(!s||wg))return new XMLHttpRequest}catch{}if(!s)try{return new gt[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch{}}const fh=typeof navigator<"u"&&typeof navigator.product=="string"&&navigator.product.toLowerCase()==="reactnative";class Rg extends Yr{get name(){return"websocket"}doOpen(){const s=this.uri(),c=this.opts.protocols,r=fh?{}:ch(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(s,c,r)}catch(d){return this.emitReserved("error",d)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=s=>this.onClose({description:"websocket connection closed",context:s}),this.ws.onmessage=s=>this.onData(s.data),this.ws.onerror=s=>this.onError("websocket error",s)}write(s){this.writable=!1;for(let c=0;c<s.length;c++){const r=s[c],d=c===s.length-1;Vr(r,this.supportsBinary,m=>{try{this.doWrite(r,m)}catch{}d&&si(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){typeof this.ws<"u"&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const s=this.opts.secure?"wss":"ws",c=this.query||{};return this.opts.timestampRequests&&(c[this.opts.timestampParam]=rh()),this.supportsBinary||(c.b64=1),this.createUri(s,c)}}const wr=gt.WebSocket||gt.MozWebSocket;class Og extends Rg{createSocket(s,c,r){return fh?new wr(s,c,r):c?new wr(s,c):new wr(s)}doWrite(s,c){this.ws.send(c)}}class Bg extends Yr{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(s){return this.emitReserved("error",s)}this._transport.closed.then(()=>{this.onClose()}).catch(s=>{this.onError("webtransport error",s)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(s=>{const c=pg(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=s.readable.pipeThrough(c).getReader(),d=gg();d.readable.pipeTo(s.writable),this._writer=d.writable.getWriter();const m=()=>{r.read().then(({done:y,value:b})=>{y||(this.onPacket(b),m())}).catch(y=>{})};m();const g={type:"open"};this.query.sid&&(g.data=`{"sid":"${this.query.sid}"}`),this._writer.write(g).then(()=>this.onOpen())})})}write(s){this.writable=!1;for(let c=0;c<s.length;c++){const r=s[c],d=c===s.length-1;this._writer.write(r).then(()=>{d&&si(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var s;(s=this._transport)===null||s===void 0||s.close()}}const Ug={websocket:Og,webtransport:Bg,polling:Dg},kg=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,qg=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Or(u){if(u.length>8e3)throw"URI too long";const s=u,c=u.indexOf("["),r=u.indexOf("]");c!=-1&&r!=-1&&(u=u.substring(0,c)+u.substring(c,r).replace(/:/g,";")+u.substring(r,u.length));let d=kg.exec(u||""),m={},g=14;for(;g--;)m[qg[g]]=d[g]||"";return c!=-1&&r!=-1&&(m.source=s,m.host=m.host.substring(1,m.host.length-1).replace(/;/g,":"),m.authority=m.authority.replace("[","").replace("]","").replace(/;/g,":"),m.ipv6uri=!0),m.pathNames=Hg(m,m.path),m.queryKey=Lg(m,m.query),m}function Hg(u,s){const c=/\/{2,9}/g,r=s.replace(c,"/").split("/");return(s.slice(0,1)=="/"||s.length===0)&&r.splice(0,1),s.slice(-1)=="/"&&r.splice(r.length-1,1),r}function Lg(u,s){const c={};return s.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(r,d,m){d&&(c[d]=m)}),c}const Br=typeof addEventListener=="function"&&typeof removeEventListener=="function",ei=[];Br&&addEventListener("offline",()=>{ei.forEach(u=>u())},!1);class Sa extends we{constructor(s,c){if(super(),this.binaryType=vg,this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,s&&typeof s=="object"&&(c=s,s=null),s){const r=Or(s);c.hostname=r.host,c.secure=r.protocol==="https"||r.protocol==="wss",c.port=r.port,r.query&&(c.query=r.query)}else c.host&&(c.hostname=Or(c.host).host);ii(this,c),this.secure=c.secure!=null?c.secure:typeof location<"u"&&location.protocol==="https:",c.hostname&&!c.port&&(c.port=this.secure?"443":"80"),this.hostname=c.hostname||(typeof location<"u"?location.hostname:"localhost"),this.port=c.port||(typeof location<"u"&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},c.transports.forEach(r=>{const d=r.prototype.name;this.transports.push(d),this._transportsByName[d]=r}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},c),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),typeof this.opts.query=="string"&&(this.opts.query=Tg(this.opts.query)),Br&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),this.hostname!=="localhost"&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},ei.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(s){const c=Object.assign({},this.opts.query);c.EIO=ih,c.transport=s,this.id&&(c.sid=this.id);const r=Object.assign({},this.opts,{query:c,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[s]);return new this._transportsByName[s](r)}_open(){if(this.transports.length===0){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}const s=this.opts.rememberUpgrade&&Sa.priorWebsocketSuccess&&this.transports.indexOf("websocket")!==-1?"websocket":this.transports[0];this.readyState="opening";const c=this.createTransport(s);c.open(),this.setTransport(c)}setTransport(s){this.transport&&this.transport.removeAllListeners(),this.transport=s,s.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",c=>this._onClose("transport close",c))}onOpen(){this.readyState="open",Sa.priorWebsocketSuccess=this.transport.name==="websocket",this.emitReserved("open"),this.flush()}_onPacket(s){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing")switch(this.emitReserved("packet",s),this.emitReserved("heartbeat"),s.type){case"open":this.onHandshake(JSON.parse(s.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const c=new Error("server error");c.code=s.data,this._onError(c);break;case"message":this.emitReserved("data",s.data),this.emitReserved("message",s.data);break}}onHandshake(s){this.emitReserved("handshake",s),this.id=s.sid,this.transport.query.sid=s.sid,this._pingInterval=s.pingInterval,this._pingTimeout=s.pingTimeout,this._maxPayload=s.maxPayload,this.onOpen(),this.readyState!=="closed"&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const s=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+s,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},s),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,this.writeBuffer.length===0?this.emitReserved("drain"):this.flush()}flush(){if(this.readyState!=="closed"&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const s=this._getWritablePackets();this.transport.send(s),this._prevBufferLen=s.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&this.transport.name==="polling"&&this.writeBuffer.length>1))return this.writeBuffer;let c=1;for(let r=0;r<this.writeBuffer.length;r++){const d=this.writeBuffer[r].data;if(d&&(c+=Eg(d)),r>0&&c>this._maxPayload)return this.writeBuffer.slice(0,r);c+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const s=Date.now()>this._pingTimeoutTime;return s&&(this._pingTimeoutTime=0,si(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),s}write(s,c,r){return this._sendPacket("message",s,c,r),this}send(s,c,r){return this._sendPacket("message",s,c,r),this}_sendPacket(s,c,r,d){if(typeof c=="function"&&(d=c,c=void 0),typeof r=="function"&&(d=r,r=null),this.readyState==="closing"||this.readyState==="closed")return;r=r||{},r.compress=r.compress!==!1;const m={type:s,data:c,options:r};this.emitReserved("packetCreate",m),this.writeBuffer.push(m),d&&this.once("flush",d),this.flush()}close(){const s=()=>{this._onClose("forced close"),this.transport.close()},c=()=>{this.off("upgrade",c),this.off("upgradeError",c),s()},r=()=>{this.once("upgrade",c),this.once("upgradeError",c)};return(this.readyState==="opening"||this.readyState==="open")&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():s()}):this.upgrading?r():s()),this}_onError(s){if(Sa.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&this.readyState==="opening")return this.transports.shift(),this._open();this.emitReserved("error",s),this._onClose("transport error",s)}_onClose(s,c){if(this.readyState==="opening"||this.readyState==="open"||this.readyState==="closing"){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Br&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const r=ei.indexOf(this._offlineEventListener);r!==-1&&ei.splice(r,1)}this.readyState="closed",this.id=null,this.emitReserved("close",s,c),this.writeBuffer=[],this._prevBufferLen=0}}}Sa.protocol=ih;class Vg extends Sa{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),this.readyState==="open"&&this.opts.upgrade)for(let s=0;s<this._upgrades.length;s++)this._probe(this._upgrades[s])}_probe(s){let c=this.createTransport(s),r=!1;Sa.priorWebsocketSuccess=!1;const d=()=>{r||(c.send([{type:"ping",data:"probe"}]),c.once("packet",R=>{if(!r)if(R.type==="pong"&&R.data==="probe"){if(this.upgrading=!0,this.emitReserved("upgrading",c),!c)return;Sa.priorWebsocketSuccess=c.name==="websocket",this.transport.pause(()=>{r||this.readyState!=="closed"&&(C(),this.setTransport(c),c.send([{type:"upgrade"}]),this.emitReserved("upgrade",c),c=null,this.upgrading=!1,this.flush())})}else{const O=new Error("probe error");O.transport=c.name,this.emitReserved("upgradeError",O)}}))};function m(){r||(r=!0,C(),c.close(),c=null)}const g=R=>{const O=new Error("probe error: "+R);O.transport=c.name,m(),this.emitReserved("upgradeError",O)};function y(){g("transport closed")}function b(){g("socket closed")}function M(R){c&&R.name!==c.name&&m()}const C=()=>{c.removeListener("open",d),c.removeListener("error",g),c.removeListener("close",y),this.off("close",b),this.off("upgrading",M)};c.once("open",d),c.once("error",g),c.once("close",y),this.once("close",b),this.once("upgrading",M),this._upgrades.indexOf("webtransport")!==-1&&s!=="webtransport"?this.setTimeoutFn(()=>{r||c.open()},200):c.open()}onHandshake(s){this._upgrades=this._filterUpgrades(s.upgrades),super.onHandshake(s)}_filterUpgrades(s){const c=[];for(let r=0;r<s.length;r++)~this.transports.indexOf(s[r])&&c.push(s[r]);return c}}let Gg=class extends Vg{constructor(s,c={}){const r=typeof s=="object"?s:c;(!r.transports||r.transports&&typeof r.transports[0]=="string")&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(d=>Ug[d]).filter(d=>!!d)),super(s,r)}};function Yg(u,s="",c){let r=u;c=c||typeof location<"u"&&location,u==null&&(u=c.protocol+"//"+c.host),typeof u=="string"&&(u.charAt(0)==="/"&&(u.charAt(1)==="/"?u=c.protocol+u:u=c.host+u),/^(https?|wss?):\/\//.test(u)||(typeof c<"u"?u=c.protocol+"//"+u:u="https://"+u),r=Or(u)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const m=r.host.indexOf(":")!==-1?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+m+":"+r.port+s,r.href=r.protocol+"://"+m+(c&&c.port===r.port?"":":"+r.port),r}const Xg=typeof ArrayBuffer=="function",Qg=u=>typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(u):u.buffer instanceof ArrayBuffer,dh=Object.prototype.toString,Zg=typeof Blob=="function"||typeof Blob<"u"&&dh.call(Blob)==="[object BlobConstructor]",Kg=typeof File=="function"||typeof File<"u"&&dh.call(File)==="[object FileConstructor]";function Xr(u){return Xg&&(u instanceof ArrayBuffer||Qg(u))||Zg&&u instanceof Blob||Kg&&u instanceof File}function ti(u,s){if(!u||typeof u!="object")return!1;if(Array.isArray(u)){for(let c=0,r=u.length;c<r;c++)if(ti(u[c]))return!0;return!1}if(Xr(u))return!0;if(u.toJSON&&typeof u.toJSON=="function"&&arguments.length===1)return ti(u.toJSON(),!0);for(const c in u)if(Object.prototype.hasOwnProperty.call(u,c)&&ti(u[c]))return!0;return!1}function Jg(u){const s=[],c=u.data,r=u;return r.data=Ur(c,s),r.attachments=s.length,{packet:r,buffers:s}}function Ur(u,s){if(!u)return u;if(Xr(u)){const c={_placeholder:!0,num:s.length};return s.push(u),c}else if(Array.isArray(u)){const c=new Array(u.length);for(let r=0;r<u.length;r++)c[r]=Ur(u[r],s);return c}else if(typeof u=="object"&&!(u instanceof Date)){const c={};for(const r in u)Object.prototype.hasOwnProperty.call(u,r)&&(c[r]=Ur(u[r],s));return c}return u}function $g(u,s){return u.data=kr(u.data,s),delete u.attachments,u}function kr(u,s){if(!u)return u;if(u&&u._placeholder===!0){if(typeof u.num=="number"&&u.num>=0&&u.num<s.length)return s[u.num];throw new Error("illegal attachments")}else if(Array.isArray(u))for(let c=0;c<u.length;c++)u[c]=kr(u[c],s);else if(typeof u=="object")for(const c in u)Object.prototype.hasOwnProperty.call(u,c)&&(u[c]=kr(u[c],s));return u}const Wg=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],Fg=5;var ee;(function(u){u[u.CONNECT=0]="CONNECT",u[u.DISCONNECT=1]="DISCONNECT",u[u.EVENT=2]="EVENT",u[u.ACK=3]="ACK",u[u.CONNECT_ERROR=4]="CONNECT_ERROR",u[u.BINARY_EVENT=5]="BINARY_EVENT",u[u.BINARY_ACK=6]="BINARY_ACK"})(ee||(ee={}));class Ig{constructor(s){this.replacer=s}encode(s){return(s.type===ee.EVENT||s.type===ee.ACK)&&ti(s)?this.encodeAsBinary({type:s.type===ee.EVENT?ee.BINARY_EVENT:ee.BINARY_ACK,nsp:s.nsp,data:s.data,id:s.id}):[this.encodeAsString(s)]}encodeAsString(s){let c=""+s.type;return(s.type===ee.BINARY_EVENT||s.type===ee.BINARY_ACK)&&(c+=s.attachments+"-"),s.nsp&&s.nsp!=="/"&&(c+=s.nsp+","),s.id!=null&&(c+=s.id),s.data!=null&&(c+=JSON.stringify(s.data,this.replacer)),c}encodeAsBinary(s){const c=Jg(s),r=this.encodeAsString(c.packet),d=c.buffers;return d.unshift(r),d}}function Wd(u){return Object.prototype.toString.call(u)==="[object Object]"}class Qr extends we{constructor(s){super(),this.reviver=s}add(s){let c;if(typeof s=="string"){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");c=this.decodeString(s);const r=c.type===ee.BINARY_EVENT;r||c.type===ee.BINARY_ACK?(c.type=r?ee.EVENT:ee.ACK,this.reconstructor=new Pg(c),c.attachments===0&&super.emitReserved("decoded",c)):super.emitReserved("decoded",c)}else if(Xr(s)||s.base64)if(this.reconstructor)c=this.reconstructor.takeBinaryData(s),c&&(this.reconstructor=null,super.emitReserved("decoded",c));else throw new Error("got binary data when not reconstructing a packet");else throw new Error("Unknown type: "+s)}decodeString(s){let c=0;const r={type:Number(s.charAt(0))};if(ee[r.type]===void 0)throw new Error("unknown packet type "+r.type);if(r.type===ee.BINARY_EVENT||r.type===ee.BINARY_ACK){const m=c+1;for(;s.charAt(++c)!=="-"&&c!=s.length;);const g=s.substring(m,c);if(g!=Number(g)||s.charAt(c)!=="-")throw new Error("Illegal attachments");r.attachments=Number(g)}if(s.charAt(c+1)==="/"){const m=c+1;for(;++c&&!(s.charAt(c)===","||c===s.length););r.nsp=s.substring(m,c)}else r.nsp="/";const d=s.charAt(c+1);if(d!==""&&Number(d)==d){const m=c+1;for(;++c;){const g=s.charAt(c);if(g==null||Number(g)!=g){--c;break}if(c===s.length)break}r.id=Number(s.substring(m,c+1))}if(s.charAt(++c)){const m=this.tryParse(s.substr(c));if(Qr.isPayloadValid(r.type,m))r.data=m;else throw new Error("invalid payload")}return r}tryParse(s){try{return JSON.parse(s,this.reviver)}catch{return!1}}static isPayloadValid(s,c){switch(s){case ee.CONNECT:return Wd(c);case ee.DISCONNECT:return c===void 0;case ee.CONNECT_ERROR:return typeof c=="string"||Wd(c);case ee.EVENT:case ee.BINARY_EVENT:return Array.isArray(c)&&(typeof c[0]=="number"||typeof c[0]=="string"&&Wg.indexOf(c[0])===-1);case ee.ACK:case ee.BINARY_ACK:return Array.isArray(c)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class Pg{constructor(s){this.packet=s,this.buffers=[],this.reconPack=s}takeBinaryData(s){if(this.buffers.push(s),this.buffers.length===this.reconPack.attachments){const c=$g(this.reconPack,this.buffers);return this.finishedReconstruction(),c}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const ep=Object.freeze(Object.defineProperty({__proto__:null,Decoder:Qr,Encoder:Ig,get PacketType(){return ee},protocol:Fg},Symbol.toStringTag,{value:"Module"}));function bt(u,s,c){return u.on(s,c),function(){u.off(s,c)}}const tp=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class hh extends we{constructor(s,c,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=s,this.nsp=c,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const s=this.io;this.subs=[bt(s,"open",this.onopen.bind(this)),bt(s,"packet",this.onpacket.bind(this)),bt(s,"error",this.onerror.bind(this)),bt(s,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected?this:(this.subEvents(),this.io._reconnecting||this.io.open(),this.io._readyState==="open"&&this.onopen(),this)}open(){return this.connect()}send(...s){return s.unshift("message"),this.emit.apply(this,s),this}emit(s,...c){var r,d,m;if(tp.hasOwnProperty(s))throw new Error('"'+s.toString()+'" is a reserved event name');if(c.unshift(s),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(c),this;const g={type:ee.EVENT,data:c};if(g.options={},g.options.compress=this.flags.compress!==!1,typeof c[c.length-1]=="function"){const C=this.ids++,R=c.pop();this._registerAckCallback(C,R),g.id=C}const y=(d=(r=this.io.engine)===null||r===void 0?void 0:r.transport)===null||d===void 0?void 0:d.writable,b=this.connected&&!(!((m=this.io.engine)===null||m===void 0)&&m._hasPingExpired());return this.flags.volatile&&!y||(b?(this.notifyOutgoingListeners(g),this.packet(g)):this.sendBuffer.push(g)),this.flags={},this}_registerAckCallback(s,c){var r;const d=(r=this.flags.timeout)!==null&&r!==void 0?r:this._opts.ackTimeout;if(d===void 0){this.acks[s]=c;return}const m=this.io.setTimeoutFn(()=>{delete this.acks[s];for(let y=0;y<this.sendBuffer.length;y++)this.sendBuffer[y].id===s&&this.sendBuffer.splice(y,1);c.call(this,new Error("operation has timed out"))},d),g=(...y)=>{this.io.clearTimeoutFn(m),c.apply(this,y)};g.withError=!0,this.acks[s]=g}emitWithAck(s,...c){return new Promise((r,d)=>{const m=(g,y)=>g?d(g):r(y);m.withError=!0,c.push(m),this.emit(s,...c)})}_addToQueue(s){let c;typeof s[s.length-1]=="function"&&(c=s.pop());const r={id:this._queueSeq++,tryCount:0,pending:!1,args:s,flags:Object.assign({fromQueue:!0},this.flags)};s.push((d,...m)=>r!==this._queue[0]?void 0:(d!==null?r.tryCount>this._opts.retries&&(this._queue.shift(),c&&c(d)):(this._queue.shift(),c&&c(null,...m)),r.pending=!1,this._drainQueue())),this._queue.push(r),this._drainQueue()}_drainQueue(s=!1){if(!this.connected||this._queue.length===0)return;const c=this._queue[0];c.pending&&!s||(c.pending=!0,c.tryCount++,this.flags=c.flags,this.emit.apply(this,c.args))}packet(s){s.nsp=this.nsp,this.io._packet(s)}onopen(){typeof this.auth=="function"?this.auth(s=>{this._sendConnectPacket(s)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(s){this.packet({type:ee.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},s):s})}onerror(s){this.connected||this.emitReserved("connect_error",s)}onclose(s,c){this.connected=!1,delete this.id,this.emitReserved("disconnect",s,c),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(s=>{if(!this.sendBuffer.some(r=>String(r.id)===s)){const r=this.acks[s];delete this.acks[s],r.withError&&r.call(this,new Error("socket has been disconnected"))}})}onpacket(s){if(s.nsp===this.nsp)switch(s.type){case ee.CONNECT:s.data&&s.data.sid?this.onconnect(s.data.sid,s.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case ee.EVENT:case ee.BINARY_EVENT:this.onevent(s);break;case ee.ACK:case ee.BINARY_ACK:this.onack(s);break;case ee.DISCONNECT:this.ondisconnect();break;case ee.CONNECT_ERROR:this.destroy();const r=new Error(s.data.message);r.data=s.data.data,this.emitReserved("connect_error",r);break}}onevent(s){const c=s.data||[];s.id!=null&&c.push(this.ack(s.id)),this.connected?this.emitEvent(c):this.receiveBuffer.push(Object.freeze(c))}emitEvent(s){if(this._anyListeners&&this._anyListeners.length){const c=this._anyListeners.slice();for(const r of c)r.apply(this,s)}super.emit.apply(this,s),this._pid&&s.length&&typeof s[s.length-1]=="string"&&(this._lastOffset=s[s.length-1])}ack(s){const c=this;let r=!1;return function(...d){r||(r=!0,c.packet({type:ee.ACK,id:s,data:d}))}}onack(s){const c=this.acks[s.id];typeof c=="function"&&(delete this.acks[s.id],c.withError&&s.data.unshift(null),c.apply(this,s.data))}onconnect(s,c){this.id=s,this.recovered=c&&this._pid===c,this._pid=c,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(s=>this.emitEvent(s)),this.receiveBuffer=[],this.sendBuffer.forEach(s=>{this.notifyOutgoingListeners(s),this.packet(s)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(s=>s()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:ee.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(s){return this.flags.compress=s,this}get volatile(){return this.flags.volatile=!0,this}timeout(s){return this.flags.timeout=s,this}onAny(s){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(s),this}prependAny(s){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(s),this}offAny(s){if(!this._anyListeners)return this;if(s){const c=this._anyListeners;for(let r=0;r<c.length;r++)if(s===c[r])return c.splice(r,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(s){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(s),this}prependAnyOutgoing(s){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(s),this}offAnyOutgoing(s){if(!this._anyOutgoingListeners)return this;if(s){const c=this._anyOutgoingListeners;for(let r=0;r<c.length;r++)if(s===c[r])return c.splice(r,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(s){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const c=this._anyOutgoingListeners.slice();for(const r of c)r.apply(this,s.data)}}}function zn(u){u=u||{},this.ms=u.min||100,this.max=u.max||1e4,this.factor=u.factor||2,this.jitter=u.jitter>0&&u.jitter<=1?u.jitter:0,this.attempts=0}zn.prototype.duration=function(){var u=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var s=Math.random(),c=Math.floor(s*this.jitter*u);u=(Math.floor(s*10)&1)==0?u-c:u+c}return Math.min(u,this.max)|0};zn.prototype.reset=function(){this.attempts=0};zn.prototype.setMin=function(u){this.ms=u};zn.prototype.setMax=function(u){this.max=u};zn.prototype.setJitter=function(u){this.jitter=u};class qr extends we{constructor(s,c){var r;super(),this.nsps={},this.subs=[],s&&typeof s=="object"&&(c=s,s=void 0),c=c||{},c.path=c.path||"/socket.io",this.opts=c,ii(this,c),this.reconnection(c.reconnection!==!1),this.reconnectionAttempts(c.reconnectionAttempts||1/0),this.reconnectionDelay(c.reconnectionDelay||1e3),this.reconnectionDelayMax(c.reconnectionDelayMax||5e3),this.randomizationFactor((r=c.randomizationFactor)!==null&&r!==void 0?r:.5),this.backoff=new zn({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(c.timeout==null?2e4:c.timeout),this._readyState="closed",this.uri=s;const d=c.parser||ep;this.encoder=new d.Encoder,this.decoder=new d.Decoder,this._autoConnect=c.autoConnect!==!1,this._autoConnect&&this.open()}reconnection(s){return arguments.length?(this._reconnection=!!s,s||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(s){return s===void 0?this._reconnectionAttempts:(this._reconnectionAttempts=s,this)}reconnectionDelay(s){var c;return s===void 0?this._reconnectionDelay:(this._reconnectionDelay=s,(c=this.backoff)===null||c===void 0||c.setMin(s),this)}randomizationFactor(s){var c;return s===void 0?this._randomizationFactor:(this._randomizationFactor=s,(c=this.backoff)===null||c===void 0||c.setJitter(s),this)}reconnectionDelayMax(s){var c;return s===void 0?this._reconnectionDelayMax:(this._reconnectionDelayMax=s,(c=this.backoff)===null||c===void 0||c.setMax(s),this)}timeout(s){return arguments.length?(this._timeout=s,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&this.backoff.attempts===0&&this.reconnect()}open(s){if(~this._readyState.indexOf("open"))return this;this.engine=new Gg(this.uri,this.opts);const c=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;const d=bt(c,"open",function(){r.onopen(),s&&s()}),m=y=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",y),s?s(y):this.maybeReconnectOnOpen()},g=bt(c,"error",m);if(this._timeout!==!1){const y=this._timeout,b=this.setTimeoutFn(()=>{d(),m(new Error("timeout")),c.close()},y);this.opts.autoUnref&&b.unref(),this.subs.push(()=>{this.clearTimeoutFn(b)})}return this.subs.push(d),this.subs.push(g),this}connect(s){return this.open(s)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const s=this.engine;this.subs.push(bt(s,"ping",this.onping.bind(this)),bt(s,"data",this.ondata.bind(this)),bt(s,"error",this.onerror.bind(this)),bt(s,"close",this.onclose.bind(this)),bt(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(s){try{this.decoder.add(s)}catch(c){this.onclose("parse error",c)}}ondecoded(s){si(()=>{this.emitReserved("packet",s)},this.setTimeoutFn)}onerror(s){this.emitReserved("error",s)}socket(s,c){let r=this.nsps[s];return r?this._autoConnect&&!r.active&&r.connect():(r=new hh(this,s,c),this.nsps[s]=r),r}_destroy(s){const c=Object.keys(this.nsps);for(const r of c)if(this.nsps[r].active)return;this._close()}_packet(s){const c=this.encoder.encode(s);for(let r=0;r<c.length;r++)this.engine.write(c[r],s.options)}cleanup(){this.subs.forEach(s=>s()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(s,c){var r;this.cleanup(),(r=this.engine)===null||r===void 0||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",s,c),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const s=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const c=this.backoff.duration();this._reconnecting=!0;const r=this.setTimeoutFn(()=>{s.skipReconnect||(this.emitReserved("reconnect_attempt",s.backoff.attempts),!s.skipReconnect&&s.open(d=>{d?(s._reconnecting=!1,s.reconnect(),this.emitReserved("reconnect_error",d)):s.onreconnect()}))},c);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){const s=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",s)}}const zl={};function ai(u,s){typeof u=="object"&&(s=u,u=void 0),s=s||{};const c=Yg(u,s.path||"/socket.io"),r=c.source,d=c.id,m=c.path,g=zl[d]&&m in zl[d].nsps,y=s.forceNew||s["force new connection"]||s.multiplex===!1||g;let b;return y?b=new qr(r,s):(zl[d]||(zl[d]=new qr(r,s)),b=zl[d]),c.query&&!s.query&&(s.query=c.queryKey),b.socket(c.path,s)}Object.assign(ai,{Manager:qr,Socket:hh,io:ai,connect:ai});const Rl="http://localhost:3001";let Cn="disconnected",mh=null,$t=null;const ni=[],li=[],Cr=()=>{$t||($t=ai(Rl),$t.on("connect",()=>{console.log("[WhatsApp] Conectado ao servidor backend")}),$t.on("disconnect",()=>{console.log("[WhatsApp] Desconectado do servidor backend")}),$t.on("whatsapp-status",u=>{console.log("[WhatsApp] Status recebido:",u),Hr(u.status,u.qrCode,u.error)}),$t.on("whatsapp-message",u=>{console.log("[WhatsApp] Mensagem recebida:",u);const s={id:u.id,text:u.text,sender:u.senderName||u.sender,timestamp:new Date(u.timestamp),isFromMe:u.isFromMe};ap(s)}))},Hr=(u,s,c)=>{Cn=u,mh=s||null,ni.forEach(r=>r(u,s,c))},ap=u=>{li.forEach(s=>s(u))},wn={connect:async()=>{try{if($t||Cr(),Cn==="connected"||Cn==="loading"||Cn==="connecting_qr"){console.warn("[WhatsApp] Connection attempt ignored, already connected or in a connection process.");return}console.log("[WhatsApp] Attempting to connect to backend...");const s=await(await fetch(`${Rl}/api/connect`,{method:"POST",headers:{"Content-Type":"application/json"}})).json();if(!s.success)throw new Error(s.message||"Falha ao conectar");console.log("[WhatsApp] Connection request sent successfully")}catch(u){console.error("[WhatsApp] Connection error:",u),Hr("error",null,u instanceof Error?u.message:"Erro de conexão")}},confirmQrScanned_mock:async()=>{console.warn("[WhatsApp] Esta função não é mais necessária com a implementação real")},disconnect:async()=>{try{console.log("[WhatsApp] Attempting to disconnect...");const s=await(await fetch(`${Rl}/api/disconnect`,{method:"POST",headers:{"Content-Type":"application/json"}})).json();if(!s.success)throw new Error(s.message||"Falha ao desconectar");console.log("[WhatsApp] Disconnected successfully")}catch(u){console.error("[WhatsApp] Disconnect error:",u),Hr("error",null,u instanceof Error?u.message:"Erro de desconexão")}},sendMessage:async(u,s)=>{try{if(Cn!=="connected")return console.warn("[WhatsApp] Cannot send message, not connected."),!1;console.log(`[WhatsApp] Sending message to ${u}: "${s}"`);const r=await(await fetch(`${Rl}/api/send-message`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({recipient:u,message:s})})).json();if(!r.success)throw new Error(r.message||"Falha ao enviar mensagem");return console.log("[WhatsApp] Message sent successfully"),!0}catch(c){return console.error("[WhatsApp] Send message error:",c),!1}},onStatusChange:u=>(ni.push(u),$t||Cr(),fetch(`${Rl}/api/status`).then(s=>s.json()).then(s=>{u(s.status,s.qrCode,s.error)}).catch(s=>{console.error("[WhatsApp] Error fetching initial status:",s),u("disconnected",null,"Erro ao conectar com o backend")}),()=>{const s=ni.indexOf(u);s>-1&&ni.splice(s,1)}),onMessage:u=>(li.push(u),$t||Cr(),()=>{const s=li.indexOf(u);s>-1&&li.splice(s,1)}),getCurrentStatus:()=>({status:Cn,qrCode:mh,error:void 0})},np=({lastBotResponseText:u})=>{const[s,c]=k.useState("requires_backend"),[r,d]=k.useState(null),[m,g]=k.useState(null),[y,b]=k.useState(null),[M,C]=k.useState(""),[R,O]=k.useState(!1),[Q,X]=k.useState([]),[D,B]=k.useState(null);k.useEffect(()=>{const te=wn.onStatusChange((ve,St,Ye)=>{c(ve),d(St||null),ve==="error"||ve==="connecting_qr"&&Ye||ve==="loading"&&Ye||ve==="requires_backend"?(g(Ye||null),b(null)):(g(null),b(Ye||null))}),q=wn.onMessage(ve=>{X(St=>[...St,ve].sort((Ye,Le)=>Ye.timestamp.getTime()-Le.timestamp.getTime()).slice(-10))}),ne=wn.getCurrentStatus();return c(ne.status),d(ne.qrCode||null),(ne.status==="requires_backend"||ne.error)&&g("An initial error state detected."),()=>{te(),q()}},[]);const J=()=>{g(null),b(null),B(null),wn.connect()},ie=()=>{g(null),b(null),wn.disconnect()},I=async()=>{if(!M.trim()){g("Recipient phone number is required.");return}if(!u){g("No bot response available to send.");return}if(s!=="connected"){g("Must be connected to WhatsApp to send messages.");return}O(!0),g(null),b(null),B(null);try{await wn.sendMessage(M,u)?B(`Mensagem enviada com sucesso para ${M} (simulado).`):g("Falha ao enviar mensagem (simulado). Não conectado ou outro problema.")}catch(te){g(`Erro ao enviar mensagem: ${te.message}`)}finally{O(!1)}},be=()=>{switch(s){case"connected":return"text-green-400";case"connecting_qr":case"loading":return"text-yellow-400";case"error":case"requires_backend":return"text-red-400";case"disconnected":return"text-gray-400";default:return"text-gray-200"}},He=()=>{switch(s){case"connected":return"fas fa-check-circle";case"connecting_qr":return"fas fa-qrcode";case"loading":return"fas fa-spinner fa-spin";case"error":return"fas fa-exclamation-triangle";case"requires_backend":return"fas fa-cogs";case"disconnected":return"fas fa-plug";default:return"fas fa-question-circle"}};return f.jsxs("div",{className:"bg-neutral-600 p-4 rounded-lg shadow space-y-3",children:[f.jsxs("h2",{className:"text-lg font-semibold text-teal-400 mb-2 flex items-center",children:[f.jsx("i",{className:"fab fa-whatsapp mr-2"})," WhatsApp Integration"]}),f.jsxs("div",{className:"text-xs text-green-300 p-2 bg-green-900 bg-opacity-50 rounded-md",children:[f.jsx("i",{className:"fas fa-info-circle mr-1"})," Conectado ao backend com",f.jsx("a",{href:"https://github.com/wppconnect-team/wppconnect",target:"_blank",rel:"noopener noreferrer",className:"underline hover:text-green-100",children:" WPPConnect "}),"para funcionalidade real do WhatsApp."]}),f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsx("span",{className:`font-medium ${be()}`,children:"Status:"}),f.jsxs("span",{className:`${be()}`,children:[f.jsx("i",{className:`${He()} mr-1`}),s.replace(/_/g," ")]})]}),m&&f.jsxs("p",{className:"text-sm text-red-400",children:[f.jsx("i",{className:"fas fa-exclamation-circle mr-1"}),"Erro: ",m]}),y&&!m&&f.jsxs("p",{className:"text-sm text-sky-300",children:[f.jsx("i",{className:"fas fa-info-circle mr-1"}),y]}),(s==="disconnected"||s==="error"&&!r)&&f.jsxs("button",{onClick:J,className:"w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-md transition-colors disabled:bg-gray-500",children:[f.jsx("i",{className:"fas fa-link mr-1"})," Conectar ao WhatsApp"]}),(s==="loading"||s==="connecting_qr"||s==="connected")&&f.jsxs("button",{onClick:ie,disabled:s==="loading"&&!r,className:"w-full bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-md transition-colors disabled:bg-gray-500",children:[f.jsx("i",{className:"fas fa-unlink mr-1"}),s==="loading"&&!r?"Cancelar Tentativa de Conexão":"Desconectar"]}),s==="connecting_qr"&&r&&f.jsxs("div",{className:"mt-2 p-2 border border-dashed border-gray-400 rounded text-center space-y-2",children:[f.jsxs("p",{className:"text-sm text-sky-300",children:[f.jsx("i",{className:"fas fa-qrcode mr-1"})," Escaneie este QR code com seu WhatsApp:"]}),f.jsx("div",{className:"bg-white p-2 rounded-md inline-block",children:f.jsx("img",{src:r,alt:"WhatsApp QR Code",className:"w-48 h-48 mx-auto"})}),f.jsx("p",{className:"text-xs text-gray-400 mt-2",children:"O QR code será atualizado automaticamente quando escaneado."})]}),s==="connected"&&f.jsxs("div",{className:"mt-3 pt-3 border-t border-neutral-500 space-y-2",children:[f.jsx("h3",{className:"text-md font-semibold text-gray-200",children:"Enviar Última Resposta do Bot via WhatsApp"}),u?f.jsxs(f.Fragment,{children:[f.jsxs("div",{children:[f.jsx("label",{htmlFor:"wa-recipient",className:"block text-sm font-medium text-gray-300 mb-1",children:"Número do Destinatário (ex: 5511999999999)"}),f.jsx("input",{type:"tel",id:"wa-recipient",value:M,onChange:te=>C(te.target.value),placeholder:"Digite o número do WhatsApp",className:"w-full p-2 border border-neutral-500 rounded-md bg-neutral-700 text-white placeholder-gray-400 focus:ring-2 focus:ring-teal-500"})]}),f.jsxs("p",{className:"text-xs text-gray-400 italic",children:['Será enviado: "',u.substring(0,50),u.length>50?"...":"",'"']}),f.jsx("button",{onClick:I,disabled:R||!M.trim()||!u,className:"w-full bg-teal-500 hover:bg-teal-600 text-white font-semibold py-2 px-4 rounded-md transition-colors disabled:bg-gray-500 disabled:opacity-70",children:R?f.jsxs(f.Fragment,{children:[f.jsx("i",{className:"fas fa-spinner fa-spin mr-1"})," Enviando..."]}):f.jsxs(f.Fragment,{children:[f.jsx("i",{className:"fab fa-whatsapp mr-1"})," Enviar via WhatsApp"]})}),D&&f.jsxs("p",{className:"text-sm text-green-400 mt-1",children:[f.jsx("i",{className:"fas fa-check-circle mr-1"}),D]})]}):f.jsx("p",{className:"text-sm text-gray-400",children:"Converse com o bot primeiro para ter uma resposta para enviar."})]}),Q.length>0&&f.jsxs("div",{className:"mt-3 pt-3 border-t border-neutral-500 space-y-1",children:[f.jsx("h4",{className:"text-sm font-semibold text-gray-300 mb-1",children:"WhatsApp Message Log (Mock):"}),f.jsx("div",{className:"max-h-32 overflow-y-auto bg-neutral-800 p-2 rounded text-xs space-y-1",children:Q.map(te=>f.jsxs("div",{className:`p-1 rounded ${te.isFromMe?"bg-teal-700 self-end ml-4 text-right":"bg-neutral-700 self-start mr-4"}`,children:[f.jsxs("span",{className:"font-bold",children:[te.isFromMe?"Me (Bot)":te.sender,": "]}),f.jsx("span",{children:te.text}),f.jsx("span",{className:`block text-neutral-400 text-[0.65rem] ${te.isFromMe?"text-left":"text-right"}`,children:te.timestamp.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})})]},te.id))})]})]})},lp=()=>{const[u,s]=k.useState([]),[c,r]=k.useState(null),[d,m]=k.useState([]),[g,y]=k.useState(null),[b,M]=k.useState(!1),[C,R]=k.useState(null),[O,Q]=k.useState(!1),[X,D]=k.useState(""),B=async()=>{M(!0),R(null);try{const ne=await(await fetch("http://localhost:3001/api/whatsapp/contacts")).json();ne.success?s(ne.data||[]):R(ne.message||"Erro ao carregar contatos")}catch(q){R("Erro ao conectar com o servidor"),console.error("Failed to load WhatsApp contacts:",q)}finally{M(!1)}},J=async q=>{M(!0);try{const ve=await(await fetch(`http://localhost:3001/api/whatsapp/contacts/${q}/messages`)).json();ve.success?m(ve.data||[]):R(ve.message||"Erro ao carregar mensagens")}catch(ne){R("Erro ao carregar mensagens"),console.error("Failed to load WhatsApp messages:",ne)}finally{M(!1)}},ie=async()=>{try{const ne=await(await fetch("http://localhost:3001/api/whatsapp/stats")).json();ne.success&&y(ne.data)}catch(q){console.error("Failed to load WhatsApp stats:",q)}},I=async q=>{if(!q.trim()){m([]);return}M(!0);try{const ve=await(await fetch(`http://localhost:3001/api/whatsapp/search?q=${encodeURIComponent(q)}`)).json();ve.success?(m(ve.data||[]),r(null)):R(ve.message||"Erro ao buscar mensagens")}catch(ne){R("Erro ao buscar mensagens"),console.error("Failed to search WhatsApp messages:",ne)}finally{M(!1)}};k.useEffect(()=>{O&&(B(),ie())},[O]);const be=q=>{r(q),D(""),J(q.id)},He=q=>q.length===13&&q.startsWith("55")?`+${q.slice(0,2)} (${q.slice(2,4)}) ${q.slice(4,9)}-${q.slice(9)}`:q,te=q=>new Date(q).toLocaleString("pt-BR");return f.jsxs("div",{className:"bg-neutral-700 rounded-lg shadow-lg",children:[f.jsxs("div",{className:"flex items-center justify-between p-4 cursor-pointer hover:bg-neutral-600 rounded-t-lg",onClick:()=>Q(!O),children:[f.jsxs("h2",{className:"text-lg font-semibold text-white flex items-center",children:[f.jsx("i",{className:"fab fa-whatsapp mr-2 text-green-400"}),"Histórico WhatsApp"]}),f.jsxs("div",{className:"flex items-center space-x-2",children:[g&&f.jsxs("div",{className:"text-xs text-gray-400",children:[g.total," mensagens"]}),f.jsx("button",{onClick:q=>{q.stopPropagation(),B(),ie()},className:"p-2 bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors",title:"Atualizar",children:f.jsx("i",{className:"fas fa-sync-alt"})}),f.jsx("i",{className:`fas fa-chevron-${O?"up":"down"} text-gray-400`})]})]}),O&&f.jsxs("div",{className:"border-t border-neutral-600",children:[g&&f.jsxs("div",{className:"p-4 bg-neutral-800 grid grid-cols-4 gap-4 text-center",children:[f.jsxs("div",{children:[f.jsx("div",{className:"text-lg font-bold text-white",children:g.total}),f.jsx("div",{className:"text-xs text-gray-400",children:"Total"})]}),f.jsxs("div",{children:[f.jsx("div",{className:"text-lg font-bold text-blue-400",children:g.user_messages}),f.jsx("div",{className:"text-xs text-gray-400",children:"Recebidas"})]}),f.jsxs("div",{children:[f.jsx("div",{className:"text-lg font-bold text-green-400",children:g.bot_messages}),f.jsx("div",{className:"text-xs text-gray-400",children:"Enviadas"})]}),f.jsxs("div",{children:[f.jsx("div",{className:"text-lg font-bold text-yellow-400",children:g.today}),f.jsx("div",{className:"text-xs text-gray-400",children:"Hoje"})]})]}),f.jsx("div",{className:"p-4 border-b border-neutral-600",children:f.jsxs("div",{className:"relative",children:[f.jsx("input",{type:"text",value:X,onChange:q=>D(q.target.value),onKeyPress:q=>q.key==="Enter"&&I(X),placeholder:"Buscar mensagens...",className:"w-full px-3 py-2 pl-10 bg-neutral-600 border border-neutral-500 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500"}),f.jsx("i",{className:"fas fa-search absolute left-3 top-3 text-gray-400"}),f.jsx("button",{onClick:()=>I(X),className:"absolute right-2 top-1 p-1 bg-green-500 hover:bg-green-600 text-white rounded transition-colors",children:f.jsx("i",{className:"fas fa-search text-xs"})})]})}),C&&f.jsxs("div",{className:"p-4 bg-red-900 bg-opacity-50 text-red-300 text-sm",children:[f.jsx("i",{className:"fas fa-exclamation-triangle mr-2"}),C]}),f.jsxs("div",{className:"flex max-h-96",children:[f.jsx("div",{className:"w-1/3 border-r border-neutral-600 overflow-y-auto",children:b?f.jsxs("div",{className:"p-4 text-center text-gray-400",children:[f.jsx("i",{className:"fas fa-spinner fa-spin mr-2"}),"Carregando..."]}):u.length===0?f.jsxs("div",{className:"p-4 text-center text-gray-400",children:[f.jsx("i",{className:"fas fa-address-book mr-2"}),"Nenhum contato encontrado"]}):u.map(q=>f.jsxs("div",{className:`p-3 border-b border-neutral-600 hover:bg-neutral-600 cursor-pointer transition-colors ${c?.id===q.id?"bg-neutral-600 border-l-4 border-l-green-400":""}`,onClick:()=>be(q),children:[f.jsx("div",{className:"text-white font-medium",children:q.name||"Sem nome"}),f.jsx("div",{className:"text-gray-400 text-sm",children:He(q.phone_number)}),q.last_message_at&&f.jsx("div",{className:"text-gray-500 text-xs mt-1",children:te(q.last_message_at)})]},q.id))}),f.jsx("div",{className:"w-2/3 overflow-y-auto",children:c?f.jsxs("div",{children:[f.jsxs("div",{className:"p-3 bg-neutral-800 border-b border-neutral-600",children:[f.jsx("div",{className:"text-white font-medium",children:c.name||"Sem nome"}),f.jsx("div",{className:"text-gray-400 text-sm",children:He(c.phone_number)})]}),f.jsx("div",{className:"p-4 space-y-3",children:d.length===0?f.jsxs("div",{className:"text-center text-gray-400",children:[f.jsx("i",{className:"fas fa-comments mr-2"}),"Nenhuma mensagem encontrada"]}):d.map(q=>f.jsxs("div",{className:`p-3 rounded-lg max-w-xs ${q.sender==="bot"?"bg-green-600 text-white ml-auto":"bg-neutral-600 text-white"}`,children:[f.jsx("div",{className:"text-sm",children:q.content}),f.jsx("div",{className:"text-xs opacity-70 mt-1",children:te(q.created_at)})]},q.id))})]}):X?f.jsx("div",{className:"p-4 space-y-3",children:d.length===0?f.jsxs("div",{className:"text-center text-gray-400",children:[f.jsx("i",{className:"fas fa-search mr-2"}),'Nenhuma mensagem encontrada para "',X,'"']}):d.map(q=>f.jsxs("div",{className:"p-3 bg-neutral-600 rounded-lg",children:[f.jsx("div",{className:"text-white text-sm",children:q.content}),f.jsxs("div",{className:"text-gray-400 text-xs mt-1",children:[te(q.created_at)," • ",q.sender==="bot"?"Enviada":"Recebida"]})]},q.id))}):f.jsxs("div",{className:"p-4 text-center text-gray-400",children:[f.jsx("i",{className:"fas fa-mouse-pointer mr-2"}),"Selecione um contato para ver as mensagens"]})})]})]})]})},sp=({lastBotResponseText:u})=>f.jsxs("div",{className:"p-6 space-y-8",children:[f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-4",children:f.jsx("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 18h.01M8 21l4-7 4 7M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"})})}),f.jsx("h2",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"Integração WhatsApp"}),f.jsx("p",{className:"text-gray-600 max-w-md mx-auto",children:"Configure e gerencie a integração com WhatsApp. Envie mensagens automaticamente e acompanhe o histórico de contatos."})]}),f.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6",children:[f.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[f.jsx("div",{className:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center",children:f.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})}),f.jsxs("div",{children:[f.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Enviar Mensagens"}),f.jsx("p",{className:"text-sm text-gray-600",children:"Configure números e envie respostas da IA"})]})]}),f.jsx(np,{lastBotResponseText:u})]}),f.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6",children:[f.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[f.jsx("div",{className:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center",children:f.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})})}),f.jsxs("div",{children:[f.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Histórico de Contatos"}),f.jsx("p",{className:"text-sm text-gray-600",children:"Visualize e gerencie contatos do WhatsApp"})]})]}),f.jsx(lp,{})]}),f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[f.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-4 text-center",children:[f.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2",children:f.jsx("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),f.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-1",children:"Status"}),f.jsx("p",{className:"text-xs text-gray-600",children:"Integração ativa"})]}),f.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-4 text-center",children:[f.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2",children:f.jsx("svg",{className:"w-4 h-4 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 19l9 2-9-18-9 18 9-2zm0 0v-8"})})}),f.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-1",children:"Mensagens"}),f.jsx("p",{className:"text-xs text-gray-600",children:"Envio automático"})]}),f.jsxs("div",{className:"bg-white border border-gray-200 rounded-lg p-4 text-center",children:[f.jsx("div",{className:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2",children:f.jsx("svg",{className:"w-4 h-4 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})})}),f.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-1",children:"Contatos"}),f.jsx("p",{className:"text-xs text-gray-600",children:"Gerenciamento"})]})]}),f.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:f.jsxs("div",{className:"flex items-start space-x-3",children:[f.jsx("svg",{className:"w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),f.jsxs("div",{children:[f.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-1",children:"Como usar"}),f.jsxs("ul",{className:"text-xs text-blue-800 space-y-1",children:[f.jsx("li",{children:'• Configure os números de WhatsApp na seção "Enviar Mensagens"'}),f.jsx("li",{children:"• As respostas da IA serão enviadas automaticamente para os contatos"}),f.jsx("li",{children:'• Acompanhe o histórico de mensagens na seção "Histórico de Contatos"'}),f.jsx("li",{children:'• Use a aba "Chat" para gerar respostas que serão enviadas via WhatsApp'})]})]})]})})]}),gh="https://lycyipyhmoerebdvpzbxc.supabase.co",ph="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5Y3lpcHlobW9lcmJkdnB6YnhjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MDM4OTAsImV4cCI6MjA2NjQ3OTg5MH0.mAjh58EJZYy1ordqYwqFAV-3eOCCFpH-i_HccxzWK4s",yh=gh.startsWith("http"),vh=ph.length>20;(!yh||!vh)&&(console.warn("⚠️  Supabase configuration missing or invalid. Database features will be disabled."),console.warn("Please set valid VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY in your .env.local file"));const Ct=yh&&vh?eh(gh,ph):null,Mt=()=>Ct!==null,Ol={async create(u,s={}){if(!Mt())return console.warn("Supabase not configured, skipping conversation creation"),{success:!1,error:"Database not configured"};try{const{data:c,error:r}=await Ct.from("conversations").insert([{title:u,metadata:s}]).select().single();if(r)throw r;return console.log("Conversation created:",c.id),{success:!0,data:c}}catch(c){return console.error("Failed to create conversation:",c.message),{success:!1,error:c.message}}},async list(u=50,s=0){if(!Mt())return{success:!1,error:"Database not configured"};try{const{data:c,error:r}=await Ct.from("conversations").select("*").order("updated_at",{ascending:!1}).range(s,s+u-1);if(r)throw r;return{success:!0,data:c}}catch(c){return console.error("Failed to list conversations:",c.message),{success:!1,error:c.message}}},async getById(u){if(!Mt())return{success:!1,error:"Database not configured"};try{const{data:s,error:c}=await Ct.from("conversations").select("*").eq("id",u).single();if(c)throw c;return{success:!0,data:s}}catch(s){return console.error("Failed to get conversation:",s.message),{success:!1,error:s.message}}},async update(u,s){if(!Mt())return{success:!1,error:"Database not configured"};try{const{data:c,error:r}=await Ct.from("conversations").update(s).eq("id",u).select().single();if(r)throw r;return{success:!0,data:c}}catch(c){return console.error("Failed to update conversation:",c.message),{success:!1,error:c.message}}},async delete(u){if(!Mt())return{success:!1,error:"Database not configured"};try{const{error:s}=await Ct.from("conversations").delete().eq("id",u);if(s)throw s;return{success:!0}}catch(s){return console.error("Failed to delete conversation:",s.message),{success:!1,error:s.message}}}},Is={async create(u,s,c,r={},d=[]){if(!Mt())return console.warn("Supabase not configured, skipping message creation"),{success:!1,error:"Database not configured"};try{const{data:m,error:g}=await Ct.from("messages").insert([{conversation_id:u,content:s,sender:c,metadata:r,sources:d}]).select().single();if(g)throw g;return await Ct.from("conversations").update({updated_at:new Date().toISOString()}).eq("id",u),{success:!0,data:m}}catch(m){return console.error("Failed to create message:",m.message),{success:!1,error:m.message}}},async listByConversation(u,s=100,c=0){if(!Mt())return{success:!1,error:"Database not configured"};try{const{data:r,error:d}=await Ct.from("messages").select("*").eq("conversation_id",u).order("created_at",{ascending:!0}).range(c,c+s-1);if(d)throw d;return{success:!0,data:r}}catch(r){return console.error("Failed to list messages:",r.message),{success:!1,error:r.message}}},async search(u,s=50){if(!Mt())return{success:!1,error:"Database not configured"};try{const{data:c,error:r}=await Ct.from("messages").select("*, conversations(title)").textSearch("content",u).order("created_at",{ascending:!1}).limit(s);if(r)throw r;return{success:!0,data:c}}catch(c){return console.error("Failed to search messages:",c.message),{success:!1,error:c.message}}},chatMessageToDb(u,s){return{conversation_id:s,content:u.text,sender:u.sender,metadata:{id:u.id,timestamp:u.timestamp},sources:u.sources||[]}},dbMessageToChat(u){return{id:u.metadata?.id||u.id,text:u.content,sender:u.sender,timestamp:new Date(u.created_at),sources:u.sources||[]}}},xh={async saveConversation(u,s){if(!Mt())return{success:!1,error:"Database not configured"};try{const c=s||`Conversa ${new Date().toLocaleString()}`,r=await Ol.create(c);if(!r.success)throw new Error(r.error);const d=r.data.id;for(const m of u){const g=Is.chatMessageToDb(m,d),y=await Is.create(g.conversation_id,g.content,g.sender,g.metadata,g.sources);y.success||console.warn("Failed to save message:",y.error)}return{success:!0,conversationId:d}}catch(c){return console.error("Failed to save conversation:",c.message),{success:!1,error:c.message}}},async loadConversation(u){if(!Mt())return{success:!1,error:"Database not configured"};try{const s=await Ol.getById(u);if(!s.success)throw new Error(s.error);const c=await Is.listByConversation(u);if(!c.success)throw new Error(c.error);return{success:!0,messages:c.data?.map(Is.dbMessageToChat)||[],conversation:s.data}}catch(s){return console.error("Failed to load conversation:",s.message),{success:!1,error:s.message}}}},ip=({onLoadConversation:u,onNewConversation:s,currentConversationId:c})=>{const[r,d]=k.useState([]),[m,g]=k.useState(!1),[y,b]=k.useState(null),[M,C]=k.useState(!1),R=async()=>{g(!0),b(null);try{const D=await Ol.list(20,0);D.success?d(D.data||[]):b(D.error||"Erro ao carregar conversas")}catch(D){b("Erro ao conectar com o banco de dados"),console.error("Failed to load conversations:",D)}finally{g(!1)}};k.useEffect(()=>{R()},[]);const O=async D=>{g(!0);try{const B=await xh.loadConversation(D);B.success&&B.messages?(u(B.messages,D),C(!1)):b(B.error||"Erro ao carregar conversa")}catch(B){b("Erro ao carregar conversa"),console.error("Failed to load conversation:",B)}finally{g(!1)}},Q=async(D,B)=>{if(B.stopPropagation(),!!confirm("Tem certeza que deseja deletar esta conversa?"))try{const J=await Ol.delete(D);J.success?(d(ie=>ie.filter(I=>I.id!==D)),D===c&&s()):b(J.error||"Erro ao deletar conversa")}catch(J){b("Erro ao deletar conversa"),console.error("Failed to delete conversation:",J)}},X=D=>{const B=new Date(D),ie=Math.abs(new Date().getTime()-B.getTime()),I=Math.ceil(ie/(1e3*60*60*24));return I===1?"Hoje":I===2?"Ontem":I<=7?`${I-1} dias atrás`:B.toLocaleDateString("pt-BR")};return f.jsxs("div",{className:"bg-neutral-700 rounded-lg shadow-lg",children:[f.jsxs("div",{className:"flex items-center justify-between p-4 cursor-pointer hover:bg-neutral-600 rounded-t-lg",onClick:()=>C(!M),children:[f.jsxs("h2",{className:"text-lg font-semibold text-white flex items-center",children:[f.jsx("i",{className:"fas fa-history mr-2 text-blue-400"}),"Histórico de Conversas"]}),f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsx("button",{onClick:D=>{D.stopPropagation(),s()},className:"p-2 bg-green-500 hover:bg-green-600 text-white rounded-md transition-colors",title:"Nova Conversa",children:f.jsx("i",{className:"fas fa-plus"})}),f.jsx("button",{onClick:D=>{D.stopPropagation(),R()},className:"p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors",title:"Atualizar",children:f.jsx("i",{className:"fas fa-sync-alt"})}),f.jsx("i",{className:`fas fa-chevron-${M?"up":"down"} text-gray-400`})]})]}),M&&f.jsxs("div",{className:"border-t border-neutral-600",children:[y&&f.jsxs("div",{className:"p-4 bg-red-900 bg-opacity-50 text-red-300 text-sm",children:[f.jsx("i",{className:"fas fa-exclamation-triangle mr-2"}),y]}),m?f.jsxs("div",{className:"p-4 text-center text-gray-400",children:[f.jsx("i",{className:"fas fa-spinner fa-spin mr-2"}),"Carregando..."]}):r.length===0?f.jsxs("div",{className:"p-4 text-center text-gray-400",children:[f.jsx("i",{className:"fas fa-comments mr-2"}),"Nenhuma conversa salva"]}):f.jsx("div",{className:"max-h-96 overflow-y-auto",children:r.map(D=>f.jsx("div",{className:`p-3 border-b border-neutral-600 hover:bg-neutral-600 cursor-pointer transition-colors ${D.id===c?"bg-neutral-600 border-l-4 border-l-blue-400":""}`,onClick:()=>O(D.id),children:f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsxs("div",{className:"flex-1 min-w-0",children:[f.jsx("h3",{className:"text-white font-medium truncate",children:D.title}),f.jsx("p",{className:"text-gray-400 text-xs mt-1",children:X(D.updated_at)})]}),f.jsx("button",{onClick:B=>Q(D.id,B),className:"ml-2 p-1 text-red-400 hover:text-red-300 hover:bg-red-900 hover:bg-opacity-50 rounded transition-colors",title:"Deletar conversa",children:f.jsx("i",{className:"fas fa-trash text-xs"})})]})},D.id))})]})]})},Mn={pessoal:{nome:"Rafaela de Nilda",cargo:"Vereadora",municipio:"Parnamirim",estado:"Rio Grande do Norte",mandato:"2021-2024"},persona:{tom:["acolhedor e empático","profissional e competente","próximo ao cidadão","transparente e honesto","determinado e proativo"],valores:["transparência na gestão pública","participação popular","justiça social","desenvolvimento sustentável","direitos humanos","ética na política"],linguagem:["clara e acessível","respeitosa e inclusiva","didática quando necessário","técnica quando apropriado","calorosa e humana"]},municipio:{nome:"Parnamirim",regiao:"Região Metropolitana de Natal",populacao:"Aproximadamente 267.000 habitantes",desafios:["mobilidade urbana","saneamento básico","habitação popular","segurança pública","educação de qualidade","saúde pública","preservação ambiental"]},atuacao:{areas_prioritarias:["Assistência Social e Direitos Humanos","Educação e Cultura","Saúde Pública","Meio Ambiente e Sustentabilidade","Desenvolvimento Urbano","Participação Popular e Transparência","Direitos da Mulher","Políticas para Terceira Idade","Juventude e Esporte"]},atendimento:{horarios:"Segunda a sexta, 8h às 17h"}};function Mr(u="geral"){const s=Mn,c=`Você é a ${s.pessoal.cargo} ${s.pessoal.nome}, representante do povo de ${s.pessoal.municipio}/${s.pessoal.estado} na Câmara Municipal.

IDENTIDADE:
- Nome: ${s.pessoal.nome}
- Cargo: ${s.pessoal.cargo} de ${s.pessoal.municipio}/${s.pessoal.estado}
- Mandato: ${s.pessoal.mandato}

PERSONA E VALORES:
- Tom: ${s.persona.tom.join(", ")}
- Valores: ${s.persona.valores.join(", ")}
- Linguagem: ${s.persona.linguagem.join(", ")}

ÁREAS DE ATUAÇÃO:
${s.atuacao.areas_prioritarias.map(r=>`- ${r}`).join(`
`)}

CONHECIMENTO SOBRE PARNAMIRIM:
- População: ${s.municipio.populacao}
- Região: ${s.municipio.regiao}
- Principais desafios: ${s.municipio.desafios.join(", ")}`;switch(u){case"rag":return`${c}

INSTRUÇÕES PARA USO DE DOCUMENTOS:
- Use as informações dos documentos oficiais do gabinete quando disponíveis
- Sempre cite que está baseando na documentação oficial
- Se o documento não contém a informação, seja transparente
- Para questões gerais, responda com base em seu conhecimento como vereadora

Responda sempre como a própria Vereadora Rafaela, usando informações oficiais quando disponíveis.`;case"whatsapp":return`${c}

INSTRUÇÕES PARA WHATSAPP:
- Mantenha respostas concisas mas completas
- Use linguagem acessível e próxima
- Seja empática com as demandas dos cidadãos
- Ofereça orientações práticas
- Quando necessário, solicite mais informações
- Encaminhe para atendimento presencial quando apropriado

Responda como a própria Vereadora Rafaela, demonstrando interesse genuíno pelas questões dos cidadãos.`;default:return`${c}

DIRETRIZES GERAIS:
- Sempre se apresente como Vereadora Rafaela de Nilda
- Demonstre conhecimento sobre Parnamirim e suas necessidades
- Seja empática e acolhedora com as demandas
- Ofereça orientações práticas sobre serviços públicos
- Explique processos legislativos de forma didática
- Incentive a participação popular
- Quando não souber algo específico, seja honesta e ofereça encaminhamento

Responda sempre como se fosse a própria Vereadora Rafaela.`}}const cp=({ragStatus:u="initializing",documentsCount:s=0})=>{const c=Mn,r=()=>{switch(u){case"ready":return"text-green-600";case"error":return"text-red-600";default:return"text-yellow-600"}},d=()=>{switch(u){case"ready":return"Sistema Ativo";case"error":return"Sistema com Erro";default:return"Inicializando..."}};return f.jsxs("div",{className:"bg-gradient-to-r from-blue-800 to-blue-900 text-white p-6 rounded-lg shadow-lg mb-6",children:[f.jsxs("div",{className:"flex items-center justify-between mb-4",children:[f.jsxs("div",{className:"flex items-center space-x-4",children:[f.jsx("div",{className:"w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center text-2xl font-bold",children:c.pessoal.nome.split(" ").map(m=>m[0]).join("")}),f.jsxs("div",{children:[f.jsx("h1",{className:"text-2xl font-bold",children:c.pessoal.nome}),f.jsxs("p",{className:"text-blue-200",children:[c.pessoal.cargo," de ",c.pessoal.municipio,"/",c.pessoal.estado]}),f.jsxs("p",{className:"text-blue-300 text-sm",children:["Mandato: ",c.pessoal.mandato]})]})]}),f.jsxs("div",{className:"text-right",children:[f.jsx("div",{className:`text-sm font-medium ${r()}`,children:d()}),s>0&&f.jsxs("div",{className:"text-blue-200 text-xs mt-1",children:[s," documentos indexados"]})]})]}),f.jsxs("div",{className:"border-t border-blue-700 pt-4",children:[f.jsx("h3",{className:"text-sm font-semibold text-blue-200 mb-2",children:"Principais Áreas de Atuação:"}),f.jsx("div",{className:"flex flex-wrap gap-2",children:c.atuacao.areas_prioritarias.slice(0,6).map((m,g)=>f.jsx("span",{className:"bg-blue-700 text-blue-100 px-3 py-1 rounded-full text-xs",children:m},g))})]}),f.jsx("div",{className:"border-t border-blue-700 pt-3 mt-3",children:f.jsxs("div",{className:"flex flex-wrap gap-4 text-sm text-blue-200",children:[f.jsxs("div",{className:"flex items-center space-x-1",children:[f.jsx("span",{children:"📍"}),f.jsxs("span",{children:[c.municipio.nome," - ",c.municipio.populacao]})]}),f.jsxs("div",{className:"flex items-center space-x-1",children:[f.jsx("span",{children:"🕒"}),f.jsx("span",{children:c.atendimento.horarios})]}),f.jsxs("div",{className:"flex items-center space-x-1",children:[f.jsx("span",{children:"💬"}),f.jsx("span",{children:"Atendimento via WhatsApp disponível"})]})]})})]})},rp="gemini-2.5-flash",up="AIzaSyAchEIEhbmB8mrF3DUZBWU56Tz1Vb6GYCA";let zr=null;function op(){if(!zr)try{zr=new Lr({apiKey:up})}catch(u){return console.error("Failed to initialize GoogleGenAI client:",u),null}return zr}const Fd=async(u,s,c,r=!1,d)=>{const m=op();if(!m)return{text:"Error: Gemini API client is not initialized. Please ensure the VITE_GEMINI_API_KEY is correctly configured."};if(!u.trim()&&!d)return{text:"Error: No input provided (neither text nor audio)."};try{let g;d&&!u.trim()?g="Process and respond to the following audio.":g=u,c&&(g=`CONTEXT DOCUMENT:
\`\`\`
${c}
\`\`\`

USER QUESTION/REQUEST:
${g}`);const y=[];g&&y.push(g),d&&y.push({inlineData:{mimeType:d.mimeType,data:d.data}});const b={model:rp,contents:y.length===1?y[0]:y,systemInstruction:s,generationConfig:{temperature:.7,topP:.95,topK:64}};r&&(b.tools=[{googleSearch:{}}]);const M=await m.models.generateContent(b),C=M.text||"",R=M.candidates?.[0]?.groundingMetadata?.groundingChunks||[];return{text:C,sources:R}}catch(g){return console.error("Gemini API Error:",g),g instanceof Error?g.message.includes("API key not valid")||g.message.includes("API_KEY_INVALID")?{text:"Error: Invalid Gemini API Key. Please check your configuration."}:{text:`Error communicating with Gemini: ${g.message}`}:{text:"An unknown error occurred while contacting Gemini."}}},bh={chunkSize:1e3,chunkOverlap:200,maxChunks:10,embeddingModel:"text-embedding-004",retrievalStrategy:"hybrid",rerankingEnabled:!0,contextOptimization:!0};class fp{constructor(s,c=bh){this.documentStore=new Map,this.vectorIndex=new Map,this.bm25Index=new Map,this.diversityFilter=(r,d,m)=>!0,this.config=c,this.aiClient=new Lr({apiKey:s})}async ingestDocument(s,c){console.log(`[RAG] Iniciando ingestão: ${c.title||c.source}`);const r=this.cleanAndNormalizeContent(s),d=await this.intelligentChunking(r,c),m=await this.generateEmbeddings(d),g=this.generateDocumentId(c);return this.documentStore.set(g,m),await this.indexForBM25(m),console.log(`[RAG] Documento ingerido: ${d.length} chunks criados`),d.map(y=>y.id)}async intelligentChunking(s,c){const r=[],d=this.splitBySections(s);for(let m=0;m<d.length;m++){const g=d[m];g.length<=this.config.chunkSize?r.push(this.createChunk(g,c,m)):this.splitWithOverlap(g,this.config.chunkSize,this.config.chunkOverlap).forEach((b,M)=>{r.push(this.createChunk(b,c,m,M))})}return r}async generateEmbeddings(s){console.log(`[RAG] Gerando embeddings para ${s.length} chunks`);const c=[];for(const r of s)try{const d=this.combineContentAndMetadata(r),m=await this.generateSingleEmbedding(d);c.push({...r,embedding:m})}catch(d){console.error(`[RAG] Erro ao gerar embedding para chunk ${r.id}:`,d),c.push(r)}return c}async retrieveRelevantChunks(s,c=this.config.maxChunks){const r=Date.now();console.log(`[RAG] Iniciando retrieval híbrido para: "${s}"`);const d=await this.generateSingleEmbedding(s),m=await this.vectorSearch(d,c*2),g=await this.bm25Search(s,c*2),y=this.fuseResults(m,g,c);let b=y;this.config.rerankingEnabled&&(b=await this.rerankResults(s,y));const M=Date.now()-r;return{chunks:b.slice(0,c),totalFound:b.length,strategy:this.config.retrievalStrategy,processingTime:M,confidence:this.calculateConfidence(b)}}async rerankResults(s,c){console.log(`[RAG] Aplicando reranking a ${c.length} chunks`);const r=[];for(const d of c){const m=await this.calculateSemanticRelevance(s,d.content);r.push({...d,score:m})}return r.sort((d,m)=>(m.score||0)-(d.score||0)).filter(this.diversityFilter)}optimizeContext(s,c=4e3){if(!this.config.contextOptimization)return s;console.log(`[RAG] Otimizando contexto para ${c} tokens`);let r=0;const d=[];for(const m of s){const g=this.estimateTokens(m.content);if(r+g<=c)d.push(m),r+=g;else{const y=this.summarizeChunk(m,c-r);y&&d.push(y);break}}return d}cleanAndNormalizeContent(s){return s.replace(/\s+/g," ").replace(/\n\s*\n/g,`

`).trim()}splitBySections(s){return s.split(/\n\s*\n/).filter(c=>c.trim().length>0)}splitWithOverlap(s,c,r){const d=[];let m=0;for(;m<s.length;){const g=Math.min(m+c,s.length);d.push(s.slice(m,g)),m=g-r}return d}createChunk(s,c,r,d){return{id:`${this.generateDocumentId(c)}_${r}${d!==void 0?`_${d}`:""}`,content:s.trim(),metadata:{...c,section:`Section ${r}${d!==void 0?`.${d}`:""}`}}}generateDocumentId(s){return`${s.type}_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}combineContentAndMetadata(s){return`${[s.metadata.title,s.metadata.category,s.metadata.tags?.join(" ")].filter(Boolean).join(" ")} ${s.content}`.trim()}async generateSingleEmbedding(s){return new Array(768).fill(0).map(()=>Math.random())}async vectorSearch(s,c){const r=[];for(const[d,m]of this.documentStore)for(const g of m)if(g.embedding){const y=this.cosineSimilarity(s,g.embedding);r.push({...g,score:y})}return r.sort((d,m)=>(m.score||0)-(d.score||0)).slice(0,c)}async bm25Search(s,c){const r=s.toLowerCase().split(/\s+/),d=[];for(const[m,g]of this.documentStore)for(const y of g){const b=y.content.toLowerCase();let M=0;for(const C of r){const R=(b.match(new RegExp(C,"g"))||[]).length;M+=R}M>0&&d.push({...y,score:M})}return d.sort((m,g)=>(g.score||0)-(m.score||0)).slice(0,c)}fuseResults(s,c,r){const d=new Map,m=new Map;return s.forEach((g,y)=>{const b=1/(60+y+1);d.set(g.id,(d.get(g.id)||0)+b),m.set(g.id,g)}),c.forEach((g,y)=>{const b=1/(60+y+1);d.set(g.id,(d.get(g.id)||0)+b),m.set(g.id,g)}),Array.from(d.entries()).sort((g,y)=>y[1]-g[1]).slice(0,r).map(([g,y])=>({...m.get(g),score:y}))}cosineSimilarity(s,c){const r=s.reduce((g,y,b)=>g+y*c[b],0),d=Math.sqrt(s.reduce((g,y)=>g+y*y,0)),m=Math.sqrt(c.reduce((g,y)=>g+y*y,0));return r/(d*m)}async indexForBM25(s){console.log(`[RAG] Indexando ${s.length} chunks para BM25`)}async calculateSemanticRelevance(s,c){return Math.random()}estimateTokens(s){return Math.ceil(s.length/4)}summarizeChunk(s,c){if(this.estimateTokens(s.content)<=c)return s;const r=c*4;return{...s,content:s.content.slice(0,r)+"..."}}calculateConfidence(s){if(s.length===0)return 0;const c=s.reduce((r,d)=>r+(d.score||0),0)/s.length;return Math.min(c*100,100)}}typeof window<"u"&&(Z0.workerSrc="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.10.38/pdf.worker.min.js");class dp{constructor(s){this.apiSources=new Map,this.config=s,this.setupDefaultAPISources()}async processFile(s){console.log(`[Ingestão] Processando arquivo: ${s.name}`),this.validateFile(s);const c=this.detectFileType(s);let r="",d={};switch(c){case"pdf":const g=await this.processPDF(s);r=g.content,d=g.extractedData;break;case"txt":r=await this.processTextFile(s);break;case"xlsx":case"csv":const y=await this.processSpreadsheet(s);r=y.content,d=y.extractedData;break;case"docx":r=await this.processWordDocument(s);break;case"image":const b=await this.processImage(s);r=b.content,d=b.extractedData;break;default:throw new Error(`Tipo de arquivo não suportado: ${c}`)}this.config.cleanContent&&(r=this.cleanContent(r));const m=await this.extractMetadata(s,c,r);return this.config.detectLanguage&&(m.language=this.detectLanguage(r)),{id:this.generateDocumentId(),originalName:s.name,content:r,metadata:{...m,type:c,size:s.size,source:`upload_${s.name}`,processingDate:new Date},extractedData:d}}async processPDF(s){const c=await s.arrayBuffer(),r=await K0({data:c}).promise;let d="";const m={pages:r.numPages,images:[],links:[]};for(let g=1;g<=r.numPages;g++){const y=await r.getPage(g),M=(await y.getTextContent()).items.map(R=>R.str).join(" ");d+=`

--- Página ${g} ---
${M}`;const C=await y.getAnnotations();for(const R of C)R.url&&m.links.push(R.url)}return{content:d.trim(),extractedData:m}}async processSpreadsheet(s){const c=await s.text();let r="";const d={tables:[],rows:0,columns:0};if(s.name.endsWith(".csv")){const m=c.split(`
`);d.rows=m.length;const g=m[0]?.split(",")||[];d.columns=g.length,r=`Planilha CSV com ${m.length} linhas e ${g.length} colunas.

`,r+=`Cabeçalhos: ${g.join(", ")}

`;for(let y=1;y<Math.min(6,m.length);y++){const b=m[y].split(",");r+=`Linha ${y}: ${b.join(" | ")}
`}d.tables.push({headers:g,rows:m.slice(1).map(y=>y.split(","))})}return{content:r,extractedData:d}}async processImage(s){console.log(`[Ingestão] Processando imagem: ${s.name}`);const c={imageType:s.type,ocrText:"",detectedObjects:[],faces:[]};return{content:`Imagem processada: ${s.name}. 
    Tipo: ${s.type}. 
    Tamanho: ${(s.size/1024).toFixed(2)} KB.
    [OCR seria aplicado aqui para extrair texto da imagem]`,extractedData:c}}async processTextFile(s){return await s.text()}async processWordDocument(s){return console.log(`[Ingestão] Processando documento Word: ${s.name}`),`Documento Word processado: ${s.name}. 
    [Conteúdo seria extraído aqui usando mammoth.js ou similar]`}async ingestFromAPI(s){const c=this.apiSources.get(s);if(!c)throw new Error(`Fonte de API não encontrada: ${s}`);console.log(`[Ingestão] Coletando dados da API: ${c.name}`);try{const r=await fetch(c.url,{method:c.method,headers:c.headers||{},body:c.method==="POST"?JSON.stringify(c.params):void 0});if(!r.ok)throw new Error(`Erro na API ${c.name}: ${r.status}`);const d=await r.json(),m=c.dataPath?this.extractDataByPath(d,c.dataPath):d;return this.convertAPIDataToDocuments(m,c)}catch(r){throw console.error(`[Ingestão] Erro ao processar API ${c.name}:`,r),r}}setupDefaultAPISources(){this.apiSources.set("camara_projetos",{name:"Projetos da Câmara",url:"https://api.camara.parnamirim.rn.gov.br/projetos",method:"GET",headers:{Accept:"application/json"},dataPath:"$.projetos",updateFrequency:"daily"}),this.apiSources.set("transparencia",{name:"Portal da Transparência",url:"https://transparencia.parnamirim.rn.gov.br/api/gastos",method:"GET",headers:{Accept:"application/json"},dataPath:"$.gastos",updateFrequency:"weekly"}),this.apiSources.set("noticias_municipio",{name:"Notícias do Município",url:"https://parnamirim.rn.gov.br/feed/rss",method:"GET",updateFrequency:"hourly"})}validateFile(s){const c=this.config.maxFileSize*1024*1024;if(s.size>c)throw new Error(`Arquivo muito grande. Máximo: ${this.config.maxFileSize}MB`);const r=this.detectFileType(s);if(!this.config.supportedFormats.includes(r))throw new Error(`Formato não suportado: ${r}`)}detectFileType(s){const c=s.name.split(".").pop()?.toLowerCase()||"";return{pdf:"pdf",txt:"txt",docx:"docx",doc:"docx",xlsx:"xlsx",xls:"xlsx",csv:"csv",jpg:"image",jpeg:"image",png:"image",gif:"image",mp3:"audio",wav:"audio",mp4:"video",avi:"video"}[c]||"unknown"}cleanContent(s){return s.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g,"").replace(/\s+/g," ").replace(/\n\s*\n\s*\n/g,`

`).trim()}async extractMetadata(s,c,r){const d={type:c,size:s.size,processingDate:new Date};if(this.config.extractMetadata){const m=r.split(`
`).filter(g=>g.trim());m.length>0&&(d.title=m[0].substring(0,100)),d.keywords=this.extractKeywords(r),d.category=this.categorizeContent(r)}return d}detectLanguage(s){const c=["que","para","com","uma","por","não","dos","das","como","mais"],r=["the","and","for","are","but","not","you","all","can","had"],d=s.toLowerCase().split(/\s+/).slice(0,100);let m=0,g=0;return d.forEach(y=>{c.includes(y)&&m++,r.includes(y)&&g++}),m>g?"pt-BR":"en"}extractKeywords(s){const c=s.toLowerCase().replace(/[^\w\s]/g,"").split(/\s+/).filter(d=>d.length>3),r={};return c.forEach(d=>{r[d]=(r[d]||0)+1}),Object.entries(r).sort((d,m)=>m[1]-d[1]).slice(0,10).map(([d])=>d)}categorizeContent(s){const c={legislacao:["lei","projeto","emenda","decreto","portaria","resolução"],orcamento:["orçamento","verba","recurso","gasto","despesa","receita"],social:["assistência","social","família","criança","idoso","deficiente"],saude:["saúde","hospital","posto","médico","enfermeiro","medicamento"],educacao:["educação","escola","professor","aluno","ensino","creche"],infraestrutura:["obra","rua","asfalto","saneamento","água","esgoto"]},r=s.toLowerCase();let d="geral",m=0;return Object.entries(c).forEach(([g,y])=>{const b=y.reduce((M,C)=>{const R=(r.match(new RegExp(C,"g"))||[]).length;return M+R},0);b>m&&(m=b,d=g)}),d}extractDataByPath(s,c){return s}convertAPIDataToDocuments(s,c){const r=[];return Array.isArray(s)?s.forEach((d,m)=>{r.push({id:this.generateDocumentId(),originalName:`${c.name}_${m}`,content:JSON.stringify(d,null,2),metadata:{type:"api",size:JSON.stringify(d).length,source:c.name,category:"api_data",processingDate:new Date}})}):r.push({id:this.generateDocumentId(),originalName:c.name,content:JSON.stringify(s,null,2),metadata:{type:"api",size:JSON.stringify(s).length,source:c.name,category:"api_data",processingDate:new Date}}),r}generateDocumentId(){return`doc_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}addAPISource(s,c){this.apiSources.set(s,c)}removeAPISource(s){this.apiSources.delete(s)}listAPISources(){return Array.from(this.apiSources.keys())}}class hp{constructor(s,c,r){this.cache=new Map,this.bm25Index=new Map,this.supabase=eh(s,c),this.config=r,this.initializeDatabase()}async initializeDatabase(){console.log("[VectorStore] Inicializando banco de dados vetorial...");try{await this.supabase.rpc("create_extension_if_not_exists",{extension_name:"vector"});const{error:s}=await this.supabase.rpc("create_vector_chunks_table",{dimensions:this.config.dimensions});s?console.error("[VectorStore] Erro ao criar tabela:",s):console.log("[VectorStore] Banco de dados vetorial inicializado com sucesso")}catch(s){console.error("[VectorStore] Erro na inicialização:",s)}}async storeChunks(s){console.log(`[VectorStore] Armazenando ${s.length} chunks...`);const c=[];for(const r of s)try{const{data:d,error:m}=await this.supabase.from("vector_chunks").insert({id:r.id,content:r.content,embedding:r.embedding,metadata:r.metadata,created_at:new Date().toISOString()}).select("id").single();m?console.error(`[VectorStore] Erro ao armazenar chunk ${r.id}:`,m):(c.push(d.id),this.indexChunkForBM25(r))}catch(d){console.error(`[VectorStore] Erro inesperado ao armazenar chunk ${r.id}:`,d)}return console.log(`[VectorStore] ${c.length} chunks armazenados com sucesso`),c}async vectorSearch(s,c=10,r=.7,d){console.log(`[VectorStore] Executando busca vetorial (limite: ${c}, threshold: ${r})`);try{let m=this.supabase.from("vector_chunks").select("*, similarity").rpc("match_chunks",{query_embedding:s,match_threshold:r,match_count:c});d&&(m=this.applyFilters(m,d));const{data:g,error:y}=await m;return y?(console.error("[VectorStore] Erro na busca vetorial:",y),[]):g.map((b,M)=>({chunk:{id:b.id,content:b.content,metadata:b.metadata,embedding:b.embedding},score:b.similarity,rank:M+1,searchType:"vector"}))}catch(m){return console.error("[VectorStore] Erro inesperado na busca vetorial:",m),[]}}async textSearch(s,c=10,r){console.log(`[VectorStore] Executando busca textual: "${s}"`);try{let d=this.supabase.from("vector_chunks").select("*").textSearch("content",s,{type:"websearch",config:"portuguese"}).limit(c);r&&(d=this.applyFilters(d,r));const{data:m,error:g}=await d;return g?(console.error("[VectorStore] Erro na busca textual:",g),[]):m.map((y,b)=>{const M=this.calculateBM25Score(s,y.content);return{chunk:{id:y.id,content:y.content,metadata:y.metadata,embedding:y.embedding},score:M,rank:b+1,searchType:"text"}}).sort((y,b)=>b.score-y.score)}catch(d){return console.error("[VectorStore] Erro inesperado na busca textual:",d),[]}}async hybridSearch(s){console.log("[VectorStore] Executando busca híbrida...");const c=[];if(s.vector){const d=await this.vectorSearch(s.vector,s.limit||20,s.threshold||.7,s.filters);c.push(...d)}if(s.text){const d=await this.textSearch(s.text,s.limit||20,s.filters);c.push(...d)}return this.fuseSearchResults(c,s.limit||10).map(d=>({...d,searchType:"hybrid"}))}async temporalSearch(s,c=.3){console.log("[VectorStore] Executando busca temporal...");const r=await this.hybridSearch(s),d=new Date;return r.map(m=>{const g=new Date(m.chunk.metadata.date||d),y=(d.getTime()-g.getTime())/(1e3*60*60*24),b=Math.exp(-y/30),M=m.score*(1-c)+b*c;return{...m,score:M}}).sort((m,g)=>g.score-m.score)}async graphSearch(s,c=2,r=10){console.log(`[VectorStore] Executando busca por grafo a partir de ${s}`);try{const{data:d,error:m}=await this.supabase.from("vector_chunks").select("*").eq("id",s).single();return m||!d?(console.error("[VectorStore] Chunk inicial não encontrado:",m),[]):(await this.vectorSearch(d.embedding,r*2,.6)).filter(y=>y.chunk.id!==s).slice(0,r)}catch(d){return console.error("[VectorStore] Erro na busca por grafo:",d),[]}}async updateChunk(s,c){try{const{error:r}=await this.supabase.from("vector_chunks").update({content:c.content,embedding:c.embedding,metadata:c.metadata,updated_at:new Date().toISOString()}).eq("id",s);return r?(console.error(`[VectorStore] Erro ao atualizar chunk ${s}:`,r),!1):(c.content&&this.updateBM25Index(s,c.content),!0)}catch(r){return console.error(`[VectorStore] Erro inesperado ao atualizar chunk ${s}:`,r),!1}}async deleteChunk(s){try{const{error:c}=await this.supabase.from("vector_chunks").delete().eq("id",s);return c?(console.error(`[VectorStore] Erro ao deletar chunk ${s}:`,c),!1):(this.bm25Index.delete(s),!0)}catch(c){return console.error(`[VectorStore] Erro inesperado ao deletar chunk ${s}:`,c),!1}}async getIndexStats(){try{const{data:s,error:c}=await this.supabase.from("vector_chunks").select("metadata, created_at");if(c)return console.error("[VectorStore] Erro ao obter estatísticas:",c),this.getEmptyStats();const r={totalDocuments:0,totalChunks:s.length,indexSize:0,lastUpdated:new Date,categories:{},sources:{}},d=new Set;return s.forEach(m=>{m.metadata?.source&&d.add(m.metadata.source);const g=m.metadata?.category||"uncategorized";r.categories[g]=(r.categories[g]||0)+1;const y=m.metadata?.source||"unknown";r.sources[y]=(r.sources[y]||0)+1;const b=new Date(m.created_at);b>r.lastUpdated&&(r.lastUpdated=b)}),r.totalDocuments=d.size,r.indexSize=s.length*this.config.dimensions*4,r}catch(s){return console.error("[VectorStore] Erro inesperado ao obter estatísticas:",s),this.getEmptyStats()}}applyFilters(s,c){return c?.category&&(s=s.in("metadata->category",c.category)),c?.source&&(s=s.in("metadata->source",c.source)),c?.type&&(s=s.in("metadata->type",c.type)),c?.dateRange&&(s=s.gte("created_at",c.dateRange.start.toISOString()).lte("created_at",c.dateRange.end.toISOString())),s}fuseSearchResults(s,c){const r=new Map,d=s.filter(g=>g.searchType==="vector"),m=s.filter(g=>g.searchType==="text");return d.forEach((g,y)=>{const b=1/(60+y+1),M=r.get(g.chunk.id);r.set(g.chunk.id,{result:g,score:(M?.score||0)+b})}),m.forEach((g,y)=>{const b=1/(60+y+1),M=r.get(g.chunk.id);r.set(g.chunk.id,{result:g,score:(M?.score||0)+b})}),Array.from(r.values()).sort((g,y)=>y.score-g.score).slice(0,c).map(({result:g,score:y},b)=>({...g,score:y,rank:b+1}))}indexChunkForBM25(s){const c=s.content.toLowerCase().replace(/[^\w\s]/g,"").split(/\s+/).filter(r=>r.length>2);this.bm25Index.set(s.id,{terms:c,termFreq:this.calculateTermFrequency(c),docLength:c.length})}updateBM25Index(s,c){const r=c.toLowerCase().replace(/[^\w\s]/g,"").split(/\s+/).filter(d=>d.length>2);this.bm25Index.set(s,{terms:r,termFreq:this.calculateTermFrequency(r),docLength:r.length})}calculateTermFrequency(s){const c={};return s.forEach(r=>{c[r]=(c[r]||0)+1}),c}calculateBM25Score(s,c){const r=s.toLowerCase().split(/\s+/),d=c.toLowerCase().split(/\s+/);let m=0;const g=1.2,y=.75,b=100;return r.forEach(M=>{const C=d.filter(R=>R===M).length;if(C>0){const R=Math.log(1001/(C+1)),O=C*(g+1)/(C+g*(1-y+y*(d.length/b)));m+=R*O}}),m}getEmptyStats(){return{totalDocuments:0,totalChunks:0,indexSize:0,lastUpdated:new Date,categories:{},sources:{}}}getCacheKey(s){return JSON.stringify({vector:s.vector?.slice(0,10),text:s.text,filters:s.filters,limit:s.limit})}getFromCache(s){return this.config.enableCache&&this.cache.get(s)||null}setCache(s,c){if(this.config.enableCache){if(this.cache.size>=this.config.cacheSize){const r=this.cache.keys().next().value;this.cache.delete(r)}this.cache.set(s,c)}}}class mp{constructor(s,c,r){this.aiClient=new Lr({apiKey:s}),this.config=c,this.contextConfig=r}async rerankResults(s,c,r=10){if(!this.config.enabled||c.length===0)return{rankedChunks:c.slice(0,r).map(b=>b.chunk),scores:c.slice(0,r).map(b=>b.score),explanations:c.slice(0,r).map(()=>"No reranking applied"),processingTime:0,strategy:"none"};const d=Date.now();console.log(`[Reranking] Iniciando reranking de ${c.length} resultados`);const m=c.slice(0,this.config.maxCandidates);let g;switch(this.config.strategy){case"cross_encoder":g=await this.crossEncoderReranking(s,m);break;case"llm_scoring":g=await this.llmScoring(s,m);break;case"semantic_similarity":g=await this.semanticSimilarityReranking(s,m);break;case"hybrid":g=await this.hybridReranking(s,m);break;default:g=await this.hybridReranking(s,m)}const y=this.applyQualityAndDiversityFilters(g,r);return y.processingTime=Date.now()-d,console.log(`[Reranking] Concluído em ${y.processingTime}ms`),y}async crossEncoderReranking(s,c){console.log("[Reranking] Aplicando Cross-Encoder...");const r=[],d=[];for(const g of c)try{const y=`
Avalie a relevância do seguinte texto para responder à pergunta.
Retorne apenas um número de 0 a 1 (onde 1 é extremamente relevante).

PERGUNTA: ${s}

TEXTO: ${g.chunk.content.substring(0,500)}...

RELEVÂNCIA (0-1):`,M=(await this.aiClient.models.generateContent({model:"gemini-2.5-flash",contents:y,generationConfig:{temperature:.1,maxOutputTokens:10}})).text?.trim()||"0",C=Math.max(0,Math.min(1,parseFloat(M)||0));r.push(C),d.push(`Cross-encoder relevance: ${C.toFixed(3)}`)}catch(y){console.error("[Reranking] Erro no cross-encoder:",y),r.push(g.score),d.push("Cross-encoder failed, using original score")}const m=r.map((g,y)=>({score:g,index:y})).sort((g,y)=>y.score-g.score).map(g=>g.index);return{rankedChunks:m.map(g=>c[g].chunk),scores:m.map(g=>r[g]),explanations:m.map(g=>d[g]),processingTime:0,strategy:"cross_encoder"}}async llmScoring(s,c){console.log("[Reranking] Aplicando LLM Scoring...");try{const r=`
Você é um especialista em avaliar relevância de documentos para perguntas específicas.
Avalie cada texto abaixo para responder à pergunta e atribua uma pontuação de 0 a 100.

PERGUNTA: ${s}

TEXTOS PARA AVALIAR:
${c.map((b,M)=>`[${M}] ${b.chunk.content.substring(0,300)}...`).join(`

`)}

Retorne apenas as pontuações no formato:
0: [pontuação]
1: [pontuação]
...

PONTUAÇÕES:`,m=(await this.aiClient.models.generateContent({model:"gemini-2.5-flash",contents:r,generationConfig:{temperature:.2,maxOutputTokens:200}})).text||"",g=this.parseScoresFromResponse(m,c.length),y=g.map((b,M)=>({score:b,index:M})).sort((b,M)=>M.score-b.score).map(b=>b.index);return{rankedChunks:y.map(b=>c[b].chunk),scores:y.map(b=>g[b]),explanations:y.map(b=>`LLM score: ${g[b].toFixed(1)}`),processingTime:0,strategy:"llm_scoring"}}catch(r){return console.error("[Reranking] Erro no LLM scoring:",r),this.fallbackReranking(c)}}async semanticSimilarityReranking(s,c){console.log("[Reranking] Aplicando Semantic Similarity...");const r=c.map(m=>{let g=m.score;if(this.config.recencyWeight>0){const y=this.calculateRecencyScore(m.chunk);g+=y*this.config.recencyWeight}if(this.config.authorityWeight>0){const y=this.calculateAuthorityScore(m.chunk);g+=y*this.config.authorityWeight}return g}),d=r.map((m,g)=>({score:m,index:g})).sort((m,g)=>g.score-m.score).map(m=>m.index);return{rankedChunks:d.map(m=>c[m].chunk),scores:d.map(m=>r[m]),explanations:d.map(m=>`Semantic similarity with recency and authority factors: ${r[m].toFixed(3)}`),processingTime:0,strategy:"semantic_similarity"}}async hybridReranking(s,c){console.log("[Reranking] Aplicando Hybrid Reranking...");const r=await this.crossEncoderReranking(s,c),d=await this.semanticSimilarityReranking(s,c),m=c.map((y,b)=>{const M=r.scores[r.rankedChunks.findIndex(R=>R.id===c[b].chunk.id)]||0,C=d.scores[d.rankedChunks.findIndex(R=>R.id===c[b].chunk.id)]||0;return M*.7+C*.3}),g=m.map((y,b)=>({score:y,index:b})).sort((y,b)=>b.score-y.score).map(y=>y.index);return{rankedChunks:g.map(y=>c[y].chunk),scores:g.map(y=>m[y]),explanations:g.map(y=>`Hybrid score (cross-encoder + semantic): ${m[y].toFixed(3)}`),processingTime:0,strategy:"hybrid"}}async optimizeContext(s,c){const r=Date.now();console.log(`[Context] Otimizando contexto de ${s.length} chunks`);let d=[...s],m=this.estimateTokens(s.map(C=>C.content).join(`
`)),g=!1,y=!1,b=this.contextConfig.preserveStructure;m>this.contextConfig.maxTokens&&(this.contextConfig.prioritizeRecent&&(d=this.prioritizeRecentChunks(d)),m>this.contextConfig.maxTokens&&(d=await this.compressChunks(d,c),g=!0,m=this.estimateTokens(d.map(C=>C.content).join(`
`))),m>this.contextConfig.maxTokens&&this.contextConfig.enableSummarization&&(d=await this.summarizeChunks(d,c),y=!0,m=this.estimateTokens(d.map(C=>C.content).join(`
`))),m>this.contextConfig.maxTokens&&(d=this.truncateChunks(d,this.contextConfig.maxTokens),m=this.contextConfig.maxTokens,b=!1));const M=Date.now()-r;return console.log(`[Context] Otimização concluída: ${s.length} -> ${d.length} chunks, ${m} tokens`),{chunks:d,totalTokens:m,compressionApplied:g,summarizationApplied:y,structurePreserved:b,optimizationTime:M}}applyQualityAndDiversityFilters(s,c){const r=s.rankedChunks.map((g,y)=>({chunk:g,score:s.scores[y],explanation:s.explanations[y]})).filter(g=>g.score>=this.config.relevanceThreshold);let d=r;this.config.diversityWeight>0&&(d=this.applyDiversityFilter(r));const m=d.slice(0,c);return{rankedChunks:m.map(g=>g.chunk),scores:m.map(g=>g.score),explanations:m.map(g=>g.explanation),processingTime:s.processingTime,strategy:s.strategy}}applyDiversityFilter(s){const c=[],r=[...s];for(;r.length>0&&c.length<this.config.maxCandidates;){let d=0,m=-1;for(let g=0;g<r.length;g++){const y=r[g];let b=0;for(const C of c){const R=this.calculateContentSimilarity(y.chunk.content,C.chunk.content);b+=R*this.config.diversityWeight}const M=y.score-b;M>m&&(m=M,d=g)}c.push(r[d]),r.splice(d,1)}return c}calculateRecencyScore(s){const c=new Date,r=new Date(s.metadata.date||s.metadata.createdDate||c),d=(c.getTime()-r.getTime())/(1e3*60*60*24);return Math.exp(-d/30)}calculateAuthorityScore(s){const c={lei:1,decreto:.9,portaria:.8,resolucao:.8,projeto:.7,documento_oficial:.9,ata:.6,relatorio:.7,noticia:.4,api:.5},r=s.metadata.type||"unknown",d=s.metadata.category||"unknown";return Math.max(c[r]||.3,c[d]||.3)}calculateContentSimilarity(s,c){const r=new Set(s.toLowerCase().split(/\s+/)),d=new Set(c.toLowerCase().split(/\s+/)),m=new Set([...r].filter(y=>d.has(y))),g=new Set([...r,...d]);return m.size/g.size}prioritizeRecentChunks(s){return s.sort((c,r)=>{const d=new Date(c.metadata.date||c.metadata.createdDate||0);return new Date(r.metadata.date||r.metadata.createdDate||0).getTime()-d.getTime()})}async compressChunks(s,c){console.log("[Context] Aplicando compressão de chunks...");const r=[];for(const d of s)try{const m=`
Comprima o seguinte texto mantendo apenas as informações mais relevantes para responder à pergunta.
Mantenha o contexto e significado, mas reduza o tamanho em aproximadamente ${Math.round(this.contextConfig.compressionRatio*100)}%.

PERGUNTA: ${c}

TEXTO ORIGINAL:
${d.content}

TEXTO COMPRIMIDO:`,y=(await this.aiClient.models.generateContent({model:"gemini-2.5-flash",contents:m,generationConfig:{temperature:.3,maxOutputTokens:Math.floor(this.estimateTokens(d.content)*this.contextConfig.compressionRatio)}})).text?.trim()||d.content;r.push({...d,content:y,metadata:{...d.metadata,compressed:!0}})}catch(m){console.error("[Context] Erro na compressão:",m),r.push(d)}return r}async summarizeChunks(s,c){console.log("[Context] Aplicando sumarização de chunks...");const r=this.groupSimilarChunks(s),d=[];for(const m of r)try{const g=m.map(C=>C.content).join(`

`),y=`
Crie um resumo conciso do seguinte conteúdo, focando nas informações mais relevantes para responder à pergunta.

PERGUNTA: ${c}

CONTEÚDO:
${g}

RESUMO:`,M=(await this.aiClient.models.generateContent({model:"gemini-2.5-flash",contents:y,generationConfig:{temperature:.3,maxOutputTokens:Math.floor(this.estimateTokens(g)*.3)}})).text?.trim()||g;d.push({id:`summary_${m[0].id}`,content:M,metadata:{...m[0].metadata,summarized:!0,originalChunks:m.length}})}catch(g){console.error("[Context] Erro na sumarização:",g),d.push(...m)}return d}groupSimilarChunks(s){const c=[],r=new Set;for(const d of s){if(r.has(d.id))continue;const m=[d];r.add(d.id);for(const g of s){if(r.has(g.id))continue;this.calculateContentSimilarity(d.content,g.content)>.6&&(m.push(g),r.add(g.id))}c.push(m)}return c}truncateChunks(s,c){console.log("[Context] Aplicando truncamento de chunks...");let r=0;const d=[];for(const m of s){const g=this.estimateTokens(m.content);if(r+g<=c)d.push(m),r+=g;else{const y=c-r;if(y>50){const b=m.content.substring(0,y*4)+"...";d.push({...m,content:b,metadata:{...m.metadata,truncated:!0}})}break}}return d}estimateTokens(s){return Math.ceil(s.length/4)}parseScoresFromResponse(s,c){const r=[],d=s.split(`
`);for(let m=0;m<c;m++){const g=d.find(y=>y.startsWith(`${m}:`));if(g){const y=g.match(/:\s*(\d+(?:\.\d+)?)/),b=y?parseFloat(y[1]):0;r.push(Math.max(0,Math.min(100,b))/100)}else r.push(0)}return r}fallbackReranking(s){return{rankedChunks:s.map(c=>c.chunk),scores:s.map(c=>c.score),explanations:s.map(()=>"Fallback ranking (original order)"),processingTime:0,strategy:"fallback"}}}const gp=()=>{const[u,s]=k.useState(!1),[c,r]=k.useState([]),[d,m]=k.useState(!1),[g,y]=k.useState(null),[b,M]=k.useState(!1),[C,R]=k.useState("chat"),[O,Q]=k.useState(null),[X,D]=k.useState(null),[B,J]=k.useState(null),[ie,I]=k.useState(null),[be,He]=k.useState(null),[te,q]=k.useState(null),[ne,ve]=k.useState(!1),St=k.useRef(null),Ye=k.useCallback((T,z)=>{r(T),D(z),console.log("Conversation loaded:",z)},[]),Le=k.useCallback(()=>{const T=`Olá! Eu sou a ${Mn.pessoal.nome}, ${Mn.pessoal.cargo} de ${Mn.pessoal.municipio}/${Mn.pessoal.estado}.

Como posso ajudá-lo hoje? Estou aqui para:
• Esclarecer dúvidas sobre políticas públicas
• Orientar sobre serviços municipais
• Receber demandas e sugestões
• Informar sobre projetos e iniciativas do meu mandato

Fique à vontade para conversar comigo!`;r([{id:"initial-system-message",text:T,sender:"system",timestamp:new Date}]),D(null),Q(null),y(null),console.log("New conversation started with Vereadora Rafaela")},[]);k.useEffect(()=>{if(c.length>1){const z=setTimeout(async()=>{try{if(X)await xh.saveConversation(c,`Conversa ${new Date().toLocaleString()}`),console.log("Conversation auto-saved");else{const H=`Conversa ${new Date().toLocaleString()}`,P=await Ol.create(H);P.success&&(D(P.data.id),console.log("New conversation created:",P.data.id))}}catch(H){console.warn("Failed to auto-save conversation:",H)}},2e3);return()=>clearTimeout(z)}},[c,X]);const Ea=k.useCallback(async()=>{try{console.log("[RAG] Inicializando sistema RAG da Vereadora Rafaela...");const T="AIzaSyAchEIEhbmB8mrF3DUZBWU56Tz1Vb6GYCA",z="https://lycyipyhmoerebdvpzbxc.supabase.co",H="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx5Y3lpcHlobW9lcmJkdnB6YnhjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MDM4OTAsImV4cCI6MjA2NjQ3OTg5MH0.mAjh58EJZYy1ordqYwqFAV-3eOCCFpH-i_HccxzWK4s",P=new fp(T,bh);J(P);const ue=new dp({supportedFormats:["pdf","txt","docx","xlsx","csv","image"],maxFileSize:50,extractMetadata:!0,cleanContent:!0,detectLanguage:!0});I(ue);const ce={dimensions:768,indexType:"hnsw",distanceMetric:"cosine",enableCache:!0,cacheSize:1e3,enableCompression:!0},re=new hp(z,H,ce);He(re);const Z={enabled:!0,strategy:"hybrid",maxCandidates:20,diversityWeight:.3,recencyWeight:.2,authorityWeight:.3,relevanceThreshold:.5},pe={maxTokens:4e3,compressionRatio:.7,preserveStructure:!0,enableSummarization:!0,prioritizeRecent:!0,includeMetadata:!0},Be=new mp(T,Z,pe);q(Be),ve(!0),console.log("[RAG] Sistema RAG inicializado com sucesso!")}catch(T){console.error("[RAG] Erro ao inicializar sistema RAG:",T)}},[]);k.useEffect(()=>{s(!0),r([]),Ea()},[Ea]);const Wt=k.useCallback(T=>{y(T),r(z=>[...z,{id:`doc-loaded-${Date.now()}`,text:T?`Document loaded (${(T.length/1024).toFixed(2)} KB). I will now use this document as primary context.`:"Document context cleared.",sender:"system",timestamp:new Date}])},[]),pt=k.useCallback(async T=>{const{text:z,audio:H}=T;if(!z?.trim()&&!H){console.warn("Attempted to send empty message.");return}if(!u)return;let P;H?P=`[Audio Input${z?.trim()?`: "${z.trim()}"`:""}]`:P=z;const ue={id:`user-${Date.now()}`,text:P,sender:"user",timestamp:new Date};r(ce=>[...ce,ue]),m(!0),Q(null);try{let ce;if(ne&&B&&g){console.log("[RAG] Usando sistema RAG avançado para resposta...");const Z=await B.retrieveRelevantChunks(z||"",5);let pe=Z.chunks;te&&(pe=(await te.rerankResults(z||"",Z.chunks.map(Rt=>({chunk:Rt,score:Rt.score||0,rank:1,searchType:"hybrid"})),3)).rankedChunks);let Be=pe;te&&(Be=(await te.optimizeContext(pe,z||"")).chunks);const Et=Be.map(st=>st.content).join(`

`),Va=Mr("rag");ce=await Fd(z||"",Va,Et,b,H),Be.length>0&&(ce.sources=Be.map(st=>({title:st.metadata.title||"Documento",source:st.metadata.source||"Desconhecido",type:st.metadata.type||"document"})))}else{const Z=Mr(g?"rag":"geral");ce=await Fd(z||"",Z,g,b,H)}const re={id:`bot-${Date.now()}`,text:ce.text,sender:"bot",timestamp:new Date,sources:ce.sources};r(Z=>[...Z,re]),Q(ce.text)}catch(ce){console.error("Error generating response:",ce);const re=`Sorry, I encountered an error. ${ce instanceof Error?ce.message:"Please try again."}`,Z={id:`error-${Date.now()}`,text:re,sender:"bot",timestamp:new Date};r(pe=>[...pe,Z]),Q(re)}finally{m(!1)}},[u,g,b]);return f.jsxs("div",{className:"flex flex-col h-screen font-sans bg-gray-50 text-gray-900",children:[f.jsx("div",{className:"p-6 pb-0",children:f.jsx(cp,{ragStatus:ne?"ready":"initializing",documentsCount:g?1:0})}),f.jsx(rg,{tabs:[{id:"chat",label:"Chat",icon:f.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})},{id:"documents",label:"Documentos",icon:f.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})},{id:"whatsapp",label:"WhatsApp",icon:f.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 18h.01M8 21l4-7 4 7M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"})})},{id:"history",label:"Histórico",icon:f.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}],activeTab:C,onTabChange:R}),!u&&f.jsx("div",{className:"mx-6 mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg",children:f.jsxs("div",{className:"flex items-center space-x-3",children:[f.jsx("svg",{className:"w-5 h-5 text-amber-600 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})}),f.jsxs("div",{children:[f.jsx("p",{className:"text-amber-800 font-medium",children:"Chave API Necessária"}),f.jsx("p",{className:"text-amber-700 text-sm",children:"Configure sua chave API do Gemini para habilitar o chat"})]})]})}),f.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[C!=="chat"&&f.jsx("aside",{className:"w-full md:w-80 bg-white border-r border-gray-200 flex flex-col overflow-hidden",children:f.jsxs("div",{className:"flex-1 overflow-y-auto",children:[C==="documents"&&f.jsx(cg,{currentDocument:g||void 0,onDocumentSelect:Wt,onDocumentDelete:T=>{console.log("Documento deletado:",T)}}),C==="whatsapp"&&f.jsx(sp,{lastBotResponseText:O}),C==="history"&&f.jsx("div",{className:"p-6",children:f.jsx(ip,{onLoadConversation:Ye,onNewConversation:Le,currentConversationId:X})})]})}),f.jsx("main",{ref:St,className:"flex-1 flex flex-col bg-white",children:C==="chat"?f.jsxs("div",{className:"flex flex-1 overflow-hidden",children:[f.jsx("aside",{className:"w-full md:w-80 bg-white border-r border-gray-200 flex flex-col overflow-hidden",children:f.jsxs("div",{className:"p-6 space-y-6 overflow-y-auto",children:[f.jsx(ig,{onDocumentLoad:Wt}),f.jsx("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:f.jsxs("label",{htmlFor:"googleSearchToggle",className:"flex items-center justify-between cursor-pointer",children:[f.jsxs("div",{className:"flex items-center space-x-3",children:[f.jsx("svg",{className:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),f.jsxs("div",{children:[f.jsx("span",{className:"text-sm font-medium text-gray-900",children:"Busca Google"}),f.jsx("p",{className:"text-xs text-gray-500",children:b?"Busca web habilitada":"Busca web desabilitada"})]})]}),f.jsx("input",{type:"checkbox",id:"googleSearchToggle",checked:b,onChange:T=>M(T.target.checked),className:"sr-only peer"}),f.jsx("div",{className:"relative w-11 h-6 bg-gray-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gray-900"})]})}),g&&f.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:f.jsxs("div",{className:"flex items-center space-x-3",children:[f.jsx("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),f.jsxs("div",{children:[f.jsx("h3",{className:"text-sm font-medium text-green-800",children:"Documento Ativo"}),f.jsxs("p",{className:"text-xs text-green-600",children:[(g.length/1024).toFixed(2)," KB carregados • RAG habilitado"]})]})]})})]})}),f.jsx("div",{className:"flex-1 flex flex-col",children:f.jsx(sg,{messages:c,onSendMessage:pt,isLoading:d,apiKeyLoaded:u})})]}):f.jsx("div",{className:"flex-1 flex items-center justify-center",children:f.jsxs("div",{className:"text-center",children:[f.jsxs("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:[C==="documents"&&"Gerenciar Documentos",C==="whatsapp"&&"Integração WhatsApp",C==="history"&&"Histórico de Conversas"]}),f.jsxs("p",{className:"text-gray-600",children:[C==="documents"&&"Visualize e gerencie seus documentos enviados",C==="whatsapp"&&"Configure e gerencie a integração com WhatsApp",C==="history"&&"Acesse suas conversas anteriores"]})]})})})]})]})},Sh=document.getElementById("root");if(!Sh)throw new Error("Could not find root element to mount to");const pp=tg.createRoot(Sh);pp.render(f.jsx(Dr.StrictMode,{children:f.jsx(gp,{})}));
