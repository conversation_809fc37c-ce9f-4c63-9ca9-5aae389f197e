// Pipeline RAG Avançado para o Gabinete da Vereadora Rafaela de <PERSON>lda
import { GoogleGenAI } from "@google/genai";

// Interfaces para o pipeline RAG
export interface DocumentChunk {
  id: string;
  content: string;
  metadata: {
    source: string;
    type: 'document' | 'spreadsheet' | 'multimedia' | 'api';
    title?: string;
    author?: string;
    date?: string;
    category?: string;
    tags?: string[];
    page?: number;
    section?: string;
  };
  embedding?: number[];
  score?: number;
}

export interface RAGConfig {
  chunkSize: number;
  chunkOverlap: number;
  maxChunks: number;
  embeddingModel: string;
  retrievalStrategy: 'hybrid' | 'vector' | 'bm25' | 'semantic';
  rerankingEnabled: boolean;
  contextOptimization: boolean;
}

export interface RetrievalResult {
  chunks: DocumentChunk[];
  totalFound: number;
  strategy: string;
  processingTime: number;
  confidence: number;
}

export interface RAGResponse {
  answer: string;
  sources: DocumentChunk[];
  confidence: number;
  processingTime: number;
  metadata: {
    chunksUsed: number;
    retrievalStrategy: string;
    rerankingApplied: boolean;
    contextOptimized: boolean;
  };
}

// Configuração padrão do RAG
export const DEFAULT_RAG_CONFIG: RAGConfig = {
  chunkSize: 1000,
  chunkOverlap: 200,
  maxChunks: 10,
  embeddingModel: 'text-embedding-004',
  retrievalStrategy: 'hybrid',
  rerankingEnabled: true,
  contextOptimization: true
};

// Classe principal do pipeline RAG
export class RAGPipeline {
  private config: RAGConfig;
  private aiClient: GoogleGenAI;
  private documentStore: Map<string, DocumentChunk[]> = new Map();
  private vectorIndex: Map<string, number[]> = new Map();
  private bm25Index: Map<string, any> = new Map();

  constructor(apiKey: string, config: RAGConfig = DEFAULT_RAG_CONFIG) {
    this.config = config;
    this.aiClient = new GoogleGenAI({ apiKey });
  }

  // 1. INGESTÃO E PRÉ-PROCESSAMENTO
  async ingestDocument(
    content: string,
    metadata: DocumentChunk['metadata']
  ): Promise<string[]> {
    console.log(`[RAG] Iniciando ingestão: ${metadata.title || metadata.source}`);
    
    // Limpeza e normalização do conteúdo
    const cleanContent = this.cleanAndNormalizeContent(content);
    
    // Chunking inteligente
    const chunks = await this.intelligentChunking(cleanContent, metadata);
    
    // Geração de embeddings
    const chunksWithEmbeddings = await this.generateEmbeddings(chunks);
    
    // Armazenamento
    const documentId = this.generateDocumentId(metadata);
    this.documentStore.set(documentId, chunksWithEmbeddings);
    
    // Indexação para BM25
    await this.indexForBM25(chunksWithEmbeddings);
    
    console.log(`[RAG] Documento ingerido: ${chunks.length} chunks criados`);
    return chunks.map(chunk => chunk.id);
  }

  // 2. CHUNKING INTELIGENTE
  private async intelligentChunking(
    content: string,
    metadata: DocumentChunk['metadata']
  ): Promise<DocumentChunk[]> {
    const chunks: DocumentChunk[] = [];
    
    // Estratégia semântica - dividir por parágrafos e seções
    const sections = this.splitBySections(content);
    
    for (let i = 0; i < sections.length; i++) {
      const section = sections[i];
      
      if (section.length <= this.config.chunkSize) {
        // Seção pequena - usar como chunk único
        chunks.push(this.createChunk(section, metadata, i));
      } else {
        // Seção grande - dividir com sobreposição
        const subChunks = this.splitWithOverlap(section, this.config.chunkSize, this.config.chunkOverlap);
        subChunks.forEach((subChunk, subIndex) => {
          chunks.push(this.createChunk(subChunk, metadata, i, subIndex));
        });
      }
    }
    
    return chunks;
  }

  // 3. GERAÇÃO DE EMBEDDINGS MULTI-MODAL
  private async generateEmbeddings(chunks: DocumentChunk[]): Promise<DocumentChunk[]> {
    console.log(`[RAG] Gerando embeddings para ${chunks.length} chunks`);
    
    const chunksWithEmbeddings: DocumentChunk[] = [];
    
    for (const chunk of chunks) {
      try {
        // Combinar conteúdo + metadados para embedding mais rico
        const embeddingText = this.combineContentAndMetadata(chunk);
        
        // Gerar embedding usando Gemini
        const embedding = await this.generateSingleEmbedding(embeddingText);
        
        chunksWithEmbeddings.push({
          ...chunk,
          embedding
        });
      } catch (error) {
        console.error(`[RAG] Erro ao gerar embedding para chunk ${chunk.id}:`, error);
        // Adicionar chunk sem embedding (será usado apenas no BM25)
        chunksWithEmbeddings.push(chunk);
      }
    }
    
    return chunksWithEmbeddings;
  }

  // 4. RETRIEVAL HÍBRIDO
  async retrieveRelevantChunks(
    query: string,
    maxChunks: number = this.config.maxChunks
  ): Promise<RetrievalResult> {
    const startTime = Date.now();
    console.log(`[RAG] Iniciando retrieval híbrido para: "${query}"`);
    
    // Gerar embedding da query
    const queryEmbedding = await this.generateSingleEmbedding(query);
    
    // Busca vetorial
    const vectorResults = await this.vectorSearch(queryEmbedding, maxChunks * 2);
    
    // Busca BM25
    const bm25Results = await this.bm25Search(query, maxChunks * 2);
    
    // Fusão de resultados (Reciprocal Rank Fusion)
    const fusedResults = this.fuseResults(vectorResults, bm25Results, maxChunks);
    
    // Reranking se habilitado
    let finalResults = fusedResults;
    if (this.config.rerankingEnabled) {
      finalResults = await this.rerankResults(query, fusedResults);
    }
    
    const processingTime = Date.now() - startTime;
    
    return {
      chunks: finalResults.slice(0, maxChunks),
      totalFound: finalResults.length,
      strategy: this.config.retrievalStrategy,
      processingTime,
      confidence: this.calculateConfidence(finalResults)
    };
  }

  // 5. RERANKING INTELIGENTE
  private async rerankResults(
    query: string,
    chunks: DocumentChunk[]
  ): Promise<DocumentChunk[]> {
    console.log(`[RAG] Aplicando reranking a ${chunks.length} chunks`);
    
    // Implementar cross-encoder scoring
    const rerankedChunks: DocumentChunk[] = [];
    
    for (const chunk of chunks) {
      // Calcular relevância semântica mais profunda
      const relevanceScore = await this.calculateSemanticRelevance(query, chunk.content);
      
      rerankedChunks.push({
        ...chunk,
        score: relevanceScore
      });
    }
    
    // Ordenar por score e aplicar diversidade
    return rerankedChunks
      .sort((a, b) => (b.score || 0) - (a.score || 0))
      .filter(this.diversityFilter);
  }

  // 6. OTIMIZAÇÃO DE CONTEXTO
  private optimizeContext(chunks: DocumentChunk[], maxTokens: number = 4000): DocumentChunk[] {
    if (!this.config.contextOptimization) return chunks;
    
    console.log(`[RAG] Otimizando contexto para ${maxTokens} tokens`);
    
    let totalTokens = 0;
    const optimizedChunks: DocumentChunk[] = [];
    
    for (const chunk of chunks) {
      const chunkTokens = this.estimateTokens(chunk.content);
      
      if (totalTokens + chunkTokens <= maxTokens) {
        optimizedChunks.push(chunk);
        totalTokens += chunkTokens;
      } else {
        // Aplicar sumarização se necessário
        const summarizedChunk = this.summarizeChunk(chunk, maxTokens - totalTokens);
        if (summarizedChunk) {
          optimizedChunks.push(summarizedChunk);
        }
        break;
      }
    }
    
    return optimizedChunks;
  }

  // MÉTODOS AUXILIARES
  private cleanAndNormalizeContent(content: string): string {
    return content
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n\n')
      .trim();
  }

  private splitBySections(content: string): string[] {
    // Dividir por seções, parágrafos e quebras naturais
    return content.split(/\n\s*\n/).filter(section => section.trim().length > 0);
  }

  private splitWithOverlap(text: string, chunkSize: number, overlap: number): string[] {
    const chunks: string[] = [];
    let start = 0;
    
    while (start < text.length) {
      const end = Math.min(start + chunkSize, text.length);
      chunks.push(text.slice(start, end));
      start = end - overlap;
    }
    
    return chunks;
  }

  private createChunk(
    content: string,
    metadata: DocumentChunk['metadata'],
    sectionIndex: number,
    subIndex?: number
  ): DocumentChunk {
    const id = `${this.generateDocumentId(metadata)}_${sectionIndex}${subIndex !== undefined ? `_${subIndex}` : ''}`;
    
    return {
      id,
      content: content.trim(),
      metadata: {
        ...metadata,
        section: `Section ${sectionIndex}${subIndex !== undefined ? `.${subIndex}` : ''}`
      }
    };
  }

  private generateDocumentId(metadata: DocumentChunk['metadata']): string {
    return `${metadata.type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private combineContentAndMetadata(chunk: DocumentChunk): string {
    const metaText = [
      chunk.metadata.title,
      chunk.metadata.category,
      chunk.metadata.tags?.join(' ')
    ].filter(Boolean).join(' ');
    
    return `${metaText} ${chunk.content}`.trim();
  }

  private async generateSingleEmbedding(text: string): Promise<number[]> {
    // Implementar geração de embedding usando Gemini
    // Por enquanto, retornar embedding mock
    return new Array(768).fill(0).map(() => Math.random());
  }

  private async vectorSearch(queryEmbedding: number[], maxResults: number): Promise<DocumentChunk[]> {
    // Implementar busca vetorial por similaridade de cosseno
    const results: DocumentChunk[] = [];
    
    for (const [docId, chunks] of this.documentStore) {
      for (const chunk of chunks) {
        if (chunk.embedding) {
          const similarity = this.cosineSimilarity(queryEmbedding, chunk.embedding);
          results.push({ ...chunk, score: similarity });
        }
      }
    }
    
    return results
      .sort((a, b) => (b.score || 0) - (a.score || 0))
      .slice(0, maxResults);
  }

  private async bm25Search(query: string, maxResults: number): Promise<DocumentChunk[]> {
    // Implementar busca BM25 (por enquanto, busca simples por palavras-chave)
    const queryTerms = query.toLowerCase().split(/\s+/);
    const results: DocumentChunk[] = [];
    
    for (const [docId, chunks] of this.documentStore) {
      for (const chunk of chunks) {
        const content = chunk.content.toLowerCase();
        let score = 0;
        
        for (const term of queryTerms) {
          const termFreq = (content.match(new RegExp(term, 'g')) || []).length;
          score += termFreq;
        }
        
        if (score > 0) {
          results.push({ ...chunk, score });
        }
      }
    }
    
    return results
      .sort((a, b) => (b.score || 0) - (a.score || 0))
      .slice(0, maxResults);
  }

  private fuseResults(
    vectorResults: DocumentChunk[],
    bm25Results: DocumentChunk[],
    maxResults: number
  ): DocumentChunk[] {
    // Reciprocal Rank Fusion (RRF)
    const fusedScores = new Map<string, number>();
    const allChunks = new Map<string, DocumentChunk>();
    
    // Processar resultados vetoriais
    vectorResults.forEach((chunk, index) => {
      const rrf = 1 / (60 + index + 1); // k=60 é padrão para RRF
      fusedScores.set(chunk.id, (fusedScores.get(chunk.id) || 0) + rrf);
      allChunks.set(chunk.id, chunk);
    });
    
    // Processar resultados BM25
    bm25Results.forEach((chunk, index) => {
      const rrf = 1 / (60 + index + 1);
      fusedScores.set(chunk.id, (fusedScores.get(chunk.id) || 0) + rrf);
      allChunks.set(chunk.id, chunk);
    });
    
    // Ordenar por score fusionado
    return Array.from(fusedScores.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, maxResults)
      .map(([id, score]) => ({
        ...allChunks.get(id)!,
        score
      }));
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    const dotProduct = a.reduce((sum, val, i) => sum + val * b[i], 0);
    const magnitudeA = Math.sqrt(a.reduce((sum, val) => sum + val * val, 0));
    const magnitudeB = Math.sqrt(b.reduce((sum, val) => sum + val * val, 0));
    return dotProduct / (magnitudeA * magnitudeB);
  }

  private async indexForBM25(chunks: DocumentChunk[]): Promise<void> {
    // Implementar indexação BM25
    console.log(`[RAG] Indexando ${chunks.length} chunks para BM25`);
  }

  private async calculateSemanticRelevance(query: string, content: string): Promise<number> {
    // Implementar cálculo de relevância semântica mais sofisticado
    return Math.random(); // Mock por enquanto
  }

  private diversityFilter = (chunk: DocumentChunk, index: number, array: DocumentChunk[]): boolean => {
    // Implementar filtro de diversidade para evitar chunks muito similares
    return true; // Por enquanto, aceitar todos
  };

  private estimateTokens(text: string): number {
    // Estimativa aproximada: 1 token ≈ 4 caracteres
    return Math.ceil(text.length / 4);
  }

  private summarizeChunk(chunk: DocumentChunk, maxTokens: number): DocumentChunk | null {
    // Implementar sumarização de chunk se necessário
    if (this.estimateTokens(chunk.content) <= maxTokens) {
      return chunk;
    }
    
    // Por enquanto, truncar
    const maxChars = maxTokens * 4;
    return {
      ...chunk,
      content: chunk.content.slice(0, maxChars) + '...'
    };
  }

  private calculateConfidence(chunks: DocumentChunk[]): number {
    if (chunks.length === 0) return 0;
    
    const avgScore = chunks.reduce((sum, chunk) => sum + (chunk.score || 0), 0) / chunks.length;
    return Math.min(avgScore * 100, 100);
  }
}
