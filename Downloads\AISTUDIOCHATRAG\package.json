{"name": "vereadora-rafaela-rag-system", "private": true, "version": "1.0.0", "type": "module", "description": "Sistema RAG Avançado para o Gabinete da Vereadora Rafaela de Nilda - Parnamirim/RN", "keywords": ["rag", "ai", "chatbot", "vereadora", "parnam<PERSON>m", "gemini"], "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "build:prod": "NODE_ENV=production vite build", "build:analyze": "vite build --mode analyze", "clean": "rm -rf dist node_modules/.vite", "deploy": "npm run build:prod && netlify deploy --prod", "deploy:preview": "npm run build && netlify deploy", "deploy:auto": "chmod +x deploy.sh && ./deploy.sh", "deploy:prod": "chmod +x deploy.sh && ./deploy.sh production", "test": "node test-vereadora-system.js", "test:build": "npm run build && npm run test", "lint": "echo 'Linting...' && echo 'No linter configured'", "type-check": "tsc --noEmit", "postinstall": "echo 'Sistema da Vereadora Rafaela instalado com sucesso!'", "start": "npm run preview", "netlify:build": "npm run build:prod", "netlify:dev": "netlify dev"}, "dependencies": {"@google/genai": "^1.6.0", "@supabase/supabase-js": "^2.50.2", "pdfjs-dist": "^4.10.38", "react": "^19.1.0", "react-dom": "^19.1.0", "socket.io-client": "^4.7.4"}, "devDependencies": {"@types/node": "^22.14.0", "typescript": "~5.7.2", "vite": "^6.2.0"}}