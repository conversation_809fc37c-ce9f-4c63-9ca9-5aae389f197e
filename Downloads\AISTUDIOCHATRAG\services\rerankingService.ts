// Sistema de Reranking Inteligente e Otimização de Contexto
import { GoogleGenAI } from "@google/genai";
import { DocumentChunk } from './ragPipeline';
import { SearchResult } from './vectorStore';

export interface RerankingConfig {
  enabled: boolean;
  strategy: 'cross_encoder' | 'llm_scoring' | 'hybrid' | 'semantic_similarity';
  maxCandidates: number;
  diversityWeight: number;
  recencyWeight: number;
  authorityWeight: number;
  relevanceThreshold: number;
}

export interface ContextOptimizationConfig {
  maxTokens: number;
  compressionRatio: number;
  preserveStructure: boolean;
  enableSummarization: boolean;
  prioritizeRecent: boolean;
  includeMetadata: boolean;
}

export interface RerankingResult {
  rankedChunks: DocumentChunk[];
  scores: number[];
  explanations: string[];
  processingTime: number;
  strategy: string;
}

export interface OptimizedContext {
  chunks: DocumentChunk[];
  totalTokens: number;
  compressionApplied: boolean;
  summarizationApplied: boolean;
  structurePreserved: boolean;
  optimizationTime: number;
}

export class RerankingService {
  private aiClient: GoogleGenAI;
  private config: RerankingConfig;
  private contextConfig: ContextOptimizationConfig;

  constructor(
    apiKey: string,
    rerankingConfig: RerankingConfig,
    contextConfig: ContextOptimizationConfig
  ) {
    this.aiClient = new GoogleGenAI({ apiKey });
    this.config = rerankingConfig;
    this.contextConfig = contextConfig;
  }

  // RERANKING PRINCIPAL
  async rerankResults(
    query: string,
    searchResults: SearchResult[],
    maxResults: number = 10
  ): Promise<RerankingResult> {
    if (!this.config.enabled || searchResults.length === 0) {
      return {
        rankedChunks: searchResults.slice(0, maxResults).map(r => r.chunk),
        scores: searchResults.slice(0, maxResults).map(r => r.score),
        explanations: searchResults.slice(0, maxResults).map(() => 'No reranking applied'),
        processingTime: 0,
        strategy: 'none'
      };
    }

    const startTime = Date.now();
    console.log(`[Reranking] Iniciando reranking de ${searchResults.length} resultados`);

    // Limitar candidatos para performance
    const candidates = searchResults.slice(0, this.config.maxCandidates);
    
    let rerankingResult: RerankingResult;

    switch (this.config.strategy) {
      case 'cross_encoder':
        rerankingResult = await this.crossEncoderReranking(query, candidates);
        break;
      case 'llm_scoring':
        rerankingResult = await this.llmScoring(query, candidates);
        break;
      case 'semantic_similarity':
        rerankingResult = await this.semanticSimilarityReranking(query, candidates);
        break;
      case 'hybrid':
        rerankingResult = await this.hybridReranking(query, candidates);
        break;
      default:
        rerankingResult = await this.hybridReranking(query, candidates);
    }

    // Aplicar filtros de qualidade e diversidade
    const filteredResult = this.applyQualityAndDiversityFilters(rerankingResult, maxResults);
    
    filteredResult.processingTime = Date.now() - startTime;
    
    console.log(`[Reranking] Concluído em ${filteredResult.processingTime}ms`);
    return filteredResult;
  }

  // CROSS-ENCODER RERANKING
  private async crossEncoderReranking(
    query: string,
    candidates: SearchResult[]
  ): Promise<RerankingResult> {
    console.log('[Reranking] Aplicando Cross-Encoder...');
    
    const scores: number[] = [];
    const explanations: string[] = [];
    
    for (const candidate of candidates) {
      try {
        // Usar Gemini para calcular relevância semântica profunda
        const relevancePrompt = `
Avalie a relevância do seguinte texto para responder à pergunta.
Retorne apenas um número de 0 a 1 (onde 1 é extremamente relevante).

PERGUNTA: ${query}

TEXTO: ${candidate.chunk.content.substring(0, 500)}...

RELEVÂNCIA (0-1):`;

        const response = await this.aiClient.models.generateContent({
          model: 'gemini-2.5-flash',
          contents: relevancePrompt,
          generationConfig: {
            temperature: 0.1,
            maxOutputTokens: 10
          }
        });

        const scoreText = response.text?.trim() || '0';
        const score = Math.max(0, Math.min(1, parseFloat(scoreText) || 0));
        
        scores.push(score);
        explanations.push(`Cross-encoder relevance: ${score.toFixed(3)}`);
        
      } catch (error) {
        console.error('[Reranking] Erro no cross-encoder:', error);
        scores.push(candidate.score);
        explanations.push('Cross-encoder failed, using original score');
      }
    }

    // Ordenar por scores
    const rankedIndices = scores
      .map((score, index) => ({ score, index }))
      .sort((a, b) => b.score - a.score)
      .map(item => item.index);

    return {
      rankedChunks: rankedIndices.map(i => candidates[i].chunk),
      scores: rankedIndices.map(i => scores[i]),
      explanations: rankedIndices.map(i => explanations[i]),
      processingTime: 0,
      strategy: 'cross_encoder'
    };
  }

  // LLM SCORING
  private async llmScoring(
    query: string,
    candidates: SearchResult[]
  ): Promise<RerankingResult> {
    console.log('[Reranking] Aplicando LLM Scoring...');
    
    try {
      // Preparar prompt para scoring em lote
      const scoringPrompt = `
Você é um especialista em avaliar relevância de documentos para perguntas específicas.
Avalie cada texto abaixo para responder à pergunta e atribua uma pontuação de 0 a 100.

PERGUNTA: ${query}

TEXTOS PARA AVALIAR:
${candidates.map((candidate, index) => 
  `[${index}] ${candidate.chunk.content.substring(0, 300)}...`
).join('\n\n')}

Retorne apenas as pontuações no formato:
0: [pontuação]
1: [pontuação]
...

PONTUAÇÕES:`;

      const response = await this.aiClient.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: scoringPrompt,
        generationConfig: {
          temperature: 0.2,
          maxOutputTokens: 200
        }
      });

      const responseText = response.text || '';
      const scores = this.parseScoresFromResponse(responseText, candidates.length);
      
      // Ordenar por scores
      const rankedIndices = scores
        .map((score, index) => ({ score, index }))
        .sort((a, b) => b.score - a.score)
        .map(item => item.index);

      return {
        rankedChunks: rankedIndices.map(i => candidates[i].chunk),
        scores: rankedIndices.map(i => scores[i]),
        explanations: rankedIndices.map(i => `LLM score: ${scores[i].toFixed(1)}`),
        processingTime: 0,
        strategy: 'llm_scoring'
      };

    } catch (error) {
      console.error('[Reranking] Erro no LLM scoring:', error);
      return this.fallbackReranking(candidates);
    }
  }

  // SEMANTIC SIMILARITY RERANKING
  private async semanticSimilarityReranking(
    query: string,
    candidates: SearchResult[]
  ): Promise<RerankingResult> {
    console.log('[Reranking] Aplicando Semantic Similarity...');
    
    // Calcular similaridade semântica usando embeddings
    const scores = candidates.map(candidate => {
      // Combinar score original com fatores adicionais
      let score = candidate.score;
      
      // Fator de recência
      if (this.config.recencyWeight > 0) {
        const recencyScore = this.calculateRecencyScore(candidate.chunk);
        score += recencyScore * this.config.recencyWeight;
      }
      
      // Fator de autoridade (baseado na fonte)
      if (this.config.authorityWeight > 0) {
        const authorityScore = this.calculateAuthorityScore(candidate.chunk);
        score += authorityScore * this.config.authorityWeight;
      }
      
      return score;
    });

    // Ordenar por scores ajustados
    const rankedIndices = scores
      .map((score, index) => ({ score, index }))
      .sort((a, b) => b.score - a.score)
      .map(item => item.index);

    return {
      rankedChunks: rankedIndices.map(i => candidates[i].chunk),
      scores: rankedIndices.map(i => scores[i]),
      explanations: rankedIndices.map(i => 
        `Semantic similarity with recency and authority factors: ${scores[i].toFixed(3)}`
      ),
      processingTime: 0,
      strategy: 'semantic_similarity'
    };
  }

  // HYBRID RERANKING
  private async hybridReranking(
    query: string,
    candidates: SearchResult[]
  ): Promise<RerankingResult> {
    console.log('[Reranking] Aplicando Hybrid Reranking...');
    
    // Combinar múltiplas estratégias
    const crossEncoderResult = await this.crossEncoderReranking(query, candidates);
    const semanticResult = await this.semanticSimilarityReranking(query, candidates);
    
    // Fusão ponderada dos scores
    const hybridScores = candidates.map((_, index) => {
      const crossScore = crossEncoderResult.scores[
        crossEncoderResult.rankedChunks.findIndex(chunk => 
          chunk.id === candidates[index].chunk.id
        )
      ] || 0;
      
      const semanticScore = semanticResult.scores[
        semanticResult.rankedChunks.findIndex(chunk => 
          chunk.id === candidates[index].chunk.id
        )
      ] || 0;
      
      return (crossScore * 0.7) + (semanticScore * 0.3);
    });

    // Ordenar por scores híbridos
    const rankedIndices = hybridScores
      .map((score, index) => ({ score, index }))
      .sort((a, b) => b.score - a.score)
      .map(item => item.index);

    return {
      rankedChunks: rankedIndices.map(i => candidates[i].chunk),
      scores: rankedIndices.map(i => hybridScores[i]),
      explanations: rankedIndices.map(i => 
        `Hybrid score (cross-encoder + semantic): ${hybridScores[i].toFixed(3)}`
      ),
      processingTime: 0,
      strategy: 'hybrid'
    };
  }

  // OTIMIZAÇÃO DE CONTEXTO
  async optimizeContext(
    chunks: DocumentChunk[],
    query: string
  ): Promise<OptimizedContext> {
    const startTime = Date.now();
    console.log(`[Context] Otimizando contexto de ${chunks.length} chunks`);

    let optimizedChunks = [...chunks];
    let totalTokens = this.estimateTokens(chunks.map(c => c.content).join('\n'));
    let compressionApplied = false;
    let summarizationApplied = false;
    let structurePreserved = this.contextConfig.preserveStructure;

    // Se exceder limite de tokens, aplicar otimizações
    if (totalTokens > this.contextConfig.maxTokens) {
      
      // 1. Priorizar chunks mais recentes se configurado
      if (this.contextConfig.prioritizeRecent) {
        optimizedChunks = this.prioritizeRecentChunks(optimizedChunks);
      }

      // 2. Aplicar compressão se necessário
      if (totalTokens > this.contextConfig.maxTokens) {
        optimizedChunks = await this.compressChunks(optimizedChunks, query);
        compressionApplied = true;
        totalTokens = this.estimateTokens(optimizedChunks.map(c => c.content).join('\n'));
      }

      // 3. Aplicar sumarização se ainda necessário
      if (totalTokens > this.contextConfig.maxTokens && this.contextConfig.enableSummarization) {
        optimizedChunks = await this.summarizeChunks(optimizedChunks, query);
        summarizationApplied = true;
        totalTokens = this.estimateTokens(optimizedChunks.map(c => c.content).join('\n'));
      }

      // 4. Truncar se ainda necessário
      if (totalTokens > this.contextConfig.maxTokens) {
        optimizedChunks = this.truncateChunks(optimizedChunks, this.contextConfig.maxTokens);
        totalTokens = this.contextConfig.maxTokens;
        structurePreserved = false;
      }
    }

    const optimizationTime = Date.now() - startTime;
    
    console.log(`[Context] Otimização concluída: ${chunks.length} -> ${optimizedChunks.length} chunks, ${totalTokens} tokens`);

    return {
      chunks: optimizedChunks,
      totalTokens,
      compressionApplied,
      summarizationApplied,
      structurePreserved,
      optimizationTime
    };
  }

  // MÉTODOS AUXILIARES
  private applyQualityAndDiversityFilters(
    result: RerankingResult,
    maxResults: number
  ): RerankingResult {
    // Filtrar por threshold de relevância
    const qualityFiltered = result.rankedChunks
      .map((chunk, index) => ({
        chunk,
        score: result.scores[index],
        explanation: result.explanations[index]
      }))
      .filter(item => item.score >= this.config.relevanceThreshold);

    // Aplicar diversidade se configurado
    let diversityFiltered = qualityFiltered;
    if (this.config.diversityWeight > 0) {
      diversityFiltered = this.applyDiversityFilter(qualityFiltered);
    }

    // Limitar resultados
    const final = diversityFiltered.slice(0, maxResults);

    return {
      rankedChunks: final.map(item => item.chunk),
      scores: final.map(item => item.score),
      explanations: final.map(item => item.explanation),
      processingTime: result.processingTime,
      strategy: result.strategy
    };
  }

  private applyDiversityFilter(
    items: Array<{ chunk: DocumentChunk; score: number; explanation: string }>
  ): Array<{ chunk: DocumentChunk; score: number; explanation: string }> {
    const selected: typeof items = [];
    const remaining = [...items];

    while (remaining.length > 0 && selected.length < this.config.maxCandidates) {
      // Selecionar próximo item baseado em score e diversidade
      let bestIndex = 0;
      let bestScore = -1;

      for (let i = 0; i < remaining.length; i++) {
        const item = remaining[i];
        let diversityPenalty = 0;

        // Calcular penalidade por similaridade com itens já selecionados
        for (const selectedItem of selected) {
          const similarity = this.calculateContentSimilarity(
            item.chunk.content,
            selectedItem.chunk.content
          );
          diversityPenalty += similarity * this.config.diversityWeight;
        }

        const adjustedScore = item.score - diversityPenalty;
        if (adjustedScore > bestScore) {
          bestScore = adjustedScore;
          bestIndex = i;
        }
      }

      selected.push(remaining[bestIndex]);
      remaining.splice(bestIndex, 1);
    }

    return selected;
  }

  private calculateRecencyScore(chunk: DocumentChunk): number {
    const now = new Date();
    const chunkDate = new Date(chunk.metadata.date || chunk.metadata.createdDate || now);
    const daysDiff = (now.getTime() - chunkDate.getTime()) / (1000 * 60 * 60 * 24);
    
    // Score decai exponencialmente com o tempo
    return Math.exp(-daysDiff / 30); // 30 dias de meia-vida
  }

  private calculateAuthorityScore(chunk: DocumentChunk): number {
    // Score baseado na fonte/tipo do documento
    const authorityMap: Record<string, number> = {
      'lei': 1.0,
      'decreto': 0.9,
      'portaria': 0.8,
      'resolucao': 0.8,
      'projeto': 0.7,
      'documento_oficial': 0.9,
      'ata': 0.6,
      'relatorio': 0.7,
      'noticia': 0.4,
      'api': 0.5
    };

    const type = chunk.metadata.type || 'unknown';
    const category = chunk.metadata.category || 'unknown';
    
    return Math.max(
      authorityMap[type] || 0.3,
      authorityMap[category] || 0.3
    );
  }

  private calculateContentSimilarity(content1: string, content2: string): number {
    // Implementação simples de similaridade textual
    const words1 = new Set(content1.toLowerCase().split(/\s+/));
    const words2 = new Set(content2.toLowerCase().split(/\s+/));
    
    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size; // Jaccard similarity
  }

  private prioritizeRecentChunks(chunks: DocumentChunk[]): DocumentChunk[] {
    return chunks.sort((a, b) => {
      const dateA = new Date(a.metadata.date || a.metadata.createdDate || 0);
      const dateB = new Date(b.metadata.date || b.metadata.createdDate || 0);
      return dateB.getTime() - dateA.getTime();
    });
  }

  private async compressChunks(chunks: DocumentChunk[], query: string): Promise<DocumentChunk[]> {
    console.log('[Context] Aplicando compressão de chunks...');
    
    const compressed: DocumentChunk[] = [];
    
    for (const chunk of chunks) {
      try {
        const compressionPrompt = `
Comprima o seguinte texto mantendo apenas as informações mais relevantes para responder à pergunta.
Mantenha o contexto e significado, mas reduza o tamanho em aproximadamente ${Math.round(this.contextConfig.compressionRatio * 100)}%.

PERGUNTA: ${query}

TEXTO ORIGINAL:
${chunk.content}

TEXTO COMPRIMIDO:`;

        const response = await this.aiClient.models.generateContent({
          model: 'gemini-2.5-flash',
          contents: compressionPrompt,
          generationConfig: {
            temperature: 0.3,
            maxOutputTokens: Math.floor(this.estimateTokens(chunk.content) * this.contextConfig.compressionRatio)
          }
        });

        const compressedContent = response.text?.trim() || chunk.content;
        
        compressed.push({
          ...chunk,
          content: compressedContent,
          metadata: {
            ...chunk.metadata,
            compressed: true
          }
        });

      } catch (error) {
        console.error('[Context] Erro na compressão:', error);
        compressed.push(chunk);
      }
    }
    
    return compressed;
  }

  private async summarizeChunks(chunks: DocumentChunk[], query: string): Promise<DocumentChunk[]> {
    console.log('[Context] Aplicando sumarização de chunks...');
    
    // Agrupar chunks similares para sumarização conjunta
    const groupedChunks = this.groupSimilarChunks(chunks);
    const summarized: DocumentChunk[] = [];
    
    for (const group of groupedChunks) {
      try {
        const combinedContent = group.map(c => c.content).join('\n\n');
        
        const summarizationPrompt = `
Crie um resumo conciso do seguinte conteúdo, focando nas informações mais relevantes para responder à pergunta.

PERGUNTA: ${query}

CONTEÚDO:
${combinedContent}

RESUMO:`;

        const response = await this.aiClient.models.generateContent({
          model: 'gemini-2.5-flash',
          contents: summarizationPrompt,
          generationConfig: {
            temperature: 0.3,
            maxOutputTokens: Math.floor(this.estimateTokens(combinedContent) * 0.3)
          }
        });

        const summary = response.text?.trim() || combinedContent;
        
        // Criar chunk sumarizado
        summarized.push({
          id: `summary_${group[0].id}`,
          content: summary,
          metadata: {
            ...group[0].metadata,
            summarized: true,
            originalChunks: group.length
          }
        });

      } catch (error) {
        console.error('[Context] Erro na sumarização:', error);
        summarized.push(...group);
      }
    }
    
    return summarized;
  }

  private groupSimilarChunks(chunks: DocumentChunk[]): DocumentChunk[][] {
    // Agrupar chunks por similaridade de conteúdo
    const groups: DocumentChunk[][] = [];
    const used = new Set<string>();
    
    for (const chunk of chunks) {
      if (used.has(chunk.id)) continue;
      
      const group = [chunk];
      used.add(chunk.id);
      
      // Encontrar chunks similares
      for (const otherChunk of chunks) {
        if (used.has(otherChunk.id)) continue;
        
        const similarity = this.calculateContentSimilarity(chunk.content, otherChunk.content);
        if (similarity > 0.6) { // Threshold de similaridade
          group.push(otherChunk);
          used.add(otherChunk.id);
        }
      }
      
      groups.push(group);
    }
    
    return groups;
  }

  private truncateChunks(chunks: DocumentChunk[], maxTokens: number): DocumentChunk[] {
    console.log('[Context] Aplicando truncamento de chunks...');
    
    let totalTokens = 0;
    const truncated: DocumentChunk[] = [];
    
    for (const chunk of chunks) {
      const chunkTokens = this.estimateTokens(chunk.content);
      
      if (totalTokens + chunkTokens <= maxTokens) {
        truncated.push(chunk);
        totalTokens += chunkTokens;
      } else {
        // Truncar chunk parcialmente se possível
        const remainingTokens = maxTokens - totalTokens;
        if (remainingTokens > 50) { // Mínimo viável
          const truncatedContent = chunk.content.substring(0, remainingTokens * 4) + '...';
          truncated.push({
            ...chunk,
            content: truncatedContent,
            metadata: {
              ...chunk.metadata,
              truncated: true
            }
          });
        }
        break;
      }
    }
    
    return truncated;
  }

  private estimateTokens(text: string): number {
    // Estimativa: 1 token ≈ 4 caracteres para português
    return Math.ceil(text.length / 4);
  }

  private parseScoresFromResponse(response: string, expectedCount: number): number[] {
    const scores: number[] = [];
    const lines = response.split('\n');
    
    for (let i = 0; i < expectedCount; i++) {
      const line = lines.find(l => l.startsWith(`${i}:`));
      if (line) {
        const scoreMatch = line.match(/:\s*(\d+(?:\.\d+)?)/);
        const score = scoreMatch ? parseFloat(scoreMatch[1]) : 0;
        scores.push(Math.max(0, Math.min(100, score)) / 100); // Normalizar para 0-1
      } else {
        scores.push(0);
      }
    }
    
    return scores;
  }

  private fallbackReranking(candidates: SearchResult[]): RerankingResult {
    return {
      rankedChunks: candidates.map(c => c.chunk),
      scores: candidates.map(c => c.score),
      explanations: candidates.map(() => 'Fallback ranking (original order)'),
      processingTime: 0,
      strategy: 'fallback'
    };
  }
}
