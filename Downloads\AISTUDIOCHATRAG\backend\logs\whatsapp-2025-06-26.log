[2025-06-26T01:47:17.679Z] [INFO] Server started on port 3001
{
  "port": "3001",
  "frontendUrl": "http://localhost:5173",
  "sessionName": "whatsapp-session",
  "nodeEnv": "development"
}
[2025-06-26T01:48:11.578Z] [INFO] Socket client connected: YqDQ0y1LsR5BhRs7AAAB
[2025-06-26T01:48:11.579Z] [INFO] Initial status sent to client YqDQ0y1LsR5BhRs7AAAB
{
  "status": "disconnected"
}
[2025-06-26T01:48:37.693Z] [INFO] Connection request received
[2025-06-26T01:48:37.693Z] [INFO] Starting WhatsApp connection process
[2025-06-26T01:48:37.694Z] [INFO] Status changed to: loading
{
  "status": "loading",
  "error": "Iniciando conexão..."
}
[2025-06-26T01:48:46.198Z] [INFO] QR Code received
[2025-06-26T01:48:46.199Z] [INFO] Status changed to: connecting_qr
{
  "status": "connecting_qr",
  "error": null
}
[2025-06-26T01:48:46.218Z] [INFO] Status: desconnectedMobile, Session: whatsapp-session
[2025-06-26T01:48:46.340Z] [INFO] Status: notLogged, Session: whatsapp-session
[2025-06-26T01:49:18.830Z] [INFO] Connection request received
[2025-06-26T01:49:18.830Z] [WARN] Connection attempt ignored - connection in progress
[2025-06-26T01:49:45.512Z] [INFO] QR Code received
[2025-06-26T01:49:45.512Z] [INFO] Status changed to: connecting_qr
{
  "status": "connecting_qr",
  "error": null
}
[2025-06-26T01:49:46.869Z] [INFO] Status: autocloseCalled, Session: whatsapp-session
[2025-06-26T01:49:46.899Z] [INFO] Status: browserClose, Session: whatsapp-session
[2025-06-26T01:49:47.244Z] [INFO] Status: qrReadError, Session: whatsapp-session
[2025-06-26T01:49:47.245Z] [ERROR] Connection error
{}
[2025-06-26T01:49:47.246Z] [INFO] Status changed to: error
{
  "status": "error",
  "error": null
}
[2025-06-26T01:52:06.858Z] [INFO] Socket client disconnected: YqDQ0y1LsR5BhRs7AAAB
[2025-06-26T01:52:07.273Z] [INFO] Socket client connected: cuaKfwopMe4aEXY4AAAD
[2025-06-26T01:52:07.274Z] [INFO] Initial status sent to client cuaKfwopMe4aEXY4AAAD
{
  "status": "error"
}
[2025-06-26T01:53:04.123Z] [ERROR] Uncaught Exception
{
  "error": "listen EADDRINUSE: address already in use :::3001",
  "stack": "Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at file:///C:/Users/<USER>/Downloads/AISTUDIOCHATRAG/backend/server.js:331:8\n    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)"
}
[2025-06-26T01:53:04.125Z] [INFO] Received uncaughtException. Starting graceful shutdown...
[2025-06-26T01:53:04.126Z] [INFO] Closing server...
[2025-06-26T01:53:04.128Z] [INFO] Server closed successfully
[2025-06-26T01:53:17.943Z] [ERROR] Uncaught Exception
{
  "error": "listen EADDRINUSE: address already in use :::3001",
  "stack": "Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at file:///C:/Users/<USER>/Downloads/AISTUDIOCHATRAG/backend/server.js:331:8\n    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)"
}
[2025-06-26T01:53:17.945Z] [INFO] Received uncaughtException. Starting graceful shutdown...
[2025-06-26T01:53:17.946Z] [INFO] Closing server...
[2025-06-26T01:53:17.946Z] [INFO] Server closed successfully
[2025-06-26T01:53:19.287Z] [ERROR] Uncaught Exception
{
  "error": "listen EADDRINUSE: address already in use :::3001",
  "stack": "Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at file:///C:/Users/<USER>/Downloads/AISTUDIOCHATRAG/backend/server.js:331:8\n    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)"
}
[2025-06-26T01:53:19.289Z] [INFO] Received uncaughtException. Starting graceful shutdown...
[2025-06-26T01:53:19.289Z] [INFO] Closing server...
[2025-06-26T01:53:19.291Z] [INFO] Server closed successfully
[2025-06-26T01:53:24.636Z] [INFO] Received SIGINT. Starting graceful shutdown...
[2025-06-26T01:53:24.637Z] [INFO] Closing server...
[2025-06-26T01:53:39.890Z] [INFO] Server started on port 3001
{
  "port": "3001",
  "frontendUrl": "http://localhost:5173",
  "sessionName": "whatsapp-session",
  "nodeEnv": "development"
}
[2025-06-26T01:53:39.963Z] [INFO] Socket client connected: B7u0NQ9f2EW0bFjdAAAB
[2025-06-26T01:53:39.963Z] [INFO] Initial status sent to client B7u0NQ9f2EW0bFjdAAAB
{
  "status": "disconnected"
}
[2025-06-26T01:54:08.239Z] [INFO] Received SIGINT. Starting graceful shutdown...
[2025-06-26T01:54:08.240Z] [INFO] Closing server...
[2025-06-26T01:54:18.253Z] [ERROR] Force closing server after timeout
[2025-06-26T02:08:56.342Z] [INFO] Server started on port 3001
{
  "port": "3001",
  "frontendUrl": "http://localhost:5173",
  "sessionName": "whatsapp-session",
  "nodeEnv": "development"
}
[2025-06-26T02:12:38.562Z] [INFO] Socket client connected: Vbjck2iM4upcePBXAAAB
[2025-06-26T02:12:38.578Z] [INFO] Initial status sent to client Vbjck2iM4upcePBXAAAB
{
  "status": "disconnected"
}
[2025-06-26T02:12:51.242Z] [INFO] Socket client connected: d0-du8VkERrOrk9YAAAD
[2025-06-26T02:12:51.244Z] [INFO] Initial status sent to client d0-du8VkERrOrk9YAAAD
{
  "status": "disconnected"
}
[2025-06-26T02:19:10.165Z] [INFO] Socket client connected: XaSVX0GP1YxKzAUAAAAF
[2025-06-26T02:19:10.167Z] [INFO] Initial status sent to client XaSVX0GP1YxKzAUAAAAF
{
  "status": "disconnected"
}
[2025-06-26T02:19:11.254Z] [INFO] Socket client disconnected: d0-du8VkERrOrk9YAAAD
[2025-06-26T02:19:12.661Z] [INFO] Socket client disconnected: Vbjck2iM4upcePBXAAAB
[2025-06-26T02:19:17.995Z] [INFO] Connection request received
[2025-06-26T02:19:17.996Z] [INFO] Starting WhatsApp connection process
[2025-06-26T02:19:17.997Z] [INFO] Status changed to: loading
{
  "status": "loading",
  "error": "Iniciando conexão..."
}
[2025-06-26T02:19:25.443Z] [INFO] Status: desconnectedMobile, Session: whatsapp-session
[2025-06-26T02:19:26.030Z] [INFO] QR Code received
[2025-06-26T02:19:26.032Z] [INFO] Status changed to: connecting_qr
{
  "status": "connecting_qr",
  "error": null
}
[2025-06-26T02:19:26.045Z] [INFO] Status: notLogged, Session: whatsapp-session
[2025-06-26T02:19:42.698Z] [INFO] Status: qrReadSuccess, Session: whatsapp-session
[2025-06-26T02:19:42.700Z] [INFO] Status changed to: loading
{
  "status": "loading",
  "error": "QR Code escaneado com sucesso. Conectando..."
}
[2025-06-26T02:19:43.915Z] [INFO] WhatsApp client created successfully
[2025-06-26T02:19:44.729Z] [INFO] State changed: PAIRING
[2025-06-26T02:19:44.739Z] [INFO] State changed: CONNECTED
[2025-06-26T02:19:46.268Z] [INFO] Status: inChat, Session: whatsapp-session
[2025-06-26T02:19:51.772Z] [INFO] Status: inChat, Session: whatsapp-session
[2025-06-26T02:19:52.102Z] [INFO] State changed: CONNECTED
[2025-06-26T02:20:39.613Z] [INFO] Socket client disconnected: XaSVX0GP1YxKzAUAAAAF
[2025-06-26T02:20:40.070Z] [INFO] Socket client connected: RTNTpwf-HkNPNpEAAAAH
[2025-06-26T02:20:40.079Z] [INFO] Initial status sent to client RTNTpwf-HkNPNpEAAAAH
{
  "status": "loading"
}
[2025-06-26T02:26:34.211Z] [INFO] Socket client disconnected: RTNTpwf-HkNPNpEAAAAH
[2025-06-26T02:26:35.007Z] [INFO] Socket client connected: iFOpNIxd_Mfl2mR8AAAJ
[2025-06-26T02:26:35.011Z] [INFO] Initial status sent to client iFOpNIxd_Mfl2mR8AAAJ
{
  "status": "loading"
}
[2025-06-26T02:26:44.096Z] [INFO] Socket client disconnected: iFOpNIxd_Mfl2mR8AAAJ
[2025-06-26T02:26:44.787Z] [INFO] Socket client connected: t_LAEvV5hax3XpKaAAAL
[2025-06-26T02:26:44.788Z] [INFO] Initial status sent to client t_LAEvV5hax3XpKaAAAL
{
  "status": "loading"
}
[2025-06-26T02:27:45.057Z] [INFO] Send message request received
{
  "recipient": "5511999999999",
  "messageLength": 52
}
[2025-06-26T02:27:45.059Z] [WARN] Send message failed - WhatsApp not connected
{
  "status": "loading"
}
[2025-06-26T02:28:44.949Z] [INFO] Server started on port 3001
{
  "port": "3001",
  "frontendUrl": "http://localhost:5173",
  "sessionName": "whatsapp-session",
  "nodeEnv": "development"
}
[2025-06-26T02:28:47.982Z] [INFO] Socket client connected: BPevY53g644HqkKKAAAB
[2025-06-26T02:28:47.985Z] [INFO] Initial status sent to client BPevY53g644HqkKKAAAB
{
  "status": "disconnected"
}
[2025-06-26T02:29:01.266Z] [INFO] Connection request received
[2025-06-26T02:29:01.267Z] [INFO] Starting WhatsApp connection process
[2025-06-26T02:29:01.268Z] [INFO] Status changed to: loading
{
  "status": "loading",
  "error": "Iniciando conexão..."
}
[2025-06-26T02:29:08.203Z] [INFO] Connection request received
[2025-06-26T02:29:08.205Z] [WARN] Connection attempt ignored - connection in progress
[2025-06-26T02:29:10.766Z] [INFO] Status: isLogged, Session: whatsapp-session
[2025-06-26T02:29:11.361Z] [INFO] Status: inChat, Session: whatsapp-session
[2025-06-26T02:29:11.365Z] [INFO] Status changed to: connected
{
  "status": "connected",
  "error": "Conectado com sucesso!"
}
[2025-06-26T02:29:11.982Z] [INFO] WhatsApp client created successfully
[2025-06-26T02:29:15.691Z] [INFO] Disconnect request received
[2025-06-26T02:29:15.692Z] [INFO] Closing WhatsApp client
[2025-06-26T02:29:15.735Z] [INFO] Status: browserClose, Session: whatsapp-session
[2025-06-26T02:29:15.741Z] [INFO] Status changed to: disconnected
{
  "status": "disconnected",
  "error": "Desconectado pelo usuário"
}
[2025-06-26T02:29:15.772Z] [INFO] WhatsApp disconnected successfully
[2025-06-26T02:29:17.135Z] [INFO] Connection request received
[2025-06-26T02:29:17.138Z] [INFO] Starting WhatsApp connection process
[2025-06-26T02:29:17.141Z] [INFO] Status changed to: loading
{
  "status": "loading",
  "error": "Iniciando conexão..."
}
[2025-06-26T02:29:24.881Z] [INFO] Status: inChat, Session: whatsapp-session
[2025-06-26T02:29:24.886Z] [INFO] Status changed to: connected
{
  "status": "connected",
  "error": "Conectado com sucesso!"
}
[2025-06-26T02:29:24.992Z] [INFO] Status: isLogged, Session: whatsapp-session
[2025-06-26T02:29:25.201Z] [INFO] WhatsApp client created successfully
[2025-06-26T02:29:25.209Z] [INFO] State changed: CONNECTED
[2025-06-26T02:29:25.211Z] [INFO] WhatsApp state is CONNECTED, updating status
[2025-06-26T02:29:25.213Z] [INFO] Status changed to: connected
{
  "status": "connected",
  "error": "Conectado com sucesso!"
}
[2025-06-26T02:29:32.593Z] [INFO] Send message request received
{
  "recipient": "5511999999999",
  "messageLength": 130
}
[2025-06-26T02:29:32.594Z] [INFO] Sending message
{
  "formattedRecipient": "<EMAIL>",
  "messageLength": 130
}
[2025-06-26T02:29:32.709Z] [WARN] Failed to upsert contact
{
  "phoneNumber": "5511999999999",
  "error": "Database not configured"
}
[2025-06-26T02:29:32.710Z] [INFO] Broadcasting <NAME_EMAIL>
{
  "messageId": "wa-in-1750904972708",
  "sender": "<EMAIL>",
  "isFromMe": false
}
[2025-06-26T02:29:35.979Z] [WARN] Failed to upsert contact
{
  "phoneNumber": "5511999999999",
  "error": "Database not configured"
}
[2025-06-26T02:29:35.980Z] [INFO] Broadcasting <NAME_EMAIL>
{
  "messageId": "wa-out-1750904975980",
  "sender": "<EMAIL>",
  "isFromMe": true
}
[2025-06-26T02:29:35.982Z] [INFO] Message sent successfully
{
  "messageId": "true_5511999999999@c.us_3EB01E4FF6A7027DB90FAE",
  "recipient": "<EMAIL>"
}
[2025-06-26T02:33:43.019Z] [INFO] Socket client disconnected: BPevY53g644HqkKKAAAB
[2025-06-26T02:33:45.565Z] [INFO] Socket client connected: L9WlPTfHe_RGa9yyAAAD
[2025-06-26T02:33:45.569Z] [INFO] Initial status sent to client L9WlPTfHe_RGa9yyAAAD
{
  "status": "connected"
}
[2025-06-26T02:34:44.046Z] [INFO] Server started on port 3001
{
  "port": "3001",
  "frontendUrl": "http://localhost:5173",
  "sessionName": "whatsapp-session",
  "nodeEnv": "development"
}
[2025-06-26T02:34:49.005Z] [INFO] Socket client connected: u2xqmepdeUmBzCCbAAAB
[2025-06-26T02:34:49.007Z] [INFO] Initial status sent to client u2xqmepdeUmBzCCbAAAB
{
  "status": "disconnected"
}
[2025-06-26T02:39:09.313Z] [INFO] Server started on port 3001
{
  "port": "3001",
  "frontendUrl": "http://localhost:5173",
  "sessionName": "whatsapp-session",
  "nodeEnv": "development"
}
[2025-06-26T02:39:13.982Z] [INFO] Socket client connected: Yr6r3_iBEqPEhBoqAAAB
[2025-06-26T02:39:13.984Z] [INFO] Initial status sent to client Yr6r3_iBEqPEhBoqAAAB
{
  "status": "disconnected"
}
[2025-06-26T02:42:05.444Z] [INFO] Socket client disconnected: Yr6r3_iBEqPEhBoqAAAB
[2025-06-26T02:42:09.446Z] [INFO] Socket client connected: uvdOnA94N1V0HsQHAAAD
[2025-06-26T02:42:09.452Z] [INFO] Initial status sent to client uvdOnA94N1V0HsQHAAAD
{
  "status": "disconnected"
}
[2025-06-26T02:45:37.694Z] [INFO] Server started on port 3001
{
  "port": "3001",
  "frontendUrl": "http://localhost:5173",
  "sessionName": "whatsapp-session",
  "nodeEnv": "development"
}
[2025-06-26T02:45:37.988Z] [INFO] Socket client connected: CYUNkZkqiOtdMl21AAAB
[2025-06-26T02:45:37.992Z] [INFO] Initial status sent to client CYUNkZkqiOtdMl21AAAB
{
  "status": "disconnected"
}
[2025-06-26T02:46:08.609Z] [ERROR] Uncaught Exception
{
  "error": "listen EADDRINUSE: address already in use :::3001",
  "stack": "Error: listen EADDRINUSE: address already in use :::3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at file:///C:/Users/<USER>/Downloads/AISTUDIOCHATRAG/backend/server.js:692:8\n    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:116:5)"
}
[2025-06-26T02:46:08.613Z] [INFO] Received uncaughtException. Starting graceful shutdown...
[2025-06-26T02:46:08.614Z] [INFO] Closing server...
[2025-06-26T02:46:08.617Z] [INFO] Server closed successfully
[2025-06-26T02:46:55.485Z] [INFO] Server started on port 3001
{
  "port": "3001",
  "frontendUrl": "http://localhost:5173",
  "sessionName": "whatsapp-session",
  "nodeEnv": "development"
}
[2025-06-26T02:46:55.557Z] [INFO] Socket client connected: oC-rA9HXaE2fMeVcAAAB
[2025-06-26T02:46:55.560Z] [INFO] Initial status sent to client oC-rA9HXaE2fMeVcAAAB
{
  "status": "disconnected"
}
[2025-06-26T02:49:20.395Z] [INFO] Connection request received
[2025-06-26T02:49:20.400Z] [INFO] Starting WhatsApp connection process
[2025-06-26T02:49:20.402Z] [INFO] Status changed to: loading
{
  "status": "loading",
  "error": "Iniciando conexão..."
}
[2025-06-26T02:49:31.176Z] [INFO] Status: isLogged, Session: whatsapp-session
[2025-06-26T02:49:31.220Z] [INFO] Status: inChat, Session: whatsapp-session
[2025-06-26T02:49:31.225Z] [INFO] Status changed to: connected
{
  "status": "connected",
  "error": "Conectado com sucesso!"
}
[2025-06-26T02:49:31.486Z] [INFO] WhatsApp client created successfully
[2025-06-26T02:49:32.561Z] [INFO] State changed: CONNECTED
[2025-06-26T02:49:32.562Z] [INFO] WhatsApp state is CONNECTED, updating status
[2025-06-26T02:49:32.568Z] [INFO] Status changed to: connected
{
  "status": "connected",
  "error": "Conectado com sucesso!"
}
[2025-06-26T02:49:50.246Z] [INFO] Send message request received
{
  "recipient": "5511999999999",
  "messageLength": 188
}
[2025-06-26T02:49:50.248Z] [INFO] Sending message
{
  "formattedRecipient": "<EMAIL>",
  "messageLength": 188
}
[2025-06-26T02:49:51.088Z] [WARN] Failed to upsert contact
{
  "phoneNumber": "5511999999999",
  "error": "TypeError: fetch failed"
}
[2025-06-26T02:49:51.089Z] [INFO] Broadcasting <NAME_EMAIL>
{
  "messageId": "wa-out-1750906191089",
  "sender": "<EMAIL>",
  "isFromMe": true
}
[2025-06-26T02:49:51.092Z] [INFO] Message sent successfully
{
  "messageId": "true_5511999999999@c.us_3EB03FC17F65BB6C52D1E4",
  "recipient": "<EMAIL>"
}
[2025-06-26T02:50:20.296Z] [INFO] Socket client connected: 52rH61V5J62wfXO_AAAD
[2025-06-26T02:50:20.298Z] [INFO] Initial status sent to client 52rH61V5J62wfXO_AAAD
{
  "status": "connected"
}
[2025-06-26T02:51:27.586Z] [INFO] Socket client disconnected: oC-rA9HXaE2fMeVcAAAB
[2025-06-26T02:52:11.640Z] [INFO] Socket client disconnected: 52rH61V5J62wfXO_AAAD
[2025-06-26T02:52:12.555Z] [INFO] Socket client connected: d0kDDEZcCuiVLp6eAAAF
[2025-06-26T02:52:12.557Z] [INFO] Initial status sent to client d0kDDEZcCuiVLp6eAAAF
{
  "status": "connected"
}
[2025-06-26T02:56:07.041Z] [INFO] Socket client disconnected: d0kDDEZcCuiVLp6eAAAF
[2025-06-26T02:56:08.494Z] [INFO] Socket client connected: yOc8HsN4jRCC-x_HAAAH
[2025-06-26T02:56:08.496Z] [INFO] Initial status sent to client yOc8HsN4jRCC-x_HAAAH
{
  "status": "connected"
}
[2025-06-26T02:56:19.024Z] [INFO] Socket client disconnected: yOc8HsN4jRCC-x_HAAAH
[2025-06-26T02:56:25.046Z] [INFO] Socket client connected: 8Z5hhS67XiR5zaRwAAAJ
[2025-06-26T02:56:25.048Z] [INFO] Initial status sent to client 8Z5hhS67XiR5zaRwAAAJ
{
  "status": "connected"
}
[2025-06-26T02:56:35.468Z] [INFO] Socket client disconnected: 8Z5hhS67XiR5zaRwAAAJ
[2025-06-26T02:56:37.060Z] [INFO] Socket client connected: saq7bOMsUaKiufIwAAAL
[2025-06-26T02:56:37.062Z] [INFO] Initial status sent to client saq7bOMsUaKiufIwAAAL
{
  "status": "connected"
}
[2025-06-26T02:59:07.970Z] [INFO] Socket client disconnected: saq7bOMsUaKiufIwAAAL
[2025-06-26T02:59:09.869Z] [INFO] Socket client connected: LXOLTlP_65qt0weBAAAN
[2025-06-26T02:59:09.871Z] [INFO] Initial status sent to client LXOLTlP_65qt0weBAAAN
{
  "status": "connected"
}
[2025-06-26T03:00:32.715Z] [INFO] Socket client disconnected: LXOLTlP_65qt0weBAAAN
[2025-06-26T03:00:34.910Z] [INFO] Socket client connected: okIFf-gX4eS4VnaJAAAP
[2025-06-26T03:00:34.913Z] [INFO] Initial status sent to client okIFf-gX4eS4VnaJAAAP
{
  "status": "connected"
}
[2025-06-26T03:01:10.642Z] [INFO] Socket client connected: A2wWNnhF_CUgIp6aAAAR
[2025-06-26T03:01:10.651Z] [INFO] Initial status sent to client A2wWNnhF_CUgIp6aAAAR
{
  "status": "connected"
}
[2025-06-26T03:01:11.744Z] [INFO] Socket client disconnected: okIFf-gX4eS4VnaJAAAP
[2025-06-26T03:06:16.270Z] [INFO] Socket client disconnected: A2wWNnhF_CUgIp6aAAAR
[2025-06-26T03:06:18.142Z] [INFO] Socket client connected: ZqAxYcYUeo_aqbmAAAAT
[2025-06-26T03:06:18.145Z] [INFO] Initial status sent to client ZqAxYcYUeo_aqbmAAAAT
{
  "status": "connected"
}
[2025-06-26T03:07:27.482Z] [INFO] Socket client disconnected: ZqAxYcYUeo_aqbmAAAAT
[2025-06-26T03:07:34.312Z] [INFO] Socket client connected: doo1JUSUuxXEpjjTAAAV
[2025-06-26T03:07:34.314Z] [INFO] Initial status sent to client doo1JUSUuxXEpjjTAAAV
{
  "status": "connected"
}
[2025-06-26T03:09:34.311Z] [INFO] Socket client disconnected: doo1JUSUuxXEpjjTAAAV
[2025-06-26T03:09:37.765Z] [INFO] Socket client connected: E6yjyHvscDCA1T-NAAAX
[2025-06-26T03:09:37.766Z] [INFO] Initial status sent to client E6yjyHvscDCA1T-NAAAX
{
  "status": "connected"
}
[2025-06-26T03:10:11.786Z] [INFO] Socket client disconnected: E6yjyHvscDCA1T-NAAAX
[2025-06-26T03:10:12.579Z] [INFO] Socket client connected: JgANwbc6CJC_FBmUAAAZ
[2025-06-26T03:10:12.587Z] [INFO] Initial status sent to client JgANwbc6CJC_FBmUAAAZ
{
  "status": "connected"
}
[2025-06-26T03:10:18.515Z] [INFO] Socket client connected: BS2cxKBRSpC-nYVUAAAb
[2025-06-26T03:10:18.519Z] [INFO] Initial status sent to client BS2cxKBRSpC-nYVUAAAb
{
  "status": "connected"
}
[2025-06-26T03:10:55.326Z] [INFO] Socket client disconnected: BS2cxKBRSpC-nYVUAAAb
[2025-06-26T03:10:55.432Z] [INFO] Socket client disconnected: JgANwbc6CJC_FBmUAAAZ
[2025-06-26T03:10:56.525Z] [INFO] Socket client connected: eS09jSQ-0MDXpQYLAAAd
[2025-06-26T03:10:56.526Z] [INFO] Initial status sent to client eS09jSQ-0MDXpQYLAAAd
{
  "status": "connected"
}
[2025-06-26T03:10:56.638Z] [INFO] Socket client connected: hFJcn_-70wC-gImbAAAf
[2025-06-26T03:10:56.639Z] [INFO] Initial status sent to client hFJcn_-70wC-gImbAAAf
{
  "status": "connected"
}
[2025-06-26T03:11:51.978Z] [INFO] Socket client disconnected: hFJcn_-70wC-gImbAAAf
[2025-06-26T03:11:52.015Z] [INFO] Socket client disconnected: eS09jSQ-0MDXpQYLAAAd
[2025-06-26T03:11:53.930Z] [INFO] Socket client connected: zC5nmMvVnPQDVXi5AAAh
[2025-06-26T03:11:53.935Z] [INFO] Initial status sent to client zC5nmMvVnPQDVXi5AAAh
{
  "status": "connected"
}
[2025-06-26T03:11:54.129Z] [INFO] Socket client connected: exTTH32kRidhhv1AAAAj
[2025-06-26T03:11:54.132Z] [INFO] Initial status sent to client exTTH32kRidhhv1AAAAj
{
  "status": "connected"
}
[2025-06-26T03:12:22.000Z] [INFO] Socket client disconnected: zC5nmMvVnPQDVXi5AAAh
[2025-06-26T03:12:22.021Z] [INFO] Socket client disconnected: exTTH32kRidhhv1AAAAj
[2025-06-26T03:12:23.926Z] [INFO] Socket client connected: 4MwGA0L4UZpx3JwCAAAl
[2025-06-26T03:12:23.929Z] [INFO] Initial status sent to client 4MwGA0L4UZpx3JwCAAAl
{
  "status": "connected"
}
[2025-06-26T03:12:27.405Z] [INFO] Socket client connected: BFjq_qYytz9evwK6AAAn
[2025-06-26T03:12:27.406Z] [INFO] Initial status sent to client BFjq_qYytz9evwK6AAAn
{
  "status": "connected"
}
[2025-06-26T03:13:07.977Z] [INFO] Socket client disconnected: BFjq_qYytz9evwK6AAAn
[2025-06-26T03:13:07.991Z] [INFO] Socket client disconnected: 4MwGA0L4UZpx3JwCAAAl
[2025-06-26T03:13:09.821Z] [INFO] Socket client connected: iigTt5yempJ5pq_gAAAp
[2025-06-26T03:13:09.822Z] [INFO] Initial status sent to client iigTt5yempJ5pq_gAAAp
{
  "status": "connected"
}
[2025-06-26T03:13:13.220Z] [INFO] Socket client connected: 2_rBjJJ6QJpZR9s3AAAr
[2025-06-26T03:13:13.222Z] [INFO] Initial status sent to client 2_rBjJJ6QJpZR9s3AAAr
{
  "status": "connected"
}
[2025-06-26T03:14:35.526Z] [INFO] Socket client disconnected: 2_rBjJJ6QJpZR9s3AAAr
[2025-06-26T03:15:17.382Z] [INFO] Socket client connected: RVx4oVAX54eg-azKAAAt
[2025-06-26T03:15:17.385Z] [INFO] Initial status sent to client RVx4oVAX54eg-azKAAAt
{
  "status": "connected"
}
[2025-06-26T03:18:44.707Z] [INFO] Socket client disconnected: RVx4oVAX54eg-azKAAAt
[2025-06-26T03:18:45.371Z] [INFO] Socket client connected: KvtHDPPv8zd5k0adAAAv
[2025-06-26T03:18:45.372Z] [INFO] Initial status sent to client KvtHDPPv8zd5k0adAAAv
{
  "status": "connected"
}
[2025-06-26T03:18:57.199Z] [INFO] Socket client disconnected: KvtHDPPv8zd5k0adAAAv
[2025-06-26T03:18:59.003Z] [INFO] Socket client connected: O8_Sov-4ROWp_LgFAAAx
[2025-06-26T03:18:59.004Z] [INFO] Initial status sent to client O8_Sov-4ROWp_LgFAAAx
{
  "status": "connected"
}
[2025-06-26T03:19:00.974Z] [INFO] Socket client disconnected: iigTt5yempJ5pq_gAAAp
[2025-06-26T03:20:34.001Z] [INFO] Socket client disconnected: O8_Sov-4ROWp_LgFAAAx
[2025-06-26T03:20:35.885Z] [INFO] Socket client connected: glkWUjsV0yQVQmaBAAAz
[2025-06-26T03:20:35.886Z] [INFO] Initial status sent to client glkWUjsV0yQVQmaBAAAz
{
  "status": "connected"
}
[2025-06-26T03:20:54.552Z] [INFO] Socket client disconnected: glkWUjsV0yQVQmaBAAAz
[2025-06-26T03:20:57.652Z] [INFO] Socket client connected: UIgHp1FtKTuPYuDUAAA1
[2025-06-26T03:20:57.653Z] [INFO] Initial status sent to client UIgHp1FtKTuPYuDUAAA1
{
  "status": "connected"
}
[2025-06-26T03:21:27.064Z] [INFO] Socket client disconnected: UIgHp1FtKTuPYuDUAAA1
[2025-06-26T03:21:28.609Z] [INFO] Socket client connected: UIWjJxnLt8lu0au4AAA3
[2025-06-26T03:21:28.610Z] [INFO] Initial status sent to client UIWjJxnLt8lu0au4AAA3
{
  "status": "connected"
}
[2025-06-26T03:23:14.396Z] [INFO] Socket client connected: OrtuUGhgQjBHGakAAAA5
[2025-06-26T03:23:14.406Z] [INFO] Initial status sent to client OrtuUGhgQjBHGakAAAA5
{
  "status": "connected"
}
[2025-06-26T03:29:05.824Z] [INFO] Socket client disconnected: UIWjJxnLt8lu0au4AAA3
[2025-06-26T03:29:09.284Z] [INFO] Disconnect request received
[2025-06-26T03:29:09.285Z] [INFO] Closing WhatsApp client
[2025-06-26T03:29:09.336Z] [INFO] Status: browserClose, Session: whatsapp-session
[2025-06-26T03:29:09.340Z] [INFO] Status changed to: disconnected
{
  "status": "disconnected",
  "error": "Desconectado pelo usuário"
}
[2025-06-26T03:29:09.343Z] [INFO] WhatsApp disconnected successfully
[2025-06-26T03:29:10.651Z] [INFO] Connection request received
[2025-06-26T03:29:10.652Z] [INFO] Starting WhatsApp connection process
[2025-06-26T03:29:10.654Z] [INFO] Status changed to: loading
{
  "status": "loading",
  "error": "Iniciando conexão..."
}
[2025-06-26T03:29:20.170Z] [INFO] Status: isLogged, Session: whatsapp-session
[2025-06-26T03:29:20.246Z] [INFO] Status: inChat, Session: whatsapp-session
[2025-06-26T03:29:20.247Z] [INFO] Status changed to: connected
{
  "status": "connected",
  "error": "Conectado com sucesso!"
}
[2025-06-26T03:29:20.374Z] [INFO] WhatsApp client created successfully
[2025-06-26T03:29:20.609Z] [INFO] State changed: CONNECTED
[2025-06-26T03:29:20.610Z] [INFO] WhatsApp state is CONNECTED, updating status
[2025-06-26T03:29:20.612Z] [INFO] Status changed to: connected
{
  "status": "connected",
  "error": "Conectado com sucesso!"
}
[2025-06-26T03:31:12.405Z] [INFO] Socket client disconnected: OrtuUGhgQjBHGakAAAA5
[2025-06-26T03:31:37.735Z] [INFO] Socket client connected: hb9NqYXVma2ITlVxAAA7
[2025-06-26T03:31:37.738Z] [INFO] Initial status sent to client hb9NqYXVma2ITlVxAAA7
{
  "status": "connected"
}
[2025-06-26T03:31:47.046Z] [INFO] Socket client connected: 63gV9I-GdJWrX5EAAAA9
[2025-06-26T03:31:47.048Z] [INFO] Initial status sent to client 63gV9I-GdJWrX5EAAAA9
{
  "status": "connected"
}
[2025-06-26T03:32:22.210Z] [INFO] Socket client disconnected: hb9NqYXVma2ITlVxAAA7
[2025-06-26T03:32:52.674Z] [INFO] Socket client connected: Qjf9eX_d__cNeWhuAAA_
[2025-06-26T03:32:52.688Z] [INFO] Initial status sent to client Qjf9eX_d__cNeWhuAAA_
{
  "status": "connected"
}
[2025-06-26T03:33:26.413Z] [INFO] Socket client disconnected: Qjf9eX_d__cNeWhuAAA_
[2025-06-26T03:33:26.588Z] [INFO] Socket client disconnected: 63gV9I-GdJWrX5EAAAA9
[2025-06-26T03:33:30.494Z] [INFO] Socket client connected: DSkWbUvHstBC-r-mAABB
[2025-06-26T03:33:30.495Z] [INFO] Initial status sent to client DSkWbUvHstBC-r-mAABB
{
  "status": "connected"
}
[2025-06-26T03:33:32.254Z] [INFO] Socket client connected: S0bSK0wx8uVwCN1jAABD
[2025-06-26T03:33:32.256Z] [INFO] Initial status sent to client S0bSK0wx8uVwCN1jAABD
{
  "status": "connected"
}
[2025-06-26T03:38:50.759Z] [WARN] Failed to upsert contact
{
  "phoneNumber": "558488501582",
  "error": "TypeError: fetch failed"
}
[2025-06-26T03:38:50.760Z] [INFO] Broadcasting <NAME_EMAIL>
{
  "messageId": "wa-in-1750909130650",
  "sender": "<EMAIL>",
  "isFromMe": false
}
[2025-06-26T03:40:21.959Z] [INFO] Socket client disconnected: DSkWbUvHstBC-r-mAABB
[2025-06-26T03:40:23.502Z] [INFO] Socket client disconnected: S0bSK0wx8uVwCN1jAABD
[2025-06-26T03:49:05.738Z] [INFO] Socket client connected: vp5lLjl4VL0TH_NwAABF
[2025-06-26T03:49:05.739Z] [INFO] Initial status sent to client vp5lLjl4VL0TH_NwAABF
{
  "status": "connected"
}
[2025-06-26T04:33:22.918Z] [INFO] Socket client disconnected: vp5lLjl4VL0TH_NwAABF
[2025-06-26T04:33:23.176Z] [INFO] Socket client connected: GtJN1Pvju8oJKdO9AABH
[2025-06-26T04:33:23.177Z] [INFO] Initial status sent to client GtJN1Pvju8oJKdO9AABH
{
  "status": "connected"
}
[2025-06-26T04:38:21.576Z] [INFO] Socket client disconnected: GtJN1Pvju8oJKdO9AABH
[2025-06-26T04:45:00.830Z] [INFO] Socket client connected: To5O2etwwh2tx_OHAABJ
[2025-06-26T04:45:00.831Z] [INFO] Initial status sent to client To5O2etwwh2tx_OHAABJ
{
  "status": "connected"
}
[2025-06-26T04:55:48.945Z] [INFO] Socket client disconnected: To5O2etwwh2tx_OHAABJ
[2025-06-26T04:55:49.741Z] [INFO] Socket client connected: H2GM1F4OWZd1yCwNAABL
[2025-06-26T04:55:49.742Z] [INFO] Initial status sent to client H2GM1F4OWZd1yCwNAABL
{
  "status": "connected"
}
[2025-06-26T04:56:05.957Z] [INFO] Socket client disconnected: H2GM1F4OWZd1yCwNAABL
[2025-06-26T04:56:06.620Z] [INFO] Socket client connected: 20VKGLRmi1jKDFdGAABN
[2025-06-26T04:56:06.621Z] [INFO] Initial status sent to client 20VKGLRmi1jKDFdGAABN
{
  "status": "connected"
}
[2025-06-26T04:56:34.953Z] [INFO] Socket client disconnected: 20VKGLRmi1jKDFdGAABN
[2025-06-26T04:56:35.674Z] [INFO] Socket client connected: kzeqRzNsQhT0A4CoAABP
[2025-06-26T04:56:35.675Z] [INFO] Initial status sent to client kzeqRzNsQhT0A4CoAABP
{
  "status": "connected"
}
[2025-06-26T04:56:54.949Z] [INFO] Socket client disconnected: kzeqRzNsQhT0A4CoAABP
[2025-06-26T04:56:55.556Z] [INFO] Socket client connected: KF2AbqQauvFEP74MAABR
[2025-06-26T04:56:55.557Z] [INFO] Initial status sent to client KF2AbqQauvFEP74MAABR
{
  "status": "connected"
}
[2025-06-26T04:57:39.948Z] [INFO] Socket client disconnected: KF2AbqQauvFEP74MAABR
[2025-06-26T04:57:40.535Z] [INFO] Socket client connected: gTBH7SoY-Px51hBUAABT
[2025-06-26T04:57:40.536Z] [INFO] Initial status sent to client gTBH7SoY-Px51hBUAABT
{
  "status": "connected"
}
[2025-06-26T04:57:52.963Z] [INFO] Socket client disconnected: gTBH7SoY-Px51hBUAABT
[2025-06-26T04:57:54.091Z] [INFO] Socket client connected: U9edSMI5WYT8poruAABV
[2025-06-26T04:57:54.092Z] [INFO] Initial status sent to client U9edSMI5WYT8poruAABV
{
  "status": "connected"
}
[2025-06-26T04:58:23.973Z] [INFO] Socket client disconnected: U9edSMI5WYT8poruAABV
[2025-06-26T04:58:24.501Z] [INFO] Socket client connected: AicqTCFDhnWm5JZGAABX
[2025-06-26T04:58:24.501Z] [INFO] Initial status sent to client AicqTCFDhnWm5JZGAABX
{
  "status": "connected"
}
[2025-06-26T04:58:40.954Z] [INFO] Socket client disconnected: AicqTCFDhnWm5JZGAABX
[2025-06-26T04:58:41.557Z] [INFO] Socket client connected: TDHCv0dpbgQCBQXqAABZ
[2025-06-26T04:58:41.558Z] [INFO] Initial status sent to client TDHCv0dpbgQCBQXqAABZ
{
  "status": "connected"
}
[2025-06-26T04:58:56.945Z] [INFO] Socket client disconnected: TDHCv0dpbgQCBQXqAABZ
[2025-06-26T04:58:57.550Z] [INFO] Socket client connected: Di8LA24iXurW7yyiAABb
[2025-06-26T04:58:57.551Z] [INFO] Initial status sent to client Di8LA24iXurW7yyiAABb
{
  "status": "connected"
}
[2025-06-26T04:59:11.951Z] [INFO] Socket client disconnected: Di8LA24iXurW7yyiAABb
[2025-06-26T04:59:12.493Z] [INFO] Socket client connected: PNpIQGfseZ_JVHCuAABd
[2025-06-26T04:59:12.494Z] [INFO] Initial status sent to client PNpIQGfseZ_JVHCuAABd
{
  "status": "connected"
}
[2025-06-26T04:59:29.948Z] [INFO] Socket client disconnected: PNpIQGfseZ_JVHCuAABd
[2025-06-26T04:59:30.482Z] [INFO] Socket client connected: 9akNcdRiY2Nwat9eAABf
[2025-06-26T04:59:30.483Z] [INFO] Initial status sent to client 9akNcdRiY2Nwat9eAABf
{
  "status": "connected"
}
[2025-06-26T04:59:46.945Z] [INFO] Socket client disconnected: 9akNcdRiY2Nwat9eAABf
[2025-06-26T04:59:47.511Z] [INFO] Socket client connected: oWyXM3uE_AafIkdLAABh
[2025-06-26T04:59:47.512Z] [INFO] Initial status sent to client oWyXM3uE_AafIkdLAABh
{
  "status": "connected"
}
[2025-06-26T05:00:03.950Z] [INFO] Socket client disconnected: oWyXM3uE_AafIkdLAABh
[2025-06-26T05:00:04.597Z] [INFO] Socket client connected: 78w86mxP544nW67iAABj
[2025-06-26T05:00:04.598Z] [INFO] Initial status sent to client 78w86mxP544nW67iAABj
{
  "status": "connected"
}
[2025-06-26T05:00:15.954Z] [INFO] Socket client disconnected: 78w86mxP544nW67iAABj
[2025-06-26T05:00:16.495Z] [INFO] Socket client connected: 0qtOexpEPf54XivMAABl
[2025-06-26T05:00:16.496Z] [INFO] Initial status sent to client 0qtOexpEPf54XivMAABl
{
  "status": "connected"
}
[2025-06-26T05:00:27.960Z] [INFO] Socket client disconnected: 0qtOexpEPf54XivMAABl
[2025-06-26T05:00:28.782Z] [INFO] Socket client connected: V0zqkOcw0ite3_6rAABn
[2025-06-26T05:00:28.783Z] [INFO] Initial status sent to client V0zqkOcw0ite3_6rAABn
{
  "status": "connected"
}
[2025-06-26T05:00:39.954Z] [INFO] Socket client disconnected: V0zqkOcw0ite3_6rAABn
[2025-06-26T05:00:42.428Z] [INFO] Socket client connected: STE0iljFVcVZbwb3AABp
[2025-06-26T05:00:42.431Z] [INFO] Initial status sent to client STE0iljFVcVZbwb3AABp
{
  "status": "connected"
}
[2025-06-26T05:00:50.963Z] [INFO] Socket client disconnected: STE0iljFVcVZbwb3AABp
[2025-06-26T05:00:52.099Z] [INFO] Socket client connected: ymBGrwi1roJp2YqZAABr
[2025-06-26T05:00:52.100Z] [INFO] Initial status sent to client ymBGrwi1roJp2YqZAABr
{
  "status": "connected"
}
[2025-06-26T05:01:11.657Z] [INFO] Socket client connected: OPJZEhvEYAnY7wiRAABt
[2025-06-26T05:01:11.661Z] [INFO] Initial status sent to client OPJZEhvEYAnY7wiRAABt
{
  "status": "connected"
}
[2025-06-26T05:06:26.056Z] [INFO] Socket client disconnected: ymBGrwi1roJp2YqZAABr
[2025-06-26T05:08:08.532Z] [INFO] Socket client disconnected: OPJZEhvEYAnY7wiRAABt
[2025-06-26T05:08:12.553Z] [INFO] Socket client connected: cbEripBJUBv5FwO0AABv
[2025-06-26T05:08:12.557Z] [INFO] Initial status sent to client cbEripBJUBv5FwO0AABv
{
  "status": "connected"
}
[2025-06-26T05:08:27.000Z] [INFO] Socket client disconnected: cbEripBJUBv5FwO0AABv
[2025-06-26T05:08:28.479Z] [INFO] Socket client connected: nf1e8Gjl8iMUcGnJAABx
[2025-06-26T05:08:28.488Z] [INFO] Initial status sent to client nf1e8Gjl8iMUcGnJAABx
{
  "status": "connected"
}
[2025-06-26T05:08:42.396Z] [INFO] Socket client disconnected: nf1e8Gjl8iMUcGnJAABx
[2025-06-26T05:13:57.707Z] [INFO] Socket client connected: InIZLSHjo8BSGbGOAABz
[2025-06-26T05:13:57.713Z] [INFO] Initial status sent to client InIZLSHjo8BSGbGOAABz
{
  "status": "connected"
}
[2025-06-26T05:16:26.151Z] [INFO] Socket client disconnected: InIZLSHjo8BSGbGOAABz
[2025-06-26T05:22:56.941Z] [INFO] Socket client connected: PR7CejIBWSLxapASAAB1
[2025-06-26T05:22:56.943Z] [INFO] Initial status sent to client PR7CejIBWSLxapASAAB1
{
  "status": "connected"
}
[2025-06-26T05:23:26.045Z] [INFO] Socket client disconnected: PR7CejIBWSLxapASAAB1
[2025-06-26T05:23:36.749Z] [INFO] Status: browserClose, Session: whatsapp-session
