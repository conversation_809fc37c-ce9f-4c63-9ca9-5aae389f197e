// Sistema de Monitoramento e Observabilidade para o Gabinete da Vereadora
import { createClient, SupabaseClient } from '@supabase/supabase-js';

export interface MetricData {
  timestamp: Date;
  metric: string;
  value: number;
  metadata?: Record<string, any>;
  category: 'performance' | 'usage' | 'quality' | 'error' | 'feedback';
}

export interface PerformanceMetrics {
  responseTime: number;
  ragProcessingTime: number;
  retrievalTime: number;
  rerankingTime: number;
  contextOptimizationTime: number;
  totalTokensUsed: number;
  chunksRetrieved: number;
  documentsProcessed: number;
}

export interface UsageMetrics {
  totalQueries: number;
  uniqueUsers: number;
  averageSessionDuration: number;
  mostCommonQueries: string[];
  peakUsageHours: number[];
  documentUploads: number;
  whatsappMessages: number;
}

export interface QualityMetrics {
  averageRelevanceScore: number;
  userSatisfactionRating: number;
  hallucinationRate: number;
  accuracyScore: number;
  contextUtilization: number;
  sourceReliability: number;
}

export interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  errorRate: number;
  memoryUsage: number;
  apiLatency: number;
  databaseConnections: number;
  lastHealthCheck: Date;
}

export interface FeedbackData {
  id: string;
  conversationId: string;
  messageId: string;
  rating: 1 | 2 | 3 | 4 | 5;
  feedback: 'helpful' | 'not_helpful' | 'incorrect' | 'incomplete' | 'excellent';
  comment?: string;
  timestamp: Date;
  userType: 'citizen' | 'staff' | 'anonymous';
}

export interface Alert {
  id: string;
  type: 'performance' | 'error' | 'security' | 'capacity';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  resolved: boolean;
  metadata?: Record<string, any>;
}

export class MonitoringService {
  private supabase: SupabaseClient;
  private metrics: Map<string, MetricData[]> = new Map();
  private alerts: Alert[] = [];
  private healthStatus: SystemHealth;

  constructor(supabaseUrl: string, supabaseKey: string) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.healthStatus = this.initializeHealthStatus();
    this.initializeDatabase();
    this.startHealthChecks();
  }

  // COLETA DE MÉTRICAS
  async recordMetric(metric: MetricData): Promise<void> {
    try {
      // Armazenar localmente
      if (!this.metrics.has(metric.metric)) {
        this.metrics.set(metric.metric, []);
      }
      this.metrics.get(metric.metric)!.push(metric);

      // Persistir no banco
      await this.supabase
        .from('system_metrics')
        .insert({
          timestamp: metric.timestamp.toISOString(),
          metric_name: metric.metric,
          value: metric.value,
          metadata: metric.metadata || {},
          category: metric.category
        });

      // Verificar alertas
      this.checkAlerts(metric);

    } catch (error) {
      console.error('[Monitoring] Erro ao registrar métrica:', error);
    }
  }

  async recordPerformanceMetrics(metrics: PerformanceMetrics): Promise<void> {
    const timestamp = new Date();
    
    const metricsToRecord: MetricData[] = [
      {
        timestamp,
        metric: 'response_time',
        value: metrics.responseTime,
        category: 'performance'
      },
      {
        timestamp,
        metric: 'rag_processing_time',
        value: metrics.ragProcessingTime,
        category: 'performance'
      },
      {
        timestamp,
        metric: 'retrieval_time',
        value: metrics.retrievalTime,
        category: 'performance'
      },
      {
        timestamp,
        metric: 'reranking_time',
        value: metrics.rerankingTime,
        category: 'performance'
      },
      {
        timestamp,
        metric: 'tokens_used',
        value: metrics.totalTokensUsed,
        category: 'usage'
      },
      {
        timestamp,
        metric: 'chunks_retrieved',
        value: metrics.chunksRetrieved,
        category: 'usage'
      }
    ];

    for (const metric of metricsToRecord) {
      await this.recordMetric(metric);
    }
  }

  // FEEDBACK DO USUÁRIO
  async recordFeedback(feedback: Omit<FeedbackData, 'id' | 'timestamp'>): Promise<string> {
    try {
      const feedbackData: FeedbackData = {
        ...feedback,
        id: `feedback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date()
      };

      const { data, error } = await this.supabase
        .from('user_feedback')
        .insert({
          id: feedbackData.id,
          conversation_id: feedbackData.conversationId,
          message_id: feedbackData.messageId,
          rating: feedbackData.rating,
          feedback_type: feedbackData.feedback,
          comment: feedbackData.comment,
          user_type: feedbackData.userType,
          timestamp: feedbackData.timestamp.toISOString()
        })
        .select('id')
        .single();

      if (error) throw error;

      // Registrar métrica de qualidade
      await this.recordMetric({
        timestamp: new Date(),
        metric: 'user_satisfaction',
        value: feedbackData.rating,
        category: 'quality',
        metadata: {
          feedback_type: feedbackData.feedback,
          user_type: feedbackData.userType
        }
      });

      console.log('[Monitoring] Feedback registrado:', feedbackData.id);
      return feedbackData.id;

    } catch (error) {
      console.error('[Monitoring] Erro ao registrar feedback:', error);
      throw error;
    }
  }

  // ANÁLISE DE MÉTRICAS
  async getPerformanceMetrics(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<PerformanceMetrics> {
    try {
      const timeFilter = this.getTimeFilter(timeRange);
      
      const { data, error } = await this.supabase
        .from('system_metrics')
        .select('metric_name, value, timestamp')
        .gte('timestamp', timeFilter)
        .in('category', ['performance', 'usage']);

      if (error) throw error;

      const metrics = this.aggregateMetrics(data);
      
      return {
        responseTime: metrics.response_time?.avg || 0,
        ragProcessingTime: metrics.rag_processing_time?.avg || 0,
        retrievalTime: metrics.retrieval_time?.avg || 0,
        rerankingTime: metrics.reranking_time?.avg || 0,
        contextOptimizationTime: metrics.context_optimization_time?.avg || 0,
        totalTokensUsed: metrics.tokens_used?.sum || 0,
        chunksRetrieved: metrics.chunks_retrieved?.sum || 0,
        documentsProcessed: metrics.documents_processed?.sum || 0
      };

    } catch (error) {
      console.error('[Monitoring] Erro ao obter métricas de performance:', error);
      return this.getEmptyPerformanceMetrics();
    }
  }

  async getUsageMetrics(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<UsageMetrics> {
    try {
      const timeFilter = this.getTimeFilter(timeRange);
      
      // Consultar métricas de uso
      const { data: metricsData, error: metricsError } = await this.supabase
        .from('system_metrics')
        .select('metric_name, value, timestamp, metadata')
        .gte('timestamp', timeFilter)
        .eq('category', 'usage');

      if (metricsError) throw metricsError;

      // Consultar conversas para análise de sessões
      const { data: conversationsData, error: conversationsError } = await this.supabase
        .from('conversations')
        .select('created_at, updated_at')
        .gte('created_at', timeFilter);

      if (conversationsError) throw conversationsError;

      const metrics = this.aggregateMetrics(metricsData);
      
      return {
        totalQueries: metrics.queries?.sum || 0,
        uniqueUsers: metrics.unique_users?.max || 0,
        averageSessionDuration: this.calculateAverageSessionDuration(conversationsData),
        mostCommonQueries: await this.getMostCommonQueries(timeRange),
        peakUsageHours: this.calculatePeakUsageHours(metricsData),
        documentUploads: metrics.document_uploads?.sum || 0,
        whatsappMessages: metrics.whatsapp_messages?.sum || 0
      };

    } catch (error) {
      console.error('[Monitoring] Erro ao obter métricas de uso:', error);
      return this.getEmptyUsageMetrics();
    }
  }

  async getQualityMetrics(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<QualityMetrics> {
    try {
      const timeFilter = this.getTimeFilter(timeRange);
      
      // Métricas de qualidade do sistema
      const { data: metricsData, error: metricsError } = await this.supabase
        .from('system_metrics')
        .select('metric_name, value')
        .gte('timestamp', timeFilter)
        .eq('category', 'quality');

      if (metricsError) throw metricsError;

      // Feedback dos usuários
      const { data: feedbackData, error: feedbackError } = await this.supabase
        .from('user_feedback')
        .select('rating, feedback_type')
        .gte('timestamp', timeFilter);

      if (feedbackError) throw feedbackError;

      const metrics = this.aggregateMetrics(metricsData);
      const avgRating = feedbackData.length > 0 
        ? feedbackData.reduce((sum, f) => sum + f.rating, 0) / feedbackData.length 
        : 0;

      return {
        averageRelevanceScore: metrics.relevance_score?.avg || 0,
        userSatisfactionRating: avgRating,
        hallucinationRate: metrics.hallucination_rate?.avg || 0,
        accuracyScore: metrics.accuracy_score?.avg || 0,
        contextUtilization: metrics.context_utilization?.avg || 0,
        sourceReliability: metrics.source_reliability?.avg || 0
      };

    } catch (error) {
      console.error('[Monitoring] Erro ao obter métricas de qualidade:', error);
      return this.getEmptyQualityMetrics();
    }
  }

  // SISTEMA DE ALERTAS
  private checkAlerts(metric: MetricData): void {
    const alertRules = this.getAlertRules();
    
    for (const rule of alertRules) {
      if (rule.metric === metric.metric && rule.condition(metric.value)) {
        this.createAlert({
          type: rule.type,
          severity: rule.severity,
          message: rule.message.replace('{value}', metric.value.toString()),
          timestamp: new Date(),
          resolved: false,
          metadata: { metric: metric.metric, value: metric.value }
        });
      }
    }
  }

  private createAlert(alert: Omit<Alert, 'id'>): void {
    const newAlert: Alert = {
      ...alert,
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };

    this.alerts.push(newAlert);
    
    // Persistir alerta
    this.supabase
      .from('system_alerts')
      .insert({
        id: newAlert.id,
        type: newAlert.type,
        severity: newAlert.severity,
        message: newAlert.message,
        timestamp: newAlert.timestamp.toISOString(),
        resolved: newAlert.resolved,
        metadata: newAlert.metadata || {}
      })
      .then(() => {
        console.log(`[Monitoring] Alerta criado: ${newAlert.message}`);
      })
      .catch(error => {
        console.error('[Monitoring] Erro ao criar alerta:', error);
      });
  }

  async getActiveAlerts(): Promise<Alert[]> {
    try {
      const { data, error } = await this.supabase
        .from('system_alerts')
        .select('*')
        .eq('resolved', false)
        .order('timestamp', { ascending: false });

      if (error) throw error;

      return data.map(alert => ({
        id: alert.id,
        type: alert.type,
        severity: alert.severity,
        message: alert.message,
        timestamp: new Date(alert.timestamp),
        resolved: alert.resolved,
        metadata: alert.metadata
      }));

    } catch (error) {
      console.error('[Monitoring] Erro ao obter alertas:', error);
      return [];
    }
  }

  // SAÚDE DO SISTEMA
  async getSystemHealth(): Promise<SystemHealth> {
    return this.healthStatus;
  }

  private startHealthChecks(): void {
    // Verificar saúde do sistema a cada 30 segundos
    setInterval(async () => {
      await this.performHealthCheck();
    }, 30000);
  }

  private async performHealthCheck(): Promise<void> {
    try {
      const startTime = Date.now();
      
      // Testar conexão com banco
      const { error } = await this.supabase
        .from('system_metrics')
        .select('id')
        .limit(1);

      const apiLatency = Date.now() - startTime;
      
      // Calcular taxa de erro das últimas 24h
      const errorRate = await this.calculateErrorRate();
      
      // Atualizar status de saúde
      this.healthStatus = {
        status: this.determineHealthStatus(apiLatency, errorRate),
        uptime: Date.now() - this.healthStatus.lastHealthCheck.getTime(),
        errorRate,
        memoryUsage: this.getMemoryUsage(),
        apiLatency,
        databaseConnections: 1, // Simplificado
        lastHealthCheck: new Date()
      };

      // Registrar métrica de saúde
      await this.recordMetric({
        timestamp: new Date(),
        metric: 'system_health',
        value: this.healthStatus.status === 'healthy' ? 1 : 0,
        category: 'performance',
        metadata: {
          api_latency: apiLatency,
          error_rate: errorRate
        }
      });

    } catch (error) {
      console.error('[Monitoring] Erro no health check:', error);
      this.healthStatus.status = 'critical';
    }
  }

  // MÉTODOS AUXILIARES
  private initializeDatabase(): void {
    // Criar tabelas se não existirem (seria feito via migration em produção)
    console.log('[Monitoring] Inicializando banco de dados de monitoramento...');
  }

  private initializeHealthStatus(): SystemHealth {
    return {
      status: 'healthy',
      uptime: 0,
      errorRate: 0,
      memoryUsage: 0,
      apiLatency: 0,
      databaseConnections: 0,
      lastHealthCheck: new Date()
    };
  }

  private getTimeFilter(timeRange: string): string {
    const now = new Date();
    const filters = {
      hour: new Date(now.getTime() - 60 * 60 * 1000),
      day: new Date(now.getTime() - 24 * 60 * 60 * 1000),
      week: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      month: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    };
    return filters[timeRange as keyof typeof filters].toISOString();
  }

  private aggregateMetrics(data: any[]): Record<string, { avg: number; sum: number; max: number; min: number }> {
    const aggregated: Record<string, number[]> = {};
    
    data.forEach(item => {
      if (!aggregated[item.metric_name]) {
        aggregated[item.metric_name] = [];
      }
      aggregated[item.metric_name].push(item.value);
    });

    const result: Record<string, { avg: number; sum: number; max: number; min: number }> = {};
    
    Object.entries(aggregated).forEach(([metric, values]) => {
      result[metric] = {
        avg: values.reduce((a, b) => a + b, 0) / values.length,
        sum: values.reduce((a, b) => a + b, 0),
        max: Math.max(...values),
        min: Math.min(...values)
      };
    });

    return result;
  }

  private calculateAverageSessionDuration(conversations: any[]): number {
    if (conversations.length === 0) return 0;
    
    const durations = conversations.map(conv => {
      const start = new Date(conv.created_at);
      const end = new Date(conv.updated_at);
      return end.getTime() - start.getTime();
    });

    return durations.reduce((a, b) => a + b, 0) / durations.length / 1000 / 60; // em minutos
  }

  private async getMostCommonQueries(timeRange: string): Promise<string[]> {
    // Implementação simplificada - em produção, analisar logs de queries
    return ['Como solicitar serviços?', 'Projetos em andamento', 'Contato do gabinete'];
  }

  private calculatePeakUsageHours(data: any[]): number[] {
    const hourCounts: Record<number, number> = {};
    
    data.forEach(item => {
      const hour = new Date(item.timestamp).getHours();
      hourCounts[hour] = (hourCounts[hour] || 0) + 1;
    });

    return Object.entries(hourCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(([hour]) => parseInt(hour));
  }

  private async calculateErrorRate(): Promise<number> {
    try {
      const timeFilter = this.getTimeFilter('day');
      
      const { data, error } = await this.supabase
        .from('system_metrics')
        .select('value')
        .gte('timestamp', timeFilter)
        .eq('category', 'error');

      if (error) return 0;
      
      const totalErrors = data.reduce((sum, item) => sum + item.value, 0);
      const totalRequests = await this.getTotalRequests(timeFilter);
      
      return totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;

    } catch (error) {
      return 0;
    }
  }

  private async getTotalRequests(timeFilter: string): Promise<number> {
    const { data, error } = await this.supabase
      .from('system_metrics')
      .select('value')
      .gte('timestamp', timeFilter)
      .eq('metric_name', 'queries');

    if (error) return 0;
    return data.reduce((sum, item) => sum + item.value, 0);
  }

  private determineHealthStatus(apiLatency: number, errorRate: number): 'healthy' | 'warning' | 'critical' {
    if (errorRate > 10 || apiLatency > 5000) return 'critical';
    if (errorRate > 5 || apiLatency > 2000) return 'warning';
    return 'healthy';
  }

  private getMemoryUsage(): number {
    // Implementação simplificada - em produção, usar APIs específicas
    return Math.random() * 100;
  }

  private getAlertRules() {
    return [
      {
        metric: 'response_time',
        condition: (value: number) => value > 5000,
        type: 'performance' as const,
        severity: 'high' as const,
        message: 'Tempo de resposta alto: {value}ms'
      },
      {
        metric: 'error_rate',
        condition: (value: number) => value > 5,
        type: 'error' as const,
        severity: 'critical' as const,
        message: 'Taxa de erro elevada: {value}%'
      },
      {
        metric: 'user_satisfaction',
        condition: (value: number) => value < 3,
        type: 'performance' as const,
        severity: 'medium' as const,
        message: 'Satisfação do usuário baixa: {value}/5'
      }
    ];
  }

  private getEmptyPerformanceMetrics(): PerformanceMetrics {
    return {
      responseTime: 0,
      ragProcessingTime: 0,
      retrievalTime: 0,
      rerankingTime: 0,
      contextOptimizationTime: 0,
      totalTokensUsed: 0,
      chunksRetrieved: 0,
      documentsProcessed: 0
    };
  }

  private getEmptyUsageMetrics(): UsageMetrics {
    return {
      totalQueries: 0,
      uniqueUsers: 0,
      averageSessionDuration: 0,
      mostCommonQueries: [],
      peakUsageHours: [],
      documentUploads: 0,
      whatsappMessages: 0
    };
  }

  private getEmptyQualityMetrics(): QualityMetrics {
    return {
      averageRelevanceScore: 0,
      userSatisfactionRating: 0,
      hallucinationRate: 0,
      accuracyScore: 0,
      contextUtilization: 0,
      sourceReliability: 0
    };
  }
}
