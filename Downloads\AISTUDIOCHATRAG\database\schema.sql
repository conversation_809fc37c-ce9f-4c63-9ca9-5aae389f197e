-- Sistema RAG Avançado da Vereadora Rafaela de Nilda - Database Schema
-- Execute este script no SQL Editor do Supabase

-- Habilitar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- <PERSON>bela para armazenar chunks de documentos (RAG)
CREATE TABLE IF NOT EXISTS document_chunks (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding vector(768),
    source TEXT,
    chunk_index INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de conversas do chat com o bot
CREATE TABLE IF NOT EXISTS conversations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT,
    user_id TEXT,
    context_type TEXT DEFAULT 'geral',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Tabela de mensagens das conversas
CREATE TABLE IF NOT EXISTS messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Tabela para documentos processados
CREATE TABLE IF NOT EXISTS processed_documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    original_name TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size INTEGER,
    content_preview TEXT,
    chunks_count INTEGER DEFAULT 0,
    processing_status TEXT DEFAULT 'pending',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Tabelas de Monitoramento
CREATE TABLE IF NOT EXISTS system_metrics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    metric_name TEXT NOT NULL,
    value NUMERIC NOT NULL,
    category TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS user_feedback (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    message_id UUID REFERENCES messages(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback_type TEXT,
    comment TEXT,
    user_type TEXT DEFAULT 'citizen',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS system_alerts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    type TEXT NOT NULL,
    severity TEXT NOT NULL,
    message TEXT NOT NULL,
    resolved BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Tabela de contatos do WhatsApp
CREATE TABLE IF NOT EXISTS whatsapp_contacts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    phone_number TEXT NOT NULL UNIQUE,
    name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Tabela de mensagens do WhatsApp
CREATE TABLE IF NOT EXISTS whatsapp_messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    contact_id UUID REFERENCES whatsapp_contacts(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    sender TEXT NOT NULL CHECK (sender IN ('user', 'bot')),
    message_type TEXT DEFAULT 'text',
    whatsapp_message_id TEXT, -- ID da mensagem no WhatsApp
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Índices para melhor performance
CREATE INDEX IF NOT EXISTS idx_conversations_created_at ON conversations(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_whatsapp_contacts_phone ON whatsapp_contacts(phone_number);
CREATE INDEX IF NOT EXISTS idx_whatsapp_contacts_updated_at ON whatsapp_contacts(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_contact_id ON whatsapp_messages(contact_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_created_at ON whatsapp_messages(created_at DESC);

-- Índices específicos para RAG
CREATE INDEX IF NOT EXISTS idx_document_chunks_source ON document_chunks(source);
CREATE INDEX IF NOT EXISTS idx_document_chunks_created_at ON document_chunks(created_at);
CREATE INDEX IF NOT EXISTS idx_system_metrics_timestamp ON system_metrics(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_system_metrics_category ON system_metrics(category);

-- Índice vetorial para busca semântica (CRÍTICO para RAG)
CREATE INDEX IF NOT EXISTS document_chunks_embedding_idx
ON document_chunks USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Índice para busca textual
CREATE INDEX IF NOT EXISTS document_chunks_content_idx
ON document_chunks USING gin (to_tsvector('portuguese', content));

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para atualizar updated_at
CREATE TRIGGER update_conversations_updated_at 
    BEFORE UPDATE ON conversations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_whatsapp_contacts_updated_at 
    BEFORE UPDATE ON whatsapp_contacts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS (Row Level Security) - Opcional, para futuro suporte a multi-usuário
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_messages ENABLE ROW LEVEL SECURITY;

-- Políticas básicas (permitir tudo por enquanto)
CREATE POLICY "Allow all operations on conversations" ON conversations FOR ALL USING (true);
CREATE POLICY "Allow all operations on messages" ON messages FOR ALL USING (true);
CREATE POLICY "Allow all operations on whatsapp_contacts" ON whatsapp_contacts FOR ALL USING (true);
CREATE POLICY "Allow all operations on whatsapp_messages" ON whatsapp_messages FOR ALL USING (true);

-- Inserir dados de exemplo (opcional)
INSERT INTO conversations (title) VALUES 
    ('Conversa de Exemplo')
ON CONFLICT DO NOTHING;

-- Comentários para documentação
COMMENT ON TABLE conversations IS 'Armazena conversas do chat com o bot Gemini';
COMMENT ON TABLE messages IS 'Armazena mensagens individuais das conversas';
COMMENT ON TABLE whatsapp_contacts IS 'Armazena contatos do WhatsApp';
COMMENT ON TABLE whatsapp_messages IS 'Armazena mensagens enviadas/recebidas via WhatsApp';

COMMENT ON COLUMN conversations.metadata IS 'Dados adicionais da conversa (configurações, contexto, etc.)';
COMMENT ON COLUMN messages.metadata IS 'Dados adicionais da mensagem (fontes RAG, etc.)';
COMMENT ON COLUMN whatsapp_contacts.metadata IS 'Dados adicionais do contato (nome do WhatsApp, foto, etc.)';
COMMENT ON COLUMN whatsapp_messages.metadata IS 'Dados adicionais da mensagem (tipo de mídia, status, etc.)';

-- Adicionar RLS para as novas tabelas
ALTER TABLE document_chunks ENABLE ROW LEVEL SECURITY;
ALTER TABLE processed_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_alerts ENABLE ROW LEVEL SECURITY;

-- Políticas para as novas tabelas
CREATE POLICY "Allow all operations on document_chunks" ON document_chunks FOR ALL USING (true);
CREATE POLICY "Allow all operations on processed_documents" ON processed_documents FOR ALL USING (true);
CREATE POLICY "Allow all operations on system_metrics" ON system_metrics FOR ALL USING (true);
CREATE POLICY "Allow all operations on user_feedback" ON user_feedback FOR ALL USING (true);
CREATE POLICY "Allow all operations on system_alerts" ON system_alerts FOR ALL USING (true);

-- Funções RPC que o sistema está tentando chamar
CREATE OR REPLACE FUNCTION create_extension_if_not_exists()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Verificar se a extensão vector já existe
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'vector') THEN
        CREATE EXTENSION vector;
        RETURN 'Extensão vector criada com sucesso';
    ELSE
        RETURN 'Extensão vector já existe';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'Erro ao criar extensão: ' || SQLERRM;
END;
$$;

CREATE OR REPLACE FUNCTION create_vector_chunks_table()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- A tabela document_chunks já foi criada acima
    -- Esta função apenas confirma que existe
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'document_chunks') THEN
        RETURN 'Tabela document_chunks já existe e está pronta';
    ELSE
        RETURN 'Erro: Tabela document_chunks não encontrada';
    END IF;
END;
$$;

-- Função para busca híbrida (vetorial + textual)
CREATE OR REPLACE FUNCTION hybrid_search(
    query_text TEXT,
    query_embedding vector(768),
    match_threshold FLOAT DEFAULT 0.5,
    match_count INT DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    content TEXT,
    metadata JSONB,
    similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        document_chunks.id,
        document_chunks.content,
        document_chunks.metadata,
        (1 - (document_chunks.embedding <=> query_embedding)) AS similarity
    FROM document_chunks
    WHERE
        (1 - (document_chunks.embedding <=> query_embedding)) > match_threshold
        OR to_tsvector('portuguese', document_chunks.content) @@ plainto_tsquery('portuguese', query_text)
    ORDER BY similarity DESC
    LIMIT match_count;
END;
$$;
