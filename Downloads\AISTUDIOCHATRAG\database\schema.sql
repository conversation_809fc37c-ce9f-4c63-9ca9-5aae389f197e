-- SCHEMA COMPLETO - Sistema RAG da Vereadora Rafaela de <PERSON>lda
-- Copie e cole este script inteiro no SQL Editor do Supabase

-- 1. EXTENSÕES NECESSÁRIAS
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- 2. TAB<PERSON>AS PRINCIPAIS

-- Tabela para chunks de documentos (RAG)
CREATE TABLE IF NOT EXISTS document_chunks (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding vector(768),
    source TEXT,
    chunk_index INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de conversas
CREATE TABLE IF NOT EXISTS conversations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT,
    user_id TEXT,
    context_type TEXT DEFAULT 'geral',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Tabela de mensagens
CREATE TABLE IF NOT EXISTS messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Tabela de documentos processados
CREATE TABLE IF NOT EXISTS processed_documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    original_name TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size INTEGER,
    content_preview TEXT,
    chunks_count INTEGER DEFAULT 0,
    processing_status TEXT DEFAULT 'pending',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Tabela de métricas do sistema
CREATE TABLE IF NOT EXISTS system_metrics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    metric_name TEXT NOT NULL,
    value NUMERIC NOT NULL,
    category TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de feedback dos usuários
CREATE TABLE IF NOT EXISTS user_feedback (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    conversation_id UUID REFERENCES conversations(id) ON DELETE CASCADE,
    message_id UUID REFERENCES messages(id) ON DELETE CASCADE,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    feedback_type TEXT,
    comment TEXT,
    user_type TEXT DEFAULT 'citizen',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabela de alertas do sistema
CREATE TABLE IF NOT EXISTS system_alerts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    type TEXT NOT NULL,
    severity TEXT NOT NULL,
    message TEXT NOT NULL,
    resolved BOOLEAN DEFAULT FALSE,
    metadata JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    resolved_at TIMESTAMP WITH TIME ZONE
);

-- Tabela de contatos WhatsApp
CREATE TABLE IF NOT EXISTS whatsapp_contacts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    phone_number TEXT NOT NULL UNIQUE,
    name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Tabela de mensagens WhatsApp
CREATE TABLE IF NOT EXISTS whatsapp_messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    contact_id UUID REFERENCES whatsapp_contacts(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    sender TEXT NOT NULL CHECK (sender IN ('user', 'bot')),
    message_type TEXT DEFAULT 'text',
    whatsapp_message_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'::jsonb
);

-- 3. ÍNDICES PARA PERFORMANCE

-- Índices básicos
CREATE INDEX IF NOT EXISTS idx_document_chunks_source ON document_chunks(source);
CREATE INDEX IF NOT EXISTS idx_document_chunks_created_at ON document_chunks(created_at);
CREATE INDEX IF NOT EXISTS idx_conversations_created_at ON conversations(created_at);
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at);
CREATE INDEX IF NOT EXISTS idx_system_metrics_timestamp ON system_metrics(timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_system_metrics_category ON system_metrics(category);
CREATE INDEX IF NOT EXISTS idx_whatsapp_contacts_phone ON whatsapp_contacts(phone_number);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_contact_id ON whatsapp_messages(contact_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_created_at ON whatsapp_messages(created_at DESC);

-- Índice vetorial para busca semântica (CRÍTICO para RAG)
CREATE INDEX IF NOT EXISTS document_chunks_embedding_idx 
ON document_chunks USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Índice para busca textual
CREATE INDEX IF NOT EXISTS document_chunks_content_idx 
ON document_chunks USING gin (to_tsvector('portuguese', content));

-- 4. FUNÇÕES AUXILIARES

-- Função para atualizar timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 5. TRIGGERS

CREATE TRIGGER update_conversations_updated_at
    BEFORE UPDATE ON conversations
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_whatsapp_contacts_updated_at
    BEFORE UPDATE ON whatsapp_contacts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_document_chunks_updated_at
    BEFORE UPDATE ON document_chunks
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 6. ROW LEVEL SECURITY (RLS)

ALTER TABLE document_chunks ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE processed_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_feedback ENABLE ROW LEVEL SECURITY;
ALTER TABLE system_alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_messages ENABLE ROW LEVEL SECURITY;

-- 7. POLÍTICAS DE ACESSO (DESENVOLVIMENTO - PERMITIR TUDO)

CREATE POLICY "Allow all operations on document_chunks" ON document_chunks FOR ALL USING (true);
CREATE POLICY "Allow all operations on conversations" ON conversations FOR ALL USING (true);
CREATE POLICY "Allow all operations on messages" ON messages FOR ALL USING (true);
CREATE POLICY "Allow all operations on processed_documents" ON processed_documents FOR ALL USING (true);
CREATE POLICY "Allow all operations on system_metrics" ON system_metrics FOR ALL USING (true);
CREATE POLICY "Allow all operations on user_feedback" ON user_feedback FOR ALL USING (true);
CREATE POLICY "Allow all operations on system_alerts" ON system_alerts FOR ALL USING (true);
CREATE POLICY "Allow all operations on whatsapp_contacts" ON whatsapp_contacts FOR ALL USING (true);
CREATE POLICY "Allow all operations on whatsapp_messages" ON whatsapp_messages FOR ALL USING (true);

-- 8. FUNÇÕES RPC QUE O SISTEMA ESTÁ TENTANDO CHAMAR

CREATE OR REPLACE FUNCTION create_extension_if_not_exists()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'vector') THEN
        CREATE EXTENSION vector;
        RETURN 'Extensão vector criada com sucesso';
    ELSE
        RETURN 'Extensão vector já existe';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'Erro ao criar extensão: ' || SQLERRM;
END;
$$;

CREATE OR REPLACE FUNCTION create_vector_chunks_table()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'document_chunks') THEN
        RETURN 'Tabela document_chunks já existe e está pronta';
    ELSE
        RETURN 'Erro: Tabela document_chunks não encontrada';
    END IF;
END;
$$;

-- 9. FUNÇÃO PARA BUSCA HÍBRIDA (VETORIAL + TEXTUAL)

CREATE OR REPLACE FUNCTION hybrid_search(
    query_text TEXT,
    query_embedding vector(768),
    match_threshold FLOAT DEFAULT 0.5,
    match_count INT DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    content TEXT,
    metadata JSONB,
    similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        document_chunks.id,
        document_chunks.content,
        document_chunks.metadata,
        (1 - (document_chunks.embedding <=> query_embedding)) AS similarity
    FROM document_chunks
    WHERE 
        (1 - (document_chunks.embedding <=> query_embedding)) > match_threshold
        OR to_tsvector('portuguese', document_chunks.content) @@ plainto_tsquery('portuguese', query_text)
    ORDER BY similarity DESC
    LIMIT match_count;
END;
$$;

-- 10. DADOS INICIAIS (OPCIONAL)

INSERT INTO conversations (id, title, context_type) VALUES 
('00000000-0000-0000-0000-000000000001', 'Conversa de Teste', 'geral')
ON CONFLICT (id) DO NOTHING;

INSERT INTO messages (conversation_id, content, role) VALUES 
('00000000-0000-0000-0000-000000000001', 'Olá! Sou a Vereadora Rafaela de Nilda. Como posso ajudá-lo?', 'assistant')
ON CONFLICT DO NOTHING;

INSERT INTO system_metrics (metric_name, value, category) VALUES 
('system_initialized', 1, 'performance'),
('database_ready', 1, 'performance')
ON CONFLICT DO NOTHING;

-- 11. VERIFICAÇÃO FINAL

SELECT 
    'Extensão vector' as item,
    CASE WHEN EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'vector') 
         THEN '✅ OK' ELSE '❌ ERRO' END as status
UNION ALL
SELECT 
    'Tabela document_chunks' as item,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'document_chunks') 
         THEN '✅ OK' ELSE '❌ ERRO' END as status
UNION ALL
SELECT 
    'Função create_extension_if_not_exists' as item,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'create_extension_if_not_exists') 
         THEN '✅ OK' ELSE '❌ ERRO' END as status
UNION ALL
SELECT 
    'Função create_vector_chunks_table' as item,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'create_vector_chunks_table') 
         THEN '✅ OK' ELSE '❌ ERRO' END as status
UNION ALL
SELECT 
    'Índice vetorial' as item,
    CASE WHEN EXISTS (SELECT 1 FROM pg_indexes WHERE indexname = 'document_chunks_embedding_idx') 
         THEN '✅ OK' ELSE '❌ ERRO' END as status;

-- 12. COMENTÁRIOS PARA DOCUMENTAÇÃO

COMMENT ON TABLE document_chunks IS 'Armazena chunks de documentos para busca vetorial RAG';
COMMENT ON TABLE conversations IS 'Armazena conversas do chat com a Vereadora Rafaela';
COMMENT ON TABLE messages IS 'Armazena mensagens individuais das conversas';
COMMENT ON TABLE processed_documents IS 'Armazena informações sobre documentos processados';
COMMENT ON TABLE system_metrics IS 'Armazena métricas de performance e uso do sistema';
COMMENT ON TABLE user_feedback IS 'Armazena feedback dos usuários sobre as respostas';
COMMENT ON TABLE system_alerts IS 'Armazena alertas e notificações do sistema';
COMMENT ON TABLE whatsapp_contacts IS 'Armazena contatos do WhatsApp';
COMMENT ON TABLE whatsapp_messages IS 'Armazena mensagens enviadas/recebidas via WhatsApp';

-- FIM DO SCRIPT
-- Sistema da Vereadora Rafaela de Nilda configurado com sucesso!

