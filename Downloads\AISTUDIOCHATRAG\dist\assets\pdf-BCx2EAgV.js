var qf=Object.defineProperty;var Pd=d=>{throw TypeError(d)};var Yf=(d,t,e)=>t in d?qf(d,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):d[t]=e;var O=(d,t,e)=>Yf(d,typeof t!="symbol"?t+"":t,e),Uh=(d,t,e)=>t.has(d)||Pd("Cannot "+e);var n=(d,t,e)=>(Uh(d,t,"read from private field"),e?e.call(d):t.get(d)),g=(d,t,e)=>t.has(d)?Pd("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(d):t.set(d,e),f=(d,t,e,s)=>(Uh(d,t,"write to private field"),s?s.call(d,e):t.set(d,e),e),m=(d,t,e)=>(Uh(d,t,"access private method"),e);var Jt=(d,t,e,s)=>({set _(i){f(d,t,i,e)},get _(){return n(d,t,s)}});var qa={};qa.d=(d,t)=>{for(var e in t)qa.o(t,e)&&!qa.o(d,e)&&Object.defineProperty(d,e,{enumerable:!0,get:t[e]})};qa.o=(d,t)=>Object.prototype.hasOwnProperty.call(d,t);var $=globalThis.pdfjsLib={};qa.d($,{AbortException:()=>ji,AnnotationEditorLayer:()=>rd,AnnotationEditorParamsType:()=>Y,AnnotationEditorType:()=>j,AnnotationEditorUIManager:()=>rr,AnnotationLayer:()=>jg,AnnotationMode:()=>ui,ColorPicker:()=>fh,DOMSVGFactory:()=>yd,DrawLayer:()=>ld,FeatureTest:()=>ne,GlobalWorkerOptions:()=>ai,ImageKind:()=>Ll,InvalidPDFException:()=>Jh,MissingPDFException:()=>Ka,OPS:()=>Ve,OutputScale:()=>tc,PDFDataRangeTransport:()=>Yu,PDFDateString:()=>gd,PDFWorker:()=>wr,PasswordResponses:()=>Zf,PermissionFlag:()=>Jf,PixelsPerInch:()=>Vi,RenderingCancelledException:()=>fd,TextLayer:()=>Qa,TouchManager:()=>dh,UnexpectedResponseException:()=>lh,Util:()=>D,VerbosityLevel:()=>Fh,XfaLayer:()=>Qu,build:()=>Eg,createValidAbsoluteUrl:()=>ip,fetchData:()=>Bh,getDocument:()=>gg,getFilenameFromUrl:()=>up,getPdfFilenameFromUrl:()=>fp,getXfaPageViewport:()=>pp,isDataScheme:()=>$h,isPdfFile:()=>pd,noContextMenu:()=>rs,normalizeUnicode:()=>hp,setLayerDimensions:()=>nr,shadow:()=>q,stopEvent:()=>Me,version:()=>Sg});const Kt=typeof process=="object"&&process+""=="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&process.type!=="browser"),qd=[1,0,0,1,0,0],Qh=[.001,0,0,.001,0,0],Kf=1e7,jh=1.35,Pe={ANY:1,DISPLAY:2,PRINT:4,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,IS_EDITING:128,OPLIST:256},ui={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},Qf="pdfjs_internal_editor_",j={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15},Y={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35,DRAW_STEP:41},Jf={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},zt={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4},Ll={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},Et={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,WIDGET:20},Pa={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5},Fh={ERRORS:0,WARNINGS:1,INFOS:5},Ve={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93},Zf={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let Nh=Fh.WARNINGS;function tp(d){Number.isInteger(d)&&(Nh=d)}function ep(){return Nh}function Oh(d){Nh>=Fh.INFOS&&console.log(`Info: ${d}`)}function V(d){Nh>=Fh.WARNINGS&&console.log(`Warning: ${d}`)}function it(d){throw new Error(d)}function wt(d,t){d||it(t)}function sp(d){switch(d?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function ip(d,t=null,e=null){if(!d)return null;try{if(e&&typeof d=="string"&&(e.addDefaultProtocol&&d.startsWith("www.")&&d.match(/\./g)?.length>=2&&(d=`http://${d}`),e.tryConvertEncoding))try{d=lp(d)}catch{}const s=t?new URL(d,t):new URL(d);if(sp(s))return s}catch{}return null}function q(d,t,e,s=!1){return Object.defineProperty(d,t,{value:e,enumerable:!s,configurable:!0,writable:!1}),e}const Xi=function(){function t(e,s){this.message=e,this.name=s}return t.prototype=new Error,t.constructor=t,t}();class Rd extends Xi{constructor(t,e){super(t,"PasswordException"),this.code=e}}class Vh extends Xi{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class Jh extends Xi{constructor(t){super(t,"InvalidPDFException")}}class Ka extends Xi{constructor(t){super(t,"MissingPDFException")}}class lh extends Xi{constructor(t,e){super(t,"UnexpectedResponseException"),this.status=e}}class np extends Xi{constructor(t){super(t,"FormatError")}}class ji extends Xi{constructor(t){super(t,"AbortException")}}function Yd(d){(typeof d!="object"||d?.length===void 0)&&it("Invalid argument for bytesToString");const t=d.length,e=8192;if(t<e)return String.fromCharCode.apply(null,d);const s=[];for(let i=0;i<t;i+=e){const r=Math.min(i+e,t),a=d.subarray(i,r);s.push(String.fromCharCode.apply(null,a))}return s.join("")}function Hh(d){typeof d!="string"&&it("Invalid argument for stringToBytes");const t=d.length,e=new Uint8Array(t);for(let s=0;s<t;++s)e[s]=d.charCodeAt(s)&255;return e}function rp(d){return String.fromCharCode(d>>24&255,d>>16&255,d>>8&255,d&255)}function dd(d){const t=Object.create(null);for(const[e,s]of d)t[e]=s;return t}function ap(){const d=new Uint8Array(4);return d[0]=1,new Uint32Array(d.buffer,0,1)[0]===1}function op(){try{return new Function(""),!0}catch{return!1}}class ne{static get isLittleEndian(){return q(this,"isLittleEndian",ap())}static get isEvalSupported(){return q(this,"isEvalSupported",op())}static get isOffscreenCanvasSupported(){return q(this,"isOffscreenCanvasSupported",typeof OffscreenCanvas<"u")}static get isImageDecoderSupported(){return q(this,"isImageDecoderSupported",typeof ImageDecoder<"u")}static get platform(){return typeof navigator<"u"&&typeof navigator?.platform=="string"?q(this,"platform",{isMac:navigator.platform.includes("Mac"),isWindows:navigator.platform.includes("Win"),isFirefox:typeof navigator?.userAgent=="string"&&navigator.userAgent.includes("Firefox")}):q(this,"platform",{isMac:!1,isWindows:!1,isFirefox:!1})}static get isCSSRoundSupported(){return q(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const Wh=Array.from(Array(256).keys(),d=>d.toString(16).padStart(2,"0"));var oi,Il,Zh;class D{static makeHexColor(t,e,s){return`#${Wh[t]}${Wh[e]}${Wh[s]}`}static scaleMinMax(t,e){let s;t[0]?(t[0]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[3],e[3]*=t[3]):(s=e[0],e[0]=e[1],e[1]=s,s=e[2],e[2]=e[3],e[3]=s,t[1]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){const s=t[0]*e[0]+t[1]*e[2]+e[4],i=t[0]*e[1]+t[1]*e[3]+e[5];return[s,i]}static applyInverseTransform(t,e){const s=e[0]*e[3]-e[1]*e[2],i=(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/s,r=(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/s;return[i,r]}static getAxialAlignedBoundingBox(t,e){const s=this.applyTransform(t,e),i=this.applyTransform(t.slice(2,4),e),r=this.applyTransform([t[0],t[3]],e),a=this.applyTransform([t[2],t[1]],e);return[Math.min(s[0],i[0],r[0],a[0]),Math.min(s[1],i[1],r[1],a[1]),Math.max(s[0],i[0],r[0],a[0]),Math.max(s[1],i[1],r[1],a[1])]}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t){const e=[t[0],t[2],t[1],t[3]],s=t[0]*e[0]+t[1]*e[2],i=t[0]*e[1]+t[1]*e[3],r=t[2]*e[0]+t[3]*e[2],a=t[2]*e[1]+t[3]*e[3],o=(s+a)/2,l=Math.sqrt((s+a)**2-4*(s*a-r*i))/2,h=o+l||1,c=o-l||1;return[Math.sqrt(h),Math.sqrt(c)]}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const s=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),i=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(s>i)return null;const r=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),a=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return r>a?null:[s,r,i,a]}static bezierBoundingBox(t,e,s,i,r,a,o,l,h){return h?(h[0]=Math.min(h[0],t,o),h[1]=Math.min(h[1],e,l),h[2]=Math.max(h[2],t,o),h[3]=Math.max(h[3],e,l)):h=[Math.min(t,o),Math.min(e,l),Math.max(t,o),Math.max(e,l)],m(this,oi,Zh).call(this,t,s,r,o,e,i,a,l,3*(-t+3*(s-r)+o),6*(t-2*s+r),3*(s-t),h),m(this,oi,Zh).call(this,t,s,r,o,e,i,a,l,3*(-e+3*(i-a)+l),6*(e-2*i+a),3*(i-e),h),h}}oi=new WeakSet,Il=function(t,e,s,i,r,a,o,l,h,c){if(h<=0||h>=1)return;const u=1-h,p=h*h,b=p*h,A=u*(u*(u*t+3*h*e)+3*p*s)+b*i,y=u*(u*(u*r+3*h*a)+3*p*o)+b*l;c[0]=Math.min(c[0],A),c[1]=Math.min(c[1],y),c[2]=Math.max(c[2],A),c[3]=Math.max(c[3],y)},Zh=function(t,e,s,i,r,a,o,l,h,c,u,p){if(Math.abs(h)<1e-12){Math.abs(c)>=1e-12&&m(this,oi,Il).call(this,t,e,s,i,r,a,o,l,-u/c,p);return}const b=c**2-4*u*h;if(b<0)return;const A=Math.sqrt(b),y=2*h;m(this,oi,Il).call(this,t,e,s,i,r,a,o,l,(-c+A)/y,p),m(this,oi,Il).call(this,t,e,s,i,r,a,o,l,(-c-A)/y,p)},g(D,oi);function lp(d){return decodeURIComponent(escape(d))}let Xh=null,Md=null;function hp(d){return Xh||(Xh=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,Md=new Map([["ﬅ","ſt"]])),d.replaceAll(Xh,(t,e,s)=>e?e.normalize("NFKC"):Md.get(s))}function cp(){if(typeof crypto.randomUUID=="function")return crypto.randomUUID();const d=new Uint8Array(32);return crypto.getRandomValues(d),Yd(d)}const ud="pdfjs_internal_id_";function dp(d){return Uint8Array.prototype.toBase64?d.toBase64():btoa(Yd(d))}typeof Promise.try!="function"&&(Promise.try=function(d,...t){return new Promise(e=>{e(d(...t))})});const Ms="http://www.w3.org/2000/svg",Qi=class Qi{};O(Qi,"CSS",96),O(Qi,"PDF",72),O(Qi,"PDF_TO_CSS_UNITS",Qi.CSS/Qi.PDF);let Vi=Qi;async function Bh(d,t="text"){if(La(d,document.baseURI)){const e=await fetch(d);if(!e.ok)throw new Error(e.statusText);switch(t){case"arraybuffer":return e.arrayBuffer();case"blob":return e.blob();case"json":return e.json()}return e.text()}return new Promise((e,s)=>{const i=new XMLHttpRequest;i.open("GET",d,!0),i.responseType=t,i.onreadystatechange=()=>{if(i.readyState===XMLHttpRequest.DONE){if(i.status===200||i.status===0){switch(t){case"arraybuffer":case"blob":case"json":e(i.response);return}e(i.responseText);return}s(new Error(i.statusText))}},i.send(null)})}class Cl{constructor({viewBox:t,userUnit:e,scale:s,rotation:i,offsetX:r=0,offsetY:a=0,dontFlip:o=!1}){this.viewBox=t,this.userUnit=e,this.scale=s,this.rotation=i,this.offsetX=r,this.offsetY=a,s*=e;const l=(t[2]+t[0])/2,h=(t[3]+t[1])/2;let c,u,p,b;switch(i%=360,i<0&&(i+=360),i){case 180:c=-1,u=0,p=0,b=1;break;case 90:c=0,u=1,p=1,b=0;break;case 270:c=0,u=-1,p=-1,b=0;break;case 0:c=1,u=0,p=0,b=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}o&&(p=-p,b=-b);let A,y,w,v;c===0?(A=Math.abs(h-t[1])*s+r,y=Math.abs(l-t[0])*s+a,w=(t[3]-t[1])*s,v=(t[2]-t[0])*s):(A=Math.abs(l-t[0])*s+r,y=Math.abs(h-t[1])*s+a,w=(t[2]-t[0])*s,v=(t[3]-t[1])*s),this.transform=[c*s,u*s,p*s,b*s,A-c*s*l-p*s*h,y-u*s*l-b*s*h],this.width=w,this.height=v}get rawDims(){const{userUnit:t,viewBox:e}=this,s=e.map(i=>i*t);return q(this,"rawDims",{pageWidth:s[2]-s[0],pageHeight:s[3]-s[1],pageX:s[0],pageY:s[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:s=this.offsetX,offsetY:i=this.offsetY,dontFlip:r=!1}={}){return new Cl({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:r})}convertToViewportPoint(t,e){return D.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){const e=D.applyTransform([t[0],t[1]],this.transform),s=D.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],s[0],s[1]]}convertToPdfPoint(t,e){return D.applyInverseTransform([t,e],this.transform)}}class fd extends Xi{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function $h(d){const t=d.length;let e=0;for(;e<t&&d[e].trim()==="";)e++;return d.substring(e,e+5).toLowerCase()==="data:"}function pd(d){return typeof d=="string"&&/\.pdf$/i.test(d)}function up(d){return[d]=d.split(/[#?]/,1),d.substring(d.lastIndexOf("/")+1)}function fp(d,t="document.pdf"){if(typeof d!="string")return t;if($h(d))return V('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),t;const e=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/,s=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,i=e.exec(d);let r=s.exec(i[1])||s.exec(i[2])||s.exec(i[3]);if(r&&(r=r[0],r.includes("%")))try{r=s.exec(decodeURIComponent(r))[0]}catch{}return r||t}class kd{constructor(){O(this,"started",Object.create(null));O(this,"times",[])}time(t){t in this.started&&V(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){t in this.started||V(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const{name:s}of this.times)e=Math.max(s.length,e);for(const{name:s,start:i,end:r}of this.times)t.push(`${s.padEnd(e)} ${r-i}ms
`);return t.join("")}}function La(d,t){try{const{protocol:e}=t?new URL(d,t):new URL(d);return e==="http:"||e==="https:"}catch{return!1}}function rs(d){d.preventDefault()}function Me(d){d.preventDefault(),d.stopPropagation()}var Za;class gd{static toDateObject(t){if(!t||typeof t!="string")return null;n(this,Za)||f(this,Za,new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const e=n(this,Za).exec(t);if(!e)return null;const s=parseInt(e[1],10);let i=parseInt(e[2],10);i=i>=1&&i<=12?i-1:0;let r=parseInt(e[3],10);r=r>=1&&r<=31?r:1;let a=parseInt(e[4],10);a=a>=0&&a<=23?a:0;let o=parseInt(e[5],10);o=o>=0&&o<=59?o:0;let l=parseInt(e[6],10);l=l>=0&&l<=59?l:0;const h=e[7]||"Z";let c=parseInt(e[8],10);c=c>=0&&c<=23?c:0;let u=parseInt(e[9],10)||0;return u=u>=0&&u<=59?u:0,h==="-"?(a+=c,o+=u):h==="+"&&(a-=c,o-=u),new Date(Date.UTC(s,i,r,a,o,l))}}Za=new WeakMap,g(gd,Za);function pp(d,{scale:t=1,rotation:e=0}){const{width:s,height:i}=d.attributes.style,r=[0,0,parseInt(s),parseInt(i)];return new Cl({viewBox:r,userUnit:1,scale:t,rotation:e})}function md(d){if(d.startsWith("#")){const t=parseInt(d.slice(1),16);return[(t&16711680)>>16,(t&65280)>>8,t&255]}return d.startsWith("rgb(")?d.slice(4,-1).split(",").map(t=>parseInt(t)):d.startsWith("rgba(")?d.slice(5,-1).split(",").map(t=>parseInt(t)).slice(0,3):(V(`Not a valid color format: "${d}"`),[0,0,0])}function gp(d){const t=document.createElement("span");t.style.visibility="hidden",document.body.append(t);for(const e of d.keys()){t.style.color=e;const s=window.getComputedStyle(t).color;d.set(e,md(s))}t.remove()}function ht(d){const{a:t,b:e,c:s,d:i,e:r,f:a}=d.getTransform();return[t,e,s,i,r,a]}function ls(d){const{a:t,b:e,c:s,d:i,e:r,f:a}=d.getTransform().invertSelf();return[t,e,s,i,r,a]}function nr(d,t,e=!1,s=!0){if(t instanceof Cl){const{pageWidth:i,pageHeight:r}=t.rawDims,{style:a}=d,o=ne.isCSSRoundSupported,l=`var(--scale-factor) * ${i}px`,h=`var(--scale-factor) * ${r}px`,c=o?`round(down, ${l}, var(--scale-round-x, 1px))`:`calc(${l})`,u=o?`round(down, ${h}, var(--scale-round-y, 1px))`:`calc(${h})`;!e||t.rotation%180===0?(a.width=c,a.height=u):(a.width=u,a.height=c)}s&&d.setAttribute("data-main-rotation",t.rotation)}class tc{constructor(){const t=window.devicePixelRatio||1;this.sx=t,this.sy=t}get scaled(){return this.sx!==1||this.sy!==1}get symmetric(){return this.sx===this.sy}}var fi,Zi,We,tn,to,eo,mh,Kd,re,Qd,Jd,Dl,Zd,sc;const Is=class Is{constructor(t){g(this,re);g(this,fi,null);g(this,Zi,null);g(this,We);g(this,tn,null);g(this,to,null);f(this,We,t),n(Is,eo)||f(Is,eo,Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button"}))}render(){const t=f(this,fi,document.createElement("div"));t.classList.add("editToolbar","hidden"),t.setAttribute("role","toolbar");const e=n(this,We)._uiManager._signal;t.addEventListener("contextmenu",rs,{signal:e}),t.addEventListener("pointerdown",m(Is,mh,Kd),{signal:e});const s=f(this,tn,document.createElement("div"));s.className="buttons",t.append(s);const i=n(this,We).toolbarPosition;if(i){const{style:r}=t,a=n(this,We)._uiManager.direction==="ltr"?1-i[0]:i[0];r.insetInlineEnd=`${100*a}%`,r.top=`calc(${100*i[1]}% + var(--editor-toolbar-vert-offset))`}return m(this,re,Zd).call(this),t}get div(){return n(this,fi)}hide(){n(this,fi).classList.add("hidden"),n(this,Zi)?.hideDropdown()}show(){n(this,fi).classList.remove("hidden"),n(this,to)?.shown()}async addAltText(t){const e=await t.render();m(this,re,Dl).call(this,e),n(this,tn).prepend(e,n(this,re,sc)),f(this,to,t)}addColorPicker(t){f(this,Zi,t);const e=t.renderButton();m(this,re,Dl).call(this,e),n(this,tn).prepend(e,n(this,re,sc))}remove(){n(this,fi).remove(),n(this,Zi)?.destroy(),f(this,Zi,null)}};fi=new WeakMap,Zi=new WeakMap,We=new WeakMap,tn=new WeakMap,to=new WeakMap,eo=new WeakMap,mh=new WeakSet,Kd=function(t){t.stopPropagation()},re=new WeakSet,Qd=function(t){n(this,We)._focusEventsAllowed=!1,Me(t)},Jd=function(t){n(this,We)._focusEventsAllowed=!0,Me(t)},Dl=function(t){const e=n(this,We)._uiManager._signal;t.addEventListener("focusin",m(this,re,Qd).bind(this),{capture:!0,signal:e}),t.addEventListener("focusout",m(this,re,Jd).bind(this),{capture:!0,signal:e}),t.addEventListener("contextmenu",rs,{signal:e})},Zd=function(){const{editorType:t,_uiManager:e}=n(this,We),s=document.createElement("button");s.className="delete",s.tabIndex=0,s.setAttribute("data-l10n-id",n(Is,eo)[t]),m(this,re,Dl).call(this,s),s.addEventListener("click",i=>{e.delete()},{signal:e._signal}),n(this,tn).append(s)},sc=function(){const t=document.createElement("div");return t.className="divider",t},g(Is,mh),g(Is,eo,null);let ec=Is;var so,en,sn,Wi,tu,eu,su;class mp{constructor(t){g(this,Wi);g(this,so,null);g(this,en,null);g(this,sn);f(this,sn,t)}show(t,e,s){const[i,r]=m(this,Wi,eu).call(this,e,s),{style:a}=n(this,en)||f(this,en,m(this,Wi,tu).call(this));t.append(n(this,en)),a.insetInlineEnd=`${100*i}%`,a.top=`calc(${100*r}% + var(--editor-toolbar-vert-offset))`}hide(){n(this,en).remove()}}so=new WeakMap,en=new WeakMap,sn=new WeakMap,Wi=new WeakSet,tu=function(){const t=f(this,en,document.createElement("div"));t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",rs,{signal:n(this,sn)._signal});const e=f(this,so,document.createElement("div"));return e.className="buttons",t.append(e),m(this,Wi,su).call(this),t},eu=function(t,e){let s=0,i=0;for(const r of t){const a=r.y+r.height;if(a<s)continue;const o=r.x+(e?r.width:0);if(a>s){i=o,s=a;continue}e?o>i&&(i=o):o<i&&(i=o)}return[e?1-i:i,s]},su=function(){const t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const s=n(this,sn)._signal;t.addEventListener("contextmenu",rs,{signal:s}),t.addEventListener("click",()=>{n(this,sn).highlightSelection("floating_button")},{signal:s}),n(this,so).append(t)};function hh(d,t,e){for(const s of e)t.addEventListener(s,d[s].bind(d))}var bh;class bp{constructor(){g(this,bh,0)}get id(){return`${Qf}${Jt(this,bh)._++}`}}bh=new WeakMap;var _r,io,Vt,Sr,Fl;const wd=class wd{constructor(){g(this,Sr);g(this,_r,cp());g(this,io,0);g(this,Vt,null)}static get _isSVGFittingCanvas(){const t='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',s=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),i=new Image;i.src=t;const r=i.decode().then(()=>(s.drawImage(i,0,0,1,1,0,0,1,3),new Uint32Array(s.getImageData(0,0,1,1).data.buffer)[0]===0));return q(this,"_isSVGFittingCanvas",r)}async getFromFile(t){const{lastModified:e,name:s,size:i,type:r}=t;return m(this,Sr,Fl).call(this,`${e}_${s}_${i}_${r}`,t)}async getFromUrl(t){return m(this,Sr,Fl).call(this,t,t)}async getFromBlob(t,e){const s=await e;return m(this,Sr,Fl).call(this,t,s)}async getFromId(t){n(this,Vt)||f(this,Vt,new Map);const e=n(this,Vt).get(t);if(!e)return null;if(e.bitmap)return e.refCounter+=1,e;if(e.file)return this.getFromFile(e.file);if(e.blobPromise){const{blobPromise:s}=e;return delete e.blobPromise,this.getFromBlob(e.id,s)}return this.getFromUrl(e.url)}getFromCanvas(t,e){n(this,Vt)||f(this,Vt,new Map);let s=n(this,Vt).get(t);if(s?.bitmap)return s.refCounter+=1,s;const i=new OffscreenCanvas(e.width,e.height);return i.getContext("2d").drawImage(e,0,0),s={bitmap:i.transferToImageBitmap(),id:`image_${n(this,_r)}_${Jt(this,io)._++}`,refCounter:1,isSvg:!1},n(this,Vt).set(t,s),n(this,Vt).set(s.id,s),s}getSvgUrl(t){const e=n(this,Vt).get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){n(this,Vt)||f(this,Vt,new Map);const e=n(this,Vt).get(t);if(!e||(e.refCounter-=1,e.refCounter!==0))return;const{bitmap:s}=e;if(!e.url&&!e.file){const i=new OffscreenCanvas(s.width,s.height);i.getContext("bitmaprenderer").transferFromImageBitmap(s),e.blobPromise=i.convertToBlob()}s.close?.(),e.bitmap=null}isValidId(t){return t.startsWith(`image_${n(this,_r)}_`)}};_r=new WeakMap,io=new WeakMap,Vt=new WeakMap,Sr=new WeakSet,Fl=async function(t,e){n(this,Vt)||f(this,Vt,new Map);let s=n(this,Vt).get(t);if(s===null)return null;if(s?.bitmap)return s.refCounter+=1,s;try{s||(s={bitmap:null,id:`image_${n(this,_r)}_${Jt(this,io)._++}`,refCounter:0,isSvg:!1});let i;if(typeof e=="string"?(s.url=e,i=await Bh(e,"blob")):e instanceof File?i=s.file=e:e instanceof Blob&&(i=e),i.type==="image/svg+xml"){const r=wd._isSVGFittingCanvas,a=new FileReader,o=new Image,l=new Promise((h,c)=>{o.onload=()=>{s.bitmap=o,s.isSvg=!0,h()},a.onload=async()=>{const u=s.svgUrl=a.result;o.src=await r?`${u}#svgView(preserveAspectRatio(none))`:u},o.onerror=a.onerror=c});a.readAsDataURL(i),await l}else s.bitmap=await createImageBitmap(i);s.refCounter=1}catch(i){V(i),s=null}return n(this,Vt).set(t,s),s&&n(this,Vt).set(s.id,s),s};let ic=wd;var bt,pi,no,ft;class Ap{constructor(t=128){g(this,bt,[]);g(this,pi,!1);g(this,no);g(this,ft,-1);f(this,no,t)}add({cmd:t,undo:e,post:s,mustExec:i,type:r=NaN,overwriteIfSameType:a=!1,keepUndo:o=!1}){if(i&&t(),n(this,pi))return;const l={cmd:t,undo:e,post:s,type:r};if(n(this,ft)===-1){n(this,bt).length>0&&(n(this,bt).length=0),f(this,ft,0),n(this,bt).push(l);return}if(a&&n(this,bt)[n(this,ft)].type===r){o&&(l.undo=n(this,bt)[n(this,ft)].undo),n(this,bt)[n(this,ft)]=l;return}const h=n(this,ft)+1;h===n(this,no)?n(this,bt).splice(0,1):(f(this,ft,h),h<n(this,bt).length&&n(this,bt).splice(h)),n(this,bt).push(l)}undo(){if(n(this,ft)===-1)return;f(this,pi,!0);const{undo:t,post:e}=n(this,bt)[n(this,ft)];t(),e?.(),f(this,pi,!1),f(this,ft,n(this,ft)-1)}redo(){if(n(this,ft)<n(this,bt).length-1){f(this,ft,n(this,ft)+1),f(this,pi,!0);const{cmd:t,post:e}=n(this,bt)[n(this,ft)];t(),e?.(),f(this,pi,!1)}}hasSomethingToUndo(){return n(this,ft)!==-1}hasSomethingToRedo(){return n(this,ft)<n(this,bt).length-1}cleanType(t){if(n(this,ft)!==-1){for(let e=n(this,ft);e>=0;e--)if(n(this,bt)[e].type!==t){n(this,bt).splice(e+1,n(this,ft)-e),f(this,ft,e);return}n(this,bt).length=0,f(this,ft,-1)}}destroy(){f(this,bt,null)}}bt=new WeakMap,pi=new WeakMap,no=new WeakMap,ft=new WeakMap;var Ah,iu;class xl{constructor(t){g(this,Ah);this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:e}=ne.platform;for(const[s,i,r={}]of t)for(const a of s){const o=a.startsWith("mac+");e&&o?(this.callbacks.set(a.slice(4),{callback:i,options:r}),this.allKeys.add(a.split("+").at(-1))):!e&&!o&&(this.callbacks.set(a,{callback:i,options:r}),this.allKeys.add(a.split("+").at(-1)))}}exec(t,e){if(!this.allKeys.has(e.key))return;const s=this.callbacks.get(m(this,Ah,iu).call(this,e));if(!s)return;const{callback:i,options:{bubbles:r=!1,args:a=[],checker:o=null}}=s;o&&!o(t,e)||(i.bind(t,...a,e)(),r||Me(e))}}Ah=new WeakSet,iu=function(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e};const yh=class yh{get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return gp(t),q(this,"_colors",t)}convert(t){const e=md(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[s,i]of this._colors)if(i.every((r,a)=>r===e[a]))return yh._colorsMapping.get(s);return e}getHexCode(t){const e=this._colors.get(t);return e?D.makeHexColor(...e):t}};O(yh,"_colorsMapping",new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]));let nc=yh;var Er,me,xt,Ft,Cr,Fs,xr,De,gi,nn,Tr,rn,ds,Xe,an,ro,ao,Pr,oo,us,mi,Rr,bi,fs,vh,Ai,lo,yi,on,ho,co,Rt,Z,Ns,ln,uo,fo,vi,ps,Os,po,Fe,P,Nl,rc,nu,ru,Ol,au,ou,lu,ac,hu,oc,lc,cu,Zt,ks,du,uu,hc,fu,Ia,cc;const Ar=class Ar{constructor(t,e,s,i,r,a,o,l,h,c,u,p,b){g(this,P);g(this,Er,new AbortController);g(this,me,null);g(this,xt,new Map);g(this,Ft,new Map);g(this,Cr,null);g(this,Fs,null);g(this,xr,null);g(this,De,new Ap);g(this,gi,null);g(this,nn,null);g(this,Tr,0);g(this,rn,new Set);g(this,ds,null);g(this,Xe,null);g(this,an,new Set);O(this,"_editorUndoBar",null);g(this,ro,!1);g(this,ao,!1);g(this,Pr,!1);g(this,oo,null);g(this,us,null);g(this,mi,null);g(this,Rr,null);g(this,bi,!1);g(this,fs,null);g(this,vh,new bp);g(this,Ai,!1);g(this,lo,!1);g(this,yi,null);g(this,on,null);g(this,ho,null);g(this,co,null);g(this,Rt,j.NONE);g(this,Z,new Set);g(this,Ns,null);g(this,ln,null);g(this,uo,null);g(this,fo,{isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1});g(this,vi,[0,0]);g(this,ps,null);g(this,Os,null);g(this,po,null);g(this,Fe,null);const A=this._signal=n(this,Er).signal;f(this,Os,t),f(this,po,e),f(this,Cr,s),this._eventBus=i,i._on("editingaction",this.onEditingAction.bind(this),{signal:A}),i._on("pagechanging",this.onPageChanging.bind(this),{signal:A}),i._on("scalechanging",this.onScaleChanging.bind(this),{signal:A}),i._on("rotationchanging",this.onRotationChanging.bind(this),{signal:A}),i._on("setpreference",this.onSetPreference.bind(this),{signal:A}),i._on("switchannotationeditorparams",y=>this.updateParams(y.type,y.value),{signal:A}),m(this,P,au).call(this),m(this,P,cu).call(this),m(this,P,ac).call(this),f(this,Fs,r.annotationStorage),f(this,oo,r.filterFactory),f(this,ln,a),f(this,Rr,o||null),f(this,ro,l),f(this,ao,h),f(this,Pr,c),f(this,co,u||null),this.viewParameters={realScale:Vi.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1,this._editorUndoBar=p||null,this._supportsPinchToZoom=b!==!1}static get _keyboardManager(){const t=Ar.prototype,e=a=>n(a,Os).contains(document.activeElement)&&document.activeElement.tagName!=="BUTTON"&&a.hasSomethingToControl(),s=(a,{target:o})=>{if(o instanceof HTMLInputElement){const{type:l}=o;return l!=="text"&&l!=="number"}return!0},i=this.TRANSLATE_SMALL,r=this.TRANSLATE_BIG;return q(this,"_keyboardManager",new xl([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:s}],[["ctrl+z","mac+meta+z"],t.undo,{checker:s}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:s}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:s}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(a,{target:o})=>!(o instanceof HTMLButtonElement)&&n(a,Os).contains(o)&&!a.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(a,{target:o})=>!(o instanceof HTMLButtonElement)&&n(a,Os).contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-r,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[r,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-r],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,r],checker:e}]]))}destroy(){n(this,Fe)?.resolve(),f(this,Fe,null),n(this,Er)?.abort(),f(this,Er,null),this._signal=null;for(const t of n(this,Ft).values())t.destroy();n(this,Ft).clear(),n(this,xt).clear(),n(this,an).clear(),f(this,me,null),n(this,Z).clear(),n(this,De).destroy(),n(this,Cr)?.destroy(),n(this,fs)?.hide(),f(this,fs,null),n(this,us)&&(clearTimeout(n(this,us)),f(this,us,null)),n(this,ps)&&(clearTimeout(n(this,ps)),f(this,ps,null)),this._editorUndoBar?.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return n(this,co)}get useNewAltTextFlow(){return n(this,ao)}get useNewAltTextWhenAddingImage(){return n(this,Pr)}get hcmFilter(){return q(this,"hcmFilter",n(this,ln)?n(this,oo).addHCMFilter(n(this,ln).foreground,n(this,ln).background):"none")}get direction(){return q(this,"direction",getComputedStyle(n(this,Os)).direction)}get highlightColors(){return q(this,"highlightColors",n(this,Rr)?new Map(n(this,Rr).split(",").map(t=>t.split("=").map(e=>e.trim()))):null)}get highlightColorNames(){return q(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,t=>t.reverse())):null)}setCurrentDrawingSession(t){t?(this.unselectAll(),this.disableUserSelect(!0)):this.disableUserSelect(!1),f(this,nn,t)}setMainHighlightColorPicker(t){f(this,ho,t)}editAltText(t,e=!1){n(this,Cr)?.editAltText(this,t,e)}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal}),this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){switch(t){case"enableNewAltTextWhenAddingImage":f(this,Pr,e);break}}onPageChanging({pageNumber:t}){f(this,Tr,t-1)}focusMainContainer(){n(this,Os).focus()}findParent(t,e){for(const s of n(this,Ft).values()){const{x:i,y:r,width:a,height:o}=s.div.getBoundingClientRect();if(t>=i&&t<=i+a&&e>=r&&e<=r+o)return s}return null}disableUserSelect(t=!1){n(this,po).classList.toggle("noUserSelect",t)}addShouldRescale(t){n(this,an).add(t)}removeShouldRescale(t){n(this,an).delete(t)}onScaleChanging({scale:t}){this.commitOrRemove(),this.viewParameters.realScale=t*Vi.PDF_TO_CSS_UNITS;for(const e of n(this,an))e.onScaleChanging();n(this,nn)?.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:s,anchorOffset:i,focusNode:r,focusOffset:a}=e,o=e.toString(),h=m(this,P,Nl).call(this,e).closest(".textLayer"),c=this.getSelectionBoxes(h);if(!c)return;e.empty();const u=m(this,P,rc).call(this,h),p=n(this,Rt)===j.NONE,b=()=>{u?.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:c,anchorNode:s,anchorOffset:i,focusNode:r,focusOffset:a,text:o}),p&&this.showAllEditors("highlight",!0,!0)};if(p){this.switchToMode(j.HIGHLIGHT,b);return}b()}addToAnnotationStorage(t){!t.isEmpty()&&n(this,Fs)&&!n(this,Fs).has(t.id)&&n(this,Fs).setValue(t.id,t)}blur(){if(this.isShiftKeyDown=!1,n(this,bi)&&(f(this,bi,!1),m(this,P,Ol).call(this,"main_toolbar")),!this.hasSelection)return;const{activeElement:t}=document;for(const e of n(this,Z))if(e.div.contains(t)){f(this,on,[e,t]),e._focusEventsAllowed=!1;break}}focus(){if(!n(this,on))return;const[t,e]=n(this,on);f(this,on,null),e.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:this._signal}),e.focus()}addEditListeners(){m(this,P,ac).call(this),m(this,P,oc).call(this)}removeEditListeners(){m(this,P,hu).call(this),m(this,P,lc).call(this)}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const s of n(this,Xe))if(s.isHandlingMimeForPasting(e)){t.dataTransfer.dropEffect="copy",t.preventDefault();return}}drop(t){for(const e of t.dataTransfer.items)for(const s of n(this,Xe))if(s.isHandlingMimeForPasting(e.type)){s.paste(e,this.currentLayer),t.preventDefault();return}}copy(t){if(t.preventDefault(),n(this,me)?.commitOrRemove(),!this.hasSelection)return;const e=[];for(const s of n(this,Z)){const i=s.serialize(!0);i&&e.push(i)}e.length!==0&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}async paste(t){t.preventDefault();const{clipboardData:e}=t;for(const r of e.items)for(const a of n(this,Xe))if(a.isHandlingMimeForPasting(r.type)){a.paste(r,this.currentLayer);return}let s=e.getData("application/pdfjs");if(!s)return;try{s=JSON.parse(s)}catch(r){V(`paste: "${r.message}".`);return}if(!Array.isArray(s))return;this.unselectAll();const i=this.currentLayer;try{const r=[];for(const l of s){const h=await i.deserialize(l);if(!h)return;r.push(h)}const a=()=>{for(const l of r)m(this,P,hc).call(this,l);m(this,P,cc).call(this,r)},o=()=>{for(const l of r)l.remove()};this.addCommands({cmd:a,undo:o,mustExec:!0})}catch(r){V(`paste: "${r.message}".`)}}keydown(t){!this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!0),n(this,Rt)!==j.NONE&&!this.isEditorHandlingKeyboard&&Ar._keyboardManager.exec(this,t)}keyup(t){this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!1,n(this,bi)&&(f(this,bi,!1),m(this,P,Ol).call(this,"main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu");break}}setEditingState(t){t?(m(this,P,ou).call(this),m(this,P,oc).call(this),m(this,P,Zt).call(this,{isEditing:n(this,Rt)!==j.NONE,isEmpty:m(this,P,Ia).call(this),hasSomethingToUndo:n(this,De).hasSomethingToUndo(),hasSomethingToRedo:n(this,De).hasSomethingToRedo(),hasSelectedEditor:!1})):(m(this,P,lu).call(this),m(this,P,lc).call(this),m(this,P,Zt).call(this,{isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!n(this,Xe)){f(this,Xe,t);for(const e of n(this,Xe))m(this,P,ks).call(this,e.defaultPropertiesToUpdate)}}getId(){return n(this,vh).id}get currentLayer(){return n(this,Ft).get(n(this,Tr))}getLayer(t){return n(this,Ft).get(t)}get currentPageIndex(){return n(this,Tr)}addLayer(t){n(this,Ft).set(t.pageIndex,t),n(this,Ai)?t.enable():t.disable()}removeLayer(t){n(this,Ft).delete(t.pageIndex)}async updateMode(t,e=null,s=!1){if(n(this,Rt)!==t&&!(n(this,Fe)&&(await n(this,Fe).promise,!n(this,Fe)))){if(f(this,Fe,Promise.withResolvers()),f(this,Rt,t),t===j.NONE){this.setEditingState(!1),m(this,P,uu).call(this),this._editorUndoBar?.hide(),n(this,Fe).resolve();return}this.setEditingState(!0),await m(this,P,du).call(this),this.unselectAll();for(const i of n(this,Ft).values())i.updateMode(t);if(!e){s&&this.addNewEditorFromKeyboard(),n(this,Fe).resolve();return}for(const i of n(this,xt).values())i.annotationElementId===e?(this.setSelected(i),i.enterInEditMode()):i.unselect();n(this,Fe).resolve()}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t!==n(this,Rt)&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){if(n(this,Xe)){switch(t){case Y.CREATE:this.currentLayer.addNewEditor();return;case Y.HIGHLIGHT_DEFAULT_COLOR:n(this,ho)?.updateColor(e);break;case Y.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(n(this,uo)||f(this,uo,new Map)).set(t,e),this.showAllEditors("highlight",e);break}for(const s of n(this,Z))s.updateParams(t,e);for(const s of n(this,Xe))s.updateDefaultParams(t,e)}}showAllEditors(t,e,s=!1){for(const r of n(this,xt).values())r.editorType===t&&r.show(e);(n(this,uo)?.get(Y.HIGHLIGHT_SHOW_ALL)??!0)!==e&&m(this,P,ks).call(this,[[Y.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(n(this,lo)!==t){f(this,lo,t);for(const e of n(this,Ft).values())t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}}getEditors(t){const e=[];for(const s of n(this,xt).values())s.pageIndex===t&&e.push(s);return e}getEditor(t){return n(this,xt).get(t)}addEditor(t){n(this,xt).set(t.id,t)}removeEditor(t){t.div.contains(document.activeElement)&&(n(this,us)&&clearTimeout(n(this,us)),f(this,us,setTimeout(()=>{this.focusMainContainer(),f(this,us,null)},0))),n(this,xt).delete(t.id),this.unselect(t),(!t.annotationElementId||!n(this,rn).has(t.annotationElementId))&&n(this,Fs)?.remove(t.id)}addDeletedAnnotationElement(t){n(this,rn).add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return n(this,rn).has(t)}removeDeletedAnnotationElement(t){n(this,rn).delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}setActiveEditor(t){n(this,me)!==t&&(f(this,me,t),t&&m(this,P,ks).call(this,t.propertiesToUpdate))}updateUI(t){n(this,P,fu)===t&&m(this,P,ks).call(this,t.propertiesToUpdate)}updateUIForDefaultProperties(t){m(this,P,ks).call(this,t.defaultPropertiesToUpdate)}toggleSelected(t){if(n(this,Z).has(t)){n(this,Z).delete(t),t.unselect(),m(this,P,Zt).call(this,{hasSelectedEditor:this.hasSelection});return}n(this,Z).add(t),t.select(),m(this,P,ks).call(this,t.propertiesToUpdate),m(this,P,Zt).call(this,{hasSelectedEditor:!0})}setSelected(t){n(this,nn)?.commitOrRemove();for(const e of n(this,Z))e!==t&&e.unselect();n(this,Z).clear(),n(this,Z).add(t),t.select(),m(this,P,ks).call(this,t.propertiesToUpdate),m(this,P,Zt).call(this,{hasSelectedEditor:!0})}isSelected(t){return n(this,Z).has(t)}get firstSelectedEditor(){return n(this,Z).values().next().value}unselect(t){t.unselect(),n(this,Z).delete(t),m(this,P,Zt).call(this,{hasSelectedEditor:this.hasSelection})}get hasSelection(){return n(this,Z).size!==0}get isEnterHandled(){return n(this,Z).size===1&&this.firstSelectedEditor.isEnterHandled}undo(){n(this,De).undo(),m(this,P,Zt).call(this,{hasSomethingToUndo:n(this,De).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:m(this,P,Ia).call(this)}),this._editorUndoBar?.hide()}redo(){n(this,De).redo(),m(this,P,Zt).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:n(this,De).hasSomethingToRedo(),isEmpty:m(this,P,Ia).call(this)})}addCommands(t){n(this,De).add(t),m(this,P,Zt).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:m(this,P,Ia).call(this)})}cleanUndoStack(t){n(this,De).cleanType(t)}delete(){this.commitOrRemove();const t=this.currentLayer?.endDrawingSession(!0);if(!this.hasSelection&&!t)return;const e=t?[t]:[...n(this,Z)],s=()=>{this._editorUndoBar?.show(i,e.length===1?e[0].editorType:e.length);for(const r of e)r.remove()},i=()=>{for(const r of e)m(this,P,hc).call(this,r)};this.addCommands({cmd:s,undo:i,mustExec:!0})}commitOrRemove(){n(this,me)?.commitOrRemove()}hasSomethingToControl(){return n(this,me)||this.hasSelection}selectAll(){for(const t of n(this,Z))t.commit();m(this,P,cc).call(this,n(this,xt).values())}unselectAll(){if(!(n(this,me)&&(n(this,me).commitOrRemove(),n(this,Rt)!==j.NONE))&&!n(this,nn)?.commitOrRemove()&&this.hasSelection){for(const t of n(this,Z))t.unselect();n(this,Z).clear(),m(this,P,Zt).call(this,{hasSelectedEditor:!1})}}translateSelectedEditors(t,e,s=!1){if(s||this.commitOrRemove(),!this.hasSelection)return;n(this,vi)[0]+=t,n(this,vi)[1]+=e;const[i,r]=n(this,vi),a=[...n(this,Z)],o=1e3;n(this,ps)&&clearTimeout(n(this,ps)),f(this,ps,setTimeout(()=>{f(this,ps,null),n(this,vi)[0]=n(this,vi)[1]=0,this.addCommands({cmd:()=>{for(const l of a)n(this,xt).has(l.id)&&l.translateInPage(i,r)},undo:()=>{for(const l of a)n(this,xt).has(l.id)&&l.translateInPage(-i,-r)},mustExec:!1})},o));for(const l of a)l.translateInPage(t,e)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),f(this,ds,new Map);for(const t of n(this,Z))n(this,ds).set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!n(this,ds))return!1;this.disableUserSelect(!1);const t=n(this,ds);f(this,ds,null);let e=!1;for(const[{x:i,y:r,pageIndex:a},o]of t)o.newX=i,o.newY=r,o.newPageIndex=a,e||(e=i!==o.savedX||r!==o.savedY||a!==o.savedPageIndex);if(!e)return!1;const s=(i,r,a,o)=>{if(n(this,xt).has(i.id)){const l=n(this,Ft).get(o);l?i._setParentAndPosition(l,r,a):(i.pageIndex=o,i.x=r,i.y=a)}};return this.addCommands({cmd:()=>{for(const[i,{newX:r,newY:a,newPageIndex:o}]of t)s(i,r,a,o)},undo:()=>{for(const[i,{savedX:r,savedY:a,savedPageIndex:o}]of t)s(i,r,a,o)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(n(this,ds))for(const s of n(this,ds).keys())s.drag(t,e)}rebuild(t){if(t.parent===null){const e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||n(this,Z).size===1&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return n(this,me)===t}getActive(){return n(this,me)}getMode(){return n(this,Rt)}get imageManager(){return q(this,"imageManager",new ic)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let h=0,c=e.rangeCount;h<c;h++)if(!t.contains(e.getRangeAt(h).commonAncestorContainer))return null;const{x:s,y:i,width:r,height:a}=t.getBoundingClientRect();let o;switch(t.getAttribute("data-main-rotation")){case"90":o=(h,c,u,p)=>({x:(c-i)/a,y:1-(h+u-s)/r,width:p/a,height:u/r});break;case"180":o=(h,c,u,p)=>({x:1-(h+u-s)/r,y:1-(c+p-i)/a,width:u/r,height:p/a});break;case"270":o=(h,c,u,p)=>({x:1-(c+p-i)/a,y:(h-s)/r,width:p/a,height:u/r});break;default:o=(h,c,u,p)=>({x:(h-s)/r,y:(c-i)/a,width:u/r,height:p/a});break}const l=[];for(let h=0,c=e.rangeCount;h<c;h++){const u=e.getRangeAt(h);if(!u.collapsed)for(const{x:p,y:b,width:A,height:y}of u.getClientRects())A===0||y===0||l.push(o(p,b,A,y))}return l.length===0?null:l}addChangedExistingAnnotation({annotationElementId:t,id:e}){(n(this,xr)||f(this,xr,new Map)).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){n(this,xr)?.delete(t)}renderAnnotationElement(t){const e=n(this,xr)?.get(t.data.id);if(!e)return;const s=n(this,Fs).getRawValue(e);s&&(n(this,Rt)===j.NONE&&!s.hasBeenModified||s.renderAnnotationElement(t))}};Er=new WeakMap,me=new WeakMap,xt=new WeakMap,Ft=new WeakMap,Cr=new WeakMap,Fs=new WeakMap,xr=new WeakMap,De=new WeakMap,gi=new WeakMap,nn=new WeakMap,Tr=new WeakMap,rn=new WeakMap,ds=new WeakMap,Xe=new WeakMap,an=new WeakMap,ro=new WeakMap,ao=new WeakMap,Pr=new WeakMap,oo=new WeakMap,us=new WeakMap,mi=new WeakMap,Rr=new WeakMap,bi=new WeakMap,fs=new WeakMap,vh=new WeakMap,Ai=new WeakMap,lo=new WeakMap,yi=new WeakMap,on=new WeakMap,ho=new WeakMap,co=new WeakMap,Rt=new WeakMap,Z=new WeakMap,Ns=new WeakMap,ln=new WeakMap,uo=new WeakMap,fo=new WeakMap,vi=new WeakMap,ps=new WeakMap,Os=new WeakMap,po=new WeakMap,Fe=new WeakMap,P=new WeakSet,Nl=function({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t},rc=function(t){const{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(const s of n(this,Ft).values())if(s.hasTextLayer(t))return s;return null},nu=function(){const t=document.getSelection();if(!t||t.isCollapsed)return;const s=m(this,P,Nl).call(this,t).closest(".textLayer"),i=this.getSelectionBoxes(s);i&&(n(this,fs)||f(this,fs,new mp(this)),n(this,fs).show(s,i,this.direction==="ltr"))},ru=function(){const t=document.getSelection();if(!t||t.isCollapsed){n(this,Ns)&&(n(this,fs)?.hide(),f(this,Ns,null),m(this,P,Zt).call(this,{hasSelectedText:!1}));return}const{anchorNode:e}=t;if(e===n(this,Ns))return;const i=m(this,P,Nl).call(this,t).closest(".textLayer");if(!i){n(this,Ns)&&(n(this,fs)?.hide(),f(this,Ns,null),m(this,P,Zt).call(this,{hasSelectedText:!1}));return}if(n(this,fs)?.hide(),f(this,Ns,e),m(this,P,Zt).call(this,{hasSelectedText:!0}),!(n(this,Rt)!==j.HIGHLIGHT&&n(this,Rt)!==j.NONE)&&(n(this,Rt)===j.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),f(this,bi,this.isShiftKeyDown),!this.isShiftKeyDown)){const r=n(this,Rt)===j.HIGHLIGHT?m(this,P,rc).call(this,i):null;r?.toggleDrawing();const a=new AbortController,o=this.combinedSignal(a),l=h=>{h.type==="pointerup"&&h.button!==0||(a.abort(),r?.toggleDrawing(!0),h.type==="pointerup"&&m(this,P,Ol).call(this,"main_toolbar"))};window.addEventListener("pointerup",l,{signal:o}),window.addEventListener("blur",l,{signal:o})}},Ol=function(t=""){n(this,Rt)===j.HIGHLIGHT?this.highlightSelection(t):n(this,ro)&&m(this,P,nu).call(this)},au=function(){document.addEventListener("selectionchange",m(this,P,ru).bind(this),{signal:this._signal})},ou=function(){if(n(this,mi))return;f(this,mi,new AbortController);const t=this.combinedSignal(n(this,mi));window.addEventListener("focus",this.focus.bind(this),{signal:t}),window.addEventListener("blur",this.blur.bind(this),{signal:t})},lu=function(){n(this,mi)?.abort(),f(this,mi,null)},ac=function(){if(n(this,yi))return;f(this,yi,new AbortController);const t=this.combinedSignal(n(this,yi));window.addEventListener("keydown",this.keydown.bind(this),{signal:t}),window.addEventListener("keyup",this.keyup.bind(this),{signal:t})},hu=function(){n(this,yi)?.abort(),f(this,yi,null)},oc=function(){if(n(this,gi))return;f(this,gi,new AbortController);const t=this.combinedSignal(n(this,gi));document.addEventListener("copy",this.copy.bind(this),{signal:t}),document.addEventListener("cut",this.cut.bind(this),{signal:t}),document.addEventListener("paste",this.paste.bind(this),{signal:t})},lc=function(){n(this,gi)?.abort(),f(this,gi,null)},cu=function(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t}),document.addEventListener("drop",this.drop.bind(this),{signal:t})},Zt=function(t){Object.entries(t).some(([s,i])=>n(this,fo)[s]!==i)&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(n(this,fo),t)}),n(this,Rt)===j.HIGHLIGHT&&t.hasSelectedEditor===!1&&m(this,P,ks).call(this,[[Y.HIGHLIGHT_FREE,!0]]))},ks=function(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})},du=async function(){if(!n(this,Ai)){f(this,Ai,!0);const t=[];for(const e of n(this,Ft).values())t.push(e.enable());await Promise.all(t);for(const e of n(this,xt).values())e.enable()}},uu=function(){if(this.unselectAll(),n(this,Ai)){f(this,Ai,!1);for(const t of n(this,Ft).values())t.disable();for(const t of n(this,xt).values())t.disable()}},hc=function(t){const e=n(this,Ft).get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))},fu=function(){let t=null;for(t of n(this,Z));return t},Ia=function(){if(n(this,xt).size===0)return!0;if(n(this,xt).size===1)for(const t of n(this,xt).values())return t.isEmpty();return!1},cc=function(t){for(const e of n(this,Z))e.unselect();n(this,Z).clear();for(const e of t)e.isEmpty()||(n(this,Z).add(e),e.select());m(this,P,Zt).call(this,{hasSelectedEditor:this.hasSelection})},O(Ar,"TRANSLATE_SMALL",1),O(Ar,"TRANSLATE_BIG",10);let rr=Ar;var Mt,gs,qe,Mr,ms,be,kr,bs,de,Hs,hn,As,wi,ns,Da,Hl;const te=class te{constructor(t){g(this,ns);g(this,Mt,null);g(this,gs,!1);g(this,qe,null);g(this,Mr,null);g(this,ms,null);g(this,be,null);g(this,kr,!1);g(this,bs,null);g(this,de,null);g(this,Hs,null);g(this,hn,null);g(this,As,!1);f(this,de,t),f(this,As,t._uiManager.useNewAltTextFlow),n(te,wi)||f(te,wi,Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"}))}static initialize(t){te._l10n??(te._l10n=t)}async render(){const t=f(this,qe,document.createElement("button"));t.className="altText",t.tabIndex="0";const e=f(this,Mr,document.createElement("span"));t.append(e),n(this,As)?(t.classList.add("new"),t.setAttribute("data-l10n-id",n(te,wi).missing),e.setAttribute("data-l10n-id",n(te,wi)["missing-label"])):(t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button"),e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label"));const s=n(this,de)._uiManager._signal;t.addEventListener("contextmenu",rs,{signal:s}),t.addEventListener("pointerdown",r=>r.stopPropagation(),{signal:s});const i=r=>{r.preventDefault(),n(this,de)._uiManager.editAltText(n(this,de)),n(this,As)&&n(this,de)._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:n(this,ns,Da)}})};return t.addEventListener("click",i,{capture:!0,signal:s}),t.addEventListener("keydown",r=>{r.target===t&&r.key==="Enter"&&(f(this,kr,!0),i(r))},{signal:s}),await m(this,ns,Hl).call(this),t}finish(){n(this,qe)&&(n(this,qe).focus({focusVisible:n(this,kr)}),f(this,kr,!1))}isEmpty(){return n(this,As)?n(this,Mt)===null:!n(this,Mt)&&!n(this,gs)}hasData(){return n(this,As)?n(this,Mt)!==null||!!n(this,Hs):this.isEmpty()}get guessedText(){return n(this,Hs)}async setGuessedText(t){n(this,Mt)===null&&(f(this,Hs,t),f(this,hn,await te._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:t})),m(this,ns,Hl).call(this))}toggleAltTextBadge(t=!1){if(!n(this,As)||n(this,Mt)){n(this,bs)?.remove(),f(this,bs,null);return}if(!n(this,bs)){const e=f(this,bs,document.createElement("div"));e.className="noAltTextBadge",n(this,de).div.append(e)}n(this,bs).classList.toggle("hidden",!t)}serialize(t){let e=n(this,Mt);return!t&&n(this,Hs)===e&&(e=n(this,hn)),{altText:e,decorative:n(this,gs),guessedText:n(this,Hs),textWithDisclaimer:n(this,hn)}}get data(){return{altText:n(this,Mt),decorative:n(this,gs)}}set data({altText:t,decorative:e,guessedText:s,textWithDisclaimer:i,cancel:r=!1}){s&&(f(this,Hs,s),f(this,hn,i)),!(n(this,Mt)===t&&n(this,gs)===e)&&(r||(f(this,Mt,t),f(this,gs,e)),m(this,ns,Hl).call(this))}toggle(t=!1){n(this,qe)&&(!t&&n(this,be)&&(clearTimeout(n(this,be)),f(this,be,null)),n(this,qe).disabled=!t)}shown(){n(this,de)._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:n(this,ns,Da)}})}destroy(){n(this,qe)?.remove(),f(this,qe,null),f(this,Mr,null),f(this,ms,null),n(this,bs)?.remove(),f(this,bs,null)}};Mt=new WeakMap,gs=new WeakMap,qe=new WeakMap,Mr=new WeakMap,ms=new WeakMap,be=new WeakMap,kr=new WeakMap,bs=new WeakMap,de=new WeakMap,Hs=new WeakMap,hn=new WeakMap,As=new WeakMap,wi=new WeakMap,ns=new WeakSet,Da=function(){return n(this,Mt)&&"added"||n(this,Mt)===null&&this.guessedText&&"review"||"missing"},Hl=async function(){const t=n(this,qe);if(!t)return;if(n(this,As)){if(t.classList.toggle("done",!!n(this,Mt)),t.setAttribute("data-l10n-id",n(te,wi)[n(this,ns,Da)]),n(this,Mr)?.setAttribute("data-l10n-id",n(te,wi)[`${n(this,ns,Da)}-label`]),!n(this,Mt)){n(this,ms)?.remove();return}}else{if(!n(this,Mt)&&!n(this,gs)){t.classList.remove("done"),n(this,ms)?.remove();return}t.classList.add("done"),t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let e=n(this,ms);if(!e){f(this,ms,e=document.createElement("span")),e.className="tooltip",e.setAttribute("role","tooltip"),e.id=`alt-text-tooltip-${n(this,de).id}`;const i=100,r=n(this,de)._uiManager._signal;r.addEventListener("abort",()=>{clearTimeout(n(this,be)),f(this,be,null)},{once:!0}),t.addEventListener("mouseenter",()=>{f(this,be,setTimeout(()=>{f(this,be,null),n(this,ms).classList.add("show"),n(this,de)._reportTelemetry({action:"alt_text_tooltip"})},i))},{signal:r}),t.addEventListener("mouseleave",()=>{n(this,be)&&(clearTimeout(n(this,be)),f(this,be,null)),n(this,ms)?.classList.remove("show")},{signal:r})}n(this,gs)?e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip"):(e.removeAttribute("data-l10n-id"),e.textContent=n(this,Mt)),e.parentNode||t.append(e),n(this,de).getImageForAltText()?.setAttribute("aria-describedby",e.id)},g(te,wi,null),O(te,"_l10n",null);let ch=te;var go,cn,mo,bo,Ao,yo,vo,Lr,Bs,dn,_i,hi,pu,gu,dc;const _d=class _d{constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:s=null,onPinchStart:i=null,onPinching:r=null,onPinchEnd:a=null,signal:o}){g(this,hi);g(this,go);g(this,cn,!1);g(this,mo,null);g(this,bo);g(this,Ao);g(this,yo);g(this,vo);g(this,Lr);g(this,Bs,null);g(this,dn);g(this,_i,null);f(this,go,t),f(this,mo,s),f(this,bo,e),f(this,Ao,i),f(this,yo,r),f(this,vo,a),f(this,dn,new AbortController),f(this,Lr,AbortSignal.any([o,n(this,dn).signal])),t.addEventListener("touchstart",m(this,hi,pu).bind(this),{passive:!1,signal:n(this,Lr)})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return q(this,"MIN_TOUCH_DISTANCE_TO_PINCH",35/(window.devicePixelRatio||1))}destroy(){n(this,dn)?.abort(),f(this,dn,null)}};go=new WeakMap,cn=new WeakMap,mo=new WeakMap,bo=new WeakMap,Ao=new WeakMap,yo=new WeakMap,vo=new WeakMap,Lr=new WeakMap,Bs=new WeakMap,dn=new WeakMap,_i=new WeakMap,hi=new WeakSet,pu=function(t){var i,r,a;if((i=n(this,bo))!=null&&i.call(this)||t.touches.length<2)return;if(!n(this,_i)){f(this,_i,new AbortController);const o=AbortSignal.any([n(this,Lr),n(this,_i).signal]),l=n(this,go),h={signal:o,passive:!1};l.addEventListener("touchmove",m(this,hi,gu).bind(this),h),l.addEventListener("touchend",m(this,hi,dc).bind(this),h),l.addEventListener("touchcancel",m(this,hi,dc).bind(this),h),(r=n(this,Ao))==null||r.call(this)}if(Me(t),t.touches.length!==2||(a=n(this,mo))!=null&&a.call(this)){f(this,Bs,null);return}let[e,s]=t.touches;e.identifier>s.identifier&&([e,s]=[s,e]),f(this,Bs,{touch0X:e.screenX,touch0Y:e.screenY,touch1X:s.screenX,touch1Y:s.screenY})},gu=function(t){var S;if(!n(this,Bs)||t.touches.length!==2)return;let[e,s]=t.touches;e.identifier>s.identifier&&([e,s]=[s,e]);const{screenX:i,screenY:r}=e,{screenX:a,screenY:o}=s,l=n(this,Bs),{touch0X:h,touch0Y:c,touch1X:u,touch1Y:p}=l,b=u-h,A=p-c,y=a-i,w=o-r,v=Math.hypot(y,w)||1,_=Math.hypot(b,A)||1;if(!n(this,cn)&&Math.abs(_-v)<=_d.MIN_TOUCH_DISTANCE_TO_PINCH)return;if(l.touch0X=i,l.touch0Y=r,l.touch1X=a,l.touch1Y=o,t.preventDefault(),!n(this,cn)){f(this,cn,!0);return}const E=[(i+a)/2,(r+o)/2];(S=n(this,yo))==null||S.call(this,E,_,v)},dc=function(t){var e;n(this,_i).abort(),f(this,_i,null),(e=n(this,vo))==null||e.call(this),n(this,Bs)&&(t.preventDefault(),f(this,Bs,null),f(this,cn,!1))};let dh=_d;var un,Ye,at,Ir,Si,wo,fn,Nt,pn,$s,Ei,_o,gn,Ae,So,mn,Gs,ys,Dr,Fr,Ne,bn,Eo,wh,H,uc,Co,fc,Bl,mu,bu,pc,$l,gc,Au,yu,vu,mc,wu,bc,_u,Su,Eu,Ac,Fa;const W=class W{constructor(t){g(this,H);g(this,un,null);g(this,Ye,null);g(this,at,null);g(this,Ir,!1);g(this,Si,null);g(this,wo,"");g(this,fn,!1);g(this,Nt,null);g(this,pn,null);g(this,$s,null);g(this,Ei,null);g(this,_o,"");g(this,gn,!1);g(this,Ae,null);g(this,So,!1);g(this,mn,!1);g(this,Gs,!1);g(this,ys,null);g(this,Dr,0);g(this,Fr,0);g(this,Ne,null);g(this,bn,null);O(this,"_editToolbar",null);O(this,"_initialOptions",Object.create(null));O(this,"_initialData",null);O(this,"_isVisible",!0);O(this,"_uiManager",null);O(this,"_focusEventsAllowed",!0);g(this,Eo,!1);g(this,wh,W._zIndex++);this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:s,pageHeight:i,pageX:r,pageY:a}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[s,i],this.pageTranslation=[r,a];const[o,l]=this.parentDimensions;this.x=t.x/o,this.y=t.y/l,this.isAttachedToDOM=!1,this.deleted=!1}static get _resizerKeyboardManager(){const t=W.prototype._resizeWithKeyboard,e=rr.TRANSLATE_SMALL,s=rr.TRANSLATE_BIG;return q(this,"_resizerKeyboardManager",new xl([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-s,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[s,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-s]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,s]}],[["Escape","mac+Escape"],W.prototype._stopResizingWithKeyboard]]))}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get isDrawer(){return!1}static get _defaultLineColor(){return q(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new yp({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t,e){if(W._l10n??(W._l10n=t),W._l10nResizer||(W._l10nResizer=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"})),W._borderLineWidth!==-1)return;const s=getComputedStyle(document.documentElement);W._borderLineWidth=parseFloat(s.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){it("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return n(this,Eo)}set _isDraggable(t){f(this,Eo,t),this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(t*2),this.y+=this.width*t/(e*2);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(t*2),this.y-=this.width*t/(e*2);break;default:this.x-=this.width/2,this.y-=this.height/2;break}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=n(this,wh)}setParent(t){t!==null?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):m(this,H,Fa).call(this),this.parent=t}focusin(t){this._focusEventsAllowed&&(n(this,gn)?f(this,gn,!1):this.parent.setSelected(this))}focusout(t){!this._focusEventsAllowed||!this.isAttachedToDOM||t.relatedTarget?.closest(`#${this.id}`)||(t.preventDefault(),this.parent?.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,s,i){const[r,a]=this.parentDimensions;[s,i]=this.screenToPageTranslation(s,i),this.x=(t+s)/r,this.y=(e+i)/a,this.fixAndSetPosition()}translate(t,e){m(this,H,uc).call(this,this.parentDimensions,t,e)}translateInPage(t,e){n(this,Ae)||f(this,Ae,[this.x,this.y,this.width,this.height]),m(this,H,uc).call(this,this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}drag(t,e){n(this,Ae)||f(this,Ae,[this.x,this.y,this.width,this.height]);const{div:s,parentDimensions:[i,r]}=this;if(this.x+=t/i,this.y+=e/r,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:u,y:p}=this.div.getBoundingClientRect();this.parent.findNewParent(this,u,p)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:a,y:o}=this;const[l,h]=this.getBaseTranslation();a+=l,o+=h;const{style:c}=s;c.left=`${(100*a).toFixed(2)}%`,c.top=`${(100*o).toFixed(2)}%`,this._onTranslating(a,o),s.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!n(this,Ae)&&(n(this,Ae)[0]!==this.x||n(this,Ae)[1]!==this.y)}get _hasBeenResized(){return!!n(this,Ae)&&(n(this,Ae)[2]!==this.width||n(this,Ae)[3]!==this.height)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:s}=W,i=s/t,r=s/e;switch(this.rotation){case 90:return[-i,r];case 180:return[i,r];case 270:return[i,-r];default:return[-i,-r]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const{div:{style:e},pageDimensions:[s,i]}=this;let{x:r,y:a,width:o,height:l}=this;if(o*=s,l*=i,r*=s,a*=i,this._mustFixPosition)switch(t){case 0:r=Math.max(0,Math.min(s-o,r)),a=Math.max(0,Math.min(i-l,a));break;case 90:r=Math.max(0,Math.min(s-l,r)),a=Math.min(i,Math.max(o,a));break;case 180:r=Math.min(s,Math.max(o,r)),a=Math.min(i,Math.max(l,a));break;case 270:r=Math.min(s,Math.max(l,r)),a=Math.max(0,Math.min(i-o,a));break}this.x=r/=s,this.y=a/=i;const[h,c]=this.getBaseTranslation();r+=h,a+=c,e.left=`${(100*r).toFixed(2)}%`,e.top=`${(100*a).toFixed(2)}%`,this.moveInDOM()}screenToPageTranslation(t,e){var s;return m(s=W,Co,fc).call(s,t,e,this.parentRotation)}pageTranslationToScreen(t,e){var s;return m(s=W,Co,fc).call(s,t,e,360-this.parentRotation)}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,s]}=this;return[e*t,s*t]}setDims(t,e){const[s,i]=this.parentDimensions,{style:r}=this.div;r.width=`${(100*t/s).toFixed(2)}%`,n(this,fn)||(r.height=`${(100*e/i).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:s}=t,i=s.endsWith("%"),r=!n(this,fn)&&e.endsWith("%");if(i&&r)return;const[a,o]=this.parentDimensions;i||(t.width=`${(100*parseFloat(s)/a).toFixed(2)}%`),!n(this,fn)&&!r&&(t.height=`${(100*parseFloat(e)/o).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}_onResized(){}static _round(t){return Math.round(t*1e4)/1e4}_onResizing(){}altTextFinish(){n(this,at)?.finish()}async addEditToolbar(){return this._editToolbar||n(this,mn)?this._editToolbar:(this._editToolbar=new ec(this),this.div.append(this._editToolbar.render()),n(this,at)&&await this._editToolbar.addAltText(n(this,at)),this._editToolbar)}removeEditToolbar(){this._editToolbar&&(this._editToolbar.remove(),this._editToolbar=null,n(this,at)?.destroy())}addContainer(t){const e=this._editToolbar?.div;e?e.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){n(this,at)||(ch.initialize(W._l10n),f(this,at,new ch(this)),n(this,un)&&(n(this,at).data=n(this,un),f(this,un,null)),await this.addEditToolbar())}get altTextData(){return n(this,at)?.data}set altTextData(t){n(this,at)&&(n(this,at).data=t)}get guessedAltText(){return n(this,at)?.guessedText}async setGuessedAltText(t){await n(this,at)?.setGuessedText(t)}serializeAltText(t){return n(this,at)?.serialize(t)}hasAltText(){return!!n(this,at)&&!n(this,at).isEmpty()}hasAltTextData(){return n(this,at)?.hasData()??!1}render(){this.div=document.createElement("div"),this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360),this.div.className=this.name,this.div.setAttribute("id",this.id),this.div.tabIndex=n(this,Ir)?-1:0,this._isVisible||this.div.classList.add("hidden"),this.setInForeground(),m(this,H,bc).call(this);const[t,e]=this.parentDimensions;this.parentRotation%180!==0&&(this.div.style.maxWidth=`${(100*e/t).toFixed(2)}%`,this.div.style.maxHeight=`${(100*t/e).toFixed(2)}%`);const[s,i]=this.getInitialTranslation();return this.translate(s,i),hh(this,this.div,["pointerdown"]),this.isResizable&&this._uiManager._supportsPinchToZoom&&(n(this,bn)||f(this,bn,new dh({container:this.div,isPinchingDisabled:()=>!this.isSelected,onPinchStart:m(this,H,Au).bind(this),onPinching:m(this,H,yu).bind(this),onPinchEnd:m(this,H,vu).bind(this),signal:this._uiManager._signal}))),this._uiManager._editorUndoBar?.hide(),this.div}pointerdown(t){const{isMac:e}=ne.platform;if(t.button!==0||t.ctrlKey&&e){t.preventDefault();return}if(f(this,gn,!0),this._isDraggable){m(this,H,wu).call(this,t);return}m(this,H,mc).call(this,t)}get isSelected(){return this._uiManager.isSelected(this)}_onStartDragging(){}_onStopDragging(){}moveInDOM(){n(this,ys)&&clearTimeout(n(this,ys)),f(this,ys,setTimeout(()=>{f(this,ys,null),this.parent?.moveEditorInDOM(this)},0))}_setParentAndPosition(t,e,s){t.changeParent(this),this.x=e,this.y=s,this.fixAndSetPosition(),this._onTranslated()}getRect(t,e,s=this.rotation){const i=this.parentScale,[r,a]=this.pageDimensions,[o,l]=this.pageTranslation,h=t/i,c=e/i,u=this.x*r,p=this.y*a,b=this.width*r,A=this.height*a;switch(s){case 0:return[u+h+o,a-p-c-A+l,u+h+b+o,a-p-c+l];case 90:return[u+c+o,a-p+h+l,u+c+A+o,a-p+h+b+l];case 180:return[u-h-b+o,a-p+c+l,u-h+o,a-p+c+A+l];case 270:return[u-c-A+o,a-p-h-b+l,u-c+o,a-p-h+l];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[s,i,r,a]=t,o=r-s,l=a-i;switch(this.rotation){case 0:return[s,e-a,o,l];case 90:return[s,e-i,l,o];case 180:return[r,e-i,o,l];case 270:return[r,e-a,l,o];default:throw new Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){f(this,mn,!0)}disableEditMode(){f(this,mn,!1)}isInEditMode(){return n(this,mn)}shouldGetKeyboardEvents(){return n(this,Gs)}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){const{top:t,left:e,bottom:s,right:i}=this.getClientDimensions(),{innerHeight:r,innerWidth:a}=window;return e<a&&i>0&&t<r&&s>0}rebuild(){m(this,H,bc).call(this)}rotate(t){}resize(){}serializeDeleted(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:this._initialData?.popupRef||""}}serialize(t=!1,e=null){it("An editor must be serializable")}static async deserialize(t,e,s){const i=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:s});i.rotation=t.rotation,f(i,un,t.accessibilityData);const[r,a]=i.pageDimensions,[o,l,h,c]=i.getRectInCurrentCoords(t.rect,a);return i.x=o/r,i.y=l/a,i.width=h/r,i.height=c/a,i}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||this.serialize()!==null)}remove(){if(n(this,Ei)?.abort(),f(this,Ei,null),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),n(this,ys)&&(clearTimeout(n(this,ys)),f(this,ys,null)),m(this,H,Fa).call(this),this.removeEditToolbar(),n(this,Ne)){for(const t of n(this,Ne).values())clearTimeout(t);f(this,Ne,null)}this.parent=null,n(this,bn)?.destroy(),f(this,bn,null)}get isResizable(){return!1}makeResizable(){this.isResizable&&(m(this,H,mu).call(this),n(this,Nt).classList.remove("hidden"),hh(this,this.div,["keydown"]))}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||t.key!=="Enter")return;this._uiManager.setSelected(this),f(this,$s,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height});const e=n(this,Nt).children;if(!n(this,Ye)){f(this,Ye,Array.from(e));const a=m(this,H,_u).bind(this),o=m(this,H,Su).bind(this),l=this._uiManager._signal;for(const h of n(this,Ye)){const c=h.getAttribute("data-resizer-name");h.setAttribute("role","spinbutton"),h.addEventListener("keydown",a,{signal:l}),h.addEventListener("blur",o,{signal:l}),h.addEventListener("focus",m(this,H,Eu).bind(this,c),{signal:l}),h.setAttribute("data-l10n-id",W._l10nResizer[c])}}const s=n(this,Ye)[0];let i=0;for(const a of e){if(a===s)break;i++}const r=(360-this.rotation+this.parentRotation)%360/90*(n(this,Ye).length/4);if(r!==i){if(r<i)for(let o=0;o<i-r;o++)n(this,Nt).append(n(this,Nt).firstChild);else if(r>i)for(let o=0;o<r-i;o++)n(this,Nt).firstChild.before(n(this,Nt).lastChild);let a=0;for(const o of e){const h=n(this,Ye)[a++].getAttribute("data-resizer-name");o.setAttribute("data-l10n-id",W._l10nResizer[h])}}m(this,H,Ac).call(this,0),f(this,Gs,!0),n(this,Nt).firstChild.focus({focusVisible:!0}),t.preventDefault(),t.stopImmediatePropagation()}_resizeWithKeyboard(t,e){n(this,Gs)&&m(this,H,gc).call(this,n(this,_o),{deltaX:t,deltaY:e,fromKeyboard:!0})}_stopResizingWithKeyboard(){m(this,H,Fa).call(this),this.div.focus()}select(){if(this.makeResizable(),this.div?.classList.add("selectedEditor"),!this._editToolbar){this.addEditToolbar().then(()=>{this.div?.classList.contains("selectedEditor")&&this._editToolbar?.show()});return}this._editToolbar?.show(),n(this,at)?.toggleAltTextBadge(!1)}unselect(){n(this,Nt)?.classList.add("hidden"),this.div?.classList.remove("selectedEditor"),this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),this._editToolbar?.hide(),n(this,at)?.toggleAltTextBadge(!0)}updateParams(t,e){}disableEditing(){}enableEditing(){}enterInEditMode(){}getImageForAltText(){return null}get contentDiv(){return this.div}get isEditing(){return n(this,So)}set isEditing(t){f(this,So,t),this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){f(this,fn,!0);const s=t/e,{style:i}=this.div;i.aspectRatio=s,i.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){n(this,Ne)||f(this,Ne,new Map);const{action:s}=t;let i=n(this,Ne).get(s);i&&clearTimeout(i),i=setTimeout(()=>{this._reportTelemetry(t),n(this,Ne).delete(s),n(this,Ne).size===0&&f(this,Ne,null)},W._telemetryTimeout),n(this,Ne).set(s,i);return}t.type||(t.type=this.editorType),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),f(this,Ir,!1)}disable(){this.div&&(this.div.tabIndex=-1),f(this,Ir,!0)}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(!e)e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.container.prepend(e);else if(e.nodeName==="CANVAS"){const s=e;e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),s.before(e)}return e}resetAnnotationElement(t){const{firstChild:e}=t.container;e?.nodeName==="DIV"&&e.classList.contains("annotationContent")&&e.remove()}};un=new WeakMap,Ye=new WeakMap,at=new WeakMap,Ir=new WeakMap,Si=new WeakMap,wo=new WeakMap,fn=new WeakMap,Nt=new WeakMap,pn=new WeakMap,$s=new WeakMap,Ei=new WeakMap,_o=new WeakMap,gn=new WeakMap,Ae=new WeakMap,So=new WeakMap,mn=new WeakMap,Gs=new WeakMap,ys=new WeakMap,Dr=new WeakMap,Fr=new WeakMap,Ne=new WeakMap,bn=new WeakMap,Eo=new WeakMap,wh=new WeakMap,H=new WeakSet,uc=function([t,e],s,i){[s,i]=this.screenToPageTranslation(s,i),this.x+=s/t,this.y+=i/e,this._onTranslating(this.x,this.y),this.fixAndSetPosition()},Co=new WeakSet,fc=function(t,e,s){switch(s){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}},Bl=function(t){switch(t){case 90:{const[e,s]=this.pageDimensions;return[0,-e/s,s/e,0]}case 180:return[-1,0,0,-1];case 270:{const[e,s]=this.pageDimensions;return[0,e/s,-s/e,0]}default:return[1,0,0,1]}},mu=function(){if(n(this,Nt))return;f(this,Nt,document.createElement("div")),n(this,Nt).classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const s of t){const i=document.createElement("div");n(this,Nt).append(i),i.classList.add("resizer",s),i.setAttribute("data-resizer-name",s),i.addEventListener("pointerdown",m(this,H,bu).bind(this,s),{signal:e}),i.addEventListener("contextmenu",rs,{signal:e}),i.tabIndex=-1}this.div.prepend(n(this,Nt))},bu=function(t,e){e.preventDefault();const{isMac:s}=ne.platform;if(e.button!==0||e.ctrlKey&&s)return;n(this,at)?.toggle(!1);const i=this._isDraggable;this._isDraggable=!1,f(this,pn,[e.screenX,e.screenY]);const r=new AbortController,a=this._uiManager.combinedSignal(r);this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",m(this,H,gc).bind(this,t),{passive:!0,capture:!0,signal:a}),window.addEventListener("touchmove",Me,{passive:!1,signal:a}),window.addEventListener("contextmenu",rs,{signal:a}),f(this,$s,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height});const o=this.parent.div.style.cursor,l=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const h=()=>{r.abort(),this.parent.togglePointerEvents(!0),n(this,at)?.toggle(!0),this._isDraggable=i,this.parent.div.style.cursor=o,this.div.style.cursor=l,m(this,H,$l).call(this)};window.addEventListener("pointerup",h,{signal:a}),window.addEventListener("blur",h,{signal:a})},pc=function(t,e,s,i){this.width=s,this.height=i,this.x=t,this.y=e;const[r,a]=this.parentDimensions;this.setDims(r*s,a*i),this.fixAndSetPosition(),this._onResized()},$l=function(){if(!n(this,$s))return;const{savedX:t,savedY:e,savedWidth:s,savedHeight:i}=n(this,$s);f(this,$s,null);const r=this.x,a=this.y,o=this.width,l=this.height;r===t&&a===e&&o===s&&l===i||this.addCommands({cmd:m(this,H,pc).bind(this,r,a,o,l),undo:m(this,H,pc).bind(this,t,e,s,i),mustExec:!0})},gc=function(t,e){const[s,i]=this.parentDimensions,r=this.x,a=this.y,o=this.width,l=this.height,h=W.MIN_SIZE/s,c=W.MIN_SIZE/i,u=m(this,H,Bl).call(this,this.rotation),p=(L,B)=>[u[0]*L+u[2]*B,u[1]*L+u[3]*B],b=m(this,H,Bl).call(this,360-this.rotation),A=(L,B)=>[b[0]*L+b[2]*B,b[1]*L+b[3]*B];let y,w,v=!1,_=!1;switch(t){case"topLeft":v=!0,y=(L,B)=>[0,0],w=(L,B)=>[L,B];break;case"topMiddle":y=(L,B)=>[L/2,0],w=(L,B)=>[L/2,B];break;case"topRight":v=!0,y=(L,B)=>[L,0],w=(L,B)=>[0,B];break;case"middleRight":_=!0,y=(L,B)=>[L,B/2],w=(L,B)=>[0,B/2];break;case"bottomRight":v=!0,y=(L,B)=>[L,B],w=(L,B)=>[0,0];break;case"bottomMiddle":y=(L,B)=>[L/2,B],w=(L,B)=>[L/2,0];break;case"bottomLeft":v=!0,y=(L,B)=>[0,B],w=(L,B)=>[L,0];break;case"middleLeft":_=!0,y=(L,B)=>[0,B/2],w=(L,B)=>[L,B/2];break}const E=y(o,l),S=w(o,l);let C=p(...S);const T=W._round(r+C[0]),x=W._round(a+C[1]);let F=1,N=1,z,U;if(e.fromKeyboard)({deltaX:z,deltaY:U}=e);else{const{screenX:L,screenY:B}=e,[ge,os]=n(this,pn);[z,U]=this.screenToPageTranslation(L-ge,B-os),n(this,pn)[0]=L,n(this,pn)[1]=B}if([z,U]=A(z/s,U/i),v){const L=Math.hypot(o,l);F=N=Math.max(Math.min(Math.hypot(S[0]-E[0]-z,S[1]-E[1]-U)/L,1/o,1/l),h/o,c/l)}else _?F=Math.max(h,Math.min(1,Math.abs(S[0]-E[0]-z)))/o:N=Math.max(c,Math.min(1,Math.abs(S[1]-E[1]-U)))/l;const dt=W._round(o*F),ut=W._round(l*N);C=p(...w(dt,ut));const nt=T-C[0],Qt=x-C[1];n(this,Ae)||f(this,Ae,[this.x,this.y,this.width,this.height]),this.width=dt,this.height=ut,this.x=nt,this.y=Qt,this.setDims(s*dt,i*ut),this.fixAndSetPosition(),this._onResizing()},Au=function(){f(this,$s,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height}),n(this,at)?.toggle(!1),this.parent.togglePointerEvents(!1)},yu=function(t,e,s){let r=.7*(s/e)+1-.7;if(r===1)return;const a=m(this,H,Bl).call(this,this.rotation),o=(T,x)=>[a[0]*T+a[2]*x,a[1]*T+a[3]*x],[l,h]=this.parentDimensions,c=this.x,u=this.y,p=this.width,b=this.height,A=W.MIN_SIZE/l,y=W.MIN_SIZE/h;r=Math.max(Math.min(r,1/p,1/b),A/p,y/b);const w=W._round(p*r),v=W._round(b*r);if(w===p&&v===b)return;n(this,Ae)||f(this,Ae,[c,u,p,b]);const _=o(p/2,b/2),E=W._round(c+_[0]),S=W._round(u+_[1]),C=o(w/2,v/2);this.x=E-C[0],this.y=S-C[1],this.width=w,this.height=v,this.setDims(l*w,h*v),this.fixAndSetPosition(),this._onResizing()},vu=function(){n(this,at)?.toggle(!0),this.parent.togglePointerEvents(!0),m(this,H,$l).call(this)},mc=function(t){const{isMac:e}=ne.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)},wu=function(t){const{isSelected:e}=this;this._uiManager.setUpDragSession();let s=!1;const i=new AbortController,r=this._uiManager.combinedSignal(i),a={capture:!0,passive:!1,signal:r},o=h=>{i.abort(),f(this,Si,null),f(this,gn,!1),this._uiManager.endDragSession()||m(this,H,mc).call(this,h),s&&this._onStopDragging()};e&&(f(this,Dr,t.clientX),f(this,Fr,t.clientY),f(this,Si,t.pointerId),f(this,wo,t.pointerType),window.addEventListener("pointermove",h=>{s||(s=!0,this._onStartDragging());const{clientX:c,clientY:u,pointerId:p}=h;if(p!==n(this,Si)){Me(h);return}const[b,A]=this.screenToPageTranslation(c-n(this,Dr),u-n(this,Fr));f(this,Dr,c),f(this,Fr,u),this._uiManager.dragSelectedEditors(b,A)},a),window.addEventListener("touchmove",Me,a),window.addEventListener("pointerdown",h=>{h.pointerType===n(this,wo)&&(n(this,bn)||h.isPrimary)&&o(h),Me(h)},a));const l=h=>{if(!n(this,Si)||n(this,Si)===h.pointerId){o(h);return}Me(h)};window.addEventListener("pointerup",l,{signal:r}),window.addEventListener("blur",l,{signal:r})},bc=function(){if(n(this,Ei)||!this.div)return;f(this,Ei,new AbortController);const t=this._uiManager.combinedSignal(n(this,Ei));this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t}),this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})},_u=function(t){W._resizerKeyboardManager.exec(this,t)},Su=function(t){n(this,Gs)&&t.relatedTarget?.parentNode!==n(this,Nt)&&m(this,H,Fa).call(this)},Eu=function(t){f(this,_o,n(this,Gs)?t:"")},Ac=function(t){if(n(this,Ye))for(const e of n(this,Ye))e.tabIndex=t},Fa=function(){f(this,Gs,!1),m(this,H,Ac).call(this,-1),m(this,H,$l).call(this)},g(W,Co),O(W,"_l10n",null),O(W,"_l10nResizer",null),O(W,"_borderLineWidth",-1),O(W,"_colorManager",new nc),O(W,"_zIndex",1),O(W,"_telemetryTimeout",1e3);let gt=W;class yp extends gt{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return this.serializeDeleted()}}const Ld=3285377520,Ie=4294901760,hs=65535;class Cu{constructor(t){this.h1=t?t&4294967295:Ld,this.h2=t?t&4294967295:Ld}update(t){let e,s;if(typeof t=="string"){e=new Uint8Array(t.length*2),s=0;for(let y=0,w=t.length;y<w;y++){const v=t.charCodeAt(y);v<=255?e[s++]=v:(e[s++]=v>>>8,e[s++]=v&255)}}else if(ArrayBuffer.isView(t))e=t.slice(),s=e.byteLength;else throw new Error("Invalid data format, must be a string or TypedArray.");const i=s>>2,r=s-i*4,a=new Uint32Array(e.buffer,0,i);let o=0,l=0,h=this.h1,c=this.h2;const u=3432918353,p=461845907,b=u&hs,A=p&hs;for(let y=0;y<i;y++)y&1?(o=a[y],o=o*u&Ie|o*b&hs,o=o<<15|o>>>17,o=o*p&Ie|o*A&hs,h^=o,h=h<<13|h>>>19,h=h*5+3864292196):(l=a[y],l=l*u&Ie|l*b&hs,l=l<<15|l>>>17,l=l*p&Ie|l*A&hs,c^=l,c=c<<13|c>>>19,c=c*5+3864292196);switch(o=0,r){case 3:o^=e[i*4+2]<<16;case 2:o^=e[i*4+1]<<8;case 1:o^=e[i*4],o=o*u&Ie|o*b&hs,o=o<<15|o>>>17,o=o*p&Ie|o*A&hs,i&1?h^=o:c^=o}this.h1=h,this.h2=c}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,t=t*3981806797&Ie|t*36045&hs,e=e*4283543511&Ie|((e<<16|t>>>16)*2950163797&Ie)>>>16,t^=e>>>1,t=t*444984403&Ie|t*60499&hs,e=e*3301882366&Ie|((e<<16|t>>>16)*3120437893&Ie)>>>16,t^=e>>>1,(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const yc=Object.freeze({map:null,hash:"",transfer:void 0});var An,yn,kt,_h,xu;class bd{constructor(){g(this,_h);g(this,An,!1);g(this,yn,null);g(this,kt,new Map);this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){const s=n(this,kt).get(t);return s===void 0?e:Object.assign(e,s)}getRawValue(t){return n(this,kt).get(t)}remove(t){if(n(this,kt).delete(t),n(this,kt).size===0&&this.resetModified(),typeof this.onAnnotationEditor=="function"){for(const e of n(this,kt).values())if(e instanceof gt)return;this.onAnnotationEditor(null)}}setValue(t,e){const s=n(this,kt).get(t);let i=!1;if(s!==void 0)for(const[r,a]of Object.entries(e))s[r]!==a&&(i=!0,s[r]=a);else i=!0,n(this,kt).set(t,e);i&&m(this,_h,xu).call(this),e instanceof gt&&typeof this.onAnnotationEditor=="function"&&this.onAnnotationEditor(e.constructor._type)}has(t){return n(this,kt).has(t)}getAll(){return n(this,kt).size>0?dd(n(this,kt)):null}setAll(t){for(const[e,s]of Object.entries(t))this.setValue(e,s)}get size(){return n(this,kt).size}resetModified(){n(this,An)&&(f(this,An,!1),typeof this.onResetModified=="function"&&this.onResetModified())}get print(){return new Tu(this)}get serializable(){if(n(this,kt).size===0)return yc;const t=new Map,e=new Cu,s=[],i=Object.create(null);let r=!1;for(const[a,o]of n(this,kt)){const l=o instanceof gt?o.serialize(!1,i):o;l&&(t.set(a,l),e.update(`${a}:${JSON.stringify(l)}`),r||(r=!!l.bitmap))}if(r)for(const a of t.values())a.bitmap&&s.push(a.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:s}:yc}get editorStats(){let t=null;const e=new Map;for(const s of n(this,kt).values()){if(!(s instanceof gt))continue;const i=s.telemetryFinalData;if(!i)continue;const{type:r}=i;e.has(r)||e.set(r,Object.getPrototypeOf(s).constructor),t||(t=Object.create(null));const a=t[r]||(t[r]=new Map);for(const[o,l]of Object.entries(i)){if(o==="type")continue;let h=a.get(o);h||(h=new Map,a.set(o,h));const c=h.get(l)??0;h.set(l,c+1)}}for(const[s,i]of e)t[s]=i.computeTelemetryFinalData(t[s]);return t}resetModifiedIds(){f(this,yn,null)}get modifiedIds(){if(n(this,yn))return n(this,yn);const t=[];for(const e of n(this,kt).values())!(e instanceof gt)||!e.annotationElementId||!e.serialize()||t.push(e.annotationElementId);return f(this,yn,{ids:new Set(t),hash:t.join(",")})}}An=new WeakMap,yn=new WeakMap,kt=new WeakMap,_h=new WeakSet,xu=function(){n(this,An)||(f(this,An,!0),typeof this.onSetModified=="function"&&this.onSetModified())};var xo;class Tu extends bd{constructor(e){super();g(this,xo);const{map:s,hash:i,transfer:r}=e.serializable,a=structuredClone(s,r?{transfer:r}:null);f(this,xo,{map:a,hash:i,transfer:r})}get print(){it("Should not call PrintAnnotationStorage.print")}get serializable(){return n(this,xo)}get modifiedIds(){return q(this,"modifiedIds",{ids:new Set,hash:""})}}xo=new WeakMap;var Nr;class vp{constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){g(this,Nr,new Set);this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),n(this,Nr).clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont({systemFontInfo:t,_inspectFont:e}){if(!(!t||n(this,Nr).has(t.loadedName))){if(wt(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:s,src:i,style:r}=t,a=new FontFace(s,i,r);this.addNativeFontFace(a);try{await a.load(),n(this,Nr).add(s),e?.(t)}catch{V(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(a)}return}it("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo){await this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){const s=t.createNativeFontFace();if(s){this.addNativeFontFace(s);try{await s.loaded}catch(i){throw V(`Failed to load font '${s.family}': '${i}'.`),t.disableFontFace=!0,i}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise(s=>{const i=this._queueLoadingCallback(s);this._prepareFontLoadEvent(t,i)})}}get isFontLoadingAPISupported(){const t=!!this._document?.fonts;return q(this,"isFontLoadingAPISupported",t)}get isSyncFontLoadingSupported(){let t=!1;return(Kt||typeof navigator<"u"&&typeof navigator?.userAgent=="string"&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(t=!0),q(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){function e(){for(wt(!i.done,"completeRequest() cannot be called twice."),i.done=!0;s.length>0&&s[0].done;){const r=s.shift();setTimeout(r.callback,0)}}const{loadingRequests:s}=this,i={done:!1,complete:e,callback:t};return s.push(i),i}get _loadTestFont(){const t=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return q(this,"_loadTestFont",t)}_prepareFontLoadEvent(t,e){function s(S,C){return S.charCodeAt(C)<<24|S.charCodeAt(C+1)<<16|S.charCodeAt(C+2)<<8|S.charCodeAt(C+3)&255}function i(S,C,T,x){const F=S.substring(0,C),N=S.substring(C+T);return F+x+N}let r,a;const o=this._document.createElement("canvas");o.width=1,o.height=1;const l=o.getContext("2d");let h=0;function c(S,C){if(++h>30){V("Load test font never loaded."),C();return}if(l.font="30px "+S,l.fillText(".",0,20),l.getImageData(0,0,1,1).data[3]>0){C();return}setTimeout(c.bind(null,S,C))}const u=`lt${Date.now()}${this.loadTestFontId++}`;let p=this._loadTestFont;p=i(p,976,u.length,u);const A=16,y=1482184792;let w=s(p,A);for(r=0,a=u.length-3;r<a;r+=4)w=w-y+s(u,r)|0;r<u.length&&(w=w-y+s(u+"XXX",r)|0),p=i(p,A,4,rp(w));const v=`url(data:font/opentype;base64,${btoa(p)});`,_=`@font-face {font-family:"${u}";src:${v}}`;this.insertRule(_);const E=this._document.createElement("div");E.style.visibility="hidden",E.style.width=E.style.height="10px",E.style.position="absolute",E.style.top=E.style.left="0px";for(const S of[t.loadedName,u]){const C=this._document.createElement("span");C.textContent="Hi",C.style.fontFamily=S,E.append(C)}this._document.body.append(E),c(u,()=>{E.remove(),e.complete()})}}Nr=new WeakMap;class wp{constructor(t,{disableFontFace:e=!1,fontExtraProperties:s=!1,inspectFont:i=null}){this.compiledGlyphs=Object.create(null);for(const r in t)this[r]=t[r];this.disableFontFace=e===!0,this.fontExtraProperties=s===!0,this._inspectFont=i}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(!this.cssFontInfo)t=new FontFace(this.loadedName,this.data,{});else{const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}return this._inspectFont?.(this),t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=`url(data:${this.mimetype};base64,${dp(this.data)});`;let e;if(!this.cssFontInfo)e=`@font-face {font-family:"${this.loadedName}";src:${t}}`;else{let s=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(s+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),e=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${s}src:${t}}`}return this._inspectFont?.(this,t),e}getPathGenerator(t,e){if(this.compiledGlyphs[e]!==void 0)return this.compiledGlyphs[e];const s=this.loadedName+"_path_"+e;let i;try{i=t.get(s)}catch(a){V(`getPathGenerator - ignoring character: "${a}".`)}const r=new Path2D(i||"");return this.fontExtraProperties||t.delete(s),this.compiledGlyphs[e]=r}}const Pl={DATA:1,ERROR:2},vt={CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function Id(){}function he(d){if(d instanceof ji||d instanceof Jh||d instanceof Ka||d instanceof Rd||d instanceof lh||d instanceof Vh)return d;switch(d instanceof Error||typeof d=="object"&&d!==null||it('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),d.name){case"AbortException":return new ji(d.message);case"InvalidPDFException":return new Jh(d.message);case"MissingPDFException":return new Ka(d.message);case"PasswordException":return new Rd(d.message,d.code);case"UnexpectedResponseException":return new lh(d.message,d.status);case"UnknownErrorException":return new Vh(d.message,d.details)}return new Vh(d.message,d.toString())}var Or,Ue,Pu,Ru,Mu,Gl;class Na{constructor(t,e,s){g(this,Ue);g(this,Or,new AbortController);this.sourceName=t,this.targetName=e,this.comObj=s,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),s.addEventListener("message",m(this,Ue,Pu).bind(this),{signal:n(this,Or).signal})}on(t,e){const s=this.actionHandler;if(s[t])throw new Error(`There is already an actionName called "${t}"`);s[t]=e}send(t,e,s){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},s)}sendWithPromise(t,e,s){const i=this.callbackId++,r=Promise.withResolvers();this.callbackCapabilities[i]=r;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:i,data:e},s)}catch(a){r.reject(a)}return r.promise}sendWithStream(t,e,s,i){const r=this.streamId++,a=this.sourceName,o=this.targetName,l=this.comObj;return new ReadableStream({start:h=>{const c=Promise.withResolvers();return this.streamControllers[r]={controller:h,startCall:c,pullCall:null,cancelCall:null,isClosed:!1},l.postMessage({sourceName:a,targetName:o,action:t,streamId:r,data:e,desiredSize:h.desiredSize},i),c.promise},pull:h=>{const c=Promise.withResolvers();return this.streamControllers[r].pullCall=c,l.postMessage({sourceName:a,targetName:o,stream:vt.PULL,streamId:r,desiredSize:h.desiredSize}),c.promise},cancel:h=>{wt(h instanceof Error,"cancel must have a valid reason");const c=Promise.withResolvers();return this.streamControllers[r].cancelCall=c,this.streamControllers[r].isClosed=!0,l.postMessage({sourceName:a,targetName:o,stream:vt.CANCEL,streamId:r,reason:he(h)}),c.promise}},s)}destroy(){n(this,Or)?.abort(),f(this,Or,null)}}Or=new WeakMap,Ue=new WeakSet,Pu=function({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream){m(this,Ue,Mu).call(this,t);return}if(t.callback){const s=t.callbackId,i=this.callbackCapabilities[s];if(!i)throw new Error(`Cannot resolve callback ${s}`);if(delete this.callbackCapabilities[s],t.callback===Pl.DATA)i.resolve(t.data);else if(t.callback===Pl.ERROR)i.reject(he(t.reason));else throw new Error("Unexpected callback case");return}const e=this.actionHandler[t.action];if(!e)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const s=this.sourceName,i=t.sourceName,r=this.comObj;Promise.try(e,t.data).then(function(a){r.postMessage({sourceName:s,targetName:i,callback:Pl.DATA,callbackId:t.callbackId,data:a})},function(a){r.postMessage({sourceName:s,targetName:i,callback:Pl.ERROR,callbackId:t.callbackId,reason:he(a)})});return}if(t.streamId){m(this,Ue,Ru).call(this,t);return}e(t.data)},Ru=function(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,r=this.comObj,a=this,o=this.actionHandler[t.action],l={enqueue(h,c=1,u){if(this.isCancelled)return;const p=this.desiredSize;this.desiredSize-=c,p>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),r.postMessage({sourceName:s,targetName:i,stream:vt.ENQUEUE,streamId:e,chunk:h},u)},close(){this.isCancelled||(this.isCancelled=!0,r.postMessage({sourceName:s,targetName:i,stream:vt.CLOSE,streamId:e}),delete a.streamSinks[e])},error(h){wt(h instanceof Error,"error must have a valid reason"),!this.isCancelled&&(this.isCancelled=!0,r.postMessage({sourceName:s,targetName:i,stream:vt.ERROR,streamId:e,reason:he(h)}))},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};l.sinkCapability.resolve(),l.ready=l.sinkCapability.promise,this.streamSinks[e]=l,Promise.try(o,t.data,l).then(function(){r.postMessage({sourceName:s,targetName:i,stream:vt.START_COMPLETE,streamId:e,success:!0})},function(h){r.postMessage({sourceName:s,targetName:i,stream:vt.START_COMPLETE,streamId:e,reason:he(h)})})},Mu=function(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,r=this.comObj,a=this.streamControllers[e],o=this.streamSinks[e];switch(t.stream){case vt.START_COMPLETE:t.success?a.startCall.resolve():a.startCall.reject(he(t.reason));break;case vt.PULL_COMPLETE:t.success?a.pullCall.resolve():a.pullCall.reject(he(t.reason));break;case vt.PULL:if(!o){r.postMessage({sourceName:s,targetName:i,stream:vt.PULL_COMPLETE,streamId:e,success:!0});break}o.desiredSize<=0&&t.desiredSize>0&&o.sinkCapability.resolve(),o.desiredSize=t.desiredSize,Promise.try(o.onPull||Id).then(function(){r.postMessage({sourceName:s,targetName:i,stream:vt.PULL_COMPLETE,streamId:e,success:!0})},function(h){r.postMessage({sourceName:s,targetName:i,stream:vt.PULL_COMPLETE,streamId:e,reason:he(h)})});break;case vt.ENQUEUE:if(wt(a,"enqueue should have stream controller"),a.isClosed)break;a.controller.enqueue(t.chunk);break;case vt.CLOSE:if(wt(a,"close should have stream controller"),a.isClosed)break;a.isClosed=!0,a.controller.close(),m(this,Ue,Gl).call(this,a,e);break;case vt.ERROR:wt(a,"error should have stream controller"),a.controller.error(he(t.reason)),m(this,Ue,Gl).call(this,a,e);break;case vt.CANCEL_COMPLETE:t.success?a.cancelCall.resolve():a.cancelCall.reject(he(t.reason)),m(this,Ue,Gl).call(this,a,e);break;case vt.CANCEL:if(!o)break;const l=he(t.reason);Promise.try(o.onCancel||Id,l).then(function(){r.postMessage({sourceName:s,targetName:i,stream:vt.CANCEL_COMPLETE,streamId:e,success:!0})},function(h){r.postMessage({sourceName:s,targetName:i,stream:vt.CANCEL_COMPLETE,streamId:e,reason:he(h)})}),o.sinkCapability.reject(l),o.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}},Gl=async function(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]),delete this.streamControllers[e]};var To;class ku{constructor({enableHWA:t=!1}){g(this,To,!1);f(this,To,t)}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const s=this._createCanvas(t,e);return{canvas:s,context:s.getContext("2d",{willReadFrequently:!n(this,To)})}}reset(t,e,s){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||s<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=s}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){it("Abstract method `_createCanvas` called.")}}To=new WeakMap;class _p extends ku{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e}),this._document=t}_createCanvas(t,e){const s=this._document.createElement("canvas");return s.width=t,s.height=e,s}}class Lu{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t,this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(e).then(s=>({cMapData:s,isCompressed:this.isCompressed})).catch(s=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)})}async _fetch(t){it("Abstract method `_fetch` called.")}}class Iu extends Lu{async _fetch(t){const e=await Bh(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):Hh(e)}}class Du{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,s,i,r){return"none"}destroy(t=!1){}}var vn,Hr,zs,Us,Wt,wn,_n,R,Ut,Oa,dr,zl,ur,Fu,vc,fr,Ha,Ba,wc,$a;class Sp extends Du{constructor({docId:e,ownerDocument:s=globalThis.document}){super();g(this,R);g(this,vn);g(this,Hr);g(this,zs);g(this,Us);g(this,Wt);g(this,wn);g(this,_n,0);f(this,Us,e),f(this,Wt,s)}addFilter(e){if(!e)return"none";let s=n(this,R,Ut).get(e);if(s)return s;const[i,r,a]=m(this,R,zl).call(this,e),o=e.length===1?i:`${i}${r}${a}`;if(s=n(this,R,Ut).get(o),s)return n(this,R,Ut).set(e,s),s;const l=`g_${n(this,Us)}_transfer_map_${Jt(this,_n)._++}`,h=m(this,R,ur).call(this,l);n(this,R,Ut).set(e,h),n(this,R,Ut).set(o,h);const c=m(this,R,fr).call(this,l);return m(this,R,Ba).call(this,i,r,a,c),h}addHCMFilter(e,s){const i=`${e}-${s}`,r="base";let a=n(this,R,Oa).get(r);if(a?.key===i||(a?(a.filter?.remove(),a.key=i,a.url="none",a.filter=null):(a={key:i,url:"none",filter:null},n(this,R,Oa).set(r,a)),!e||!s))return a.url;const o=m(this,R,$a).call(this,e);e=D.makeHexColor(...o);const l=m(this,R,$a).call(this,s);if(s=D.makeHexColor(...l),n(this,R,dr).style.color="",e==="#000000"&&s==="#ffffff"||e===s)return a.url;const h=new Array(256);for(let A=0;A<=255;A++){const y=A/255;h[A]=y<=.03928?y/12.92:((y+.055)/1.055)**2.4}const c=h.join(","),u=`g_${n(this,Us)}_hcm_filter`,p=a.filter=m(this,R,fr).call(this,u);m(this,R,Ba).call(this,c,c,c,p),m(this,R,vc).call(this,p);const b=(A,y)=>{const w=o[A]/255,v=l[A]/255,_=new Array(y+1);for(let E=0;E<=y;E++)_[E]=w+E/y*(v-w);return _.join(",")};return m(this,R,Ba).call(this,b(0,5),b(1,5),b(2,5),p),a.url=m(this,R,ur).call(this,u),a.url}addAlphaFilter(e){let s=n(this,R,Ut).get(e);if(s)return s;const[i]=m(this,R,zl).call(this,[e]),r=`alpha_${i}`;if(s=n(this,R,Ut).get(r),s)return n(this,R,Ut).set(e,s),s;const a=`g_${n(this,Us)}_alpha_map_${Jt(this,_n)._++}`,o=m(this,R,ur).call(this,a);n(this,R,Ut).set(e,o),n(this,R,Ut).set(r,o);const l=m(this,R,fr).call(this,a);return m(this,R,wc).call(this,i,l),o}addLuminosityFilter(e){let s=n(this,R,Ut).get(e||"luminosity");if(s)return s;let i,r;if(e?([i]=m(this,R,zl).call(this,[e]),r=`luminosity_${i}`):r="luminosity",s=n(this,R,Ut).get(r),s)return n(this,R,Ut).set(e,s),s;const a=`g_${n(this,Us)}_luminosity_map_${Jt(this,_n)._++}`,o=m(this,R,ur).call(this,a);n(this,R,Ut).set(e,o),n(this,R,Ut).set(r,o);const l=m(this,R,fr).call(this,a);return m(this,R,Fu).call(this,l),e&&m(this,R,wc).call(this,i,l),o}addHighlightHCMFilter(e,s,i,r,a){const o=`${s}-${i}-${r}-${a}`;let l=n(this,R,Oa).get(e);if(l?.key===o||(l?(l.filter?.remove(),l.key=o,l.url="none",l.filter=null):(l={key:o,url:"none",filter:null},n(this,R,Oa).set(e,l)),!s||!i))return l.url;const[h,c]=[s,i].map(m(this,R,$a).bind(this));let u=Math.round(.2126*h[0]+.7152*h[1]+.0722*h[2]),p=Math.round(.2126*c[0]+.7152*c[1]+.0722*c[2]),[b,A]=[r,a].map(m(this,R,$a).bind(this));p<u&&([u,p,b,A]=[p,u,A,b]),n(this,R,dr).style.color="";const y=(_,E,S)=>{const C=new Array(256),T=(p-u)/S,x=_/255,F=(E-_)/(255*S);let N=0;for(let z=0;z<=S;z++){const U=Math.round(u+z*T),dt=x+z*F;for(let ut=N;ut<=U;ut++)C[ut]=dt;N=U+1}for(let z=N;z<256;z++)C[z]=C[N-1];return C.join(",")},w=`g_${n(this,Us)}_hcm_${e}_filter`,v=l.filter=m(this,R,fr).call(this,w);return m(this,R,vc).call(this,v),m(this,R,Ba).call(this,y(b[0],A[0],5),y(b[1],A[1],5),y(b[2],A[2],5),v),l.url=m(this,R,ur).call(this,w),l.url}destroy(e=!1){e&&n(this,wn)?.size||(n(this,zs)?.parentNode.parentNode.remove(),f(this,zs,null),n(this,Hr)?.clear(),f(this,Hr,null),n(this,wn)?.clear(),f(this,wn,null),f(this,_n,0))}}vn=new WeakMap,Hr=new WeakMap,zs=new WeakMap,Us=new WeakMap,Wt=new WeakMap,wn=new WeakMap,_n=new WeakMap,R=new WeakSet,Ut=function(){return n(this,Hr)||f(this,Hr,new Map)},Oa=function(){return n(this,wn)||f(this,wn,new Map)},dr=function(){if(!n(this,zs)){const e=n(this,Wt).createElement("div"),{style:s}=e;s.visibility="hidden",s.contain="strict",s.width=s.height=0,s.position="absolute",s.top=s.left=0,s.zIndex=-1;const i=n(this,Wt).createElementNS(Ms,"svg");i.setAttribute("width",0),i.setAttribute("height",0),f(this,zs,n(this,Wt).createElementNS(Ms,"defs")),e.append(i),i.append(n(this,zs)),n(this,Wt).body.append(e)}return n(this,zs)},zl=function(e){if(e.length===1){const h=e[0],c=new Array(256);for(let p=0;p<256;p++)c[p]=h[p]/255;const u=c.join(",");return[u,u,u]}const[s,i,r]=e,a=new Array(256),o=new Array(256),l=new Array(256);for(let h=0;h<256;h++)a[h]=s[h]/255,o[h]=i[h]/255,l[h]=r[h]/255;return[a.join(","),o.join(","),l.join(",")]},ur=function(e){if(n(this,vn)===void 0){f(this,vn,"");const s=n(this,Wt).URL;s!==n(this,Wt).baseURI&&($h(s)?V('#createUrl: ignore "data:"-URL for performance reasons.'):f(this,vn,s.split("#",1)[0]))}return`url(${n(this,vn)}#${e})`},Fu=function(e){const s=n(this,Wt).createElementNS(Ms,"feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),e.append(s)},vc=function(e){const s=n(this,Wt).createElementNS(Ms,"feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),e.append(s)},fr=function(e){const s=n(this,Wt).createElementNS(Ms,"filter");return s.setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("id",e),n(this,R,dr).append(s),s},Ha=function(e,s,i){const r=n(this,Wt).createElementNS(Ms,s);r.setAttribute("type","discrete"),r.setAttribute("tableValues",i),e.append(r)},Ba=function(e,s,i,r){const a=n(this,Wt).createElementNS(Ms,"feComponentTransfer");r.append(a),m(this,R,Ha).call(this,a,"feFuncR",e),m(this,R,Ha).call(this,a,"feFuncG",s),m(this,R,Ha).call(this,a,"feFuncB",i)},wc=function(e,s){const i=n(this,Wt).createElementNS(Ms,"feComponentTransfer");s.append(i),m(this,R,Ha).call(this,i,"feFuncA",e)},$a=function(e){return n(this,R,dr).style.color=e,md(getComputedStyle(n(this,R,dr)).getPropertyValue("color"))};class Nu{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch(s=>{throw new Error(`Unable to load font data at: ${e}`)})}async _fetch(t){it("Abstract method `_fetch` called.")}}class Ou extends Nu{async _fetch(t){const e=await Bh(t,"arraybuffer");return new Uint8Array(e)}}Kt&&V("Please use the `legacy` build in Node.js environments.");async function Hu(d){const e=await process.getBuiltinModule("fs").promises.readFile(d);return new Uint8Array(e)}class Ep extends Du{}class Cp extends ku{_createCanvas(t,e){return process.getBuiltinModule("module").createRequire(import.meta.url)("@napi-rs/canvas").createCanvas(t,e)}}class xp extends Lu{async _fetch(t){return Hu(t)}}class Tp extends Nu{async _fetch(t){return Hu(t)}}const Yt={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};function _c(d,t){if(!t)return;const e=t[2]-t[0],s=t[3]-t[1],i=new Path2D;i.rect(t[0],t[1],e,s),d.clip(i)}class Ad{getPattern(){it("Abstract method `getPattern` called.")}}class Pp extends Ad{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;this._type==="axial"?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):this._type==="radial"&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const s of this._colorStops)e.addColorStop(s[0],s[1]);return e}getPattern(t,e,s,i){let r;if(i===Yt.STROKE||i===Yt.FILL){const a=e.current.getClippedPathBoundingBox(i,ht(t))||[0,0,0,0],o=Math.ceil(a[2]-a[0])||1,l=Math.ceil(a[3]-a[1])||1,h=e.cachedCanvases.getCanvas("pattern",o,l),c=h.context;c.clearRect(0,0,c.canvas.width,c.canvas.height),c.beginPath(),c.rect(0,0,c.canvas.width,c.canvas.height),c.translate(-a[0],-a[1]),s=D.transform(s,[1,0,0,1,a[0],a[1]]),c.transform(...e.baseTransform),this.matrix&&c.transform(...this.matrix),_c(c,this._bbox),c.fillStyle=this._createGradient(c),c.fill(),r=t.createPattern(h.canvas,"no-repeat");const u=new DOMMatrix(s);r.setTransform(u)}else _c(t,this._bbox),r=this._createGradient(t);return r}}function qh(d,t,e,s,i,r,a,o){const l=t.coords,h=t.colors,c=d.data,u=d.width*4;let p;l[e+1]>l[s+1]&&(p=e,e=s,s=p,p=r,r=a,a=p),l[s+1]>l[i+1]&&(p=s,s=i,i=p,p=a,a=o,o=p),l[e+1]>l[s+1]&&(p=e,e=s,s=p,p=r,r=a,a=p);const b=(l[e]+t.offsetX)*t.scaleX,A=(l[e+1]+t.offsetY)*t.scaleY,y=(l[s]+t.offsetX)*t.scaleX,w=(l[s+1]+t.offsetY)*t.scaleY,v=(l[i]+t.offsetX)*t.scaleX,_=(l[i+1]+t.offsetY)*t.scaleY;if(A>=_)return;const E=h[r],S=h[r+1],C=h[r+2],T=h[a],x=h[a+1],F=h[a+2],N=h[o],z=h[o+1],U=h[o+2],dt=Math.round(A),ut=Math.round(_);let nt,Qt,L,B,ge,os,Rs,Le;for(let Gt=dt;Gt<=ut;Gt++){if(Gt<w){const ot=Gt<A?0:(A-Gt)/(A-w);nt=b-(b-y)*ot,Qt=E-(E-T)*ot,L=S-(S-x)*ot,B=C-(C-F)*ot}else{let ot;Gt>_?ot=1:w===_?ot=0:ot=(w-Gt)/(w-_),nt=y-(y-v)*ot,Qt=T-(T-N)*ot,L=x-(x-z)*ot,B=F-(F-U)*ot}let St;Gt<A?St=0:Gt>_?St=1:St=(A-Gt)/(A-_),ge=b-(b-v)*St,os=E-(E-N)*St,Rs=S-(S-z)*St,Le=C-(C-U)*St;const hr=Math.round(Math.min(nt,ge)),Ta=Math.round(Math.max(nt,ge));let oe=u*Gt+hr*4;for(let ot=hr;ot<=Ta;ot++)St=(nt-ot)/(nt-ge),St<0?St=0:St>1&&(St=1),c[oe++]=Qt-(Qt-os)*St|0,c[oe++]=L-(L-Rs)*St|0,c[oe++]=B-(B-Le)*St|0,c[oe++]=255}}function Rp(d,t,e){const s=t.coords,i=t.colors;let r,a;switch(t.type){case"lattice":const o=t.verticesPerRow,l=Math.floor(s.length/o)-1,h=o-1;for(r=0;r<l;r++){let c=r*o;for(let u=0;u<h;u++,c++)qh(d,e,s[c],s[c+1],s[c+o],i[c],i[c+1],i[c+o]),qh(d,e,s[c+o+1],s[c+1],s[c+o],i[c+o+1],i[c+1],i[c+o])}break;case"triangles":for(r=0,a=s.length;r<a;r+=3)qh(d,e,s[r],s[r+1],s[r+2],i[r],i[r+1],i[r+2]);break;default:throw new Error("illegal figure")}}class Mp extends Ad{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[7],this._background=t[8],this.matrix=null}_createMeshCanvas(t,e,s){const o=Math.floor(this._bounds[0]),l=Math.floor(this._bounds[1]),h=Math.ceil(this._bounds[2])-o,c=Math.ceil(this._bounds[3])-l,u=Math.min(Math.ceil(Math.abs(h*t[0]*1.1)),3e3),p=Math.min(Math.ceil(Math.abs(c*t[1]*1.1)),3e3),b=h/u,A=c/p,y={coords:this._coords,colors:this._colors,offsetX:-o,offsetY:-l,scaleX:1/b,scaleY:1/A},w=u+2*2,v=p+2*2,_=s.getCanvas("mesh",w,v),E=_.context,S=E.createImageData(u,p);if(e){const T=S.data;for(let x=0,F=T.length;x<F;x+=4)T[x]=e[0],T[x+1]=e[1],T[x+2]=e[2],T[x+3]=255}for(const T of this._figures)Rp(S,T,y);return E.putImageData(S,2,2),{canvas:_.canvas,offsetX:o-2*b,offsetY:l-2*A,scaleX:b,scaleY:A}}getPattern(t,e,s,i){_c(t,this._bbox);let r;if(i===Yt.SHADING)r=D.singularValueDecompose2dScale(ht(t));else if(r=D.singularValueDecompose2dScale(e.baseTransform),this.matrix){const o=D.singularValueDecompose2dScale(this.matrix);r=[r[0]*o[0],r[1]*o[1]]}const a=this._createMeshCanvas(r,i===Yt.SHADING?null:this._background,e.cachedCanvases);return i!==Yt.SHADING&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(a.offsetX,a.offsetY),t.scale(a.scaleX,a.scaleY),t.createPattern(a.canvas,"no-repeat")}}class kp extends Ad{getPattern(){return"hotpink"}}function Lp(d){switch(d[0]){case"RadialAxial":return new Pp(d);case"Mesh":return new Mp(d);case"Dummy":return new kp}throw new Error(`Unknown IR type: ${d[0]}`)}const Dd={COLORED:1,UNCOLORED:2},Sh=class Sh{constructor(t,e,s,i,r){this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.color=e,this.ctx=s,this.canvasGraphicsFactory=i,this.baseTransform=r}createPatternCanvas(t){const{bbox:e,operatorList:s,paintType:i,tilingType:r,color:a,canvasGraphicsFactory:o}=this;let{xstep:l,ystep:h}=this;l=Math.abs(l),h=Math.abs(h),Oh("TilingType: "+r);const c=e[0],u=e[1],p=e[2],b=e[3],A=p-c,y=b-u,w=D.singularValueDecompose2dScale(this.matrix),v=D.singularValueDecompose2dScale(this.baseTransform),_=w[0]*v[0],E=w[1]*v[1];let S=A,C=y,T=!1,x=!1;const F=Math.ceil(l*_),N=Math.ceil(h*E),z=Math.ceil(A*_),U=Math.ceil(y*E);F>=z?S=l:T=!0,N>=U?C=h:x=!0;const dt=this.getSizeAndScale(S,this.ctx.canvas.width,_),ut=this.getSizeAndScale(C,this.ctx.canvas.height,E),nt=t.cachedCanvases.getCanvas("pattern",dt.size,ut.size),Qt=nt.context,L=o.createCanvasGraphics(Qt);if(L.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(L,i,a),Qt.translate(-dt.scale*c,-ut.scale*u),L.transform(dt.scale,0,0,ut.scale,0,0),Qt.save(),this.clipBbox(L,c,u,p,b),L.baseTransform=ht(L.ctx),L.executeOperatorList(s),L.endDrawing(),Qt.restore(),T||x){const B=nt.canvas;T&&(S=l),x&&(C=h);const ge=this.getSizeAndScale(S,this.ctx.canvas.width,_),os=this.getSizeAndScale(C,this.ctx.canvas.height,E),Rs=ge.size,Le=os.size,Gt=t.cachedCanvases.getCanvas("pattern-workaround",Rs,Le),St=Gt.context,hr=T?Math.floor(A/l):0,Ta=x?Math.floor(y/h):0;for(let oe=0;oe<=hr;oe++)for(let ot=0;ot<=Ta;ot++)St.drawImage(B,Rs*oe,Le*ot,Rs,Le,0,0,Rs,Le);return{canvas:Gt.canvas,scaleX:ge.scale,scaleY:os.scale,offsetX:c,offsetY:u}}return{canvas:nt.canvas,scaleX:dt.scale,scaleY:ut.scale,offsetX:c,offsetY:u}}getSizeAndScale(t,e,s){const i=Math.max(Sh.MAX_PATTERN_SIZE,e);let r=Math.ceil(t*s);return r>=i?r=i:s=r/t,{scale:s,size:r}}clipBbox(t,e,s,i,r){const a=i-e,o=r-s;t.ctx.rect(e,s,a,o),t.current.updateRectMinMax(ht(t.ctx),[e,s,i,r]),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,s){const i=t.ctx,r=t.current;switch(e){case Dd.COLORED:const a=this.ctx;i.fillStyle=a.fillStyle,i.strokeStyle=a.strokeStyle,r.fillColor=a.fillStyle,r.strokeColor=a.strokeStyle;break;case Dd.UNCOLORED:const o=D.makeHexColor(s[0],s[1],s[2]);i.fillStyle=o,i.strokeStyle=o,r.fillColor=o,r.strokeColor=o;break;default:throw new np(`Unsupported paint type: ${e}`)}}getPattern(t,e,s,i){let r=s;i!==Yt.SHADING&&(r=D.transform(r,e.baseTransform),this.matrix&&(r=D.transform(r,this.matrix)));const a=this.createPatternCanvas(e);let o=new DOMMatrix(r);o=o.translate(a.offsetX,a.offsetY),o=o.scale(1/a.scaleX,1/a.scaleY);const l=t.createPattern(a.canvas,"repeat");return l.setTransform(o),l}};O(Sh,"MAX_PATTERN_SIZE",3e3);let Sc=Sh;function Ip({src:d,srcPos:t=0,dest:e,width:s,height:i,nonBlackColor:r=4294967295,inverseDecode:a=!1}){const o=ne.isLittleEndian?4278190080:255,[l,h]=a?[r,o]:[o,r],c=s>>3,u=s&7,p=d.length;e=new Uint32Array(e.buffer);let b=0;for(let A=0;A<i;A++){for(const w=t+c;t<w;t++){const v=t<p?d[t]:255;e[b++]=v&128?h:l,e[b++]=v&64?h:l,e[b++]=v&32?h:l,e[b++]=v&16?h:l,e[b++]=v&8?h:l,e[b++]=v&4?h:l,e[b++]=v&2?h:l,e[b++]=v&1?h:l}if(u===0)continue;const y=t<p?d[t++]:255;for(let w=0;w<u;w++)e[b++]=y&1<<7-w?h:l}return{srcPos:t,destPos:b}}const Fd=16,Nd=100,Dp=15,Od=10,Hd=1e3,pe=16;function Fp(d,t){if(d._removeMirroring)throw new Error("Context is already forwarding operations.");d.__originalSave=d.save,d.__originalRestore=d.restore,d.__originalRotate=d.rotate,d.__originalScale=d.scale,d.__originalTranslate=d.translate,d.__originalTransform=d.transform,d.__originalSetTransform=d.setTransform,d.__originalResetTransform=d.resetTransform,d.__originalClip=d.clip,d.__originalMoveTo=d.moveTo,d.__originalLineTo=d.lineTo,d.__originalBezierCurveTo=d.bezierCurveTo,d.__originalRect=d.rect,d.__originalClosePath=d.closePath,d.__originalBeginPath=d.beginPath,d._removeMirroring=()=>{d.save=d.__originalSave,d.restore=d.__originalRestore,d.rotate=d.__originalRotate,d.scale=d.__originalScale,d.translate=d.__originalTranslate,d.transform=d.__originalTransform,d.setTransform=d.__originalSetTransform,d.resetTransform=d.__originalResetTransform,d.clip=d.__originalClip,d.moveTo=d.__originalMoveTo,d.lineTo=d.__originalLineTo,d.bezierCurveTo=d.__originalBezierCurveTo,d.rect=d.__originalRect,d.closePath=d.__originalClosePath,d.beginPath=d.__originalBeginPath,delete d._removeMirroring},d.save=function(){t.save(),this.__originalSave()},d.restore=function(){t.restore(),this.__originalRestore()},d.translate=function(s,i){t.translate(s,i),this.__originalTranslate(s,i)},d.scale=function(s,i){t.scale(s,i),this.__originalScale(s,i)},d.transform=function(s,i,r,a,o,l){t.transform(s,i,r,a,o,l),this.__originalTransform(s,i,r,a,o,l)},d.setTransform=function(s,i,r,a,o,l){t.setTransform(s,i,r,a,o,l),this.__originalSetTransform(s,i,r,a,o,l)},d.resetTransform=function(){t.resetTransform(),this.__originalResetTransform()},d.rotate=function(s){t.rotate(s),this.__originalRotate(s)},d.clip=function(s){t.clip(s),this.__originalClip(s)},d.moveTo=function(e,s){t.moveTo(e,s),this.__originalMoveTo(e,s)},d.lineTo=function(e,s){t.lineTo(e,s),this.__originalLineTo(e,s)},d.bezierCurveTo=function(e,s,i,r,a,o){t.bezierCurveTo(e,s,i,r,a,o),this.__originalBezierCurveTo(e,s,i,r,a,o)},d.rect=function(e,s,i,r){t.rect(e,s,i,r),this.__originalRect(e,s,i,r)},d.closePath=function(){t.closePath(),this.__originalClosePath()},d.beginPath=function(){t.beginPath(),this.__originalBeginPath()}}class Np{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,s){let i;return this.cache[t]!==void 0?(i=this.cache[t],this.canvasFactory.reset(i,e,s)):(i=this.canvasFactory.create(e,s),this.cache[t]=i),i}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function Rl(d,t,e,s,i,r,a,o,l,h){const[c,u,p,b,A,y]=ht(d);if(u===0&&p===0){const _=a*c+A,E=Math.round(_),S=o*b+y,C=Math.round(S),T=(a+l)*c+A,x=Math.abs(Math.round(T)-E)||1,F=(o+h)*b+y,N=Math.abs(Math.round(F)-C)||1;return d.setTransform(Math.sign(c),0,0,Math.sign(b),E,C),d.drawImage(t,e,s,i,r,0,0,x,N),d.setTransform(c,u,p,b,A,y),[x,N]}if(c===0&&b===0){const _=o*p+A,E=Math.round(_),S=a*u+y,C=Math.round(S),T=(o+h)*p+A,x=Math.abs(Math.round(T)-E)||1,F=(a+l)*u+y,N=Math.abs(Math.round(F)-C)||1;return d.setTransform(0,Math.sign(u),Math.sign(p),0,E,C),d.drawImage(t,e,s,i,r,0,0,N,x),d.setTransform(c,u,p,b,A,y),[N,x]}d.drawImage(t,e,s,i,r,a,o,l,h);const w=Math.hypot(c,u),v=Math.hypot(p,b);return[w*l,v*h]}function Op(d){const{width:t,height:e}=d;if(t>Hd||e>Hd)return null;const s=1e3,i=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),r=t+1;let a=new Uint8Array(r*(e+1)),o,l,h;const c=t+7&-8;let u=new Uint8Array(c*e),p=0;for(const v of d.data){let _=128;for(;_>0;)u[p++]=v&_?0:255,_>>=1}let b=0;for(p=0,u[p]!==0&&(a[0]=1,++b),l=1;l<t;l++)u[p]!==u[p+1]&&(a[l]=u[p]?2:1,++b),p++;for(u[p]!==0&&(a[l]=2,++b),o=1;o<e;o++){p=o*c,h=o*r,u[p-c]!==u[p]&&(a[h]=u[p]?1:8,++b);let v=(u[p]?4:0)+(u[p-c]?8:0);for(l=1;l<t;l++)v=(v>>2)+(u[p+1]?4:0)+(u[p-c+1]?8:0),i[v]&&(a[h+l]=i[v],++b),p++;if(u[p-c]!==u[p]&&(a[h+l]=u[p]?2:4,++b),b>s)return null}for(p=c*(e-1),h=o*r,u[p]!==0&&(a[h]=8,++b),l=1;l<t;l++)u[p]!==u[p+1]&&(a[h+l]=u[p]?4:8,++b),p++;if(u[p]!==0&&(a[h+l]=4,++b),b>s)return null;const A=new Int32Array([0,r,-1,0,-r,0,0,0,1]),y=new Path2D;for(o=0;b&&o<=e;o++){let v=o*r;const _=v+t;for(;v<_&&!a[v];)v++;if(v===_)continue;y.moveTo(v%r,o);const E=v;let S=a[v];do{const C=A[S];do v+=C;while(!a[v]);const T=a[v];T!==5&&T!==10?(S=T,a[v]=0):(S=T&51*S>>4,a[v]&=S>>2|S<<2),y.lineTo(v%r,v/r|0),a[v]||--b}while(E!==v);--o}return u=null,a=null,function(v){v.save(),v.scale(1/t,-1/e),v.translate(0,-e),v.fill(y),v.beginPath(),v.restore()}}class Bd{constructor(t,e){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=qd,this.textMatrixScale=1,this.fontMatrix=Qh,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=zt.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.patternStroke=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.transferMaps="none",this.startNewPathAndClipBox([0,0,t,e])}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t}setCurrentPoint(t,e){this.x=t,this.y=e}updatePathMinMax(t,e,s){[e,s]=D.applyTransform([e,s],t),this.minX=Math.min(this.minX,e),this.minY=Math.min(this.minY,s),this.maxX=Math.max(this.maxX,e),this.maxY=Math.max(this.maxY,s)}updateRectMinMax(t,e){const s=D.applyTransform(e,t),i=D.applyTransform(e.slice(2),t),r=D.applyTransform([e[0],e[3]],t),a=D.applyTransform([e[2],e[1]],t);this.minX=Math.min(this.minX,s[0],i[0],r[0],a[0]),this.minY=Math.min(this.minY,s[1],i[1],r[1],a[1]),this.maxX=Math.max(this.maxX,s[0],i[0],r[0],a[0]),this.maxY=Math.max(this.maxY,s[1],i[1],r[1],a[1])}updateScalingPathMinMax(t,e){D.scaleMinMax(t,e),this.minX=Math.min(this.minX,e[0]),this.minY=Math.min(this.minY,e[1]),this.maxX=Math.max(this.maxX,e[2]),this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,s,i,r,a,o,l,h,c){const u=D.bezierBoundingBox(e,s,i,r,a,o,l,h,c);c||this.updateRectMinMax(t,u)}getPathBoundingBox(t=Yt.FILL,e=null){const s=[this.minX,this.minY,this.maxX,this.maxY];if(t===Yt.STROKE){e||it("Stroke bounding box must include transform.");const i=D.singularValueDecompose2dScale(e),r=i[0]*this.lineWidth/2,a=i[1]*this.lineWidth/2;s[0]-=r,s[1]-=a,s[2]+=r,s[3]+=a}return s}updateClipFromPath(){const t=D.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t,this.minX=1/0,this.minY=1/0,this.maxX=0,this.maxY=0}getClippedPathBoundingBox(t=Yt.FILL,e=null){return D.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function $d(d,t){if(t instanceof ImageData){d.putImageData(t,0,0);return}const e=t.height,s=t.width,i=e%pe,r=(e-i)/pe,a=i===0?r:r+1,o=d.createImageData(s,pe);let l=0,h;const c=t.data,u=o.data;let p,b,A,y;if(t.kind===Ll.GRAYSCALE_1BPP){const w=c.byteLength,v=new Uint32Array(u.buffer,0,u.byteLength>>2),_=v.length,E=s+7>>3,S=4294967295,C=ne.isLittleEndian?4278190080:255;for(p=0;p<a;p++){for(A=p<r?pe:i,h=0,b=0;b<A;b++){const T=w-l;let x=0;const F=T>E?s:T*8-7,N=F&-8;let z=0,U=0;for(;x<N;x+=8)U=c[l++],v[h++]=U&128?S:C,v[h++]=U&64?S:C,v[h++]=U&32?S:C,v[h++]=U&16?S:C,v[h++]=U&8?S:C,v[h++]=U&4?S:C,v[h++]=U&2?S:C,v[h++]=U&1?S:C;for(;x<F;x++)z===0&&(U=c[l++],z=128),v[h++]=U&z?S:C,z>>=1}for(;h<_;)v[h++]=0;d.putImageData(o,0,p*pe)}}else if(t.kind===Ll.RGBA_32BPP){for(b=0,y=s*pe*4,p=0;p<r;p++)u.set(c.subarray(l,l+y)),l+=y,d.putImageData(o,0,b),b+=pe;p<a&&(y=s*i*4,u.set(c.subarray(l,l+y)),d.putImageData(o,0,b))}else if(t.kind===Ll.RGB_24BPP)for(A=pe,y=s*A,p=0;p<a;p++){for(p>=r&&(A=i,y=s*A),h=0,b=y;b--;)u[h++]=c[l++],u[h++]=c[l++],u[h++]=c[l++],u[h++]=255;d.putImageData(o,0,p*pe)}else throw new Error(`bad image kind: ${t.kind}`)}function Gd(d,t){if(t.bitmap){d.drawImage(t.bitmap,0,0);return}const e=t.height,s=t.width,i=e%pe,r=(e-i)/pe,a=i===0?r:r+1,o=d.createImageData(s,pe);let l=0;const h=t.data,c=o.data;for(let u=0;u<a;u++){const p=u<r?pe:i;({srcPos:l}=Ip({src:h,srcPos:l,dest:c,width:s,height:p,nonBlackColor:0})),d.putImageData(o,0,u*pe)}}function Ra(d,t){const e=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of e)d[s]!==void 0&&(t[s]=d[s]);d.setLineDash!==void 0&&(t.setLineDash(d.getLineDash()),t.lineDashOffset=d.lineDashOffset)}function Ml(d){if(d.strokeStyle=d.fillStyle="#000000",d.fillRule="nonzero",d.globalAlpha=1,d.lineWidth=1,d.lineCap="butt",d.lineJoin="miter",d.miterLimit=10,d.globalCompositeOperation="source-over",d.font="10px sans-serif",d.setLineDash!==void 0&&(d.setLineDash([]),d.lineDashOffset=0),!Kt){const{filter:t}=d;t!=="none"&&t!==""&&(d.filter="none")}}function zd(d,t){if(t)return!0;const e=D.singularValueDecompose2dScale(d);e[0]=Math.fround(e[0]),e[1]=Math.fround(e[1]);const s=Math.fround((globalThis.devicePixelRatio||1)*Vi.PDF_TO_CSS_UNITS);return e[0]<=s&&e[1]<=s}const Hp=["butt","round","square"],Bp=["miter","round","bevel"],$p={},Ud={};var as,Ec,Cc,xc;const Sd=class Sd{constructor(t,e,s,i,r,{optionalContentConfig:a,markedContentStack:o=null},l,h){g(this,as);this.ctx=t,this.current=new Bd(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=s,this.canvasFactory=i,this.filterFactory=r,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=o||[],this.optionalContentConfig=a,this.cachedCanvases=new Np(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=l,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=h,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return typeof t=="string"?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:s=!1,background:i=null}){const r=this.ctx.canvas.width,a=this.ctx.canvas.height,o=this.ctx.fillStyle;if(this.ctx.fillStyle=i||"#ffffff",this.ctx.fillRect(0,0,r,a),this.ctx.fillStyle=o,s){const l=this.cachedCanvases.getCanvas("transparent",r,a);this.compositeCtx=this.ctx,this.transparentCanvas=l.canvas,this.ctx=l.context,this.ctx.save(),this.ctx.transform(...ht(this.compositeCtx))}this.ctx.save(),Ml(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=ht(this.ctx)}executeOperatorList(t,e,s,i){const r=t.argsArray,a=t.fnArray;let o=e||0;const l=r.length;if(l===o)return o;const h=l-o>Od&&typeof s=="function",c=h?Date.now()+Dp:0;let u=0;const p=this.commonObjs,b=this.objs;let A;for(;;){if(i!==void 0&&o===i.nextBreakPoint)return i.breakIt(o,s),o;if(A=a[o],A!==Ve.dependency)this[A].apply(this,r[o]);else for(const y of r[o]){const w=y.startsWith("g_")?p:b;if(!w.has(y))return w.get(y,s),o}if(o++,o===l)return o;if(h&&++u>Od){if(Date.now()>c)return s(),o;u=0}}}endDrawing(){m(this,as,Ec).call(this),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())typeof HTMLCanvasElement<"u"&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),m(this,as,Cc).call(this)}_scaleImage(t,e){const s=t.width??t.displayWidth,i=t.height??t.displayHeight;let r=Math.max(Math.hypot(e[0],e[1]),1),a=Math.max(Math.hypot(e[2],e[3]),1),o=s,l=i,h="prescale1",c,u;for(;r>2&&o>1||a>2&&l>1;){let p=o,b=l;r>2&&o>1&&(p=o>=16384?Math.floor(o/2)-1||1:Math.ceil(o/2),r/=o/p),a>2&&l>1&&(b=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l)/2,a/=l/b),c=this.cachedCanvases.getCanvas(h,p,b),u=c.context,u.clearRect(0,0,p,b),u.drawImage(t,0,0,o,l,0,0,p,b),t=c.canvas,o=p,l=b,h=h==="prescale1"?"prescale2":"prescale1"}return{img:t,paintWidth:o,paintHeight:l}}_createMaskCanvas(t){const e=this.ctx,{width:s,height:i}=t,r=this.current.fillColor,a=this.current.patternFill,o=ht(e);let l,h,c,u;if((t.bitmap||t.data)&&t.count>1){const F=t.bitmap||t.data.buffer;h=JSON.stringify(a?o:[o.slice(0,4),r]),l=this._cachedBitmapsMap.get(F),l||(l=new Map,this._cachedBitmapsMap.set(F,l));const N=l.get(h);if(N&&!a){const z=Math.round(Math.min(o[0],o[2])+o[4]),U=Math.round(Math.min(o[1],o[3])+o[5]);return{canvas:N,offsetX:z,offsetY:U}}c=N}c||(u=this.cachedCanvases.getCanvas("maskCanvas",s,i),Gd(u.context,t));let p=D.transform(o,[1/s,0,0,-1/i,0,0]);p=D.transform(p,[1,0,0,1,0,-i]);const[b,A,y,w]=D.getAxialAlignedBoundingBox([0,0,s,i],p),v=Math.round(y-b)||1,_=Math.round(w-A)||1,E=this.cachedCanvases.getCanvas("fillCanvas",v,_),S=E.context,C=b,T=A;S.translate(-C,-T),S.transform(...p),c||(c=this._scaleImage(u.canvas,ls(S)),c=c.img,l&&a&&l.set(h,c)),S.imageSmoothingEnabled=zd(ht(S),t.interpolate),Rl(S,c,0,0,c.width,c.height,0,0,s,i),S.globalCompositeOperation="source-in";const x=D.transform(ls(S),[1,0,0,1,-C,-T]);return S.fillStyle=a?r.getPattern(e,this,x,Yt.FILL):r,S.fillRect(0,0,s,i),l&&!a&&(this.cachedCanvases.delete("fillCanvas"),l.set(h,E.canvas)),{canvas:E.canvas,offsetX:Math.round(C),offsetY:Math.round(T)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=Hp[t]}setLineJoin(t){this.ctx.lineJoin=Bp[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const s=this.ctx;s.setLineDash!==void 0&&(s.setLineDash(t),s.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,s]of t)switch(e){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s[0],s[1]);break;case"CA":this.current.strokeAlpha=s;break;case"ca":this.current.fillAlpha=s,this.ctx.globalAlpha=s;break;case"BM":this.ctx.globalCompositeOperation=s;break;case"SMask":this.current.activeSMask=s?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(s);break}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,s="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(s,t,e);this.suspendedCtx=this.ctx,this.ctx=i.context;const r=this.ctx;r.setTransform(...ht(this.suspendedCtx)),Ra(this.suspendedCtx,r),Fp(r,this.suspendedCtx),this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),Ra(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,s=this.suspendedCtx;this.composeSMask(s,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,s,i){const r=i[0],a=i[1],o=i[2]-r,l=i[3]-a;o===0||l===0||(this.genericComposeSMask(e.context,s,o,l,e.subtype,e.backdrop,e.transferMap,r,a,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(s.canvas,0,0),t.restore())}genericComposeSMask(t,e,s,i,r,a,o,l,h,c,u){let p=t.canvas,b=l-c,A=h-u;if(a){const w=D.makeHexColor(...a);if(b<0||A<0||b+s>p.width||A+i>p.height){const v=this.cachedCanvases.getCanvas("maskExtension",s,i),_=v.context;_.drawImage(p,-b,-A),_.globalCompositeOperation="destination-atop",_.fillStyle=w,_.fillRect(0,0,s,i),_.globalCompositeOperation="source-over",p=v.canvas,b=A=0}else{t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);const v=new Path2D;v.rect(b,A,s,i),t.clip(v),t.globalCompositeOperation="destination-atop",t.fillStyle=w,t.fillRect(b,A,s,i),t.restore()}}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),r==="Alpha"&&o?e.filter=this.filterFactory.addAlphaFilter(o):r==="Luminosity"&&(e.filter=this.filterFactory.addLuminosityFilter(o));const y=new Path2D;y.rect(l,h,s,i),e.clip(y),e.globalCompositeOperation="destination-in",e.drawImage(p,b,A,s,i,l,h,s,i),e.restore()}save(){this.inSMaskMode?(Ra(this.ctx,this.suspendedCtx),this.suspendedCtx.save()):this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){this.stateStack.length===0&&this.inSMaskMode&&this.endSMaskMode(),this.stateStack.length!==0&&(this.current=this.stateStack.pop(),this.inSMaskMode?(this.suspendedCtx.restore(),Ra(this.suspendedCtx,this.ctx)):this.ctx.restore(),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null)}transform(t,e,s,i,r,a){this.ctx.transform(t,e,s,i,r,a),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,s){const i=this.ctx,r=this.current;let a=r.x,o=r.y,l,h;const c=ht(i),u=c[0]===0&&c[3]===0||c[1]===0&&c[2]===0,p=u?s.slice(0):null;for(let b=0,A=0,y=t.length;b<y;b++)switch(t[b]|0){case Ve.rectangle:a=e[A++],o=e[A++];const w=e[A++],v=e[A++],_=a+w,E=o+v;i.moveTo(a,o),w===0||v===0?i.lineTo(_,E):(i.lineTo(_,o),i.lineTo(_,E),i.lineTo(a,E)),u||r.updateRectMinMax(c,[a,o,_,E]),i.closePath();break;case Ve.moveTo:a=e[A++],o=e[A++],i.moveTo(a,o),u||r.updatePathMinMax(c,a,o);break;case Ve.lineTo:a=e[A++],o=e[A++],i.lineTo(a,o),u||r.updatePathMinMax(c,a,o);break;case Ve.curveTo:l=a,h=o,a=e[A+4],o=e[A+5],i.bezierCurveTo(e[A],e[A+1],e[A+2],e[A+3],a,o),r.updateCurvePathMinMax(c,l,h,e[A],e[A+1],e[A+2],e[A+3],a,o,p),A+=6;break;case Ve.curveTo2:l=a,h=o,i.bezierCurveTo(a,o,e[A],e[A+1],e[A+2],e[A+3]),r.updateCurvePathMinMax(c,l,h,a,o,e[A],e[A+1],e[A+2],e[A+3],p),a=e[A+2],o=e[A+3],A+=4;break;case Ve.curveTo3:l=a,h=o,a=e[A+2],o=e[A+3],i.bezierCurveTo(e[A],e[A+1],a,o,a,o),r.updateCurvePathMinMax(c,l,h,e[A],e[A+1],a,o,a,o,p),A+=4;break;case Ve.closePath:i.closePath();break}u&&r.updateScalingPathMinMax(c,p),r.setCurrentPoint(a,o)}closePath(){this.ctx.closePath()}stroke(t=!0){const e=this.ctx,s=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha,this.contentVisible&&(typeof s=="object"&&s?.getPattern?(e.save(),e.strokeStyle=s.getPattern(e,this,ls(e),Yt.STROKE),this.rescaleAndStroke(!1),e.restore()):this.rescaleAndStroke(!0)),t&&this.consumePath(this.current.getClippedPathBoundingBox()),e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath(),this.stroke()}fill(t=!0){const e=this.ctx,s=this.current.fillColor,i=this.current.patternFill;let r=!1;i&&(e.save(),e.fillStyle=s.getPattern(e,this,ls(e),Yt.FILL),r=!0);const a=this.current.getClippedPathBoundingBox();this.contentVisible&&a!==null&&(this.pendingEOFill?(e.fill("evenodd"),this.pendingEOFill=!1):e.fill()),r&&e.restore(),t&&this.consumePath(a)}eoFill(){this.pendingEOFill=!0,this.fill()}fillStroke(){this.fill(!1),this.stroke(!1),this.consumePath()}eoFillStroke(){this.pendingEOFill=!0,this.fillStroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=$p}eoClip(){this.pendingClip=Ud}beginText(){this.current.textMatrix=qd,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(t===void 0){e.beginPath();return}const s=new Path2D,i=e.getTransform().invertSelf();for(const{transform:r,x:a,y:o,fontSize:l,path:h}of t)s.addPath(h,new DOMMatrix(r).preMultiplySelf(i).translate(a,o).scale(l,-l));e.clip(s),e.beginPath(),delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const s=this.commonObjs.get(t),i=this.current;if(!s)throw new Error(`Can't find font for ${t}`);if(i.fontMatrix=s.fontMatrix||Qh,(i.fontMatrix[0]===0||i.fontMatrix[3]===0)&&V("Invalid font matrix for font "+t),e<0?(e=-e,i.fontDirection=-1):i.fontDirection=1,this.current.font=s,this.current.fontSize=e,s.isType3Font)return;const r=s.loadedName||"sans-serif",a=s.systemFontInfo?.css||`"${r}", ${s.fallbackName}`;let o="normal";s.black?o="900":s.bold&&(o="bold");const l=s.italic?"italic":"normal";let h=e;e<Fd?h=Fd:e>Nd&&(h=Nd),this.current.fontSizeScale=e/h,this.ctx.font=`${l} ${o} ${h}px ${a}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t,e,s,i,r,a){this.current.textMatrix=[t,e,s,i,r,a],this.current.textMatrixScale=Math.hypot(t,e),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,s,i,r){const a=this.ctx,o=this.current,l=o.font,h=o.textRenderingMode,c=o.fontSize/o.fontSizeScale,u=h&zt.FILL_STROKE_MASK,p=!!(h&zt.ADD_TO_PATH_FLAG),b=o.patternFill&&!l.missingFile,A=o.patternStroke&&!l.missingFile;let y;if((l.disableFontFace||p||b||A)&&(y=l.getPathGenerator(this.commonObjs,t)),l.disableFontFace||b||A){if(a.save(),a.translate(e,s),a.scale(c,-c),u===zt.FILL||u===zt.FILL_STROKE)if(i){const w=a.getTransform();a.setTransform(...i),a.fill(m(this,as,xc).call(this,y,w,i))}else a.fill(y);if(u===zt.STROKE||u===zt.FILL_STROKE)if(r){const w=a.getTransform();a.setTransform(...r),a.stroke(m(this,as,xc).call(this,y,w,r))}else a.lineWidth/=c,a.stroke(y);a.restore()}else(u===zt.FILL||u===zt.FILL_STROKE)&&a.fillText(t,e,s),(u===zt.STROKE||u===zt.FILL_STROKE)&&a.strokeText(t,e,s);p&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:ht(a),x:e,y:s,fontSize:c,path:y})}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let s=!1;for(let i=3;i<e.length;i+=4)if(e[i]>0&&e[i]<255){s=!0;break}return q(this,"isFontSubpixelAAEnabled",s)}showText(t){const e=this.current,s=e.font;if(s.isType3Font)return this.showType3Text(t);const i=e.fontSize;if(i===0)return;const r=this.ctx,a=e.fontSizeScale,o=e.charSpacing,l=e.wordSpacing,h=e.fontDirection,c=e.textHScale*h,u=t.length,p=s.vertical,b=p?1:-1,A=s.defaultVMetrics,y=i*e.fontMatrix[0],w=e.textRenderingMode===zt.FILL&&!s.disableFontFace&&!e.patternFill;r.save(),r.transform(...e.textMatrix),r.translate(e.x,e.y+e.textRise),h>0?r.scale(c,-1):r.scale(c,1);let v,_;if(e.patternFill){r.save();const x=e.fillColor.getPattern(r,this,ls(r),Yt.FILL);v=ht(r),r.restore(),r.fillStyle=x}if(e.patternStroke){r.save();const x=e.strokeColor.getPattern(r,this,ls(r),Yt.STROKE);_=ht(r),r.restore(),r.strokeStyle=x}let E=e.lineWidth;const S=e.textMatrixScale;if(S===0||E===0){const x=e.textRenderingMode&zt.FILL_STROKE_MASK;(x===zt.STROKE||x===zt.FILL_STROKE)&&(E=this.getSinglePixelWidth())}else E/=S;if(a!==1&&(r.scale(a,a),E/=a),r.lineWidth=E,s.isInvalidPDFjsFont){const x=[];let F=0;for(const N of t)x.push(N.unicode),F+=N.width;r.fillText(x.join(""),0,0),e.x+=F*y*c,r.restore(),this.compose();return}let C=0,T;for(T=0;T<u;++T){const x=t[T];if(typeof x=="number"){C+=b*x*i/1e3;continue}let F=!1;const N=(x.isSpace?l:0)+o,z=x.fontChar,U=x.accent;let dt,ut,nt=x.width;if(p){const L=x.vmetric||A,B=-(x.vmetric?L[1]:nt*.5)*y,ge=L[2]*y;nt=L?-L[0]:nt,dt=B/a,ut=(C+ge)/a}else dt=C/a,ut=0;if(s.remeasure&&nt>0){const L=r.measureText(z).width*1e3/i*a;if(nt<L&&this.isFontSubpixelAAEnabled){const B=nt/L;F=!0,r.save(),r.scale(B,1),dt/=B}else nt!==L&&(dt+=(nt-L)/2e3*i/a)}if(this.contentVisible&&(x.isInFont||s.missingFile)){if(w&&!U)r.fillText(z,dt,ut);else if(this.paintChar(z,dt,ut,v,_),U){const L=dt+i*U.offset.x/a,B=ut-i*U.offset.y/a;this.paintChar(U.fontChar,L,B,v,_)}}const Qt=p?nt*y-N*h:nt*y+N*h;C+=Qt,F&&r.restore()}p?e.y-=C:e.x+=C*c,r.restore(),this.compose()}showType3Text(t){const e=this.ctx,s=this.current,i=s.font,r=s.fontSize,a=s.fontDirection,o=i.vertical?1:-1,l=s.charSpacing,h=s.wordSpacing,c=s.textHScale*a,u=s.fontMatrix||Qh,p=t.length,b=s.textRenderingMode===zt.INVISIBLE;let A,y,w,v;if(!(b||r===0)){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,e.save(),e.transform(...s.textMatrix),e.translate(s.x,s.y),e.scale(c,a),A=0;A<p;++A){if(y=t[A],typeof y=="number"){v=o*y*r/1e3,this.ctx.translate(v,0),s.x+=v*c;continue}const _=(y.isSpace?h:0)+l,E=i.charProcOperatorList[y.operatorListId];if(!E){V(`Type3 character "${y.operatorListId}" is not available.`);continue}this.contentVisible&&(this.processingType3=y,this.save(),e.scale(r,r),e.transform(...u),this.executeOperatorList(E),this.restore()),w=D.applyTransform([y.width,0],u)[0]*r+_,e.translate(w,0),s.x+=w*c}e.restore(),this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,s,i,r,a){this.ctx.rect(s,i,r-s,a-i),this.ctx.clip(),this.endPath()}getColorN_Pattern(t){let e;if(t[0]==="TilingPattern"){const s=t[1],i=this.baseTransform||ht(this.ctx),r={createCanvasGraphics:a=>new Sd(a,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new Sc(t,s,this.ctx,r,i)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments),this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t,e,s){this.ctx.strokeStyle=this.current.strokeColor=D.makeHexColor(t,e,s),this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent",this.current.patternStroke=!1}setFillRGBColor(t,e,s){this.ctx.fillStyle=this.current.fillColor=D.makeHexColor(t,e,s),this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent",this.current.patternFill=!1}_getPattern(t,e=null){let s;return this.cachedPatterns.has(t)?s=this.cachedPatterns.get(t):(s=Lp(this.getObject(t)),this.cachedPatterns.set(t,s)),e&&(s.matrix=e),s}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const s=this._getPattern(t);e.fillStyle=s.getPattern(e,this,ls(e),Yt.SHADING);const i=ls(e);if(i){const{width:r,height:a}=e.canvas,[o,l,h,c]=D.getAxialAlignedBoundingBox([0,0,r,a],i);this.ctx.fillRect(o,l,h-o,c-l)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){it("Should not call beginInlineImage")}beginImageData(){it("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=ht(this.ctx),e)){const s=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],s,i),this.current.updateRectMinMax(ht(this.ctx),e),this.clip(),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||Oh("TODO: Support non-isolated groups."),t.knockout&&V("Knockout groups not supported.");const s=ht(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw new Error("Bounding box is required.");let i=D.getAxialAlignedBoundingBox(t.bbox,ht(e));const r=[0,0,e.canvas.width,e.canvas.height];i=D.intersect(i,r)||[0,0,0,0];const a=Math.floor(i[0]),o=Math.floor(i[1]),l=Math.max(Math.ceil(i[2])-a,1),h=Math.max(Math.ceil(i[3])-o,1);this.current.startNewPathAndClipBox([0,0,l,h]);let c="groupAt"+this.groupLevel;t.smask&&(c+="_smask_"+this.smaskCounter++%2);const u=this.cachedCanvases.getCanvas(c,l,h),p=u.context;p.translate(-a,-o),p.transform(...s),t.smask?this.smaskStack.push({canvas:u.canvas,context:p,offsetX:a,offsetY:o,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(a,o),e.save()),Ra(e,p),this.ctx=p,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,s=this.groupStack.pop();if(this.ctx=s,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const i=ht(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...i);const r=D.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],i);this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(r)}}beginAnnotation(t,e,s,i,r){if(m(this,as,Ec).call(this),Ml(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){const a=e[2]-e[0],o=e[3]-e[1];if(r&&this.annotationCanvasMap){s=s.slice(),s[4]-=e[0],s[5]-=e[1],e=e.slice(),e[0]=e[1]=0,e[2]=a,e[3]=o;const[l,h]=D.singularValueDecompose2dScale(ht(this.ctx)),{viewportScale:c}=this,u=Math.ceil(a*this.outputScaleX*c),p=Math.ceil(o*this.outputScaleY*c);this.annotationCanvas=this.canvasFactory.create(u,p);const{canvas:b,context:A}=this.annotationCanvas;this.annotationCanvasMap.set(t,b),this.annotationCanvas.savedCtx=this.ctx,this.ctx=A,this.ctx.save(),this.ctx.setTransform(l,0,0,-h,0,o*h),Ml(this.ctx)}else Ml(this.ctx),this.endPath(),this.ctx.rect(e[0],e[1],a,o),this.ctx.clip(),this.ctx.beginPath()}this.current=new Bd(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...s),this.transform(...i)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),m(this,as,Cc).call(this),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;t=this.getObject(t.data,t),t.count=e;const s=this.ctx,i=this.processingType3;if(i&&(i.compiled===void 0&&(i.compiled=Op(t)),i.compiled)){i.compiled(s);return}const r=this._createMaskCanvas(t),a=r.canvas;s.save(),s.setTransform(1,0,0,1,0,0),s.drawImage(a,r.offsetX,r.offsetY),s.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,s=0,i=0,r,a){if(!this.contentVisible)return;t=this.getObject(t.data,t);const o=this.ctx;o.save();const l=ht(o);o.transform(e,s,i,r,0,0);const h=this._createMaskCanvas(t);o.setTransform(1,0,0,1,h.offsetX-l[4],h.offsetY-l[5]);for(let c=0,u=a.length;c<u;c+=2){const p=D.transform(l,[e,s,i,r,a[c],a[c+1]]),[b,A]=D.applyTransform([0,0],p);o.drawImage(h.canvas,b,A)}o.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,s=this.current.fillColor,i=this.current.patternFill;for(const r of t){const{data:a,width:o,height:l,transform:h}=r,c=this.cachedCanvases.getCanvas("maskCanvas",o,l),u=c.context;u.save();const p=this.getObject(a,r);Gd(u,p),u.globalCompositeOperation="source-in",u.fillStyle=i?s.getPattern(u,this,ls(e),Yt.FILL):s,u.fillRect(0,0,o,l),u.restore(),e.save(),e.transform(...h),e.scale(1,-1),Rl(e,c.canvas,0,0,o,l,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);if(!e){V("Dependent image isn't ready yet");return}this.paintInlineImageXObject(e)}paintImageXObjectRepeat(t,e,s,i){if(!this.contentVisible)return;const r=this.getObject(t);if(!r){V("Dependent image isn't ready yet");return}const a=r.width,o=r.height,l=[];for(let h=0,c=i.length;h<c;h+=2)l.push({transform:[e,0,0,s,i[h],i[h+1]],x:0,y:0,w:a,h:o});this.paintInlineImageXObjectGroup(r,l)}applyTransferMapsToCanvas(t){return this.current.transferMaps!=="none"&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if(this.current.transferMaps==="none")return t.bitmap;const{bitmap:e,width:s,height:i}=t,r=this.cachedCanvases.getCanvas("inlineImage",s,i),a=r.context;return a.filter=this.current.transferMaps,a.drawImage(e,0,0),a.filter="none",r.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,s=t.height,i=this.ctx;if(this.save(),!Kt){const{filter:o}=i;o!=="none"&&o!==""&&(i.filter="none")}i.scale(1/e,-1/s);let r;if(t.bitmap)r=this.applyTransferMapsToBitmap(t);else if(typeof HTMLElement=="function"&&t instanceof HTMLElement||!t.data)r=t;else{const l=this.cachedCanvases.getCanvas("inlineImage",e,s).context;$d(l,t),r=this.applyTransferMapsToCanvas(l)}const a=this._scaleImage(r,ls(i));i.imageSmoothingEnabled=zd(ht(i),t.interpolate),Rl(i,a.img,0,0,a.paintWidth,a.paintHeight,0,-s,e,s),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const s=this.ctx;let i;if(t.bitmap)i=t.bitmap;else{const r=t.width,a=t.height,l=this.cachedCanvases.getCanvas("inlineImage",r,a).context;$d(l,t),i=this.applyTransferMapsToCanvas(l)}for(const r of e)s.save(),s.transform(...r.transform),s.scale(1,-1),Rl(s,i,r.x,r.y,r.w,r.h,0,-1,1,1),s.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){t==="OC"?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){const e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(t);const s=this.ctx;this.pendingClip&&(e||(this.pendingClip===Ud?s.clip("evenodd"):s.clip()),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox),s.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=ht(this.ctx);if(t[1]===0&&t[2]===0)this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),s=Math.hypot(t[0],t[2]),i=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(s,i)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(this._cachedScaleForStroking[0]===-1){const{lineWidth:t}=this.current,{a:e,b:s,c:i,d:r}=this.ctx.getTransform();let a,o;if(s===0&&i===0){const l=Math.abs(e),h=Math.abs(r);if(l===h)if(t===0)a=o=1/l;else{const c=l*t;a=o=c<1?1/c:1}else if(t===0)a=1/l,o=1/h;else{const c=l*t,u=h*t;a=c<1?1/c:1,o=u<1?1/u:1}}else{const l=Math.abs(e*r-s*i),h=Math.hypot(e,s),c=Math.hypot(i,r);if(t===0)a=c/l,o=h/l;else{const u=t*l;a=c>u?c/u:1,o=h>u?h/u:1}}this._cachedScaleForStroking[0]=a,this._cachedScaleForStroking[1]=o}return this._cachedScaleForStroking}rescaleAndStroke(t){const{ctx:e}=this,{lineWidth:s}=this.current,[i,r]=this.getScaleForStroking();if(e.lineWidth=s||1,i===1&&r===1){e.stroke();return}const a=e.getLineDash();if(t&&e.save(),e.scale(i,r),a.length>0){const o=Math.max(i,r);e.setLineDash(a.map(l=>l/o)),e.lineDashOffset/=o}e.stroke(),t&&e.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}};as=new WeakSet,Ec=function(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null,this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)},Cc=function(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if(t!=="none"){const e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}},xc=function(t,e,s){const i=new Path2D;return i.addPath(t,new DOMMatrix(s).invertSelf().multiplySelf(e)),i};let vr=Sd;for(const d in Ve)vr.prototype[d]!==void 0&&(vr.prototype[Ve[d]]=vr.prototype[d]);var Po,Ro;class ai{static get workerPort(){return n(this,Po)}static set workerPort(t){if(!(typeof Worker<"u"&&t instanceof Worker)&&t!==null)throw new Error("Invalid `workerPort` type.");f(this,Po,t)}static get workerSrc(){return n(this,Ro)}static set workerSrc(t){if(typeof t!="string")throw new Error("Invalid `workerSrc` type.");f(this,Ro,t)}}Po=new WeakMap,Ro=new WeakMap,g(ai,Po,null),g(ai,Ro,"");var Sn,Mo;class Gp{constructor({parsedData:t,rawData:e}){g(this,Sn);g(this,Mo);f(this,Sn,t),f(this,Mo,e)}getRaw(){return n(this,Mo)}get(t){return n(this,Sn).get(t)??null}getAll(){return dd(n(this,Sn))}has(t){return n(this,Sn).has(t)}}Sn=new WeakMap,Mo=new WeakMap;const pr=Symbol("INTERNAL");var ko,Lo,Io,Br;class zp{constructor(t,{name:e,intent:s,usage:i,rbGroups:r}){g(this,ko,!1);g(this,Lo,!1);g(this,Io,!1);g(this,Br,!0);f(this,ko,!!(t&Pe.DISPLAY)),f(this,Lo,!!(t&Pe.PRINT)),this.name=e,this.intent=s,this.usage=i,this.rbGroups=r}get visible(){if(n(this,Io))return n(this,Br);if(!n(this,Br))return!1;const{print:t,view:e}=this.usage;return n(this,ko)?e?.viewState!=="OFF":n(this,Lo)?t?.printState!=="OFF":!0}_setVisible(t,e,s=!1){t!==pr&&it("Internal method `_setVisible` called."),f(this,Io,s),f(this,Br,e)}}ko=new WeakMap,Lo=new WeakMap,Io=new WeakMap,Br=new WeakMap;var Ci,tt,$r,Gr,Do,Tc;class Up{constructor(t,e=Pe.DISPLAY){g(this,Do);g(this,Ci,null);g(this,tt,new Map);g(this,$r,null);g(this,Gr,null);if(this.renderingIntent=e,this.name=null,this.creator=null,t!==null){this.name=t.name,this.creator=t.creator,f(this,Gr,t.order);for(const s of t.groups)n(this,tt).set(s.id,new zp(e,s));if(t.baseState==="OFF")for(const s of n(this,tt).values())s._setVisible(pr,!1);for(const s of t.on)n(this,tt).get(s)._setVisible(pr,!0);for(const s of t.off)n(this,tt).get(s)._setVisible(pr,!1);f(this,$r,this.getHash())}}isVisible(t){if(n(this,tt).size===0)return!0;if(!t)return Oh("Optional content group not defined."),!0;if(t.type==="OCG")return n(this,tt).has(t.id)?n(this,tt).get(t.id).visible:(V(`Optional content group not found: ${t.id}`),!0);if(t.type==="OCMD"){if(t.expression)return m(this,Do,Tc).call(this,t.expression);if(!t.policy||t.policy==="AnyOn"){for(const e of t.ids){if(!n(this,tt).has(e))return V(`Optional content group not found: ${e}`),!0;if(n(this,tt).get(e).visible)return!0}return!1}else if(t.policy==="AllOn"){for(const e of t.ids){if(!n(this,tt).has(e))return V(`Optional content group not found: ${e}`),!0;if(!n(this,tt).get(e).visible)return!1}return!0}else if(t.policy==="AnyOff"){for(const e of t.ids){if(!n(this,tt).has(e))return V(`Optional content group not found: ${e}`),!0;if(!n(this,tt).get(e).visible)return!0}return!1}else if(t.policy==="AllOff"){for(const e of t.ids){if(!n(this,tt).has(e))return V(`Optional content group not found: ${e}`),!0;if(n(this,tt).get(e).visible)return!1}return!0}return V(`Unknown optional content policy ${t.policy}.`),!0}return V(`Unknown group type ${t.type}.`),!0}setVisibility(t,e=!0,s=!0){const i=n(this,tt).get(t);if(!i){V(`Optional content group not found: ${t}`);return}if(s&&e&&i.rbGroups.length)for(const r of i.rbGroups)for(const a of r)a!==t&&n(this,tt).get(a)?._setVisible(pr,!1,!0);i._setVisible(pr,!!e,!0),f(this,Ci,null)}setOCGState({state:t,preserveRB:e}){let s;for(const i of t){switch(i){case"ON":case"OFF":case"Toggle":s=i;continue}const r=n(this,tt).get(i);if(r)switch(s){case"ON":this.setVisibility(i,!0,e);break;case"OFF":this.setVisibility(i,!1,e);break;case"Toggle":this.setVisibility(i,!r.visible,e);break}}f(this,Ci,null)}get hasInitialVisibility(){return n(this,$r)===null||this.getHash()===n(this,$r)}getOrder(){return n(this,tt).size?n(this,Gr)?n(this,Gr).slice():[...n(this,tt).keys()]:null}getGroups(){return n(this,tt).size>0?dd(n(this,tt)):null}getGroup(t){return n(this,tt).get(t)||null}getHash(){if(n(this,Ci)!==null)return n(this,Ci);const t=new Cu;for(const[e,s]of n(this,tt))t.update(`${e}:${s.visible}`);return f(this,Ci,t.hexdigest())}}Ci=new WeakMap,tt=new WeakMap,$r=new WeakMap,Gr=new WeakMap,Do=new WeakSet,Tc=function(t){const e=t.length;if(e<2)return!0;const s=t[0];for(let i=1;i<e;i++){const r=t[i];let a;if(Array.isArray(r))a=m(this,Do,Tc).call(this,r);else if(n(this,tt).has(r))a=n(this,tt).get(r).visible;else return V(`Optional content group not found: ${r}`),!0;switch(s){case"And":if(!a)return!1;break;case"Or":if(a)return!0;break;case"Not":return!a;default:return!0}}return s==="And"};class jp{constructor(t,{disableRange:e=!1,disableStream:s=!1}){wt(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:i,initialData:r,progressiveDone:a,contentDispositionFilename:o}=t;if(this._queuedChunks=[],this._progressiveDone=a,this._contentDispositionFilename=o,r?.length>0){const l=r instanceof Uint8Array&&r.byteLength===r.buffer.byteLength?r.buffer:new Uint8Array(r).buffer;this._queuedChunks.push(l)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!s,this._isRangeSupported=!e,this._contentLength=i,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener((l,h)=>{this._onReceiveData({begin:l,chunk:h})}),t.addProgressListener((l,h)=>{this._onProgress({loaded:l,total:h})}),t.addProgressiveReadListener(l=>{this._onReceiveData({chunk:l})}),t.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),t.transportReady()}_onReceiveData({begin:t,chunk:e}){const s=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(t===void 0)this._fullRequestReader?this._fullRequestReader._enqueue(s):this._queuedChunks.push(s);else{const i=this._rangeReaders.some(function(r){return r._begin!==t?!1:(r._enqueue(s),!0)});wt(i,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){t.total===void 0?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){wt(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new Vp(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new Wp(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}class Vp{constructor(t,e,s=!1,i=null){this._stream=t,this._done=s||!1,this._filename=pd(i)?i:null,this._queuedChunks=e||[],this._loaded=0;for(const r of this._queuedChunks)this._loaded+=r.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){this._done||(this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunks.push(t),this._loaded+=t.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class Wp{constructor(t,e,s){this._stream=t,this._begin=e,this._end=s,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length===0)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function Xp(d){let t=!0,e=s("filename\\*","i").exec(d);if(e){e=e[1];let c=o(e);return c=unescape(c),c=l(c),c=h(c),r(c)}if(e=a(d),e){const c=h(e);return r(c)}if(e=s("filename","i").exec(d),e){e=e[1];let c=o(e);return c=h(c),r(c)}function s(c,u){return new RegExp("(?:^|;)\\s*"+c+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',u)}function i(c,u){if(c){if(!/^[\x00-\xFF]+$/.test(u))return u;try{const p=new TextDecoder(c,{fatal:!0}),b=Hh(u);u=p.decode(b),t=!1}catch{}}return u}function r(c){return t&&/[\x80-\xff]/.test(c)&&(c=i("utf-8",c),t&&(c=i("iso-8859-1",c))),c}function a(c){const u=[];let p;const b=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;(p=b.exec(c))!==null;){let[,y,w,v]=p;if(y=parseInt(y,10),y in u){if(y===0)break;continue}u[y]=[w,v]}const A=[];for(let y=0;y<u.length&&y in u;++y){let[w,v]=u[y];v=o(v),w&&(v=unescape(v),y===0&&(v=l(v))),A.push(v)}return A.join("")}function o(c){if(c.startsWith('"')){const u=c.slice(1).split('\\"');for(let p=0;p<u.length;++p){const b=u[p].indexOf('"');b!==-1&&(u[p]=u[p].slice(0,b),u.length=p+1),u[p]=u[p].replaceAll(/\\(.)/g,"$1")}c=u.join('"')}return c}function l(c){const u=c.indexOf("'");if(u===-1)return c;const p=c.slice(0,u),A=c.slice(u+1).replace(/^[^']*'/,"");return i(p,A)}function h(c){return!c.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(c)?c:c.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(u,p,b,A){if(b==="q"||b==="Q")return A=A.replaceAll("_"," "),A=A.replaceAll(/=([0-9a-fA-F]{2})/g,function(y,w){return String.fromCharCode(parseInt(w,16))}),i(p,A);try{A=atob(A)}catch{}return i(p,A)})}return""}function Bu(d,t){const e=new Headers;if(!d||!t||typeof t!="object")return e;for(const s in t){const i=t[s];i!==void 0&&e.append(s,i)}return e}function Gh(d){try{return new URL(d).origin}catch{}return null}function $u({responseHeaders:d,isHttp:t,rangeChunkSize:e,disableRange:s}){const i={allowRangeRequests:!1,suggestedLength:void 0},r=parseInt(d.get("Content-Length"),10);return!Number.isInteger(r)||(i.suggestedLength=r,r<=2*e)||s||!t||d.get("Accept-Ranges")!=="bytes"||(d.get("Content-Encoding")||"identity")!=="identity"||(i.allowRangeRequests=!0),i}function Gu(d){const t=d.get("Content-Disposition");if(t){let e=Xp(t);if(e.includes("%"))try{e=decodeURIComponent(e)}catch{}if(pd(e))return e}return null}function zh(d,t){return d===404||d===0&&t.startsWith("file:")?new Ka('Missing PDF "'+t+'".'):new lh(`Unexpected server response (${d}) while retrieving PDF "${t}".`,d)}function zu(d){return d===200||d===206}function Uu(d,t,e){return{method:"GET",headers:d,signal:e.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}function ju(d){return d instanceof Uint8Array?d.buffer:d instanceof ArrayBuffer?d:(V(`getArrayBuffer - unexpected data format: ${d}`),new Uint8Array(d).buffer)}class jd{constructor(t){O(this,"_responseOrigin",null);this.source=t,this.isHttp=/^https?:/i.test(t.url),this.headers=Bu(this.isHttp,t.httpHeaders),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return wt(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new qp(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new Yp(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class qp{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange;const s=new Headers(t.headers),i=e.url;fetch(i,Uu(s,this._withCredentials,this._abortController)).then(r=>{if(t._responseOrigin=Gh(r.url),!zu(r.status))throw zh(r.status,i);this._reader=r.body.getReader(),this._headersCapability.resolve();const a=r.headers,{allowRangeRequests:o,suggestedLength:l}=$u({responseHeaders:a,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=o,this._contentLength=l||this._contentLength,this._filename=Gu(a),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new ji("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:ju(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class Yp{constructor(t,e,s){this._stream=t,this._reader=null,this._loaded=0;const i=t.source;this._withCredentials=i.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!i.disableStream,this._abortController=new AbortController;const r=new Headers(t.headers);r.append("Range",`bytes=${e}-${s-1}`);const a=i.url;fetch(a,Uu(r,this._withCredentials,this._abortController)).then(o=>{const l=Gh(o.url);if(l!==t._responseOrigin)throw new Error(`Expected range response-origin "${l}" to match "${t._responseOrigin}".`);if(!zu(o.status))throw zh(o.status,a);this._readCapability.resolve(),this._reader=o.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded}),{value:ju(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}const Yh=200,Kh=206;function Kp(d){const t=d.response;return typeof t!="string"?t:Hh(t).buffer}class Qp{constructor({url:t,httpHeaders:e,withCredentials:s}){O(this,"_responseOrigin",null);this.url=t,this.isHttp=/^https?:/i.test(t),this.headers=Bu(this.isHttp,e),this.withCredentials=s||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}request(t){const e=new XMLHttpRequest,s=this.currXhrId++,i=this.pendingRequests[s]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const[r,a]of this.headers)e.setRequestHeader(r,a);return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),i.expectedStatus=Kh):i.expectedStatus=Yh,e.responseType="arraybuffer",wt(t.onError,"Expected `onError` callback to be provided."),e.onerror=()=>{t.onError(e.status)},e.onreadystatechange=this.onStateChange.bind(this,s),e.onprogress=this.onProgress.bind(this,s),i.onHeadersReceived=t.onHeadersReceived,i.onDone=t.onDone,i.onError=t.onError,i.onProgress=t.onProgress,e.send(null),s}onProgress(t,e){const s=this.pendingRequests[t];s&&s.onProgress?.(e)}onStateChange(t,e){const s=this.pendingRequests[t];if(!s)return;const i=s.xhr;if(i.readyState>=2&&s.onHeadersReceived&&(s.onHeadersReceived(),delete s.onHeadersReceived),i.readyState!==4||!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],i.status===0&&this.isHttp){s.onError(i.status);return}const r=i.status||Yh;if(!(r===Yh&&s.expectedStatus===Kh)&&r!==s.expectedStatus){s.onError(i.status);return}const o=Kp(i);if(r===Kh){const l=i.getResponseHeader("Content-Range"),h=/bytes (\d+)-(\d+)\/(\d+)/.exec(l);h?s.onDone({begin:parseInt(h[1],10),chunk:o}):(V('Missing or invalid "Content-Range" header.'),s.onError(0))}else o?s.onDone({begin:0,chunk:o}):s.onError(i.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class Jp{constructor(t){this._source=t,this._manager=new Qp(t),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return wt(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new Zp(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const s=new tg(this._manager,t,e);return s.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class Zp{constructor(t,e){this._manager=t,this._url=e.url,this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=Gh(e.responseURL);const s=e.getAllResponseHeaders(),i=new Headers(s?s.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map(o=>{const[l,...h]=o.split(": ");return[l,h.join(": ")]}):[]),{allowRangeRequests:r,suggestedLength:a}=$u({responseHeaders:i,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});r&&(this._isRangeSupported=!0),this._contentLength=a||this._contentLength,this._filename=Gu(i),this._isRangeSupported&&this._manager.abortRequest(t),this._headersCapability.resolve()}_onDone(t){if(t&&(this._requests.length>0?this._requests.shift().resolve({value:t.chunk,done:!1}):this._cachedChunks.push(t.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=zh(t,this._url),this._headersCapability.reject(this._storedError);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}async read(){if(await this._headersCapability.promise,this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0,this._headersCapability.reject(t);for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class tg{constructor(t,e,s){this._manager=t,this._url=t.url,this._requestId=t.request({begin:e,end:s,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_onHeadersReceived(){const t=Gh(this._manager.getRequestXhr(this._requestId)?.responseURL);t!==this._manager._responseOrigin&&(this._storedError=new Error(`Expected range response-origin "${t}" to match "${this._manager._responseOrigin}".`),this._onError(0))}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._queuedChunk=e,this._done=!0;for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){this._storedError??(this._storedError=zh(t,this._url));for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(this._queuedChunk!==null){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}const eg=/^[a-z][a-z0-9\-+.]+:/i;function sg(d){if(eg.test(d))return new URL(d);const t=process.getBuiltinModule("url");return new URL(t.pathToFileURL(d))}class ig{constructor(t){this.source=t,this.url=sg(t.url),wt(this.url.protocol==="file:","PDFNodeStream only supports file:// URLs."),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return wt(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=new ng(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new rg(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class ng{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,!this._rangeChunkSize&&!this._disableRange&&(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers();const s=process.getBuiltinModule("fs");s.promises.lstat(this._url).then(i=>{this._contentLength=i.size,this._setReadableStream(s.createReadStream(this._url)),this._headersCapability.resolve()},i=>{i.code==="ENOENT"&&(i=new Ka(`Missing PDF "${this._url}".`)),this._storedError=i,this._headersCapability.reject(i)})}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new ji("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class rg{constructor(t,e,s){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();const i=t.source;this._isStreamingSupported=!i.disableStream;const r=process.getBuiltinModule("fs");this._setReadableStream(r.createReadStream(this._url,{start:e,end:s-1}))}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,this.onProgress?.({loaded:this._loaded}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){if(!this._readableStream){this._error(t);return}this._readableStream.destroy(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),this._storedError&&this._readableStream.destroy(this._storedError)}}const ag=1e5,le=30,og=.8;var xi,ue,Fo,No,En,js,Oo,Ho,Cn,zr,Ur,Ti,jr,Bo,Vr,xn,$o,Go,Tn,Pn,zo,Pi,Wr,ci,Vu,Wu,Pc,ke,Ul,Rc,Xu,qu;const Ct=class Ct{constructor({textContentSource:t,container:e,viewport:s}){g(this,ci);g(this,xi,Promise.withResolvers());g(this,ue,null);g(this,Fo,!1);g(this,No,!!globalThis.FontInspector?.enabled);g(this,En,null);g(this,js,null);g(this,Oo,0);g(this,Ho,0);g(this,Cn,null);g(this,zr,null);g(this,Ur,0);g(this,Ti,0);g(this,jr,Object.create(null));g(this,Bo,[]);g(this,Vr,null);g(this,xn,[]);g(this,$o,new WeakMap);g(this,Go,null);var l;if(t instanceof ReadableStream)f(this,Vr,t);else if(typeof t=="object")f(this,Vr,new ReadableStream({start(h){h.enqueue(t),h.close()}}));else throw new Error('No "textContentSource" parameter specified.');f(this,ue,f(this,zr,e)),f(this,Ti,s.scale*(globalThis.devicePixelRatio||1)),f(this,Ur,s.rotation),f(this,js,{div:null,properties:null,ctx:null});const{pageWidth:i,pageHeight:r,pageX:a,pageY:o}=s.rawDims;f(this,Go,[1,0,0,-1,-a,o+r]),f(this,Ho,i),f(this,Oo,r),m(l=Ct,ke,Xu).call(l),nr(e,s),n(this,xi).promise.finally(()=>{n(Ct,Wr).delete(this),f(this,js,null),f(this,jr,null)}).catch(()=>{})}static get fontFamilyMap(){const{isWindows:t,isFirefox:e}=ne.platform;return q(this,"fontFamilyMap",new Map([["sans-serif",`${t&&e?"Calibri, ":""}sans-serif`],["monospace",`${t&&e?"Lucida Console, ":""}monospace`]]))}render(){const t=()=>{n(this,Cn).read().then(({value:e,done:s})=>{if(s){n(this,xi).resolve();return}n(this,En)??f(this,En,e.lang),Object.assign(n(this,jr),e.styles),m(this,ci,Vu).call(this,e.items),t()},n(this,xi).reject)};return f(this,Cn,n(this,Vr).getReader()),n(Ct,Wr).add(this),t(),n(this,xi).promise}update({viewport:t,onBefore:e=null}){var r;const s=t.scale*(globalThis.devicePixelRatio||1),i=t.rotation;if(i!==n(this,Ur)&&(e?.(),f(this,Ur,i),nr(n(this,zr),{rotation:i})),s!==n(this,Ti)){e?.(),f(this,Ti,s);const a={div:null,properties:null,ctx:m(r=Ct,ke,Ul).call(r,n(this,En))};for(const o of n(this,xn))a.properties=n(this,$o).get(o),a.div=o,m(this,ci,Pc).call(this,a)}}cancel(){const t=new ji("TextLayer task cancelled.");n(this,Cn)?.cancel(t).catch(()=>{}),f(this,Cn,null),n(this,xi).reject(t)}get textDivs(){return n(this,xn)}get textContentItemsStr(){return n(this,Bo)}static cleanup(){if(!(n(this,Wr).size>0)){n(this,Tn).clear();for(const{canvas:t}of n(this,Pn).values())t.remove();n(this,Pn).clear()}}};xi=new WeakMap,ue=new WeakMap,Fo=new WeakMap,No=new WeakMap,En=new WeakMap,js=new WeakMap,Oo=new WeakMap,Ho=new WeakMap,Cn=new WeakMap,zr=new WeakMap,Ur=new WeakMap,Ti=new WeakMap,jr=new WeakMap,Bo=new WeakMap,Vr=new WeakMap,xn=new WeakMap,$o=new WeakMap,Go=new WeakMap,Tn=new WeakMap,Pn=new WeakMap,zo=new WeakMap,Pi=new WeakMap,Wr=new WeakMap,ci=new WeakSet,Vu=function(t){var i,r;if(n(this,Fo))return;(r=n(this,js)).ctx??(r.ctx=m(i=Ct,ke,Ul).call(i,n(this,En)));const e=n(this,xn),s=n(this,Bo);for(const a of t){if(e.length>ag){V("Ignoring additional textDivs for performance reasons."),f(this,Fo,!0);return}if(a.str===void 0){if(a.type==="beginMarkedContentProps"||a.type==="beginMarkedContent"){const o=n(this,ue);f(this,ue,document.createElement("span")),n(this,ue).classList.add("markedContent"),a.id!==null&&n(this,ue).setAttribute("id",`${a.id}`),o.append(n(this,ue))}else a.type==="endMarkedContent"&&f(this,ue,n(this,ue).parentNode);continue}s.push(a.str),m(this,ci,Wu).call(this,a)}},Wu=function(t){var y;const e=document.createElement("span"),s={angle:0,canvasWidth:0,hasText:t.str!=="",hasEOL:t.hasEOL,fontSize:0};n(this,xn).push(e);const i=D.transform(n(this,Go),t.transform);let r=Math.atan2(i[1],i[0]);const a=n(this,jr)[t.fontName];a.vertical&&(r+=Math.PI/2);let o=n(this,No)&&a.fontSubstitution||a.fontFamily;o=Ct.fontFamilyMap.get(o)||o;const l=Math.hypot(i[2],i[3]),h=l*m(y=Ct,ke,qu).call(y,o,n(this,En));let c,u;r===0?(c=i[4],u=i[5]-h):(c=i[4]+h*Math.sin(r),u=i[5]-h*Math.cos(r));const p="calc(var(--scale-factor)*",b=e.style;n(this,ue)===n(this,zr)?(b.left=`${(100*c/n(this,Ho)).toFixed(2)}%`,b.top=`${(100*u/n(this,Oo)).toFixed(2)}%`):(b.left=`${p}${c.toFixed(2)}px)`,b.top=`${p}${u.toFixed(2)}px)`),b.fontSize=`${p}${(n(Ct,Pi)*l).toFixed(2)}px)`,b.fontFamily=o,s.fontSize=l,e.setAttribute("role","presentation"),e.textContent=t.str,e.dir=t.dir,n(this,No)&&(e.dataset.fontName=a.fontSubstitutionLoadedName||t.fontName),r!==0&&(s.angle=r*(180/Math.PI));let A=!1;if(t.str.length>1)A=!0;else if(t.str!==" "&&t.transform[0]!==t.transform[3]){const w=Math.abs(t.transform[0]),v=Math.abs(t.transform[3]);w!==v&&Math.max(w,v)/Math.min(w,v)>1.5&&(A=!0)}if(A&&(s.canvasWidth=a.vertical?t.height:t.width),n(this,$o).set(e,s),n(this,js).div=e,n(this,js).properties=s,m(this,ci,Pc).call(this,n(this,js)),s.hasText&&n(this,ue).append(e),s.hasEOL){const w=document.createElement("br");w.setAttribute("role","presentation"),n(this,ue).append(w)}},Pc=function(t){var o;const{div:e,properties:s,ctx:i}=t,{style:r}=e;let a="";if(n(Ct,Pi)>1&&(a=`scale(${1/n(Ct,Pi)})`),s.canvasWidth!==0&&s.hasText){const{fontFamily:l}=r,{canvasWidth:h,fontSize:c}=s;m(o=Ct,ke,Rc).call(o,i,c*n(this,Ti),l);const{width:u}=i.measureText(e.textContent);u>0&&(a=`scaleX(${h*n(this,Ti)/u}) ${a}`)}s.angle!==0&&(a=`rotate(${s.angle}deg) ${a}`),a.length>0&&(r.transform=a)},ke=new WeakSet,Ul=function(t=null){let e=n(this,Pn).get(t||(t=""));if(!e){const s=document.createElement("canvas");s.className="hiddenCanvasElement",s.lang=t,document.body.append(s),e=s.getContext("2d",{alpha:!1,willReadFrequently:!0}),n(this,Pn).set(t,e),n(this,zo).set(e,{size:0,family:""})}return e},Rc=function(t,e,s){const i=n(this,zo).get(t);e===i.size&&s===i.family||(t.font=`${e}px ${s}`,i.size=e,i.family=s)},Xu=function(){if(n(this,Pi)!==null)return;const t=document.createElement("div");t.style.opacity=0,t.style.lineHeight=1,t.style.fontSize="1px",t.style.position="absolute",t.textContent="X",document.body.append(t),f(this,Pi,t.getBoundingClientRect().height),t.remove()},qu=function(t,e){const s=n(this,Tn).get(t);if(s)return s;const i=m(this,ke,Ul).call(this,e);i.canvas.width=i.canvas.height=le,m(this,ke,Rc).call(this,i,le,t);const r=i.measureText("");let a=r.fontBoundingBoxAscent,o=Math.abs(r.fontBoundingBoxDescent);if(a){const c=a/(a+o);return n(this,Tn).set(t,c),i.canvas.width=i.canvas.height=0,c}i.strokeStyle="red",i.clearRect(0,0,le,le),i.strokeText("g",0,0);let l=i.getImageData(0,0,le,le).data;o=0;for(let c=l.length-1-3;c>=0;c-=4)if(l[c]>0){o=Math.ceil(c/4/le);break}i.clearRect(0,0,le,le),i.strokeText("A",0,le),l=i.getImageData(0,0,le,le).data,a=0;for(let c=0,u=l.length;c<u;c+=4)if(l[c]>0){a=le-Math.floor(c/4/le);break}i.canvas.width=i.canvas.height=0;const h=a?a/(a+o):og;return n(this,Tn).set(t,h),h},g(Ct,ke),g(Ct,Tn,new Map),g(Ct,Pn,new Map),g(Ct,zo,new WeakMap),g(Ct,Pi,null),g(Ct,Wr,new Set);let Qa=Ct;class Ja{static textContent(t){const e=[],s={items:e,styles:Object.create(null)};function i(r){if(!r)return;let a=null;const o=r.name;if(o==="#text")a=r.value;else if(Ja.shouldBuildText(o))r?.attributes?.textContent?a=r.attributes.textContent:r.value&&(a=r.value);else return;if(a!==null&&e.push({str:a}),!!r.children)for(const l of r.children)i(l)}return i(t),s}static shouldBuildText(t){return!(t==="textarea"||t==="input"||t==="option"||t==="select")}}const lg=65536,hg=100,cg=5e3,dg=Kt?Cp:_p,ug=Kt?xp:Iu,fg=Kt?Ep:Sp,pg=Kt?Tp:Ou;function gg(d={}){typeof d=="string"||d instanceof URL?d={url:d}:(d instanceof ArrayBuffer||ArrayBuffer.isView(d))&&(d={data:d});const t=new Mc,{docId:e}=t,s=d.url?mg(d.url):null,i=d.data?bg(d.data):null,r=d.httpHeaders||null,a=d.withCredentials===!0,o=d.password??null,l=d.range instanceof Yu?d.range:null,h=Number.isInteger(d.rangeChunkSize)&&d.rangeChunkSize>0?d.rangeChunkSize:lg;let c=d.worker instanceof wr?d.worker:null;const u=d.verbosity,p=typeof d.docBaseUrl=="string"&&!$h(d.docBaseUrl)?d.docBaseUrl:null,b=typeof d.cMapUrl=="string"?d.cMapUrl:null,A=d.cMapPacked!==!1,y=d.CMapReaderFactory||ug,w=typeof d.standardFontDataUrl=="string"?d.standardFontDataUrl:null,v=d.StandardFontDataFactory||pg,_=d.stopAtErrors!==!0,E=Number.isInteger(d.maxImageSize)&&d.maxImageSize>-1?d.maxImageSize:-1,S=d.isEvalSupported!==!1,C=typeof d.isOffscreenCanvasSupported=="boolean"?d.isOffscreenCanvasSupported:!Kt,T=typeof d.isImageDecoderSupported=="boolean"?d.isImageDecoderSupported:!Kt&&(ne.platform.isFirefox||!globalThis.chrome),x=Number.isInteger(d.canvasMaxAreaInBytes)?d.canvasMaxAreaInBytes:-1,F=typeof d.disableFontFace=="boolean"?d.disableFontFace:Kt,N=d.fontExtraProperties===!0,z=d.enableXfa===!0,U=d.ownerDocument||globalThis.document,dt=d.disableRange===!0,ut=d.disableStream===!0,nt=d.disableAutoFetch===!0,Qt=d.pdfBug===!0,L=d.CanvasFactory||dg,B=d.FilterFactory||fg,ge=d.enableHWA===!0,os=l?l.length:d.length??NaN,Rs=typeof d.useSystemFonts=="boolean"?d.useSystemFonts:!Kt&&!F,Le=typeof d.useWorkerFetch=="boolean"?d.useWorkerFetch:y===Iu&&v===Ou&&b&&w&&La(b,document.baseURI)&&La(w,document.baseURI),Gt=null;tp(u);const St={canvasFactory:new L({ownerDocument:U,enableHWA:ge}),filterFactory:new B({docId:e,ownerDocument:U}),cMapReaderFactory:Le?null:new y({baseUrl:b,isCompressed:A}),standardFontDataFactory:Le?null:new v({baseUrl:w})};if(!c){const oe={verbosity:u,port:ai.workerPort};c=oe.port?wr.fromPort(oe):new wr(oe),t._worker=c}const hr={docId:e,apiVersion:"4.10.38",data:i,password:o,disableAutoFetch:nt,rangeChunkSize:h,length:os,docBaseUrl:p,enableXfa:z,evaluatorOptions:{maxImageSize:E,disableFontFace:F,ignoreErrors:_,isEvalSupported:S,isOffscreenCanvasSupported:C,isImageDecoderSupported:T,canvasMaxAreaInBytes:x,fontExtraProperties:N,useSystemFonts:Rs,cMapUrl:Le?b:null,standardFontDataUrl:Le?w:null}},Ta={disableFontFace:F,fontExtraProperties:N,ownerDocument:U,pdfBug:Qt,styleElement:Gt,loadingParams:{disableAutoFetch:nt,enableXfa:z}};return c.promise.then(function(){if(t.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const oe=c.messageHandler.sendWithPromise("GetDocRequest",hr,i?[i.buffer]:null);let ot;if(l)ot=new jp(l,{disableRange:dt,disableStream:ut});else if(!i){if(!s)throw new Error("getDocument - no `url` parameter provided.");let cr;if(Kt)if(La(s)){if(typeof fetch>"u"||typeof Response>"u"||!("body"in Response.prototype))throw new Error("getDocument - the Fetch API was disabled in Node.js, see `--no-experimental-fetch`.");cr=jd}else cr=ig;else cr=La(s)?jd:Jp;ot=new cr({url:s,length:os,httpHeaders:r,withCredentials:a,rangeChunkSize:h,disableRange:dt,disableStream:ut})}return oe.then(cr=>{if(t.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const Td=new Na(e,cr,c.port),Xf=new wg(Td,t,ot,Ta,St);t._transport=Xf,Td.send("Ready",null)})}).catch(t._capability.reject),t}function mg(d){if(d instanceof URL)return d.href;try{return new URL(d,window.location).href}catch{if(Kt&&typeof d=="string")return d}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function bg(d){if(Kt&&typeof Buffer<"u"&&d instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(d instanceof Uint8Array&&d.byteLength===d.buffer.byteLength)return d;if(typeof d=="string")return Hh(d);if(d instanceof ArrayBuffer||ArrayBuffer.isView(d)||typeof d=="object"&&!isNaN(d?.length))return new Uint8Array(d);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}function Vd(d){return typeof d=="object"&&Number.isInteger(d?.num)&&d.num>=0&&Number.isInteger(d?.gen)&&d.gen>=0}var Eh;const Ch=class Ch{constructor(){this._capability=Promise.withResolvers(),this._transport=null,this._worker=null,this.docId=`d${Jt(Ch,Eh)._++}`,this.destroyed=!1,this.onPassword=null,this.onProgress=null}get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0),await this._transport?.destroy()}catch(t){throw this._worker?.port&&delete this._worker._pendingDestroy,t}this._transport=null,this._worker?.destroy(),this._worker=null}};Eh=new WeakMap,g(Ch,Eh,0);let Mc=Ch;class Yu{constructor(t,e,s=!1,i=null){this.length=t,this.initialData=e,this.progressiveDone=s,this.contentDispositionFilename=i,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=Promise.withResolvers()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const s of this._rangeListeners)s(t,e)}onDataProgress(t,e){this._readyCapability.promise.then(()=>{for(const s of this._progressListeners)s(t,e)})}onDataProgressiveRead(t){this._readyCapability.promise.then(()=>{for(const e of this._progressiveReadListeners)e(t)})}onDataProgressiveDone(){this._readyCapability.promise.then(()=>{for(const t of this._progressiveDoneListeners)t()})}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){it("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class Ag{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return q(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}var Ri,Vs,Ge,gr,jl;class yg{constructor(t,e,s,i=!1){g(this,Ge);g(this,Ri,null);g(this,Vs,!1);this._pageIndex=t,this._pageInfo=e,this._transport=s,this._stats=i?new kd:null,this._pdfBug=i,this.commonObjs=s.commonObjs,this.objs=new Ku,this._maybeCleanupAfterRender=!1,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:s=0,offsetY:i=0,dontFlip:r=!1}={}){return new Cl({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:r})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return q(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:s="display",annotationMode:i=ui.ENABLE,transform:r=null,background:a=null,optionalContentConfigPromise:o=null,annotationCanvasMap:l=null,pageColors:h=null,printAnnotationStorage:c=null,isEditing:u=!1}){this._stats?.time("Overall");const p=this._transport.getRenderingIntent(s,i,c,u),{renderingIntent:b,cacheKey:A}=p;f(this,Vs,!1),m(this,Ge,jl).call(this),o||(o=this._transport.getOptionalContentConfig(b));let y=this._intentStates.get(A);y||(y=Object.create(null),this._intentStates.set(A,y)),y.streamReaderCancelTimeout&&(clearTimeout(y.streamReaderCancelTimeout),y.streamReaderCancelTimeout=null);const w=!!(b&Pe.PRINT);y.displayReadyCapability||(y.displayReadyCapability=Promise.withResolvers(),y.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(p));const v=S=>{y.renderTasks.delete(_),(this._maybeCleanupAfterRender||w)&&f(this,Vs,!0),m(this,Ge,gr).call(this,!w),S?(_.capability.reject(S),this._abortOperatorList({intentState:y,reason:S instanceof Error?S:new Error(S)})):_.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats))},_=new Lc({callback:v,params:{canvasContext:t,viewport:e,transform:r,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:l,operatorList:y.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!w,pdfBug:this._pdfBug,pageColors:h});(y.renderTasks||(y.renderTasks=new Set)).add(_);const E=_.task;return Promise.all([y.displayReadyCapability.promise,o]).then(([S,C])=>{if(this.destroyed){v();return}if(this._stats?.time("Rendering"),!(C.renderingIntent&b))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");_.initializeGraphics({transparency:S,optionalContentConfig:C}),_.operatorListChanged()}).catch(v),E}getOperatorList({intent:t="display",annotationMode:e=ui.ENABLE,printAnnotationStorage:s=null,isEditing:i=!1}={}){function r(){o.operatorList.lastChunk&&(o.opListReadCapability.resolve(o.operatorList),o.renderTasks.delete(l))}const a=this._transport.getRenderingIntent(t,e,s,i,!0);let o=this._intentStates.get(a.cacheKey);o||(o=Object.create(null),this._intentStates.set(a.cacheKey,o));let l;return o.opListReadCapability||(l=Object.create(null),l.operatorListChanged=r,o.opListReadCapability=Promise.withResolvers(),(o.renderTasks||(o.renderTasks=new Set)).add(l),o.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(a)),o.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:t===!0,disableNormalization:e===!0},{highWaterMark:100,size(i){return i.items.length}})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then(s=>Ja.textContent(s));const e=this.streamTextContent(t);return new Promise(function(s,i){function r(){a.read().then(function({value:l,done:h}){if(h){s(o);return}o.lang??(o.lang=l.lang),Object.assign(o.styles,l.styles),o.items.push(...l.items),r()},i)}const a=e.getReader(),o={items:[],styles:Object.create(null),lang:null};r()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const s of e.renderTasks)t.push(s.completed),s.cancel();return this.objs.clear(),f(this,Vs,!1),m(this,Ge,jl).call(this),Promise.all(t)}cleanup(t=!1){f(this,Vs,!0);const e=m(this,Ge,gr).call(this,!1);return t&&e&&this._stats&&(this._stats=new kd),e}_startRenderPage(t,e){const s=this._intentStates.get(e);s&&(this._stats?.timeEnd("Page Request"),s.displayReadyCapability?.resolve(t))}_renderPageChunk(t,e){for(let s=0,i=t.length;s<i;s++)e.operatorList.fnArray.push(t.fnArray[s]),e.operatorList.argsArray.push(t.argsArray[s]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const s of e.renderTasks)s.operatorListChanged();t.lastChunk&&m(this,Ge,gr).call(this,!0)}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:s,modifiedIds:i}){const{map:r,transfer:a}=s,l=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:r,modifiedIds:i},a).getReader(),h=this._intentStates.get(e);h.streamReader=l;const c=()=>{l.read().then(({value:u,done:p})=>{if(p){h.streamReader=null;return}this._transport.destroyed||(this._renderPageChunk(u,h),c())},u=>{if(h.streamReader=null,!this._transport.destroyed){if(h.operatorList){h.operatorList.lastChunk=!0;for(const p of h.renderTasks)p.operatorListChanged();m(this,Ge,gr).call(this,!0)}if(h.displayReadyCapability)h.displayReadyCapability.reject(u);else if(h.opListReadCapability)h.opListReadCapability.reject(u);else throw u}})};c()}_abortOperatorList({intentState:t,reason:e,force:s=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!s){if(t.renderTasks.size>0)return;if(e instanceof fd){let i=hg;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay),t.streamReaderCancelTimeout=setTimeout(()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})},i);return}}if(t.streamReader.cancel(new ji(e.message)).catch(()=>{}),t.streamReader=null,!this._transport.destroyed){for(const[i,r]of this._intentStates)if(r===t){this._intentStates.delete(i);break}this.cleanup()}}}get stats(){return this._stats}}Ri=new WeakMap,Vs=new WeakMap,Ge=new WeakSet,gr=function(t=!1){if(m(this,Ge,jl).call(this),!n(this,Vs)||this.destroyed)return!1;if(t)return f(this,Ri,setTimeout(()=>{f(this,Ri,null),m(this,Ge,gr).call(this,!1)},cg)),!1;for(const{renderTasks:e,operatorList:s}of this._intentStates.values())if(e.size>0||!s.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),f(this,Vs,!1),!0},jl=function(){n(this,Ri)&&(clearTimeout(n(this,Ri)),f(this,Ri,null))};var Ws,xh;class vg{constructor(){g(this,Ws,new Map);g(this,xh,Promise.resolve())}postMessage(t,e){const s={data:structuredClone(t,e?{transfer:e}:null)};n(this,xh).then(()=>{for(const[i]of n(this,Ws))i.call(this,s)})}addEventListener(t,e,s=null){let i=null;if(s?.signal instanceof AbortSignal){const{signal:r}=s;if(r.aborted){V("LoopbackPort - cannot use an `aborted` signal.");return}const a=()=>this.removeEventListener(t,e);i=()=>r.removeEventListener("abort",a),r.addEventListener("abort",a)}n(this,Ws).set(e,i)}removeEventListener(t,e){n(this,Ws).get(e)?.(),n(this,Ws).delete(e)}terminate(){for(const[,t]of n(this,Ws))t?.();n(this,Ws).clear()}}Ws=new WeakMap,xh=new WeakMap;var Th,Rn,Mn,Xr,Vl,qr,Wl;const lt=class lt{constructor({name:t=null,port:e=null,verbosity:s=ep()}={}){g(this,Xr);if(this.name=t,this.destroyed=!1,this.verbosity=s,this._readyCapability=Promise.withResolvers(),this._port=null,this._webWorker=null,this._messageHandler=null,e){if(n(lt,Mn)?.has(e))throw new Error("Cannot use more than one PDFWorker per port.");(n(lt,Mn)||f(lt,Mn,new WeakMap)).set(e,this),this._initializeFromPort(e);return}this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t,this._messageHandler=new Na("main","worker",t),this._messageHandler.on("ready",function(){}),m(this,Xr,Vl).call(this)}_initialize(){if(n(lt,Rn)||n(lt,qr,Wl)){this._setupFakeWorker();return}let{workerSrc:t}=lt;try{lt._isSameOrigin(window.location.href,t)||(t=lt._createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),s=new Na("main","worker",e),i=()=>{r.abort(),s.destroy(),e.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},r=new AbortController;e.addEventListener("error",()=>{this._webWorker||i()},{signal:r.signal}),s.on("test",o=>{if(r.abort(),this.destroyed||!o){i();return}this._messageHandler=s,this._port=e,this._webWorker=e,m(this,Xr,Vl).call(this)}),s.on("ready",o=>{if(r.abort(),this.destroyed){i();return}try{a()}catch{this._setupFakeWorker()}});const a=()=>{const o=new Uint8Array;s.send("test",o,[o.buffer])};a();return}catch{Oh("The worker has been disabled.")}this._setupFakeWorker()}_setupFakeWorker(){n(lt,Rn)||(V("Setting up fake worker."),f(lt,Rn,!0)),lt._setupFakeWorkerGlobal.then(t=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const e=new vg;this._port=e;const s=`fake${Jt(lt,Th)._++}`,i=new Na(s+"_worker",s,e);t.setup(i,e),this._messageHandler=new Na(s,s+"_worker",e),m(this,Xr,Vl).call(this)}).catch(t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))})}destroy(){this.destroyed=!0,this._webWorker?.terminate(),this._webWorker=null,n(lt,Mn)?.delete(this._port),this._port=null,this._messageHandler?.destroy(),this._messageHandler=null}static fromPort(t){if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");const e=n(this,Mn)?.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new lt(t)}static get workerSrc(){if(ai.workerSrc)return ai.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _setupFakeWorkerGlobal(){return q(this,"_setupFakeWorkerGlobal",(async()=>n(this,qr,Wl)?n(this,qr,Wl):(await import(this.workerSrc)).WorkerMessageHandler)())}};Th=new WeakMap,Rn=new WeakMap,Mn=new WeakMap,Xr=new WeakSet,Vl=function(){this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})},qr=new WeakSet,Wl=function(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}},g(lt,qr),g(lt,Th,0),g(lt,Rn,!1),g(lt,Mn),Kt&&(f(lt,Rn,!0),ai.workerSrc||(ai.workerSrc="./pdf.worker.mjs")),lt._isSameOrigin=(t,e)=>{let s;try{if(s=new URL(t),!s.origin||s.origin==="null")return!1}catch{return!1}const i=new URL(e,s);return s.origin===i.origin},lt._createCDNWrapper=t=>{const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))};let wr=lt;var Xs,vs,Yr,Kr,qs,kn,Ga;class wg{constructor(t,e,s,i,r){g(this,kn);g(this,Xs,new Map);g(this,vs,new Map);g(this,Yr,new Map);g(this,Kr,new Map);g(this,qs,null);this.messageHandler=t,this.loadingTask=e,this.commonObjs=new Ku,this.fontLoader=new vp({ownerDocument:i.ownerDocument,styleElement:i.styleElement}),this.loadingParams=i.loadingParams,this._params=i,this.canvasFactory=r.canvasFactory,this.filterFactory=r.filterFactory,this.cMapReaderFactory=r.cMapReaderFactory,this.standardFontDataFactory=r.standardFontDataFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=s,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.setupMessageHandler()}get annotationStorage(){return q(this,"annotationStorage",new bd)}getRenderingIntent(t,e=ui.ENABLE,s=null,i=!1,r=!1){let a=Pe.DISPLAY,o=yc;switch(t){case"any":a=Pe.ANY;break;case"display":break;case"print":a=Pe.PRINT;break;default:V(`getRenderingIntent - invalid intent: ${t}`)}const l=a&Pe.PRINT&&s instanceof Tu?s:this.annotationStorage;switch(e){case ui.DISABLE:a+=Pe.ANNOTATIONS_DISABLE;break;case ui.ENABLE:break;case ui.ENABLE_FORMS:a+=Pe.ANNOTATIONS_FORMS;break;case ui.ENABLE_STORAGE:a+=Pe.ANNOTATIONS_STORAGE,o=l.serializable;break;default:V(`getRenderingIntent - invalid annotationMode: ${e}`)}i&&(a+=Pe.IS_EDITING),r&&(a+=Pe.OPLIST);const{ids:h,hash:c}=l.modifiedIds,u=[a,o.hash,c];return{renderingIntent:a,cacheKey:u.join("_"),annotationStorageSerializable:o,modifiedIds:h}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),n(this,qs)?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const s of n(this,vs).values())t.push(s._destroy());n(this,vs).clear(),n(this,Yr).clear(),n(this,Kr).clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then(()=>{this.commonObjs.clear(),this.fontLoader.clear(),n(this,Xs).clear(),this.filterFactory.destroy(),Qa.cleanup(),this._networkStream?.cancelAllRequests(new ji("Worker was terminated.")),this.messageHandler?.destroy(),this.messageHandler=null,this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",(s,i)=>{wt(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=r=>{this._lastProgress={loaded:r.loaded,total:r.total}},i.onPull=()=>{this._fullReader.read().then(function({value:r,done:a}){if(a){i.close();return}wt(r instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(r),1,[r])}).catch(r=>{i.error(r)})},i.onCancel=r=>{this._fullReader.cancel(r),i.ready.catch(a=>{if(!this.destroyed)throw a})}}),t.on("ReaderHeadersReady",async s=>{await this._fullReader.headersReady;const{isStreamingSupported:i,isRangeSupported:r,contentLength:a}=this._fullReader;return(!i||!r)&&(this._lastProgress&&e.onProgress?.(this._lastProgress),this._fullReader.onProgress=o=>{e.onProgress?.({loaded:o.loaded,total:o.total})}),{isStreamingSupported:i,isRangeSupported:r,contentLength:a}}),t.on("GetRangeReader",(s,i)=>{wt(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const r=this._networkStream.getRangeReader(s.begin,s.end);if(!r){i.close();return}i.onPull=()=>{r.read().then(function({value:a,done:o}){if(o){i.close();return}wt(a instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(a),1,[a])}).catch(a=>{i.error(a)})},i.onCancel=a=>{r.cancel(a),i.ready.catch(o=>{if(!this.destroyed)throw o})}}),t.on("GetDoc",({pdfInfo:s})=>{this._numPages=s.numPages,this._htmlForXfa=s.htmlForXfa,delete s.htmlForXfa,e._capability.resolve(new Ag(s,this))}),t.on("DocException",s=>{e._capability.reject(he(s))}),t.on("PasswordRequest",s=>{f(this,qs,Promise.withResolvers());try{if(!e.onPassword)throw he(s);const i=r=>{r instanceof Error?n(this,qs).reject(r):n(this,qs).resolve({password:r})};e.onPassword(i,s.code)}catch(i){n(this,qs).reject(i)}return n(this,qs).promise}),t.on("DataLoaded",s=>{e.onProgress?.({loaded:s.length,total:s.length}),this.downloadInfoCapability.resolve(s)}),t.on("StartRenderPage",s=>{if(this.destroyed)return;n(this,vs).get(s.pageIndex)._startRenderPage(s.transparency,s.cacheKey)}),t.on("commonobj",([s,i,r])=>{if(this.destroyed||this.commonObjs.has(s))return null;switch(i){case"Font":const{disableFontFace:a,fontExtraProperties:o,pdfBug:l}=this._params;if("error"in r){const p=r.error;V(`Error during font loading: ${p}`),this.commonObjs.resolve(s,p);break}const h=l&&globalThis.FontInspector?.enabled?(p,b)=>globalThis.FontInspector.fontAdded(p,b):null,c=new wp(r,{disableFontFace:a,fontExtraProperties:o,inspectFont:h});this.fontLoader.bind(c).catch(()=>t.sendWithPromise("FontFallback",{id:s})).finally(()=>{!o&&c.data&&(c.data=null),this.commonObjs.resolve(s,c)});break;case"CopyLocalImage":const{imageRef:u}=r;wt(u,"The imageRef must be defined.");for(const p of n(this,vs).values())for(const[,b]of p.objs)if(b?.ref===u)return b.dataLen?(this.commonObjs.resolve(s,structuredClone(b)),b.dataLen):null;break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(s,r);break;default:throw new Error(`Got unknown common object type ${i}`)}return null}),t.on("obj",([s,i,r,a])=>{if(this.destroyed)return;const o=n(this,vs).get(i);if(!o.objs.has(s)){if(o._intentStates.size===0){a?.bitmap?.close();return}switch(r){case"Image":o.objs.resolve(s,a),a?.dataLen>Kf&&(o._maybeCleanupAfterRender=!0);break;case"Pattern":o.objs.resolve(s,a);break;default:throw new Error(`Got unknown object type ${r}`)}}}),t.on("DocProgress",s=>{this.destroyed||e.onProgress?.({loaded:s.loaded,total:s.total})}),t.on("FetchBuiltInCMap",async s=>{if(this.destroyed)throw new Error("Worker was destroyed.");if(!this.cMapReaderFactory)throw new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter.");return this.cMapReaderFactory.fetch(s)}),t.on("FetchStandardFontData",async s=>{if(this.destroyed)throw new Error("Worker was destroyed.");if(!this.standardFontDataFactory)throw new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter.");return this.standardFontDataFactory.fetch(s)})}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&V("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally(()=>{this.annotationStorage.resetModified()})}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,s=n(this,Yr).get(e);if(s)return s;const i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then(r=>{if(this.destroyed)throw new Error("Transport destroyed");r.refStr&&n(this,Kr).set(r.refStr,t);const a=new yg(e,r,this,this._params.pdfBug);return n(this,vs).set(e,a),a});return n(this,Yr).set(e,i),i}getPageIndex(t){return Vd(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return m(this,kn,Ga).call(this,"GetFieldObjects")}hasJSActions(){return m(this,kn,Ga).call(this,"HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return typeof t!="string"?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return m(this,kn,Ga).call(this,"GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return m(this,kn,Ga).call(this,"GetOptionalContentConfig").then(e=>new Up(e,t))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=n(this,Xs).get(t);if(e)return e;const s=this.messageHandler.sendWithPromise(t,null).then(i=>({info:i[0],metadata:i[1]?new Gp(i[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null}));return n(this,Xs).set(t,s),s}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const e of n(this,vs).values())if(!e.cleanup())throw new Error(`startCleanup: Page ${e.pageNumber} is currently rendering.`);this.commonObjs.clear(),t||this.fontLoader.clear(),n(this,Xs).clear(),this.filterFactory.destroy(!0),Qa.cleanup()}}cachedPageNumber(t){if(!Vd(t))return null;const e=t.gen===0?`${t.num}R`:`${t.num}R${t.gen}`;return n(this,Kr).get(e)??null}}Xs=new WeakMap,vs=new WeakMap,Yr=new WeakMap,Kr=new WeakMap,qs=new WeakMap,kn=new WeakSet,Ga=function(t,e=null){const s=n(this,Xs).get(t);if(s)return s;const i=this.messageHandler.sendWithPromise(t,e);return n(this,Xs).set(t,i),i};const Ma=Symbol("INITIAL_DATA");var ye,Uo,kc;class Ku{constructor(){g(this,Uo);g(this,ye,Object.create(null))}get(t,e=null){if(e){const i=m(this,Uo,kc).call(this,t);return i.promise.then(()=>e(i.data)),null}const s=n(this,ye)[t];if(!s||s.data===Ma)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return s.data}has(t){const e=n(this,ye)[t];return!!e&&e.data!==Ma}delete(t){const e=n(this,ye)[t];return!e||e.data===Ma?!1:(delete n(this,ye)[t],!0)}resolve(t,e=null){const s=m(this,Uo,kc).call(this,t);s.data=e,s.resolve()}clear(){for(const t in n(this,ye)){const{data:e}=n(this,ye)[t];e?.bitmap?.close()}f(this,ye,Object.create(null))}*[Symbol.iterator](){for(const t in n(this,ye)){const{data:e}=n(this,ye)[t];e!==Ma&&(yield[t,e])}}}ye=new WeakMap,Uo=new WeakSet,kc=function(t){var e;return(e=n(this,ye))[t]||(e[t]={...Promise.withResolvers(),data:Ma})};var Mi;class _g{constructor(t){g(this,Mi,null);f(this,Mi,t),this.onContinue=null}get promise(){return n(this,Mi).capability.promise}cancel(t=0){n(this,Mi).cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=n(this,Mi).operatorList;if(!t)return!1;const{annotationCanvasMap:e}=n(this,Mi);return t.form||t.canvas&&e?.size>0}}Mi=new WeakMap;var ki,Ln;const Ji=class Ji{constructor({callback:t,params:e,objs:s,commonObjs:i,annotationCanvasMap:r,operatorList:a,pageIndex:o,canvasFactory:l,filterFactory:h,useRequestAnimationFrame:c=!1,pdfBug:u=!1,pageColors:p=null}){g(this,ki,null);this.callback=t,this.params=e,this.objs=s,this.commonObjs=i,this.annotationCanvasMap=r,this.operatorListIdx=null,this.operatorList=a,this._pageIndex=o,this.canvasFactory=l,this.filterFactory=h,this._pdfBug=u,this.pageColors=p,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=c===!0&&typeof window<"u",this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new _g(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(n(Ji,Ln).has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");n(Ji,Ln).add(this._canvas)}this._pdfBug&&globalThis.StepperManager?.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:s,viewport:i,transform:r,background:a}=this.params;this.gfx=new vr(s,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:r,viewport:i,transparency:t,background:a}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1,this.cancelled=!0,this.gfx?.endDrawing(),n(this,ki)&&(window.cancelAnimationFrame(n(this,ki)),f(this,ki,null)),n(Ji,Ln).delete(this._canvas),this.callback(t||new fd(`Rendering cancelled, page ${this._pageIndex+1}`,e))}operatorListChanged(){if(!this.graphicsReady){this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound);return}this.stepper?.updateOperatorList(this.operatorList),!this.running&&this._continue()}_continue(){this.running=!0,!this.cancelled&&(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?f(this,ki,window.requestAnimationFrame(()=>{f(this,ki,null),this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),n(Ji,Ln).delete(this._canvas),this.callback())))}};ki=new WeakMap,Ln=new WeakMap,g(Ji,Ln,new WeakSet);let Lc=Ji;const Sg="4.10.38",Eg="f9bea397f";function Wd(d){return Math.floor(Math.max(0,Math.min(1,d))*255).toString(16).padStart(2,"0")}function ka(d){return Math.max(0,Math.min(255,255*d))}class Xd{static CMYK_G([t,e,s,i]){return["G",1-Math.min(1,.3*t+.59*s+.11*e+i)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return t=ka(t),[t,t,t]}static G_HTML([t]){const e=Wd(t);return`#${e}${e}${e}`}static RGB_G([t,e,s]){return["G",.3*t+.59*e+.11*s]}static RGB_rgb(t){return t.map(ka)}static RGB_HTML(t){return`#${t.map(Wd).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,s,i]){return["RGB",1-Math.min(1,t+i),1-Math.min(1,s+i),1-Math.min(1,e+i)]}static CMYK_rgb([t,e,s,i]){return[ka(1-Math.min(1,t+i)),ka(1-Math.min(1,s+i)),ka(1-Math.min(1,e+i))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,s]){const i=1-t,r=1-e,a=1-s,o=Math.min(i,r,a);return["CMYK",i,r,a,o]}}class Cg{create(t,e,s=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const i=this._createSVG("svg:svg");return i.setAttribute("version","1.1"),s||(i.setAttribute("width",`${t}px`),i.setAttribute("height",`${e}px`)),i.setAttribute("preserveAspectRatio","none"),i.setAttribute("viewBox",`0 0 ${t} ${e}`),i}createElement(t){if(typeof t!="string")throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){it("Abstract method `_createSVG` called.")}}class yd extends Cg{_createSVG(t){return document.createElementNS(Ms,t)}}class Qu{static setupStorage(t,e,s,i,r){const a=i.getValue(e,{value:null});switch(s.name){case"textarea":if(a.value!==null&&(t.textContent=a.value),r==="print")break;t.addEventListener("input",o=>{i.setValue(e,{value:o.target.value})});break;case"input":if(s.attributes.type==="radio"||s.attributes.type==="checkbox"){if(a.value===s.attributes.xfaOn?t.setAttribute("checked",!0):a.value===s.attributes.xfaOff&&t.removeAttribute("checked"),r==="print")break;t.addEventListener("change",o=>{i.setValue(e,{value:o.target.checked?o.target.getAttribute("xfaOn"):o.target.getAttribute("xfaOff")})})}else{if(a.value!==null&&t.setAttribute("value",a.value),r==="print")break;t.addEventListener("input",o=>{i.setValue(e,{value:o.target.value})})}break;case"select":if(a.value!==null){t.setAttribute("value",a.value);for(const o of s.children)o.attributes.value===a.value?o.attributes.selected=!0:o.attributes.hasOwnProperty("selected")&&delete o.attributes.selected}t.addEventListener("input",o=>{const l=o.target.options,h=l.selectedIndex===-1?"":l[l.selectedIndex].value;i.setValue(e,{value:h})});break}}static setAttributes({html:t,element:e,storage:s=null,intent:i,linkService:r}){const{attributes:a}=e,o=t instanceof HTMLAnchorElement;a.type==="radio"&&(a.name=`${a.name}-${i}`);for(const[l,h]of Object.entries(a))if(h!=null)switch(l){case"class":h.length&&t.setAttribute(l,h.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",h);break;case"style":Object.assign(t.style,h);break;case"textContent":t.textContent=h;break;default:(!o||l!=="href"&&l!=="newWindow")&&t.setAttribute(l,h)}o&&r.addLinkAttributes(t,a.href,a.newWindow),s&&a.dataId&&this.setupStorage(t,a.dataId,e,s)}static render(t){const e=t.annotationStorage,s=t.linkService,i=t.xfaHtml,r=t.intent||"display",a=document.createElement(i.name);i.attributes&&this.setAttributes({html:a,element:i,intent:r,linkService:s});const o=r!=="richText",l=t.div;if(l.append(a),t.viewport){const u=`matrix(${t.viewport.transform.join(",")})`;l.style.transform=u}o&&l.setAttribute("class","xfaLayer xfaFont");const h=[];if(i.children.length===0){if(i.value){const u=document.createTextNode(i.value);a.append(u),o&&Ja.shouldBuildText(i.name)&&h.push(u)}return{textDivs:h}}const c=[[i,-1,a]];for(;c.length>0;){const[u,p,b]=c.at(-1);if(p+1===u.children.length){c.pop();continue}const A=u.children[++c.at(-1)[1]];if(A===null)continue;const{name:y}=A;if(y==="#text"){const v=document.createTextNode(A.value);h.push(v),b.append(v);continue}const w=A?.attributes?.xmlns?document.createElementNS(A.attributes.xmlns,y):document.createElement(y);if(b.append(w),A.attributes&&this.setAttributes({html:w,element:A,storage:e,intent:r,linkService:s}),A.children?.length>0)c.push([A,-1,w]);else if(A.value){const v=document.createTextNode(A.value);o&&Ja.shouldBuildText(y)&&h.push(v),w.append(v)}}for(const u of l.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))u.setAttribute("readOnly",!0);return{textDivs:h}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}const Tl=1e3,xg=9,ar=new WeakSet;function Ui(d){return{width:d[2]-d[0],height:d[3]-d[1]}}class Tg{static create(t){switch(t.data.annotationType){case Et.LINK:return new Ju(t);case Et.TEXT:return new Pg(t);case Et.WIDGET:switch(t.data.fieldType){case"Tx":return new Rg(t);case"Btn":return t.data.radioButton?new ef(t):t.data.checkBox?new kg(t):new Lg(t);case"Ch":return new Ig(t);case"Sig":return new Mg(t)}return new lr(t);case Et.POPUP:return new Dc(t);case Et.FREETEXT:return new of(t);case Et.LINE:return new Fg(t);case Et.SQUARE:return new Ng(t);case Et.CIRCLE:return new Og(t);case Et.POLYLINE:return new lf(t);case Et.CARET:return new Bg(t);case Et.INK:return new vd(t);case Et.POLYGON:return new Hg(t);case Et.HIGHLIGHT:return new hf(t);case Et.UNDERLINE:return new $g(t);case Et.SQUIGGLY:return new Gg(t);case Et.STRIKEOUT:return new zg(t);case Et.STAMP:return new cf(t);case Et.FILEATTACHMENT:return new Ug(t);default:return new yt(t)}}}var In,Qr,Jr,jo,Ic;const Ed=class Ed{constructor(t,{isRenderable:e=!1,ignoreBorder:s=!1,createQuadrilaterals:i=!1}={}){g(this,jo);g(this,In,null);g(this,Qr,!1);g(this,Jr,null);this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(s)),i&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:s}){return!!(t?.str||e?.str||s?.str)}get _isEditable(){return this.data.isEditable}get hasPopupData(){return Ed._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;n(this,In)||f(this,In,{rect:this.data.rect.slice(0)});const{rect:e}=t;e&&m(this,jo,Ic).call(this,e),n(this,Jr)?.popup.updateEdited(t)}resetEdited(){n(this,In)&&(m(this,jo,Ic).call(this,n(this,In).rect),n(this,Jr)?.popup.resetEdited(),f(this,In,null))}_createContainer(t){const{data:e,parent:{page:s,viewport:i}}=this,r=document.createElement("section");r.setAttribute("data-annotation-id",e.id),this instanceof lr||(r.tabIndex=Tl);const{style:a}=r;if(a.zIndex=this.parent.zIndex++,e.alternativeText&&(r.title=e.alternativeText),e.noRotate&&r.classList.add("norotate"),!e.rect||this instanceof Dc){const{rotation:y}=e;return!e.hasOwnCanvas&&y!==0&&this.setRotation(y,r),r}const{width:o,height:l}=Ui(e.rect);if(!t&&e.borderStyle.width>0){a.borderWidth=`${e.borderStyle.width}px`;const y=e.borderStyle.horizontalCornerRadius,w=e.borderStyle.verticalCornerRadius;if(y>0||w>0){const _=`calc(${y}px * var(--scale-factor)) / calc(${w}px * var(--scale-factor))`;a.borderRadius=_}else if(this instanceof ef){const _=`calc(${o}px * var(--scale-factor)) / calc(${l}px * var(--scale-factor))`;a.borderRadius=_}switch(e.borderStyle.style){case Pa.SOLID:a.borderStyle="solid";break;case Pa.DASHED:a.borderStyle="dashed";break;case Pa.BEVELED:V("Unimplemented border style: beveled");break;case Pa.INSET:V("Unimplemented border style: inset");break;case Pa.UNDERLINE:a.borderBottomStyle="solid";break}const v=e.borderColor||null;v?(f(this,Qr,!0),a.borderColor=D.makeHexColor(v[0]|0,v[1]|0,v[2]|0)):a.borderWidth=0}const h=D.normalizeRect([e.rect[0],s.view[3]-e.rect[1]+s.view[1],e.rect[2],s.view[3]-e.rect[3]+s.view[1]]),{pageWidth:c,pageHeight:u,pageX:p,pageY:b}=i.rawDims;a.left=`${100*(h[0]-p)/c}%`,a.top=`${100*(h[1]-b)/u}%`;const{rotation:A}=e;return e.hasOwnCanvas||A===0?(a.width=`${100*o/c}%`,a.height=`${100*l/u}%`):this.setRotation(A,r),r}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:s,pageHeight:i}=this.parent.viewport.rawDims,{width:r,height:a}=Ui(this.data.rect);let o,l;t%180===0?(o=100*r/s,l=100*a/i):(o=100*a/s,l=100*r/i),e.style.width=`${o}%`,e.style.height=`${l}%`,e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(e,s,i)=>{const r=i.detail[e],a=r[0],o=r.slice(1);i.target.style[s]=Xd[`${a}_HTML`](o),this.annotationStorage.setValue(this.data.id,{[s]:Xd[`${a}_rgb`](o)})};return q(this,"_commonActions",{display:e=>{const{display:s}=e.detail,i=s%2===1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:s===1||s===2})},print:e=>{this.annotationStorage.setValue(this.data.id,{noPrint:!e.detail.print})},hidden:e=>{const{hidden:s}=e.detail;this.container.style.visibility=s?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:s,noView:s})},focus:e=>{setTimeout(()=>e.target.focus({preventScroll:!1}),0)},userName:e=>{e.target.title=e.detail.userName},readonly:e=>{e.target.disabled=e.detail.readonly},required:e=>{this._setRequired(e.target,e.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:e=>{const s=e.detail.rotation;this.setRotation(s),this.annotationStorage.setValue(this.data.id,{rotation:s})}})}_dispatchEventFromSandbox(t,e){const s=this._commonActions;for(const i of Object.keys(e.detail))(t[i]||s[i])?.(e)}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const s=this._commonActions;for(const[i,r]of Object.entries(e)){const a=s[i];if(a){const o={detail:{[i]:r},target:t};a(o),delete e[i]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,s,i,r]=this.data.rect.map(y=>Math.fround(y));if(t.length===8){const[y,w,v,_]=t.subarray(2,6);if(i===y&&r===w&&e===v&&s===_)return}const{style:a}=this.container;let o;if(n(this,Qr)){const{borderColor:y,borderWidth:w}=a;a.borderWidth=0,o=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${y}" stroke-width="${w}">`],this.container.classList.add("hasBorder")}const l=i-e,h=r-s,{svgFactory:c}=this,u=c.createElement("svg");u.classList.add("quadrilateralsContainer"),u.setAttribute("width",0),u.setAttribute("height",0);const p=c.createElement("defs");u.append(p);const b=c.createElement("clipPath"),A=`clippath_${this.data.id}`;b.setAttribute("id",A),b.setAttribute("clipPathUnits","objectBoundingBox"),p.append(b);for(let y=2,w=t.length;y<w;y+=8){const v=t[y],_=t[y+1],E=t[y+2],S=t[y+3],C=c.createElement("rect"),T=(E-e)/l,x=(r-_)/h,F=(v-E)/l,N=(_-S)/h;C.setAttribute("x",T),C.setAttribute("y",x),C.setAttribute("width",F),C.setAttribute("height",N),b.append(C),o?.push(`<rect vector-effect="non-scaling-stroke" x="${T}" y="${x}" width="${F}" height="${N}"/>`)}n(this,Qr)&&(o.push("</g></svg>')"),a.backgroundImage=o.join("")),this.container.append(u),this.container.style.clipPath=`url(#${A})`}_createPopup(){const{data:t}=this,e=f(this,Jr,new Dc({data:{color:t.color,titleObj:t.titleObj,modificationDate:t.modificationDate,contentsObj:t.contentsObj,richText:t.richText,parentRect:t.rect,borderStyle:0,id:`popup_${t.id}`,rotation:t.rotation},parent:this.parent,elements:[this]}));this.parent.div.append(e.render())}render(){it("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const s=[];if(this._fieldObjects){const i=this._fieldObjects[t];if(i)for(const{page:r,id:a,exportValues:o}of i){if(r===-1||a===e)continue;const l=typeof o=="string"?o:null,h=document.querySelector(`[data-element-id="${a}"]`);if(h&&!ar.has(h)){V(`_getElementsByName - element not allowed: ${a}`);continue}s.push({id:a,exportValue:l,domElement:h})}return s}for(const i of document.getElementsByName(t)){const{exportValue:r}=i,a=i.getAttribute("data-element-id");a!==e&&ar.has(i)&&s.push({id:a,exportValue:r,domElement:i})}return s}show(){this.container&&(this.container.hidden=!1),this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0),this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})})}};In=new WeakMap,Qr=new WeakMap,Jr=new WeakMap,jo=new WeakSet,Ic=function(t){const{container:{style:e},data:{rect:s,rotation:i},parent:{viewport:{rawDims:{pageWidth:r,pageHeight:a,pageX:o,pageY:l}}}}=this;s?.splice(0,4,...t);const{width:h,height:c}=Ui(t);e.left=`${100*(t[0]-o)/r}%`,e.top=`${100*(a-t[3]+l)/a}%`,i===0?(e.width=`${100*h/r}%`,e.height=`${100*c/a}%`):this.setRotation(i)};let yt=Ed;var Re,qi,Zu,tf;class Ju extends yt{constructor(e,s=null){super(e,{isRenderable:!0,ignoreBorder:!!s?.ignoreBorder,createQuadrilaterals:!0});g(this,Re);this.isTooltipOnly=e.data.isTooltipOnly}render(){const{data:e,linkService:s}=this,i=document.createElement("a");i.setAttribute("data-element-id",e.id);let r=!1;return e.url?(s.addLinkAttributes(i,e.url,e.newWindow),r=!0):e.action?(this._bindNamedAction(i,e.action),r=!0):e.attachment?(m(this,Re,Zu).call(this,i,e.attachment,e.attachmentDest),r=!0):e.setOCGState?(m(this,Re,tf).call(this,i,e.setOCGState),r=!0):e.dest?(this._bindLink(i,e.dest),r=!0):(e.actions&&(e.actions.Action||e.actions["Mouse Up"]||e.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(i,e),r=!0),e.resetForm?(this._bindResetFormAction(i,e.resetForm),r=!0):this.isTooltipOnly&&!r&&(this._bindLink(i,""),r=!0)),this.container.classList.add("linkAnnotation"),r&&this.container.append(i),this.container}_bindLink(e,s){e.href=this.linkService.getDestinationHash(s),e.onclick=()=>(s&&this.linkService.goToDestination(s),!1),(s||s==="")&&m(this,Re,qi).call(this)}_bindNamedAction(e,s){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>(this.linkService.executeNamedAction(s),!1),m(this,Re,qi).call(this)}_bindJSAction(e,s){e.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const r of Object.keys(s.actions)){const a=i.get(r);a&&(e[a]=()=>(this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:s.id,name:r}}),!1))}e.onclick||(e.onclick=()=>!1),m(this,Re,qi).call(this)}_bindResetFormAction(e,s){const i=e.onclick;if(i||(e.href=this.linkService.getAnchorUrl("")),m(this,Re,qi).call(this),!this._fieldObjects){V('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),i||(e.onclick=()=>!1);return}e.onclick=()=>{i?.();const{fields:r,refs:a,include:o}=s,l=[];if(r.length!==0||a.length!==0){const u=new Set(a);for(const p of r){const b=this._fieldObjects[p]||[];for(const{id:A}of b)u.add(A)}for(const p of Object.values(this._fieldObjects))for(const b of p)u.has(b.id)===o&&l.push(b)}else for(const u of Object.values(this._fieldObjects))l.push(...u);const h=this.annotationStorage,c=[];for(const u of l){const{id:p}=u;switch(c.push(p),u.type){case"text":{const A=u.defaultValue||"";h.setValue(p,{value:A});break}case"checkbox":case"radiobutton":{const A=u.defaultValue===u.exportValues;h.setValue(p,{value:A});break}case"combobox":case"listbox":{const A=u.defaultValue||"";h.setValue(p,{value:A});break}default:continue}const b=document.querySelector(`[data-element-id="${p}"]`);if(b){if(!ar.has(b)){V(`_bindResetFormAction - element not allowed: ${p}`);continue}}else continue;b.dispatchEvent(new Event("resetform"))}return this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:c,name:"ResetForm"}}),!1}}}Re=new WeakSet,qi=function(){this.container.setAttribute("data-internal-link","")},Zu=function(e,s,i=null){e.href=this.linkService.getAnchorUrl(""),s.description&&(e.title=s.description),e.onclick=()=>(this.downloadManager?.openOrDownloadData(s.content,s.filename,i),!1),m(this,Re,qi).call(this)},tf=function(e,s){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>(this.linkService.executeSetOCGState(s),!1),m(this,Re,qi).call(this)};class Pg extends yt{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class lr extends yt{render(){return this.container}showElementAndHideCanvas(t){this.data.hasOwnCanvas&&(t.previousSibling?.nodeName==="CANVAS"&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return ne.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,s,i,r){s.includes("mouse")?t.addEventListener(s,a=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:r(a),shift:a.shiftKey,modifier:this._getKeyModifier(a)}})}):t.addEventListener(s,a=>{if(s==="blur"){if(!e.focused||!a.relatedTarget)return;e.focused=!1}else if(s==="focus"){if(e.focused)return;e.focused=!0}r&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:r(a)}})})}_setEventListeners(t,e,s,i){for(const[r,a]of s)(a==="Action"||this.data.actions?.[a])&&((a==="Focus"||a==="Blur")&&(e||(e={focused:!1})),this._setEventListener(t,e,r,a,i),a==="Focus"&&!this.data.actions?.Blur?this._setEventListener(t,e,"blur","Blur",null):a==="Blur"&&!this.data.actions?.Focus&&this._setEventListener(t,e,"focus","Focus",null))}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=e===null?"transparent":D.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:s}=this.data.defaultAppearanceData,i=this.data.defaultAppearanceData.fontSize||xg,r=t.style;let a;const o=2,l=h=>Math.round(10*h)/10;if(this.data.multiLine){const h=Math.abs(this.data.rect[3]-this.data.rect[1]-o),c=Math.round(h/(jh*i))||1,u=h/c;a=Math.min(i,l(u/jh))}else{const h=Math.abs(this.data.rect[3]-this.data.rect[1]-o);a=Math.min(i,l(h/jh))}r.fontSize=`calc(${a}px * var(--scale-factor))`,r.color=D.makeHexColor(s[0],s[1],s[2]),this.data.textAlignment!==null&&(r.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class Rg extends lr{constructor(t){const e=t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue;super(t,{isRenderable:e})}setPropertyOnSiblings(t,e,s,i){const r=this.annotationStorage;for(const a of this._getElementsByName(t.name,t.id))a.domElement&&(a.domElement[e]=s),r.setValue(a.id,{[i]:s})}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let s=null;if(this.renderForms){const i=t.getValue(e,{value:this.data.fieldValue});let r=i.value||"";const a=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;a&&r.length>a&&(r=r.slice(0,a));let o=i.formattedValue||this.data.textContent?.join(`
`)||null;o&&this.data.comb&&(o=o.replaceAll(/\s+/g,""));const l={userValue:r,formattedValue:o,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(s=document.createElement("textarea"),s.textContent=o??r,this.data.doNotScroll&&(s.style.overflowY="hidden")):(s=document.createElement("input"),s.type="text",s.setAttribute("value",o??r),this.data.doNotScroll&&(s.style.overflowX="hidden")),this.data.hasOwnCanvas&&(s.hidden=!0),ar.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,s.name=this.data.fieldName,s.tabIndex=Tl,this._setRequired(s,this.data.required),a&&(s.maxLength=a),s.addEventListener("input",c=>{t.setValue(e,{value:c.target.value}),this.setPropertyOnSiblings(s,"value",c.target.value,"value"),l.formattedValue=null}),s.addEventListener("resetform",c=>{const u=this.data.defaultFieldValue??"";s.value=l.userValue=u,l.formattedValue=null});let h=c=>{const{formattedValue:u}=l;u!=null&&(c.target.value=u),c.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){s.addEventListener("focus",u=>{if(l.focused)return;const{target:p}=u;l.userValue&&(p.value=l.userValue),l.lastCommittedValue=p.value,l.commitKey=1,this.data.actions?.Focus||(l.focused=!0)}),s.addEventListener("updatefromsandbox",u=>{this.showElementAndHideCanvas(u.target);const p={value(b){l.userValue=b.detail.value??"",t.setValue(e,{value:l.userValue.toString()}),b.target.value=l.userValue},formattedValue(b){const{formattedValue:A}=b.detail;l.formattedValue=A,A!=null&&b.target!==document.activeElement&&(b.target.value=A),t.setValue(e,{formattedValue:A})},selRange(b){b.target.setSelectionRange(...b.detail.selRange)},charLimit:b=>{const{charLimit:A}=b.detail,{target:y}=b;if(A===0){y.removeAttribute("maxLength");return}y.setAttribute("maxLength",A);let w=l.userValue;!w||w.length<=A||(w=w.slice(0,A),y.value=l.userValue=w,t.setValue(e,{value:w}),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:w,willCommit:!0,commitKey:1,selStart:y.selectionStart,selEnd:y.selectionEnd}}))}};this._dispatchEventFromSandbox(p,u)}),s.addEventListener("keydown",u=>{l.commitKey=1;let p=-1;if(u.key==="Escape"?p=0:u.key==="Enter"&&!this.data.multiLine?p=2:u.key==="Tab"&&(l.commitKey=3),p===-1)return;const{value:b}=u.target;l.lastCommittedValue!==b&&(l.lastCommittedValue=b,l.userValue=b,this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:b,willCommit:!0,commitKey:p,selStart:u.target.selectionStart,selEnd:u.target.selectionEnd}}))});const c=h;h=null,s.addEventListener("blur",u=>{if(!l.focused||!u.relatedTarget)return;this.data.actions?.Blur||(l.focused=!1);const{value:p}=u.target;l.userValue=p,l.lastCommittedValue!==p&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:p,willCommit:!0,commitKey:l.commitKey,selStart:u.target.selectionStart,selEnd:u.target.selectionEnd}}),c(u)}),this.data.actions?.Keystroke&&s.addEventListener("beforeinput",u=>{l.lastCommittedValue=null;const{data:p,target:b}=u,{value:A,selectionStart:y,selectionEnd:w}=b;let v=y,_=w;switch(u.inputType){case"deleteWordBackward":{const E=A.substring(0,y).match(/\w*[^\w]*$/);E&&(v-=E[0].length);break}case"deleteWordForward":{const E=A.substring(y).match(/^[^\w]*\w*/);E&&(_+=E[0].length);break}case"deleteContentBackward":y===w&&(v-=1);break;case"deleteContentForward":y===w&&(_+=1);break}u.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:A,change:p||"",willCommit:!1,selStart:v,selEnd:_}})}),this._setEventListeners(s,l,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],u=>u.target.value)}if(h&&s.addEventListener("blur",h),this.data.comb){const u=(this.data.rect[2]-this.data.rect[0])/a;s.classList.add("comb"),s.style.letterSpacing=`calc(${u}px * var(--scale-factor) - 1ch)`}}else s=document.createElement("div"),s.textContent=this.data.fieldValue,s.style.verticalAlign="middle",s.style.display="table-cell",this.data.hasOwnCanvas&&(s.hidden=!0);return this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class Mg extends lr{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class kg extends lr{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.exportValue===e.fieldValue}).value;typeof i=="string"&&(i=i!=="Off",t.setValue(s,{value:i})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const r=document.createElement("input");return ar.add(r),r.setAttribute("data-element-id",s),r.disabled=e.readOnly,this._setRequired(r,this.data.required),r.type="checkbox",r.name=e.fieldName,i&&r.setAttribute("checked",!0),r.setAttribute("exportValue",e.exportValue),r.tabIndex=Tl,r.addEventListener("change",a=>{const{name:o,checked:l}=a.target;for(const h of this._getElementsByName(o,s)){const c=l&&h.exportValue===e.exportValue;h.domElement&&(h.domElement.checked=c),t.setValue(h.id,{value:c})}t.setValue(s,{value:l})}),r.addEventListener("resetform",a=>{const o=e.defaultFieldValue||"Off";a.target.checked=o===e.exportValue}),this.enableScripting&&this.hasJSActions&&(r.addEventListener("updatefromsandbox",a=>{const o={value(l){l.target.checked=l.detail.value!=="Off",t.setValue(s,{value:l.target.checked})}};this._dispatchEventFromSandbox(o,a)}),this._setEventListeners(r,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],a=>a.target.checked)),this._setBackgroundColor(r),this._setDefaultPropertiesFromJS(r),this.container.append(r),this.container}}class ef extends lr{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.fieldValue===e.buttonValue}).value;if(typeof i=="string"&&(i=i!==e.buttonValue,t.setValue(s,{value:i})),i)for(const a of this._getElementsByName(e.fieldName,s))t.setValue(a.id,{value:!1});const r=document.createElement("input");if(ar.add(r),r.setAttribute("data-element-id",s),r.disabled=e.readOnly,this._setRequired(r,this.data.required),r.type="radio",r.name=e.fieldName,i&&r.setAttribute("checked",!0),r.tabIndex=Tl,r.addEventListener("change",a=>{const{name:o,checked:l}=a.target;for(const h of this._getElementsByName(o,s))t.setValue(h.id,{value:!1});t.setValue(s,{value:l})}),r.addEventListener("resetform",a=>{const o=e.defaultFieldValue;a.target.checked=o!=null&&o===e.buttonValue}),this.enableScripting&&this.hasJSActions){const a=e.buttonValue;r.addEventListener("updatefromsandbox",o=>{const l={value:h=>{const c=a===h.detail.value;for(const u of this._getElementsByName(h.target.name)){const p=c&&u.id===s;u.domElement&&(u.domElement.checked=p),t.setValue(u.id,{value:p})}}};this._dispatchEventFromSandbox(l,o)}),this._setEventListeners(r,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],o=>o.target.checked)}return this._setBackgroundColor(r),this._setDefaultPropertiesFromJS(r),this.container.append(r),this.container}}class Lg extends Ju{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",s=>{this._dispatchEventFromSandbox({},s)})),t}}class Ig extends lr{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,s=t.getValue(e,{value:this.data.fieldValue}),i=document.createElement("select");ar.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,this._setRequired(i,this.data.required),i.name=this.data.fieldName,i.tabIndex=Tl;let r=this.data.combo&&this.data.options.length>0;this.data.combo||(i.size=this.data.options.length,this.data.multiSelect&&(i.multiple=!0)),i.addEventListener("resetform",c=>{const u=this.data.defaultFieldValue;for(const p of i.options)p.selected=p.value===u});for(const c of this.data.options){const u=document.createElement("option");u.textContent=c.displayValue,u.value=c.exportValue,s.value.includes(c.exportValue)&&(u.setAttribute("selected",!0),r=!1),i.append(u)}let a=null;if(r){const c=document.createElement("option");c.value=" ",c.setAttribute("hidden",!0),c.setAttribute("selected",!0),i.prepend(c),a=()=>{c.remove(),i.removeEventListener("input",a),a=null},i.addEventListener("input",a)}const o=c=>{const u=c?"value":"textContent",{options:p,multiple:b}=i;return b?Array.prototype.filter.call(p,A=>A.selected).map(A=>A[u]):p.selectedIndex===-1?null:p[p.selectedIndex][u]};let l=o(!1);const h=c=>{const u=c.target.options;return Array.prototype.map.call(u,p=>({displayValue:p.textContent,exportValue:p.value}))};return this.enableScripting&&this.hasJSActions?(i.addEventListener("updatefromsandbox",c=>{const u={value(p){a?.();const b=p.detail.value,A=new Set(Array.isArray(b)?b:[b]);for(const y of i.options)y.selected=A.has(y.value);t.setValue(e,{value:o(!0)}),l=o(!1)},multipleSelection(p){i.multiple=!0},remove(p){const b=i.options,A=p.detail.remove;b[A].selected=!1,i.remove(A),b.length>0&&Array.prototype.findIndex.call(b,w=>w.selected)===-1&&(b[0].selected=!0),t.setValue(e,{value:o(!0),items:h(p)}),l=o(!1)},clear(p){for(;i.length!==0;)i.remove(0);t.setValue(e,{value:null,items:[]}),l=o(!1)},insert(p){const{index:b,displayValue:A,exportValue:y}=p.detail.insert,w=i.children[b],v=document.createElement("option");v.textContent=A,v.value=y,w?w.before(v):i.append(v),t.setValue(e,{value:o(!0),items:h(p)}),l=o(!1)},items(p){const{items:b}=p.detail;for(;i.length!==0;)i.remove(0);for(const A of b){const{displayValue:y,exportValue:w}=A,v=document.createElement("option");v.textContent=y,v.value=w,i.append(v)}i.options.length>0&&(i.options[0].selected=!0),t.setValue(e,{value:o(!0),items:h(p)}),l=o(!1)},indices(p){const b=new Set(p.detail.indices);for(const A of p.target.options)A.selected=b.has(A.index);t.setValue(e,{value:o(!0)}),l=o(!1)},editable(p){p.target.disabled=!p.detail.editable}};this._dispatchEventFromSandbox(u,c)}),i.addEventListener("input",c=>{const u=o(!0),p=o(!1);t.setValue(e,{value:u}),c.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:l,change:p,changeEx:u,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(i,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],c=>c.target.value)):i.addEventListener("input",function(c){t.setValue(e,{value:o(!0)})}),this.data.combo&&this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class Dc extends yt{constructor(t){const{data:e,elements:s}=t;super(t,{isRenderable:yt._hasPopupData(e)}),this.elements=s,this.popup=null}render(){this.container.classList.add("popupAnnotation");const t=this.popup=new Dg({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const s of this.elements)s.popup=t,s.container.ariaHasPopup="dialog",e.push(s.data.id),s.addHighlightArea();return this.container.setAttribute("aria-controls",e.map(s=>`${ud}${s}`).join(",")),this.container}}var Zr,Ph,Rh,ta,Dn,mt,Ys,ea,Vo,Wo,sa,Ks,Ke,Qs,Xo,Js,qo,Fn,Nn,st,Xl,Fc,sf,nf,rf,af,ql,Yl,Nc;class Dg{constructor({container:t,color:e,elements:s,titleObj:i,modificationDate:r,contentsObj:a,richText:o,parent:l,rect:h,parentRect:c,open:u}){g(this,st);g(this,Zr,m(this,st,rf).bind(this));g(this,Ph,m(this,st,Nc).bind(this));g(this,Rh,m(this,st,Yl).bind(this));g(this,ta,m(this,st,ql).bind(this));g(this,Dn,null);g(this,mt,null);g(this,Ys,null);g(this,ea,null);g(this,Vo,null);g(this,Wo,null);g(this,sa,null);g(this,Ks,!1);g(this,Ke,null);g(this,Qs,null);g(this,Xo,null);g(this,Js,null);g(this,qo,null);g(this,Fn,null);g(this,Nn,!1);f(this,mt,t),f(this,qo,i),f(this,Ys,a),f(this,Js,o),f(this,Wo,l),f(this,Dn,e),f(this,Xo,h),f(this,sa,c),f(this,Vo,s),f(this,ea,gd.toDateObject(r)),this.trigger=s.flatMap(p=>p.getElementsToTriggerPopup());for(const p of this.trigger)p.addEventListener("click",n(this,ta)),p.addEventListener("mouseenter",n(this,Rh)),p.addEventListener("mouseleave",n(this,Ph)),p.classList.add("popupTriggerArea");for(const p of s)p.container?.addEventListener("keydown",n(this,Zr));n(this,mt).hidden=!0,u&&m(this,st,ql).call(this)}render(){if(n(this,Ke))return;const t=f(this,Ke,document.createElement("div"));if(t.className="popup",n(this,Dn)){const r=t.style.outlineColor=D.makeHexColor(...n(this,Dn));CSS.supports("background-color","color-mix(in srgb, red 30%, white)")?t.style.backgroundColor=`color-mix(in srgb, ${r} 30%, white)`:t.style.backgroundColor=D.makeHexColor(...n(this,Dn).map(o=>Math.floor(.7*(255-o)+o)))}const e=document.createElement("span");e.className="header";const s=document.createElement("h1");if(e.append(s),{dir:s.dir,str:s.textContent}=n(this,qo),t.append(e),n(this,ea)){const r=document.createElement("span");r.classList.add("popupDate"),r.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string"),r.setAttribute("data-l10n-args",JSON.stringify({dateObj:n(this,ea).valueOf()})),e.append(r)}const i=n(this,st,Xl);if(i)Qu.render({xfaHtml:i,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{const r=this._formatContents(n(this,Ys));t.append(r)}n(this,mt).append(t)}_formatContents({str:t,dir:e}){const s=document.createElement("p");s.classList.add("popupContent"),s.dir=e;const i=t.split(/(?:\r\n?|\n)/);for(let r=0,a=i.length;r<a;++r){const o=i[r];s.append(document.createTextNode(o)),r<a-1&&s.append(document.createElement("br"))}return s}updateEdited({rect:t,popupContent:e}){n(this,Fn)||f(this,Fn,{contentsObj:n(this,Ys),richText:n(this,Js)}),t&&f(this,Qs,null),e&&(f(this,Js,m(this,st,nf).call(this,e)),f(this,Ys,null)),n(this,Ke)?.remove(),f(this,Ke,null)}resetEdited(){n(this,Fn)&&({contentsObj:Jt(this,Ys)._,richText:Jt(this,Js)._}=n(this,Fn),f(this,Fn,null),n(this,Ke)?.remove(),f(this,Ke,null),f(this,Qs,null))}forceHide(){f(this,Nn,this.isVisible),n(this,Nn)&&(n(this,mt).hidden=!0)}maybeShow(){n(this,Nn)&&(n(this,Ke)||m(this,st,Yl).call(this),f(this,Nn,!1),n(this,mt).hidden=!1)}get isVisible(){return n(this,mt).hidden===!1}}Zr=new WeakMap,Ph=new WeakMap,Rh=new WeakMap,ta=new WeakMap,Dn=new WeakMap,mt=new WeakMap,Ys=new WeakMap,ea=new WeakMap,Vo=new WeakMap,Wo=new WeakMap,sa=new WeakMap,Ks=new WeakMap,Ke=new WeakMap,Qs=new WeakMap,Xo=new WeakMap,Js=new WeakMap,qo=new WeakMap,Fn=new WeakMap,Nn=new WeakMap,st=new WeakSet,Xl=function(){const t=n(this,Js),e=n(this,Ys);return t?.str&&(!e?.str||e.str===t.str)&&n(this,Js).html||null},Fc=function(){return n(this,st,Xl)?.attributes?.style?.fontSize||0},sf=function(){return n(this,st,Xl)?.attributes?.style?.color||null},nf=function(t){const e=[],s={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},i={style:{color:n(this,st,sf),fontSize:n(this,st,Fc)?`calc(${n(this,st,Fc)}px * var(--scale-factor))`:""}};for(const r of t.split(`
`))e.push({name:"span",value:r,attributes:i});return s},rf=function(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||(t.key==="Enter"||t.key==="Escape"&&n(this,Ks))&&m(this,st,ql).call(this)},af=function(){if(n(this,Qs)!==null)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:s,pageX:i,pageY:r}}}=n(this,Wo);let a=!!n(this,sa),o=a?n(this,sa):n(this,Xo);for(const A of n(this,Vo))if(!o||D.intersect(A.data.rect,o)!==null){o=A.data.rect,a=!0;break}const l=D.normalizeRect([o[0],t[3]-o[1]+t[1],o[2],t[3]-o[3]+t[1]]),c=a?o[2]-o[0]+5:0,u=l[0]+c,p=l[1];f(this,Qs,[100*(u-i)/e,100*(p-r)/s]);const{style:b}=n(this,mt);b.left=`${n(this,Qs)[0]}%`,b.top=`${n(this,Qs)[1]}%`},ql=function(){f(this,Ks,!n(this,Ks)),n(this,Ks)?(m(this,st,Yl).call(this),n(this,mt).addEventListener("click",n(this,ta)),n(this,mt).addEventListener("keydown",n(this,Zr))):(m(this,st,Nc).call(this),n(this,mt).removeEventListener("click",n(this,ta)),n(this,mt).removeEventListener("keydown",n(this,Zr)))},Yl=function(){n(this,Ke)||this.render(),this.isVisible?n(this,Ks)&&n(this,mt).classList.add("focused"):(m(this,st,af).call(this),n(this,mt).hidden=!1,n(this,mt).style.zIndex=parseInt(n(this,mt).style.zIndex)+1e3)},Nc=function(){n(this,mt).classList.remove("focused"),!(n(this,Ks)||!this.isVisible)&&(n(this,mt).hidden=!0,n(this,mt).style.zIndex=parseInt(n(this,mt).style.zIndex)-1e3)};class of extends yt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=j.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent"),t.setAttribute("role","comment");for(const e of this.textContent){const s=document.createElement("span");s.textContent=e,t.append(s)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}var Yo;class Fg extends yt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});g(this,Yo,null)}render(){this.container.classList.add("lineAnnotation");const e=this.data,{width:s,height:i}=Ui(e.rect),r=this.svgFactory.create(s,i,!0),a=f(this,Yo,this.svgFactory.createElement("svg:line"));return a.setAttribute("x1",e.rect[2]-e.lineCoordinates[0]),a.setAttribute("y1",e.rect[3]-e.lineCoordinates[1]),a.setAttribute("x2",e.rect[2]-e.lineCoordinates[2]),a.setAttribute("y2",e.rect[3]-e.lineCoordinates[3]),a.setAttribute("stroke-width",e.borderStyle.width||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),r.append(a),this.container.append(r),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return n(this,Yo)}addHighlightArea(){this.container.classList.add("highlightArea")}}Yo=new WeakMap;var Ko;class Ng extends yt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});g(this,Ko,null)}render(){this.container.classList.add("squareAnnotation");const e=this.data,{width:s,height:i}=Ui(e.rect),r=this.svgFactory.create(s,i,!0),a=e.borderStyle.width,o=f(this,Ko,this.svgFactory.createElement("svg:rect"));return o.setAttribute("x",a/2),o.setAttribute("y",a/2),o.setAttribute("width",s-a),o.setAttribute("height",i-a),o.setAttribute("stroke-width",a||1),o.setAttribute("stroke","transparent"),o.setAttribute("fill","transparent"),r.append(o),this.container.append(r),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return n(this,Ko)}addHighlightArea(){this.container.classList.add("highlightArea")}}Ko=new WeakMap;var Qo;class Og extends yt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});g(this,Qo,null)}render(){this.container.classList.add("circleAnnotation");const e=this.data,{width:s,height:i}=Ui(e.rect),r=this.svgFactory.create(s,i,!0),a=e.borderStyle.width,o=f(this,Qo,this.svgFactory.createElement("svg:ellipse"));return o.setAttribute("cx",s/2),o.setAttribute("cy",i/2),o.setAttribute("rx",s/2-a/2),o.setAttribute("ry",i/2-a/2),o.setAttribute("stroke-width",a||1),o.setAttribute("stroke","transparent"),o.setAttribute("fill","transparent"),r.append(o),this.container.append(r),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return n(this,Qo)}addHighlightArea(){this.container.classList.add("highlightArea")}}Qo=new WeakMap;var Jo;class lf extends yt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});g(this,Jo,null);this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:e,vertices:s,borderStyle:i,popupRef:r}}=this;if(!s)return this.container;const{width:a,height:o}=Ui(e),l=this.svgFactory.create(a,o,!0);let h=[];for(let u=0,p=s.length;u<p;u+=2){const b=s[u]-e[0],A=e[3]-s[u+1];h.push(`${b},${A}`)}h=h.join(" ");const c=f(this,Jo,this.svgFactory.createElement(this.svgElementName));return c.setAttribute("points",h),c.setAttribute("stroke-width",i.width||1),c.setAttribute("stroke","transparent"),c.setAttribute("fill","transparent"),l.append(c),this.container.append(l),!r&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return n(this,Jo)}addHighlightArea(){this.container.classList.add("highlightArea")}}Jo=new WeakMap;class Hg extends lf{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class Bg extends yt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}var Zo,On,tl,Oc;class vd extends yt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});g(this,tl);g(this,Zo,null);g(this,On,[]);this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=this.data.it==="InkHighlight"?j.HIGHLIGHT:j.INK}render(){this.container.classList.add(this.containerClassName);const{data:{rect:e,rotation:s,inkLists:i,borderStyle:r,popupRef:a}}=this,{transform:o,width:l,height:h}=m(this,tl,Oc).call(this,s,e),c=this.svgFactory.create(l,h,!0),u=f(this,Zo,this.svgFactory.createElement("svg:g"));c.append(u),u.setAttribute("stroke-width",r.width||1),u.setAttribute("stroke-linecap","round"),u.setAttribute("stroke-linejoin","round"),u.setAttribute("stroke-miterlimit",10),u.setAttribute("stroke","transparent"),u.setAttribute("fill","transparent"),u.setAttribute("transform",o);for(let p=0,b=i.length;p<b;p++){const A=this.svgFactory.createElement(this.svgElementName);n(this,On).push(A),A.setAttribute("points",i[p].join(",")),u.append(A)}return!a&&this.hasPopupData&&this._createPopup(),this.container.append(c),this._editOnDoubleClick(),this.container}updateEdited(e){super.updateEdited(e);const{thickness:s,points:i,rect:r}=e,a=n(this,Zo);if(s>=0&&a.setAttribute("stroke-width",s||1),i)for(let o=0,l=n(this,On).length;o<l;o++)n(this,On)[o].setAttribute("points",i[o].join(","));if(r){const{transform:o,width:l,height:h}=m(this,tl,Oc).call(this,this.data.rotation,r);a.parentElement.setAttribute("viewBox",`0 0 ${l} ${h}`),a.setAttribute("transform",o)}}getElementsToTriggerPopup(){return n(this,On)}addHighlightArea(){this.container.classList.add("highlightArea")}}Zo=new WeakMap,On=new WeakMap,tl=new WeakSet,Oc=function(e,s){switch(e){case 90:return{transform:`rotate(90) translate(${-s[0]},${s[1]}) scale(1,-1)`,width:s[3]-s[1],height:s[2]-s[0]};case 180:return{transform:`rotate(180) translate(${-s[2]},${s[1]}) scale(1,-1)`,width:s[2]-s[0],height:s[3]-s[1]};case 270:return{transform:`rotate(270) translate(${-s[2]},${s[3]}) scale(1,-1)`,width:s[3]-s[1],height:s[2]-s[0]};default:return{transform:`translate(${-s[0]},${s[3]}) scale(1,-1)`,width:s[2]-s[0],height:s[3]-s[1]}}};class hf extends yt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}),this.annotationEditorType=j.HIGHLIGHT}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this._editOnDoubleClick(),this.container}}class $g extends yt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class Gg extends yt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class zg extends yt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class cf extends yt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.annotationEditorType=j.STAMP}render(){return this.container.classList.add("stampAnnotation"),this.container.setAttribute("role","img"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}var el,sl,Hc;class Ug extends yt{constructor(e){super(e,{isRenderable:!0});g(this,sl);g(this,el,null);const{file:s}=this.data;this.filename=s.filename,this.content=s.content,this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...s})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:e,data:s}=this;let i;s.hasAppearance||s.fillAlpha===0?i=document.createElement("div"):(i=document.createElement("img"),i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(s.name)?"paperclip":"pushpin"}.svg`,s.fillAlpha&&s.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(s.fillAlpha*100)}%);`)),i.addEventListener("dblclick",m(this,sl,Hc).bind(this)),f(this,el,i);const{isMac:r}=ne.platform;return e.addEventListener("keydown",a=>{a.key==="Enter"&&(r?a.metaKey:a.ctrlKey)&&m(this,sl,Hc).call(this)}),!s.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea"),e.append(i),e}getElementsToTriggerPopup(){return n(this,el)}addHighlightArea(){this.container.classList.add("highlightArea")}}el=new WeakMap,sl=new WeakSet,Hc=function(){this.downloadManager?.openOrDownloadData(this.content,this.filename)};var il,Hn,Bn,nl,or,df,Bc;class jg{constructor({div:t,accessibilityManager:e,annotationCanvasMap:s,annotationEditorUIManager:i,page:r,viewport:a,structTreeLayer:o}){g(this,or);g(this,il,null);g(this,Hn,null);g(this,Bn,new Map);g(this,nl,null);this.div=t,f(this,il,e),f(this,Hn,s),f(this,nl,o||null),this.page=r,this.viewport=a,this.zIndex=0,this._annotationEditorUIManager=i}hasEditableAnnotations(){return n(this,Bn).size>0}async render(t){const{annotations:e}=t,s=this.div;nr(s,this.viewport);const i=new Map,r={data:null,layer:s,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:t.renderForms!==!1,svgFactory:new yd,annotationStorage:t.annotationStorage||new bd,enableScripting:t.enableScripting===!0,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const a of e){if(a.noHTML)continue;const o=a.annotationType===Et.POPUP;if(o){const c=i.get(a.id);if(!c)continue;r.elements=c}else{const{width:c,height:u}=Ui(a.rect);if(c<=0||u<=0)continue}r.data=a;const l=Tg.create(r);if(!l.isRenderable)continue;if(!o&&a.popupRef){const c=i.get(a.popupRef);c?c.push(l):i.set(a.popupRef,[l])}const h=l.render();a.hidden&&(h.style.visibility="hidden"),await m(this,or,df).call(this,h,a.id),l._isEditable&&(n(this,Bn).set(l.data.id,l),this._annotationEditorUIManager?.renderAnnotationElement(l))}m(this,or,Bc).call(this)}update({viewport:t}){const e=this.div;this.viewport=t,nr(e,{rotation:t.rotation}),m(this,or,Bc).call(this),e.hidden=!1}getEditableAnnotations(){return Array.from(n(this,Bn).values())}getEditableAnnotation(t){return n(this,Bn).get(t)}}il=new WeakMap,Hn=new WeakMap,Bn=new WeakMap,nl=new WeakMap,or=new WeakSet,df=async function(t,e){const s=t.firstChild||t,i=s.id=`${ud}${e}`,r=await n(this,nl)?.getAriaAttributes(i);if(r)for(const[a,o]of r)s.setAttribute(a,o);this.div.append(t),n(this,il)?.moveElementInDOM(this.div,t,s,!1)},Bc=function(){if(!n(this,Hn))return;const t=this.div;for(const[e,s]of n(this,Hn)){const i=t.querySelector(`[data-annotation-id="${e}"]`);if(!i)continue;s.className="annotationContent";const{firstChild:r}=i;r?r.nodeName==="CANVAS"?r.replaceWith(s):r.classList.contains("annotationContent")?r.after(s):r.before(s):i.append(s)}n(this,Hn).clear()};const kl=/\r\n?|\n/g;var Qe,ve,rl,$n,we,_t,uf,ff,pf,Kl,li,Ql,Jl,gf,Gc,mf;const rt=class rt extends gt{constructor(e){super({...e,name:"freeTextEditor"});g(this,_t);g(this,Qe);g(this,ve,"");g(this,rl,`${this.id}-editor`);g(this,$n,null);g(this,we);f(this,Qe,e.color||rt._defaultColor||gt._defaultLineColor),f(this,we,e.fontSize||rt._defaultFontSize)}static get _keyboardManager(){const e=rt.prototype,s=a=>a.isEmpty(),i=rr.TRANSLATE_SMALL,r=rr.TRANSLATE_BIG;return q(this,"_keyboardManager",new xl([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],e.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],e.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],e._translateEmpty,{args:[-i,0],checker:s}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],e._translateEmpty,{args:[-r,0],checker:s}],[["ArrowRight","mac+ArrowRight"],e._translateEmpty,{args:[i,0],checker:s}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],e._translateEmpty,{args:[r,0],checker:s}],[["ArrowUp","mac+ArrowUp"],e._translateEmpty,{args:[0,-i],checker:s}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],e._translateEmpty,{args:[0,-r],checker:s}],[["ArrowDown","mac+ArrowDown"],e._translateEmpty,{args:[0,i],checker:s}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],e._translateEmpty,{args:[0,r],checker:s}]]))}static initialize(e,s){gt.initialize(e,s);const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(e,s){switch(e){case Y.FREETEXT_SIZE:rt._defaultFontSize=s;break;case Y.FREETEXT_COLOR:rt._defaultColor=s;break}}updateParams(e,s){switch(e){case Y.FREETEXT_SIZE:m(this,_t,uf).call(this,s);break;case Y.FREETEXT_COLOR:m(this,_t,ff).call(this,s);break}}static get defaultPropertiesToUpdate(){return[[Y.FREETEXT_SIZE,rt._defaultFontSize],[Y.FREETEXT_COLOR,rt._defaultColor||gt._defaultLineColor]]}get propertiesToUpdate(){return[[Y.FREETEXT_SIZE,n(this,we)],[Y.FREETEXT_COLOR,n(this,Qe)]]}_translateEmpty(e,s){this._uiManager.translateSelectedEditors(e,s,!0)}getInitialTranslation(){const e=this.parentScale;return[-rt._internalPadding*e,-(rt._internalPadding+n(this,we))*e]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){if(this.isInEditMode())return;this.parent.setEditingState(!1),this.parent.updateToolbar(j.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),f(this,$n,new AbortController);const e=this._uiManager.combinedSignal(n(this,$n));this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:e}),this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:e}),this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:e}),this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:e}),this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:e})}disableEditMode(){this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",n(this,rl)),this._isDraggable=!0,n(this,$n)?.abort(),f(this,$n,null),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing"))}focusin(e){this._focusEventsAllowed&&(super.focusin(e),e.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(e){this.width||(this.enableEditMode(),e&&this.editorDiv.focus(),this._initialOptions?.isCentered&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||this.editorDiv.innerText.trim()===""}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const e=n(this,ve),s=f(this,ve,m(this,_t,pf).call(this).trimEnd());if(e===s)return;const i=r=>{if(f(this,ve,r),!r){this.remove();return}m(this,_t,Jl).call(this),this._uiManager.rebuild(this),m(this,_t,Kl).call(this)};this.addCommands({cmd:()=>{i(s)},undo:()=>{i(e)},mustExec:!1}),m(this,_t,Kl).call(this)}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(e){this.enterInEditMode()}keydown(e){e.target===this.div&&e.key==="Enter"&&(this.enterInEditMode(),e.preventDefault())}editorDivKeydown(e){rt._keyboardManager.exec(this,e)}editorDivFocus(e){this.isEditing=!0}editorDivBlur(e){this.isEditing=!1}editorDivInput(e){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let e,s;this.width&&(e=this.x,s=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",n(this,rl)),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2"),this.editorDiv.setAttribute("data-l10n-attrs","default-content"),this.enableEditing(),this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;if(i.fontSize=`calc(${n(this,we)}px * var(--scale-factor))`,i.color=n(this,Qe),this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),hh(this,this.div,["dblclick","keydown"]),this.width){const[r,a]=this.parentDimensions;if(this.annotationElementId){const{position:o}=this._initialData;let[l,h]=this.getInitialTranslation();[l,h]=this.pageTranslationToScreen(l,h);const[c,u]=this.pageDimensions,[p,b]=this.pageTranslation;let A,y;switch(this.rotation){case 0:A=e+(o[0]-p)/c,y=s+this.height-(o[1]-b)/u;break;case 90:A=e+(o[0]-p)/c,y=s-(o[1]-b)/u,[l,h]=[h,-l];break;case 180:A=e-this.width+(o[0]-p)/c,y=s-(o[1]-b)/u,[l,h]=[-l,-h];break;case 270:A=e+(o[0]-p-this.height*u)/c,y=s+(o[1]-b-this.width*c)/u,[l,h]=[-h,l];break}this.setAt(A*r,y*a,l,h)}else this.setAt(e*r,s*a,this.width*r,this.height*a);m(this,_t,Jl).call(this),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}editorDivPaste(e){var A,y,w;const s=e.clipboardData||window.clipboardData,{types:i}=s;if(i.length===1&&i[0]==="text/plain")return;e.preventDefault();const r=m(A=rt,li,Gc).call(A,s.getData("text")||"").replaceAll(kl,`
`);if(!r)return;const a=window.getSelection();if(!a.rangeCount)return;this.editorDiv.normalize(),a.deleteFromDocument();const o=a.getRangeAt(0);if(!r.includes(`
`)){o.insertNode(document.createTextNode(r)),this.editorDiv.normalize(),a.collapseToStart();return}const{startContainer:l,startOffset:h}=o,c=[],u=[];if(l.nodeType===Node.TEXT_NODE){const v=l.parentElement;if(u.push(l.nodeValue.slice(h).replaceAll(kl,"")),v!==this.editorDiv){let _=c;for(const E of this.editorDiv.childNodes){if(E===v){_=u;continue}_.push(m(y=rt,li,Ql).call(y,E))}}c.push(l.nodeValue.slice(0,h).replaceAll(kl,""))}else if(l===this.editorDiv){let v=c,_=0;for(const E of this.editorDiv.childNodes)_++===h&&(v=u),v.push(m(w=rt,li,Ql).call(w,E))}f(this,ve,`${c.join(`
`)}${r}${u.join(`
`)}`),m(this,_t,Jl).call(this);const p=new Range;let b=c.reduce((v,_)=>v+_.length,0);for(const{firstChild:v}of this.editorDiv.childNodes)if(v.nodeType===Node.TEXT_NODE){const _=v.nodeValue.length;if(b<=_){p.setStart(v,b),p.setEnd(v,b);break}b-=_}a.removeAllRanges(),a.addRange(p)}get contentDiv(){return this.editorDiv}static async deserialize(e,s,i){var o;let r=null;if(e instanceof of){const{data:{defaultAppearanceData:{fontSize:l,fontColor:h},rect:c,rotation:u,id:p,popupRef:b},textContent:A,textPosition:y,parent:{page:{pageNumber:w}}}=e;if(!A||A.length===0)return null;r=e={annotationType:j.FREETEXT,color:Array.from(h),fontSize:l,value:A.join(`
`),position:y,pageIndex:w-1,rect:c.slice(0),rotation:u,id:p,deleted:!1,popupRef:b}}const a=await super.deserialize(e,s,i);return f(a,we,e.fontSize),f(a,Qe,D.makeHexColor(...e.color)),f(a,ve,m(o=rt,li,Gc).call(o,e.value)),a.annotationElementId=e.id||null,a._initialData=r,a}serialize(e=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const s=rt._internalPadding*this.parentScale,i=this.getRect(s,s),r=gt._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:n(this,Qe)),a={annotationType:j.FREETEXT,color:r,fontSize:n(this,we),value:m(this,_t,gf).call(this),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?a:this.annotationElementId&&!m(this,_t,mf).call(this,a)?null:(a.id=this.annotationElementId,a)}renderAnnotationElement(e){const s=super.renderAnnotationElement(e);if(this.deleted)return s;const{style:i}=s;i.fontSize=`calc(${n(this,we)}px * var(--scale-factor))`,i.color=n(this,Qe),s.replaceChildren();for(const a of n(this,ve).split(`
`)){const o=document.createElement("div");o.append(a?document.createTextNode(a):document.createElement("br")),s.append(o)}const r=rt._internalPadding*this.parentScale;return e.updateEdited({rect:this.getRect(r,r),popupContent:n(this,ve)}),s}resetAnnotationElement(e){super.resetAnnotationElement(e),e.resetEdited()}};Qe=new WeakMap,ve=new WeakMap,rl=new WeakMap,$n=new WeakMap,we=new WeakMap,_t=new WeakSet,uf=function(e){const s=r=>{this.editorDiv.style.fontSize=`calc(${r}px * var(--scale-factor))`,this.translate(0,-(r-n(this,we))*this.parentScale),f(this,we,r),m(this,_t,Kl).call(this)},i=n(this,we);this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Y.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})},ff=function(e){const s=r=>{f(this,Qe,this.editorDiv.style.color=r)},i=n(this,Qe);this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Y.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})},pf=function(){var i;const e=[];this.editorDiv.normalize();let s=null;for(const r of this.editorDiv.childNodes)s?.nodeType===Node.TEXT_NODE&&r.nodeName==="BR"||(e.push(m(i=rt,li,Ql).call(i,r)),s=r);return e.join(`
`)},Kl=function(){const[e,s]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:r,div:a}=this,o=a.style.display,l=a.classList.contains("hidden");a.classList.remove("hidden"),a.style.display="hidden",r.div.append(this.div),i=a.getBoundingClientRect(),a.remove(),a.style.display=o,a.classList.toggle("hidden",l)}this.rotation%180===this.parentRotation%180?(this.width=i.width/e,this.height=i.height/s):(this.width=i.height/e,this.height=i.width/s),this.fixAndSetPosition()},li=new WeakSet,Ql=function(e){return(e.nodeType===Node.TEXT_NODE?e.nodeValue:e.innerText).replaceAll(kl,"")},Jl=function(){if(this.editorDiv.replaceChildren(),!!n(this,ve))for(const e of n(this,ve).split(`
`)){const s=document.createElement("div");s.append(e?document.createTextNode(e):document.createElement("br")),this.editorDiv.append(s)}},gf=function(){return n(this,ve).replaceAll(" "," ")},Gc=function(e){return e.replaceAll(" "," ")},mf=function(e){const{value:s,fontSize:i,color:r,pageIndex:a}=this._initialData;return this._hasBeenMoved||e.value!==s||e.fontSize!==i||e.color.some((o,l)=>o!==r[l])||e.pageIndex!==a},g(rt,li),O(rt,"_freeTextDefaultContent",""),O(rt,"_internalPadding",0),O(rt,"_defaultColor",null),O(rt,"_defaultFontSize",10),O(rt,"_type","freetext"),O(rt,"_editorType",j.FREETEXT);let $c=rt;class M{toSVGPath(){it("Abstract method `toSVGPath` must be implemented.")}get box(){it("Abstract getter `box` must be implemented.")}serialize(t,e){it("Abstract method `serialize` must be implemented.")}static _rescale(t,e,s,i,r,a){a||(a=new Float32Array(t.length));for(let o=0,l=t.length;o<l;o+=2)a[o]=e+t[o]*i,a[o+1]=s+t[o+1]*r;return a}static _rescaleAndSwap(t,e,s,i,r,a){a||(a=new Float32Array(t.length));for(let o=0,l=t.length;o<l;o+=2)a[o]=e+t[o+1]*i,a[o+1]=s+t[o]*r;return a}static _translate(t,e,s,i){i||(i=new Float32Array(t.length));for(let r=0,a=t.length;r<a;r+=2)i[r]=e+t[r],i[r+1]=s+t[r+1];return i}static svgRound(t){return Math.round(t*1e4)}static _normalizePoint(t,e,s,i,r){switch(r){case 90:return[1-e/s,t/i];case 180:return[1-t/s,1-e/i];case 270:return[e/s,1-t/i];default:return[t/s,e/i]}}static _normalizePagePoint(t,e,s){switch(s){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,s,i,r,a){return[(t+5*s)/6,(e+5*i)/6,(5*s+r)/6,(5*i+a)/6,(s+r)/2,(i+a)/2]}}O(M,"PRECISION",1e-4);var _e,Je,ia,na,ws,X,Gn,zn,al,ol,ra,aa,Li,ll,Mh,kh,Pt,za,bf,Af,yf,vf,wf,_f;const Ds=class Ds{constructor({x:t,y:e},s,i,r,a,o=0){g(this,Pt);g(this,_e);g(this,Je,[]);g(this,ia);g(this,na);g(this,ws,[]);g(this,X,new Float32Array(18));g(this,Gn);g(this,zn);g(this,al);g(this,ol);g(this,ra);g(this,aa);g(this,Li,[]);f(this,_e,s),f(this,aa,r*i),f(this,na,a),n(this,X).set([NaN,NaN,NaN,NaN,t,e],6),f(this,ia,o),f(this,ol,n(Ds,ll)*i),f(this,al,n(Ds,kh)*i),f(this,ra,i),n(this,Li).push(t,e)}isEmpty(){return isNaN(n(this,X)[8])}add({x:t,y:e}){f(this,Gn,t),f(this,zn,e);const[s,i,r,a]=n(this,_e);let[o,l,h,c]=n(this,X).subarray(8,12);const u=t-h,p=e-c,b=Math.hypot(u,p);if(b<n(this,al))return!1;const A=b-n(this,ol),y=A/b,w=y*u,v=y*p;let _=o,E=l;o=h,l=c,h+=w,c+=v,n(this,Li)?.push(t,e);const S=-v/A,C=w/A,T=S*n(this,aa),x=C*n(this,aa);return n(this,X).set(n(this,X).subarray(2,8),0),n(this,X).set([h+T,c+x],4),n(this,X).set(n(this,X).subarray(14,18),12),n(this,X).set([h-T,c-x],16),isNaN(n(this,X)[6])?(n(this,ws).length===0&&(n(this,X).set([o+T,l+x],2),n(this,ws).push(NaN,NaN,NaN,NaN,(o+T-s)/r,(l+x-i)/a),n(this,X).set([o-T,l-x],14),n(this,Je).push(NaN,NaN,NaN,NaN,(o-T-s)/r,(l-x-i)/a)),n(this,X).set([_,E,o,l,h,c],6),!this.isEmpty()):(n(this,X).set([_,E,o,l,h,c],6),Math.abs(Math.atan2(E-l,_-o)-Math.atan2(v,w))<Math.PI/2?([o,l,h,c]=n(this,X).subarray(2,6),n(this,ws).push(NaN,NaN,NaN,NaN,((o+h)/2-s)/r,((l+c)/2-i)/a),[o,l,_,E]=n(this,X).subarray(14,18),n(this,Je).push(NaN,NaN,NaN,NaN,((_+o)/2-s)/r,((E+l)/2-i)/a),!0):([_,E,o,l,h,c]=n(this,X).subarray(0,6),n(this,ws).push(((_+5*o)/6-s)/r,((E+5*l)/6-i)/a,((5*o+h)/6-s)/r,((5*l+c)/6-i)/a,((o+h)/2-s)/r,((l+c)/2-i)/a),[h,c,o,l,_,E]=n(this,X).subarray(12,18),n(this,Je).push(((_+5*o)/6-s)/r,((E+5*l)/6-i)/a,((5*o+h)/6-s)/r,((5*l+c)/6-i)/a,((o+h)/2-s)/r,((l+c)/2-i)/a),!0))}toSVGPath(){if(this.isEmpty())return"";const t=n(this,ws),e=n(this,Je);if(isNaN(n(this,X)[6])&&!this.isEmpty())return m(this,Pt,bf).call(this);const s=[];s.push(`M${t[4]} ${t[5]}`);for(let i=6;i<t.length;i+=6)isNaN(t[i])?s.push(`L${t[i+4]} ${t[i+5]}`):s.push(`C${t[i]} ${t[i+1]} ${t[i+2]} ${t[i+3]} ${t[i+4]} ${t[i+5]}`);m(this,Pt,yf).call(this,s);for(let i=e.length-6;i>=6;i-=6)isNaN(e[i])?s.push(`L${e[i+4]} ${e[i+5]}`):s.push(`C${e[i]} ${e[i+1]} ${e[i+2]} ${e[i+3]} ${e[i+4]} ${e[i+5]}`);return m(this,Pt,Af).call(this,s),s.join(" ")}newFreeDrawOutline(t,e,s,i,r,a){return new Sf(t,e,s,i,r,a)}getOutlines(){const t=n(this,ws),e=n(this,Je),s=n(this,X),[i,r,a,o]=n(this,_e),l=new Float32Array((n(this,Li)?.length??0)+2);for(let u=0,p=l.length-2;u<p;u+=2)l[u]=(n(this,Li)[u]-i)/a,l[u+1]=(n(this,Li)[u+1]-r)/o;if(l[l.length-2]=(n(this,Gn)-i)/a,l[l.length-1]=(n(this,zn)-r)/o,isNaN(s[6])&&!this.isEmpty())return m(this,Pt,vf).call(this,l);const h=new Float32Array(n(this,ws).length+24+n(this,Je).length);let c=t.length;for(let u=0;u<c;u+=2){if(isNaN(t[u])){h[u]=h[u+1]=NaN;continue}h[u]=t[u],h[u+1]=t[u+1]}c=m(this,Pt,_f).call(this,h,c);for(let u=e.length-6;u>=6;u-=6)for(let p=0;p<6;p+=2){if(isNaN(e[u+p])){h[c]=h[c+1]=NaN,c+=2;continue}h[c]=e[u+p],h[c+1]=e[u+p+1],c+=2}return m(this,Pt,wf).call(this,h,c),this.newFreeDrawOutline(h,l,n(this,_e),n(this,ra),n(this,ia),n(this,na))}};_e=new WeakMap,Je=new WeakMap,ia=new WeakMap,na=new WeakMap,ws=new WeakMap,X=new WeakMap,Gn=new WeakMap,zn=new WeakMap,al=new WeakMap,ol=new WeakMap,ra=new WeakMap,aa=new WeakMap,Li=new WeakMap,ll=new WeakMap,Mh=new WeakMap,kh=new WeakMap,Pt=new WeakSet,za=function(){const t=n(this,X).subarray(4,6),e=n(this,X).subarray(16,18),[s,i,r,a]=n(this,_e);return[(n(this,Gn)+(t[0]-e[0])/2-s)/r,(n(this,zn)+(t[1]-e[1])/2-i)/a,(n(this,Gn)+(e[0]-t[0])/2-s)/r,(n(this,zn)+(e[1]-t[1])/2-i)/a]},bf=function(){const[t,e,s,i]=n(this,_e),[r,a,o,l]=m(this,Pt,za).call(this);return`M${(n(this,X)[2]-t)/s} ${(n(this,X)[3]-e)/i} L${(n(this,X)[4]-t)/s} ${(n(this,X)[5]-e)/i} L${r} ${a} L${o} ${l} L${(n(this,X)[16]-t)/s} ${(n(this,X)[17]-e)/i} L${(n(this,X)[14]-t)/s} ${(n(this,X)[15]-e)/i} Z`},Af=function(t){const e=n(this,Je);t.push(`L${e[4]} ${e[5]} Z`)},yf=function(t){const[e,s,i,r]=n(this,_e),a=n(this,X).subarray(4,6),o=n(this,X).subarray(16,18),[l,h,c,u]=m(this,Pt,za).call(this);t.push(`L${(a[0]-e)/i} ${(a[1]-s)/r} L${l} ${h} L${c} ${u} L${(o[0]-e)/i} ${(o[1]-s)/r}`)},vf=function(t){const e=n(this,X),[s,i,r,a]=n(this,_e),[o,l,h,c]=m(this,Pt,za).call(this),u=new Float32Array(36);return u.set([NaN,NaN,NaN,NaN,(e[2]-s)/r,(e[3]-i)/a,NaN,NaN,NaN,NaN,(e[4]-s)/r,(e[5]-i)/a,NaN,NaN,NaN,NaN,o,l,NaN,NaN,NaN,NaN,h,c,NaN,NaN,NaN,NaN,(e[16]-s)/r,(e[17]-i)/a,NaN,NaN,NaN,NaN,(e[14]-s)/r,(e[15]-i)/a],0),this.newFreeDrawOutline(u,t,n(this,_e),n(this,ra),n(this,ia),n(this,na))},wf=function(t,e){const s=n(this,Je);return t.set([NaN,NaN,NaN,NaN,s[4],s[5]],e),e+=6},_f=function(t,e){const s=n(this,X).subarray(4,6),i=n(this,X).subarray(16,18),[r,a,o,l]=n(this,_e),[h,c,u,p]=m(this,Pt,za).call(this);return t.set([NaN,NaN,NaN,NaN,(s[0]-r)/o,(s[1]-a)/l,NaN,NaN,NaN,NaN,h,c,NaN,NaN,NaN,NaN,u,p,NaN,NaN,NaN,NaN,(i[0]-r)/o,(i[1]-a)/l],e),e+=24},g(Ds,ll,8),g(Ds,Mh,2),g(Ds,kh,n(Ds,ll)+n(Ds,Mh));let uh=Ds;var oa,Un,Zs,hl,Se,cl,At,Lh,Ef;class Sf extends M{constructor(e,s,i,r,a,o){super();g(this,Lh);g(this,oa);g(this,Un,new Float32Array(4));g(this,Zs);g(this,hl);g(this,Se);g(this,cl);g(this,At);f(this,At,e),f(this,Se,s),f(this,oa,i),f(this,cl,r),f(this,Zs,a),f(this,hl,o),this.lastPoint=[NaN,NaN],m(this,Lh,Ef).call(this,o);const[l,h,c,u]=n(this,Un);for(let p=0,b=e.length;p<b;p+=2)e[p]=(e[p]-l)/c,e[p+1]=(e[p+1]-h)/u;for(let p=0,b=s.length;p<b;p+=2)s[p]=(s[p]-l)/c,s[p+1]=(s[p+1]-h)/u}toSVGPath(){const e=[`M${n(this,At)[4]} ${n(this,At)[5]}`];for(let s=6,i=n(this,At).length;s<i;s+=6){if(isNaN(n(this,At)[s])){e.push(`L${n(this,At)[s+4]} ${n(this,At)[s+5]}`);continue}e.push(`C${n(this,At)[s]} ${n(this,At)[s+1]} ${n(this,At)[s+2]} ${n(this,At)[s+3]} ${n(this,At)[s+4]} ${n(this,At)[s+5]}`)}return e.push("Z"),e.join(" ")}serialize([e,s,i,r],a){const o=i-e,l=r-s;let h,c;switch(a){case 0:h=M._rescale(n(this,At),e,r,o,-l),c=M._rescale(n(this,Se),e,r,o,-l);break;case 90:h=M._rescaleAndSwap(n(this,At),e,s,o,l),c=M._rescaleAndSwap(n(this,Se),e,s,o,l);break;case 180:h=M._rescale(n(this,At),i,s,-o,l),c=M._rescale(n(this,Se),i,s,-o,l);break;case 270:h=M._rescaleAndSwap(n(this,At),i,r,-o,-l),c=M._rescaleAndSwap(n(this,Se),i,r,-o,-l);break}return{outline:Array.from(h),points:[Array.from(c)]}}get box(){return n(this,Un)}newOutliner(e,s,i,r,a,o=0){return new uh(e,s,i,r,a,o)}getNewOutline(e,s){const[i,r,a,o]=n(this,Un),[l,h,c,u]=n(this,oa),p=a*c,b=o*u,A=i*c+l,y=r*u+h,w=this.newOutliner({x:n(this,Se)[0]*p+A,y:n(this,Se)[1]*b+y},n(this,oa),n(this,cl),e,n(this,hl),s??n(this,Zs));for(let v=2;v<n(this,Se).length;v+=2)w.add({x:n(this,Se)[v]*p+A,y:n(this,Se)[v+1]*b+y});return w.getOutlines()}}oa=new WeakMap,Un=new WeakMap,Zs=new WeakMap,hl=new WeakMap,Se=new WeakMap,cl=new WeakMap,At=new WeakMap,Lh=new WeakSet,Ef=function(e){const s=n(this,At);let i=s[4],r=s[5],a=i,o=r,l=i,h=r,c=i,u=r;const p=e?Math.max:Math.min;for(let A=6,y=s.length;A<y;A+=6){if(isNaN(s[A]))a=Math.min(a,s[A+4]),o=Math.min(o,s[A+5]),l=Math.max(l,s[A+4]),h=Math.max(h,s[A+5]),u<s[A+5]?(c=s[A+4],u=s[A+5]):u===s[A+5]&&(c=p(c,s[A+4]));else{const w=D.bezierBoundingBox(i,r,...s.slice(A,A+6));a=Math.min(a,w[0]),o=Math.min(o,w[1]),l=Math.max(l,w[2]),h=Math.max(h,w[3]),u<w[3]?(c=w[2],u=w[3]):u===w[3]&&(c=p(c,w[2]))}i=s[A+4],r=s[A+5]}const b=n(this,Un);b[0]=a-n(this,Zs),b[1]=o-n(this,Zs),b[2]=l-a+2*n(this,Zs),b[3]=h-o+2*n(this,Zs),this.lastPoint=[c,u]};var dl,ul,Ii,Ze,ae,Cf,Zl,xf,Tf,Uc;class zc{constructor(t,e=0,s=0,i=!0){g(this,ae);g(this,dl);g(this,ul);g(this,Ii,[]);g(this,Ze,[]);let r=1/0,a=-1/0,o=1/0,l=-1/0;const h=10**-4;for(const{x:w,y:v,width:_,height:E}of t){const S=Math.floor((w-e)/h)*h,C=Math.ceil((w+_+e)/h)*h,T=Math.floor((v-e)/h)*h,x=Math.ceil((v+E+e)/h)*h,F=[S,T,x,!0],N=[C,T,x,!1];n(this,Ii).push(F,N),r=Math.min(r,S),a=Math.max(a,C),o=Math.min(o,T),l=Math.max(l,x)}const c=a-r+2*s,u=l-o+2*s,p=r-s,b=o-s,A=n(this,Ii).at(i?-1:-2),y=[A[0],A[2]];for(const w of n(this,Ii)){const[v,_,E]=w;w[0]=(v-p)/c,w[1]=(_-b)/u,w[2]=(E-b)/u}f(this,dl,new Float32Array([p,b,c,u])),f(this,ul,y)}getOutlines(){n(this,Ii).sort((e,s)=>e[0]-s[0]||e[1]-s[1]||e[2]-s[2]);const t=[];for(const e of n(this,Ii))e[3]?(t.push(...m(this,ae,Uc).call(this,e)),m(this,ae,xf).call(this,e)):(m(this,ae,Tf).call(this,e),t.push(...m(this,ae,Uc).call(this,e)));return m(this,ae,Cf).call(this,t)}}dl=new WeakMap,ul=new WeakMap,Ii=new WeakMap,Ze=new WeakMap,ae=new WeakSet,Cf=function(t){const e=[],s=new Set;for(const a of t){const[o,l,h]=a;e.push([o,l,a],[o,h,a])}e.sort((a,o)=>a[1]-o[1]||a[0]-o[0]);for(let a=0,o=e.length;a<o;a+=2){const l=e[a][2],h=e[a+1][2];l.push(h),h.push(l),s.add(l),s.add(h)}const i=[];let r;for(;s.size>0;){const a=s.values().next().value;let[o,l,h,c,u]=a;s.delete(a);let p=o,b=l;for(r=[o,h],i.push(r);;){let A;if(s.has(c))A=c;else if(s.has(u))A=u;else break;s.delete(A),[o,l,h,c,u]=A,p!==o&&(r.push(p,b,o,b===l?l:h),p=o),b=b===l?h:l}r.push(p,b)}return new Vg(i,n(this,dl),n(this,ul))},Zl=function(t){const e=n(this,Ze);let s=0,i=e.length-1;for(;s<=i;){const r=s+i>>1,a=e[r][0];if(a===t)return r;a<t?s=r+1:i=r-1}return i+1},xf=function([,t,e]){const s=m(this,ae,Zl).call(this,t);n(this,Ze).splice(s,0,[t,e])},Tf=function([,t,e]){const s=m(this,ae,Zl).call(this,t);for(let i=s;i<n(this,Ze).length;i++){const[r,a]=n(this,Ze)[i];if(r!==t)break;if(r===t&&a===e){n(this,Ze).splice(i,1);return}}for(let i=s-1;i>=0;i--){const[r,a]=n(this,Ze)[i];if(r!==t)break;if(r===t&&a===e){n(this,Ze).splice(i,1);return}}},Uc=function(t){const[e,s,i]=t,r=[[e,s,i]],a=m(this,ae,Zl).call(this,i);for(let o=0;o<a;o++){const[l,h]=n(this,Ze)[o];for(let c=0,u=r.length;c<u;c++){const[,p,b]=r[c];if(!(h<=p||b<=l)){if(p>=l){if(b>h)r[c][1]=h;else{if(u===1)return[];r.splice(c,1),c--,u--}continue}r[c][2]=l,b>h&&r.push([e,h,b])}}}return r};var fl,la;class Vg extends M{constructor(e,s,i){super();g(this,fl);g(this,la);f(this,la,e),f(this,fl,s),this.lastPoint=i}toSVGPath(){const e=[];for(const s of n(this,la)){let[i,r]=s;e.push(`M${i} ${r}`);for(let a=2;a<s.length;a+=2){const o=s[a],l=s[a+1];o===i?(e.push(`V${l}`),r=l):l===r&&(e.push(`H${o}`),i=o)}e.push("Z")}return e.join(" ")}serialize([e,s,i,r],a){const o=[],l=i-e,h=r-s;for(const c of n(this,la)){const u=new Array(c.length);for(let p=0;p<c.length;p+=2)u[p]=e+c[p]*l,u[p+1]=r-c[p+1]*h;o.push(u)}return o}get box(){return n(this,fl)}get classNamesForOutlining(){return["highlightOutline"]}}fl=new WeakMap,la=new WeakMap;class jc extends uh{newFreeDrawOutline(t,e,s,i,r,a){return new Wg(t,e,s,i,r,a)}}class Wg extends Sf{newOutliner(t,e,s,i,r,a=0){return new jc(t,e,s,i,r,a)}}var ts,jn,ha,Tt,pl,ca,gl,ml,Di,es,da,bl,et,Vc,Wc,Xc,Yi,Pf,di;const ce=class ce{constructor({editor:t=null,uiManager:e=null}){g(this,et);g(this,ts,null);g(this,jn,null);g(this,ha);g(this,Tt,null);g(this,pl,!1);g(this,ca,!1);g(this,gl,null);g(this,ml);g(this,Di,null);g(this,es,null);g(this,da);t?(f(this,ca,!1),f(this,da,Y.HIGHLIGHT_COLOR),f(this,gl,t)):(f(this,ca,!0),f(this,da,Y.HIGHLIGHT_DEFAULT_COLOR)),f(this,es,t?._uiManager||e),f(this,ml,n(this,es)._eventBus),f(this,ha,t?.color||n(this,es)?.highlightColors.values().next().value||"#FFFF98"),n(ce,bl)||f(ce,bl,Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"}))}static get _keyboardManager(){return q(this,"_keyboardManager",new xl([[["Escape","mac+Escape"],ce.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],ce.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],ce.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],ce.prototype._moveToPrevious],[["Home","mac+Home"],ce.prototype._moveToBeginning],[["End","mac+End"],ce.prototype._moveToEnd]]))}renderButton(){const t=f(this,ts,document.createElement("button"));t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.setAttribute("aria-haspopup",!0);const e=n(this,es)._signal;t.addEventListener("click",m(this,et,Yi).bind(this),{signal:e}),t.addEventListener("keydown",m(this,et,Xc).bind(this),{signal:e});const s=f(this,jn,document.createElement("span"));return s.className="swatch",s.setAttribute("aria-hidden",!0),s.style.backgroundColor=n(this,ha),t.append(s),t}renderMainDropdown(){const t=f(this,Tt,m(this,et,Vc).call(this));return t.setAttribute("aria-orientation","horizontal"),t.setAttribute("aria-labelledby","highlightColorPickerLabel"),t}_colorSelectFromKeyboard(t){if(t.target===n(this,ts)){m(this,et,Yi).call(this,t);return}const e=t.target.getAttribute("data-color");e&&m(this,et,Wc).call(this,e,t)}_moveToNext(t){if(!n(this,et,di)){m(this,et,Yi).call(this,t);return}if(t.target===n(this,ts)){n(this,Tt).firstChild?.focus();return}t.target.nextSibling?.focus()}_moveToPrevious(t){if(t.target===n(this,Tt)?.firstChild||t.target===n(this,ts)){n(this,et,di)&&this._hideDropdownFromKeyboard();return}n(this,et,di)||m(this,et,Yi).call(this,t),t.target.previousSibling?.focus()}_moveToBeginning(t){if(!n(this,et,di)){m(this,et,Yi).call(this,t);return}n(this,Tt).firstChild?.focus()}_moveToEnd(t){if(!n(this,et,di)){m(this,et,Yi).call(this,t);return}n(this,Tt).lastChild?.focus()}hideDropdown(){n(this,Tt)?.classList.add("hidden"),n(this,Di)?.abort(),f(this,Di,null)}_hideDropdownFromKeyboard(){if(!n(this,ca)){if(!n(this,et,di)){n(this,gl)?.unselect();return}this.hideDropdown(),n(this,ts).focus({preventScroll:!0,focusVisible:n(this,pl)})}}updateColor(t){if(n(this,jn)&&(n(this,jn).style.backgroundColor=t),!n(this,Tt))return;const e=n(this,es).highlightColors.values();for(const s of n(this,Tt).children)s.setAttribute("aria-selected",e.next().value===t)}destroy(){n(this,ts)?.remove(),f(this,ts,null),f(this,jn,null),n(this,Tt)?.remove(),f(this,Tt,null)}};ts=new WeakMap,jn=new WeakMap,ha=new WeakMap,Tt=new WeakMap,pl=new WeakMap,ca=new WeakMap,gl=new WeakMap,ml=new WeakMap,Di=new WeakMap,es=new WeakMap,da=new WeakMap,bl=new WeakMap,et=new WeakSet,Vc=function(){const t=document.createElement("div"),e=n(this,es)._signal;t.addEventListener("contextmenu",rs,{signal:e}),t.className="dropdown",t.role="listbox",t.setAttribute("aria-multiselectable",!1),t.setAttribute("aria-orientation","vertical"),t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");for(const[s,i]of n(this,es).highlightColors){const r=document.createElement("button");r.tabIndex="0",r.role="option",r.setAttribute("data-color",i),r.title=s,r.setAttribute("data-l10n-id",n(ce,bl)[s]);const a=document.createElement("span");r.append(a),a.className="swatch",a.style.backgroundColor=i,r.setAttribute("aria-selected",i===n(this,ha)),r.addEventListener("click",m(this,et,Wc).bind(this,i),{signal:e}),t.append(r)}return t.addEventListener("keydown",m(this,et,Xc).bind(this),{signal:e}),t},Wc=function(t,e){e.stopPropagation(),n(this,ml).dispatch("switchannotationeditorparams",{source:this,type:n(this,da),value:t})},Xc=function(t){ce._keyboardManager.exec(this,t)},Yi=function(t){if(n(this,et,di)){this.hideDropdown();return}if(f(this,pl,t.detail===0),n(this,Di)||(f(this,Di,new AbortController),window.addEventListener("pointerdown",m(this,et,Pf).bind(this),{signal:n(this,es).combinedSignal(n(this,Di))})),n(this,Tt)){n(this,Tt).classList.remove("hidden");return}const e=f(this,Tt,m(this,et,Vc).call(this));n(this,ts).append(e)},Pf=function(t){n(this,Tt)?.contains(t.target)||this.hideDropdown()},di=function(){return n(this,Tt)&&!n(this,Tt).classList.contains("hidden")},g(ce,bl,null);let fh=ce;var ua,Al,ti,Vn,fa,fe,yl,vl,Wn,Oe,Ee,Ot,pa,ei,Xt,ga,He,wl,G,qc,th,Rf,Mf,kf,Yc,Ki,ze,mr,Lf,eh,Ua,If,Df,Ff,Nf,Of;const J=class J extends gt{constructor(e){super({...e,name:"highlightEditor"});g(this,G);g(this,ua,null);g(this,Al,0);g(this,ti);g(this,Vn,null);g(this,fa,null);g(this,fe,null);g(this,yl,null);g(this,vl,0);g(this,Wn,null);g(this,Oe,null);g(this,Ee,null);g(this,Ot,!1);g(this,pa,null);g(this,ei);g(this,Xt,null);g(this,ga,"");g(this,He);g(this,wl,"");this.color=e.color||J._defaultColor,f(this,He,e.thickness||J._defaultThickness),f(this,ei,e.opacity||J._defaultOpacity),f(this,ti,e.boxes||null),f(this,wl,e.methodOfCreation||""),f(this,ga,e.text||""),this._isDraggable=!1,e.highlightId>-1?(f(this,Ot,!0),m(this,G,th).call(this,e),m(this,G,Ki).call(this)):n(this,ti)&&(f(this,ua,e.anchorNode),f(this,Al,e.anchorOffset),f(this,yl,e.focusNode),f(this,vl,e.focusOffset),m(this,G,qc).call(this),m(this,G,Ki).call(this),this.rotate(this.rotation))}static get _keyboardManager(){const e=J.prototype;return q(this,"_keyboardManager",new xl([[["ArrowLeft","mac+ArrowLeft"],e._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],e._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],e._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],e._moveCaret,{args:[3]}]]))}get telemetryInitialData(){return{action:"added",type:n(this,Ot)?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:n(this,He),methodOfCreation:n(this,wl)}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(e){return{numberOfColors:e.get("color").size}}static initialize(e,s){gt.initialize(e,s),J._defaultColor||(J._defaultColor=s.highlightColors?.values().next().value||"#fff066")}static updateDefaultParams(e,s){switch(e){case Y.HIGHLIGHT_DEFAULT_COLOR:J._defaultColor=s;break;case Y.HIGHLIGHT_THICKNESS:J._defaultThickness=s;break}}translateInPage(e,s){}get toolbarPosition(){return n(this,pa)}updateParams(e,s){switch(e){case Y.HIGHLIGHT_COLOR:m(this,G,Rf).call(this,s);break;case Y.HIGHLIGHT_THICKNESS:m(this,G,Mf).call(this,s);break}}static get defaultPropertiesToUpdate(){return[[Y.HIGHLIGHT_DEFAULT_COLOR,J._defaultColor],[Y.HIGHLIGHT_THICKNESS,J._defaultThickness]]}get propertiesToUpdate(){return[[Y.HIGHLIGHT_COLOR,this.color||J._defaultColor],[Y.HIGHLIGHT_THICKNESS,n(this,He)||J._defaultThickness],[Y.HIGHLIGHT_FREE,n(this,Ot)]]}async addEditToolbar(){const e=await super.addEditToolbar();return e?(this._uiManager.highlightColors&&(f(this,fa,new fh({editor:this})),e.addColorPicker(n(this,fa))),e):null}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(m(this,G,Ua).call(this))}getBaseTranslation(){return[0,0]}getRect(e,s){return super.getRect(e,s,m(this,G,Ua).call(this))}onceAdded(e){this.annotationElementId||this.parent.addUndoableEditor(this),e&&this.div.focus()}remove(){m(this,G,Yc).call(this),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(m(this,G,Ki).call(this),this.isAttachedToDOM||this.parent.add(this)))}setParent(e){let s=!1;this.parent&&!e?m(this,G,Yc).call(this):e&&(m(this,G,Ki).call(this,e),s=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(e),this.show(this._isVisible),s&&this.select()}rotate(e){var r,a,o;const{drawLayer:s}=this.parent;let i;n(this,Ot)?(e=(e-this.rotation+360)%360,i=m(r=J,ze,mr).call(r,n(this,Oe).box,e)):i=m(a=J,ze,mr).call(a,[this.x,this.y,this.width,this.height],e),s.updateProperties(n(this,Ee),{bbox:i,root:{"data-main-rotation":e}}),s.updateProperties(n(this,Xt),{bbox:m(o=J,ze,mr).call(o,n(this,fe).box,e),root:{"data-main-rotation":e}})}render(){if(this.div)return this.div;const e=super.render();n(this,ga)&&(e.setAttribute("aria-label",n(this,ga)),e.setAttribute("role","mark")),n(this,Ot)?e.classList.add("free"):this.div.addEventListener("keydown",m(this,G,Lf).bind(this),{signal:this._uiManager._signal});const s=f(this,Wn,document.createElement("div"));e.append(s),s.setAttribute("aria-hidden","true"),s.className="internal",s.style.clipPath=n(this,Vn);const[i,r]=this.parentDimensions;return this.setDims(this.width*i,this.height*r),hh(this,n(this,Wn),["pointerover","pointerleave"]),this.enableEditing(),e}pointerover(){this.isSelected||this.parent?.drawLayer.updateProperties(n(this,Xt),{rootClass:{hovered:!0}})}pointerleave(){this.isSelected||this.parent?.drawLayer.updateProperties(n(this,Xt),{rootClass:{hovered:!1}})}_moveCaret(e){switch(this.parent.unselect(this),e){case 0:case 2:m(this,G,eh).call(this,!0);break;case 1:case 3:m(this,G,eh).call(this,!1);break}}select(){super.select(),n(this,Xt)&&this.parent?.drawLayer.updateProperties(n(this,Xt),{rootClass:{hovered:!1,selected:!0}})}unselect(){super.unselect(),n(this,Xt)&&(this.parent?.drawLayer.updateProperties(n(this,Xt),{rootClass:{selected:!1}}),n(this,Ot)||m(this,G,eh).call(this,!1))}get _mustFixPosition(){return!n(this,Ot)}show(e=this._isVisible){super.show(e),this.parent&&(this.parent.drawLayer.updateProperties(n(this,Ee),{rootClass:{hidden:!e}}),this.parent.drawLayer.updateProperties(n(this,Xt),{rootClass:{hidden:!e}}))}static startHighlighting(e,s,{target:i,x:r,y:a}){const{x:o,y:l,width:h,height:c}=i.getBoundingClientRect(),u=new AbortController,p=e.combinedSignal(u),b=A=>{u.abort(),m(this,ze,Nf).call(this,e,A)};window.addEventListener("blur",b,{signal:p}),window.addEventListener("pointerup",b,{signal:p}),window.addEventListener("pointerdown",Me,{capture:!0,passive:!1,signal:p}),window.addEventListener("contextmenu",rs,{signal:p}),i.addEventListener("pointermove",m(this,ze,Ff).bind(this,e),{signal:p}),this._freeHighlight=new jc({x:r,y:a},[o,l,h,c],e.scale,this._defaultThickness/2,s,.001),{id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0)}static async deserialize(e,s,i){var y,w,v,_;let r=null;if(e instanceof hf){const{data:{quadPoints:E,rect:S,rotation:C,id:T,color:x,opacity:F,popupRef:N},parent:{page:{pageNumber:z}}}=e;r=e={annotationType:j.HIGHLIGHT,color:Array.from(x),opacity:F,quadPoints:E,boxes:null,pageIndex:z-1,rect:S.slice(0),rotation:C,id:T,deleted:!1,popupRef:N}}else if(e instanceof vd){const{data:{inkLists:E,rect:S,rotation:C,id:T,color:x,borderStyle:{rawWidth:F},popupRef:N},parent:{page:{pageNumber:z}}}=e;r=e={annotationType:j.HIGHLIGHT,color:Array.from(x),thickness:F,inkLists:E,boxes:null,pageIndex:z-1,rect:S.slice(0),rotation:C,id:T,deleted:!1,popupRef:N}}const{color:a,quadPoints:o,inkLists:l,opacity:h}=e,c=await super.deserialize(e,s,i);c.color=D.makeHexColor(...a),f(c,ei,h||1),l&&f(c,He,e.thickness),c.annotationElementId=e.id||null,c._initialData=r;const[u,p]=c.pageDimensions,[b,A]=c.pageTranslation;if(o){const E=f(c,ti,[]);for(let S=0;S<o.length;S+=8)E.push({x:(o[S]-b)/u,y:1-(o[S+1]-A)/p,width:(o[S+2]-o[S])/u,height:(o[S+1]-o[S+5])/p});m(y=c,G,qc).call(y),m(w=c,G,Ki).call(w),c.rotate(c.rotation)}else if(l){f(c,Ot,!0);const E=l[0],S={x:E[0]-b,y:p-(E[1]-A)},C=new jc(S,[0,0,u,p],1,n(c,He)/2,!0,.001);for(let F=0,N=E.length;F<N;F+=2)S.x=E[F]-b,S.y=p-(E[F+1]-A),C.add(S);const{id:T,clipPathId:x}=s.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:c.color,"fill-opacity":c._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:C.toSVGPath()}},!0,!0);m(v=c,G,th).call(v,{highlightOutlines:C.getOutlines(),highlightId:T,clipPathId:x}),m(_=c,G,Ki).call(_)}return c}serialize(e=!1){if(this.isEmpty()||e)return null;if(this.deleted)return this.serializeDeleted();const s=this.getRect(0,0),i=gt._colorManager.convert(this.color),r={annotationType:j.HIGHLIGHT,color:i,opacity:n(this,ei),thickness:n(this,He),quadPoints:m(this,G,If).call(this),outlines:m(this,G,Df).call(this,s),pageIndex:this.pageIndex,rect:s,rotation:m(this,G,Ua).call(this),structTreeParentId:this._structTreeParentId};return this.annotationElementId&&!m(this,G,Of).call(this,r)?null:(r.id=this.annotationElementId,r)}renderAnnotationElement(e){return e.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}};ua=new WeakMap,Al=new WeakMap,ti=new WeakMap,Vn=new WeakMap,fa=new WeakMap,fe=new WeakMap,yl=new WeakMap,vl=new WeakMap,Wn=new WeakMap,Oe=new WeakMap,Ee=new WeakMap,Ot=new WeakMap,pa=new WeakMap,ei=new WeakMap,Xt=new WeakMap,ga=new WeakMap,He=new WeakMap,wl=new WeakMap,G=new WeakSet,qc=function(){const e=new zc(n(this,ti),.001);f(this,Oe,e.getOutlines()),[this.x,this.y,this.width,this.height]=n(this,Oe).box;const s=new zc(n(this,ti),.0025,.001,this._uiManager.direction==="ltr");f(this,fe,s.getOutlines());const{lastPoint:i}=n(this,fe);f(this,pa,[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height])},th=function({highlightOutlines:e,highlightId:s,clipPathId:i}){var u,p;if(f(this,Oe,e),f(this,fe,e.getNewOutline(n(this,He)/2+1.5,.0025)),s>=0)f(this,Ee,s),f(this,Vn,i),this.parent.drawLayer.finalizeDraw(s,{bbox:e.box,path:{d:e.toSVGPath()}}),f(this,Xt,this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:n(this,fe).box,path:{d:n(this,fe).toSVGPath()}},!0));else if(this.parent){const b=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(n(this,Ee),{bbox:m(u=J,ze,mr).call(u,n(this,Oe).box,(b-this.rotation+360)%360),path:{d:e.toSVGPath()}}),this.parent.drawLayer.updateProperties(n(this,Xt),{bbox:m(p=J,ze,mr).call(p,n(this,fe).box,b),path:{d:n(this,fe).toSVGPath()}})}const[a,o,l,h]=e.box;switch(this.rotation){case 0:this.x=a,this.y=o,this.width=l,this.height=h;break;case 90:{const[b,A]=this.parentDimensions;this.x=o,this.y=1-a,this.width=l*A/b,this.height=h*b/A;break}case 180:this.x=1-a,this.y=1-o,this.width=l,this.height=h;break;case 270:{const[b,A]=this.parentDimensions;this.x=1-o,this.y=a,this.width=l*A/b,this.height=h*b/A;break}}const{lastPoint:c}=n(this,fe);f(this,pa,[(c[0]-a)/l,(c[1]-o)/h])},Rf=function(e){const s=(a,o)=>{this.color=a,f(this,ei,o),this.parent?.drawLayer.updateProperties(n(this,Ee),{root:{fill:a,"fill-opacity":o}}),n(this,fa)?.updateColor(a)},i=this.color,r=n(this,ei);this.addCommands({cmd:s.bind(this,e,J._defaultOpacity),undo:s.bind(this,i,r),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Y.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(e)},!0)},Mf=function(e){const s=n(this,He),i=r=>{f(this,He,r),m(this,G,kf).call(this,r)};this.addCommands({cmd:i.bind(this,e),undo:i.bind(this,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Y.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:e},!0)},kf=function(e){if(!n(this,Ot))return;m(this,G,th).call(this,{highlightOutlines:n(this,Oe).getNewOutline(e/2)}),this.fixAndSetPosition();const[s,i]=this.parentDimensions;this.setDims(this.width*s,this.height*i)},Yc=function(){n(this,Ee)===null||!this.parent||(this.parent.drawLayer.remove(n(this,Ee)),f(this,Ee,null),this.parent.drawLayer.remove(n(this,Xt)),f(this,Xt,null))},Ki=function(e=this.parent){n(this,Ee)===null&&({id:Jt(this,Ee)._,clipPathId:Jt(this,Vn)._}=e.drawLayer.draw({bbox:n(this,Oe).box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":n(this,ei)},rootClass:{highlight:!0,free:n(this,Ot)},path:{d:n(this,Oe).toSVGPath()}},!1,!0),f(this,Xt,e.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:n(this,Ot)},bbox:n(this,fe).box,path:{d:n(this,fe).toSVGPath()}},n(this,Ot))),n(this,Wn)&&(n(this,Wn).style.clipPath=n(this,Vn)))},ze=new WeakSet,mr=function([e,s,i,r],a){switch(a){case 90:return[1-s-r,e,r,i];case 180:return[1-e-i,1-s-r,i,r];case 270:return[s,1-e-i,r,i]}return[e,s,i,r]},Lf=function(e){J._keyboardManager.exec(this,e)},eh=function(e){if(!n(this,ua))return;const s=window.getSelection();e?s.setPosition(n(this,ua),n(this,Al)):s.setPosition(n(this,yl),n(this,vl))},Ua=function(){return n(this,Ot)?this.rotation:0},If=function(){if(n(this,Ot))return null;const[e,s]=this.pageDimensions,[i,r]=this.pageTranslation,a=n(this,ti),o=new Float32Array(a.length*8);let l=0;for(const{x:h,y:c,width:u,height:p}of a){const b=h*e+i,A=(1-c)*s+r;o[l]=o[l+4]=b,o[l+1]=o[l+3]=A,o[l+2]=o[l+6]=b+u*e,o[l+5]=o[l+7]=A-p*s,l+=8}return o},Df=function(e){return n(this,Oe).serialize(e,m(this,G,Ua).call(this))},Ff=function(e,s){this._freeHighlight.add(s)&&e.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})},Nf=function(e,s){this._freeHighlight.isEmpty()?e.drawLayer.remove(this._freeHighlightId):e.createAndAddNewEditor(s,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""},Of=function(e){const{color:s}=this._initialData;return e.color.some((i,r)=>i!==s[r])},g(J,ze),O(J,"_defaultColor",null),O(J,"_defaultOpacity",1),O(J,"_defaultThickness",12),O(J,"_type","highlight"),O(J,"_editorType",j.HIGHLIGHT),O(J,"_freeHighlightId",-1),O(J,"_freeHighlight",null),O(J,"_freeHighlightClipId","");let ph=J;var Xn;class Xg{constructor(){g(this,Xn,Object.create(null))}updateProperty(t,e){this[t]=e,this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(const[e,s]of Object.entries(t))this.updateProperty(e,s)}updateSVGProperty(t,e){n(this,Xn)[t]=e}toSVGProperties(){const t=n(this,Xn);return f(this,Xn,Object.create(null)),{root:t}}reset(){f(this,Xn,Object.create(null))}updateAll(t=this){this.updateProperties(t)}clone(){it("Not implemented")}}Xn=new WeakMap;var Ce,ma,Lt,qn,Yn,Fi,Ni,Oi,Kn,K,Qc,Jc,Zc,ja,Hf,sh,Va,br;const k=class k extends gt{constructor(e){super(e);g(this,K);g(this,Ce,null);g(this,ma);O(this,"_drawId",null);f(this,ma,e.mustBeCommitted||!1),e.drawOutlines&&(m(this,K,Qc).call(this,e),m(this,K,ja).call(this))}static _mergeSVGProperties(e,s){const i=new Set(Object.keys(e));for(const[r,a]of Object.entries(s))i.has(r)?Object.assign(e[r],a):e[r]=a;return e}static getDefaultDrawingOptions(e){it("Not implemented")}static get typesMap(){it("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(e,s){const i=this.typesMap.get(e);i&&this._defaultDrawingOptions.updateProperty(i,s),this._currentParent&&(n(k,Lt).updateProperty(i,s),this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}updateParams(e,s){const i=this.constructor.typesMap.get(e);i&&this._updateProperty(e,i,s)}static get defaultPropertiesToUpdate(){const e=[],s=this._defaultDrawingOptions;for(const[i,r]of this.typesMap)e.push([i,s[r]]);return e}get propertiesToUpdate(){const e=[],{_drawingOptions:s}=this;for(const[i,r]of this.constructor.typesMap)e.push([i,s[r]]);return e}_updateProperty(e,s,i){const r=this._drawingOptions,a=r[s],o=l=>{r.updateProperty(s,l);const h=n(this,Ce).updateProperty(s,l);h&&m(this,K,Va).call(this,h),this.parent?.drawLayer.updateProperties(this._drawId,r.toSVGProperties())};this.addCommands({cmd:o.bind(this,i),undo:o.bind(this,a),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:e,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){this.parent?.drawLayer.updateProperties(this._drawId,k._mergeSVGProperties(n(this,Ce).getPathResizingSVGProperties(m(this,K,sh).call(this)),{bbox:m(this,K,br).call(this)}))}_onResized(){this.parent?.drawLayer.updateProperties(this._drawId,k._mergeSVGProperties(n(this,Ce).getPathResizedSVGProperties(m(this,K,sh).call(this)),{bbox:m(this,K,br).call(this)}))}_onTranslating(e,s){this.parent?.drawLayer.updateProperties(this._drawId,{bbox:m(this,K,br).call(this,e,s)})}_onTranslated(){this.parent?.drawLayer.updateProperties(this._drawId,k._mergeSVGProperties(n(this,Ce).getPathTranslatedSVGProperties(m(this,K,sh).call(this),this.parentDimensions),{bbox:m(this,K,br).call(this)}))}_onStartDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit(),this.disableEditMode(),this.disableEditing()}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(e){this.annotationElementId||this.parent.addUndoableEditor(this),this._isDraggable=!0,n(this,ma)&&(f(this,ma,!1),this.commit(),this.parent.setSelected(this),e&&this.isOnScreen&&this.div.focus())}remove(){m(this,K,Zc).call(this),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(m(this,K,ja).call(this),m(this,K,Va).call(this,n(this,Ce).box),this.isAttachedToDOM||this.parent.add(this)))}setParent(e){let s=!1;this.parent&&!e?(this._uiManager.removeShouldRescale(this),m(this,K,Zc).call(this)):e&&(this._uiManager.addShouldRescale(this),m(this,K,ja).call(this,e),s=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(e),s&&this.select()}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,k._mergeSVGProperties({bbox:m(this,K,br).call(this)},n(this,Ce).updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&m(this,K,Va).call(this,n(this,Ce).updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){if(this.div)return this.div;const e=super.render();e.classList.add("draw");const s=document.createElement("div");e.append(s),s.setAttribute("aria-hidden","true"),s.className="internal";const[i,r]=this.parentDimensions;return this.setDims(this.width*i,this.height*r),this._uiManager.addShouldRescale(this),this.disableEditing(),e}static createDrawerInstance(e,s,i,r,a){it("Not implemented")}static startDrawing(e,s,i,r){const{target:a,offsetX:o,offsetY:l,pointerId:h,pointerType:c}=r;if(n(k,Ni)&&n(k,Ni)!==c)return;const{viewport:{rotation:u}}=e,{width:p,height:b}=a.getBoundingClientRect(),A=f(k,qn,new AbortController),y=e.combinedSignal(A);if(n(k,Fi)||f(k,Fi,h),n(k,Ni)??f(k,Ni,c),window.addEventListener("pointerup",w=>{n(k,Fi)===w.pointerId?this._endDraw(w):n(k,Oi)?.delete(w.pointerId)},{signal:y}),window.addEventListener("pointercancel",w=>{n(k,Fi)===w.pointerId?this._currentParent.endDrawingSession():n(k,Oi)?.delete(w.pointerId)},{signal:y}),window.addEventListener("pointerdown",w=>{n(k,Ni)===w.pointerType&&((n(k,Oi)||f(k,Oi,new Set)).add(w.pointerId),n(k,Lt).isCancellable()&&(n(k,Lt).removeLastElement(),n(k,Lt).isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)))},{capture:!0,passive:!1,signal:y}),window.addEventListener("contextmenu",rs,{signal:y}),a.addEventListener("pointermove",this._drawMove.bind(this),{signal:y}),a.addEventListener("touchmove",w=>{w.timeStamp===n(k,Kn)&&Me(w)},{signal:y}),e.toggleDrawing(),s._editorUndoBar?.hide(),n(k,Lt)){e.drawLayer.updateProperties(this._currentDrawId,n(k,Lt).startNew(o,l,p,b,u));return}s.updateUIForDefaultProperties(this),f(k,Lt,this.createDrawerInstance(o,l,p,b,u)),f(k,Yn,this.getDefaultDrawingOptions()),this._currentParent=e,{id:this._currentDrawId}=e.drawLayer.draw(this._mergeSVGProperties(n(k,Yn).toSVGProperties(),n(k,Lt).defaultSVGProperties),!0,!1)}static _drawMove(e){if(f(k,Kn,-1),!n(k,Lt))return;const{offsetX:s,offsetY:i,pointerId:r}=e;if(n(k,Fi)===r){if(n(k,Oi)?.size>=1){this._endDraw(e);return}this._currentParent.drawLayer.updateProperties(this._currentDrawId,n(k,Lt).add(s,i)),f(k,Kn,e.timeStamp),Me(e)}}static _cleanup(e){e&&(this._currentDrawId=-1,this._currentParent=null,f(k,Lt,null),f(k,Yn,null),f(k,Ni,null),f(k,Kn,NaN)),n(k,qn)&&(n(k,qn).abort(),f(k,qn,null),f(k,Fi,NaN),f(k,Oi,null))}static _endDraw(e){const s=this._currentParent;if(s){if(s.toggleDrawing(!0),this._cleanup(!1),e&&s.drawLayer.updateProperties(this._currentDrawId,n(k,Lt).end(e.offsetX,e.offsetY)),this.supportMultipleDrawings){const i=n(k,Lt),r=this._currentDrawId,a=i.getLastElement();s.addCommands({cmd:()=>{s.drawLayer.updateProperties(r,i.setLastElement(a))},undo:()=>{s.drawLayer.updateProperties(r,i.removeLastElement())},mustExec:!1,type:Y.DRAW_STEP});return}this.endDrawing(!1)}}static endDrawing(e){const s=this._currentParent;if(!s)return null;if(s.toggleDrawing(!0),s.cleanUndoStack(Y.DRAW_STEP),!n(k,Lt).isEmpty()){const{pageDimensions:[i,r],scale:a}=s,o=s.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:n(k,Lt).getOutlines(i*a,r*a,a,this._INNER_MARGIN),drawingOptions:n(k,Yn),mustBeCommitted:!e});return this._cleanup(!0),o}return s.drawLayer.remove(this._currentDrawId),this._cleanup(!0),null}createDrawingOptions(e){}static deserializeDraw(e,s,i,r,a,o){it("Not implemented")}static async deserialize(e,s,i){var u,p;const{rawDims:{pageWidth:r,pageHeight:a,pageX:o,pageY:l}}=s.viewport,h=this.deserializeDraw(o,l,r,a,this._INNER_MARGIN,e),c=await super.deserialize(e,s,i);return c.createDrawingOptions(e),m(u=c,K,Qc).call(u,{drawOutlines:h}),m(p=c,K,ja).call(p),c.onScaleChanging(),c.rotate(),c}serializeDraw(e){const[s,i]=this.pageTranslation,[r,a]=this.pageDimensions;return n(this,Ce).serialize([s,i,r,a],e)}renderAnnotationElement(e){return e.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}};Ce=new WeakMap,ma=new WeakMap,Lt=new WeakMap,qn=new WeakMap,Yn=new WeakMap,Fi=new WeakMap,Ni=new WeakMap,Oi=new WeakMap,Kn=new WeakMap,K=new WeakSet,Qc=function({drawOutlines:e,drawId:s,drawingOptions:i}){f(this,Ce,e),this._drawingOptions||(this._drawingOptions=i),s>=0?(this._drawId=s,this.parent.drawLayer.finalizeDraw(s,e.defaultProperties)):this._drawId=m(this,K,Jc).call(this,e,this.parent),m(this,K,Va).call(this,e.box)},Jc=function(e,s){const{id:i}=s.drawLayer.draw(k._mergeSVGProperties(this._drawingOptions.toSVGProperties(),e.defaultSVGProperties),!1,!1);return i},Zc=function(){this._drawId===null||!this.parent||(this.parent.drawLayer.remove(this._drawId),this._drawId=null,this._drawingOptions.reset())},ja=function(e=this.parent){if(!(this._drawId!==null&&this.parent===e)){if(this._drawId!==null){this.parent.drawLayer.updateParent(this._drawId,e.drawLayer);return}this._drawingOptions.updateAll(),this._drawId=m(this,K,Jc).call(this,n(this,Ce),e)}},Hf=function([e,s,i,r]){const{parentDimensions:[a,o],rotation:l}=this;switch(l){case 90:return[s,1-e,i*(o/a),r*(a/o)];case 180:return[1-e,1-s,i,r];case 270:return[1-s,e,i*(o/a),r*(a/o)];default:return[e,s,i,r]}},sh=function(){const{x:e,y:s,width:i,height:r,parentDimensions:[a,o],rotation:l}=this;switch(l){case 90:return[1-s,e,i*(a/o),r*(o/a)];case 180:return[1-e,1-s,i,r];case 270:return[s,1-e,i*(a/o),r*(o/a)];default:return[e,s,i,r]}},Va=function(e){if([this.x,this.y,this.width,this.height]=m(this,K,Hf).call(this,e),this.div){this.fixAndSetPosition();const[s,i]=this.parentDimensions;this.setDims(this.width*s,this.height*i)}this._onResized()},br=function(){const{x:e,y:s,width:i,height:r,rotation:a,parentRotation:o,parentDimensions:[l,h]}=this;switch((a*4+o)/90){case 1:return[1-s-r,e,r,i];case 2:return[1-e-i,1-s-r,i,r];case 3:return[s,1-e-i,r,i];case 4:return[e,s-i*(l/h),r*(h/l),i*(l/h)];case 5:return[1-s,e,i*(l/h),r*(h/l)];case 6:return[1-e-r*(h/l),1-s,r*(h/l),i*(l/h)];case 7:return[s-i*(l/h),1-e-r*(h/l),i*(l/h),r*(h/l)];case 8:return[e-i,s-r,i,r];case 9:return[1-s,e-i,r,i];case 10:return[1-e,1-s,i,r];case 11:return[s-r,1-e,r,i];case 12:return[e-r*(h/l),s,r*(h/l),i*(l/h)];case 13:return[1-s-i*(l/h),e-r*(h/l),i*(l/h),r*(h/l)];case 14:return[1-e,1-s-i*(l/h),r*(h/l),i*(l/h)];case 15:return[s,1-e,i*(l/h),r*(h/l)];default:return[e,s,i,r]}},O(k,"_currentDrawId",-1),O(k,"_currentParent",null),g(k,Lt,null),g(k,qn,null),g(k,Yn,null),g(k,Fi,NaN),g(k,Ni,null),g(k,Oi,null),g(k,Kn,NaN),O(k,"_INNER_MARGIN",3);let Kc=k;var _s,It,Dt,Qn,ba,ee,Ht,Be,Jn,Zn,tr,Aa,ih;class qg{constructor(t,e,s,i,r,a){g(this,Aa);g(this,_s,new Float64Array(6));g(this,It);g(this,Dt);g(this,Qn);g(this,ba);g(this,ee);g(this,Ht,"");g(this,Be,0);g(this,Jn,new gh);g(this,Zn);g(this,tr);f(this,Zn,s),f(this,tr,i),f(this,Qn,r),f(this,ba,a),[t,e]=m(this,Aa,ih).call(this,t,e);const o=f(this,It,[NaN,NaN,NaN,NaN,t,e]);f(this,ee,[t,e]),f(this,Dt,[{line:o,points:n(this,ee)}]),n(this,_s).set(o,0)}updateProperty(t,e){t==="stroke-width"&&f(this,ba,e)}isEmpty(){return!n(this,Dt)||n(this,Dt).length===0}isCancellable(){return n(this,ee).length<=10}add(t,e){[t,e]=m(this,Aa,ih).call(this,t,e);const[s,i,r,a]=n(this,_s).subarray(2,6),o=t-r,l=e-a;return Math.hypot(n(this,Zn)*o,n(this,tr)*l)<=2?null:(n(this,ee).push(t,e),isNaN(s)?(n(this,_s).set([r,a,t,e],2),n(this,It).push(NaN,NaN,NaN,NaN,t,e),{path:{d:this.toSVGPath()}}):(isNaN(n(this,_s)[0])&&n(this,It).splice(6,6),n(this,_s).set([s,i,r,a,t,e],0),n(this,It).push(...M.createBezierPoints(s,i,r,a,t,e)),{path:{d:this.toSVGPath()}}))}end(t,e){const s=this.add(t,e);return s||(n(this,ee).length===2?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,s,i,r){f(this,Zn,s),f(this,tr,i),f(this,Qn,r),[t,e]=m(this,Aa,ih).call(this,t,e);const a=f(this,It,[NaN,NaN,NaN,NaN,t,e]);f(this,ee,[t,e]);const o=n(this,Dt).at(-1);return o&&(o.line=new Float32Array(o.line),o.points=new Float32Array(o.points)),n(this,Dt).push({line:a,points:n(this,ee)}),n(this,_s).set(a,0),f(this,Be,0),this.toSVGPath(),null}getLastElement(){return n(this,Dt).at(-1)}setLastElement(t){return n(this,Dt)?(n(this,Dt).push(t),f(this,It,t.line),f(this,ee,t.points),f(this,Be,0),{path:{d:this.toSVGPath()}}):n(this,Jn).setLastElement(t)}removeLastElement(){if(!n(this,Dt))return n(this,Jn).removeLastElement();n(this,Dt).pop(),f(this,Ht,"");for(let t=0,e=n(this,Dt).length;t<e;t++){const{line:s,points:i}=n(this,Dt)[t];f(this,It,s),f(this,ee,i),f(this,Be,0),this.toSVGPath()}return{path:{d:n(this,Ht)}}}toSVGPath(){const t=M.svgRound(n(this,It)[4]),e=M.svgRound(n(this,It)[5]);if(n(this,ee).length===2)return f(this,Ht,`${n(this,Ht)} M ${t} ${e} Z`),n(this,Ht);if(n(this,ee).length<=6){const i=n(this,Ht).lastIndexOf("M");f(this,Ht,`${n(this,Ht).slice(0,i)} M ${t} ${e}`),f(this,Be,6)}if(n(this,ee).length===4){const i=M.svgRound(n(this,It)[10]),r=M.svgRound(n(this,It)[11]);return f(this,Ht,`${n(this,Ht)} L ${i} ${r}`),f(this,Be,12),n(this,Ht)}const s=[];n(this,Be)===0&&(s.push(`M ${t} ${e}`),f(this,Be,6));for(let i=n(this,Be),r=n(this,It).length;i<r;i+=6){const[a,o,l,h,c,u]=n(this,It).slice(i,i+6).map(M.svgRound);s.push(`C${a} ${o} ${l} ${h} ${c} ${u}`)}return f(this,Ht,n(this,Ht)+s.join(" ")),f(this,Be,n(this,It).length),n(this,Ht)}getOutlines(t,e,s,i){const r=n(this,Dt).at(-1);return r.line=new Float32Array(r.line),r.points=new Float32Array(r.points),n(this,Jn).build(n(this,Dt),t,e,s,n(this,Qn),n(this,ba),i),f(this,_s,null),f(this,It,null),f(this,Dt,null),f(this,Ht,null),n(this,Jn)}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}_s=new WeakMap,It=new WeakMap,Dt=new WeakMap,Qn=new WeakMap,ba=new WeakMap,ee=new WeakMap,Ht=new WeakMap,Be=new WeakMap,Jn=new WeakMap,Zn=new WeakMap,tr=new WeakMap,Aa=new WeakSet,ih=function(t,e){return M._normalizePoint(t,e,n(this,Zn),n(this,tr),n(this,Qn))};var se,_l,Sl,xe,Ss,Es,ya,va,wa,$t,Ls,Bf,$f,Gf;const Cd=class Cd extends M{constructor(){super(...arguments);g(this,$t);g(this,se);g(this,_l,0);g(this,Sl);g(this,xe);g(this,Ss);g(this,Es);g(this,ya);g(this,va);g(this,wa)}build(e,s,i,r,a,o,l){f(this,Ss,s),f(this,Es,i),f(this,ya,r),f(this,va,a),f(this,wa,o),f(this,Sl,l??0),f(this,xe,e),m(this,$t,$f).call(this)}setLastElement(e){return n(this,xe).push(e),{path:{d:this.toSVGPath()}}}removeLastElement(){return n(this,xe).pop(),{path:{d:this.toSVGPath()}}}toSVGPath(){const e=[];for(const{line:s}of n(this,xe)){if(e.push(`M${M.svgRound(s[4])} ${M.svgRound(s[5])}`),s.length===6){e.push("Z");continue}if(s.length===12){e.push(`L${M.svgRound(s[10])} ${M.svgRound(s[11])}`);continue}for(let i=6,r=s.length;i<r;i+=6){const[a,o,l,h,c,u]=s.subarray(i,i+6).map(M.svgRound);e.push(`C${a} ${o} ${l} ${h} ${c} ${u}`)}}return e.join("")}serialize([e,s,i,r],a){const o=[],l=[],[h,c,u,p]=m(this,$t,Bf).call(this);let b,A,y,w,v,_,E,S,C;switch(n(this,va)){case 0:C=M._rescale,b=e,A=s+r,y=i,w=-r,v=e+h*i,_=s+(1-c-p)*r,E=e+(h+u)*i,S=s+(1-c)*r;break;case 90:C=M._rescaleAndSwap,b=e,A=s,y=i,w=r,v=e+c*i,_=s+h*r,E=e+(c+p)*i,S=s+(h+u)*r;break;case 180:C=M._rescale,b=e+i,A=s,y=-i,w=r,v=e+(1-h-u)*i,_=s+c*r,E=e+(1-h)*i,S=s+(c+p)*r;break;case 270:C=M._rescaleAndSwap,b=e+i,A=s+r,y=-i,w=-r,v=e+(1-c-p)*i,_=s+(1-h-u)*r,E=e+(1-c)*i,S=s+(1-h)*r;break}for(const{line:T,points:x}of n(this,xe))o.push(C(T,b,A,y,w,a?new Array(T.length):null)),l.push(C(x,b,A,y,w,a?new Array(x.length):null));return{lines:o,points:l,rect:[v,_,E,S]}}static deserialize(e,s,i,r,a,{paths:{lines:o,points:l},rotation:h,thickness:c}){const u=[];let p,b,A,y,w;switch(h){case 0:w=M._rescale,p=-e/i,b=s/r+1,A=1/i,y=-1/r;break;case 90:w=M._rescaleAndSwap,p=-s/r,b=-e/i,A=1/r,y=1/i;break;case 180:w=M._rescale,p=e/i+1,b=-s/r,A=-1/i,y=1/r;break;case 270:w=M._rescaleAndSwap,p=s/r+1,b=e/i+1,A=-1/r,y=-1/i;break}if(!o){o=[];for(const _ of l){const E=_.length;if(E===2){o.push(new Float32Array([NaN,NaN,NaN,NaN,_[0],_[1]]));continue}if(E===4){o.push(new Float32Array([NaN,NaN,NaN,NaN,_[0],_[1],NaN,NaN,NaN,NaN,_[2],_[3]]));continue}const S=new Float32Array(3*(E-2));o.push(S);let[C,T,x,F]=_.subarray(0,4);S.set([NaN,NaN,NaN,NaN,C,T],0);for(let N=4;N<E;N+=2){const z=_[N],U=_[N+1];S.set(M.createBezierPoints(C,T,x,F,z,U),(N-2)*3),[C,T,x,F]=[x,F,z,U]}}}for(let _=0,E=o.length;_<E;_++)u.push({line:w(o[_].map(S=>S??NaN),p,b,A,y),points:w(l[_].map(S=>S??NaN),p,b,A,y)});const v=new Cd;return v.build(u,i,r,1,h,c,a),v}get box(){return n(this,se)}updateProperty(e,s){return e==="stroke-width"?m(this,$t,Gf).call(this,s):null}updateParentDimensions([e,s],i){const[r,a]=m(this,$t,Ls).call(this);f(this,Ss,e),f(this,Es,s),f(this,ya,i);const[o,l]=m(this,$t,Ls).call(this),h=o-r,c=l-a,u=n(this,se);return u[0]-=h,u[1]-=c,u[2]+=2*h,u[3]+=2*c,u}updateRotation(e){return f(this,_l,e),{path:{transform:this.rotationTransform}}}get viewBox(){return n(this,se).map(M.svgRound).join(" ")}get defaultProperties(){const[e,s]=n(this,se);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${M.svgRound(e)} ${M.svgRound(s)}`}}}get rotationTransform(){const[,,e,s]=n(this,se);let i=0,r=0,a=0,o=0,l=0,h=0;switch(n(this,_l)){case 90:r=s/e,a=-e/s,l=e;break;case 180:i=-1,o=-1,l=e,h=s;break;case 270:r=-s/e,a=e/s,h=s;break;default:return""}return`matrix(${i} ${r} ${a} ${o} ${M.svgRound(l)} ${M.svgRound(h)})`}getPathResizingSVGProperties([e,s,i,r]){const[a,o]=m(this,$t,Ls).call(this),[l,h,c,u]=n(this,se);if(Math.abs(c-a)<=M.PRECISION||Math.abs(u-o)<=M.PRECISION){const w=e+i/2-(l+c/2),v=s+r/2-(h+u/2);return{path:{"transform-origin":`${M.svgRound(e)} ${M.svgRound(s)}`,transform:`${this.rotationTransform} translate(${w} ${v})`}}}const p=(i-2*a)/(c-2*a),b=(r-2*o)/(u-2*o),A=c/i,y=u/r;return{path:{"transform-origin":`${M.svgRound(l)} ${M.svgRound(h)}`,transform:`${this.rotationTransform} scale(${A} ${y}) translate(${M.svgRound(a)} ${M.svgRound(o)}) scale(${p} ${b}) translate(${M.svgRound(-a)} ${M.svgRound(-o)})`}}}getPathResizedSVGProperties([e,s,i,r]){const[a,o]=m(this,$t,Ls).call(this),l=n(this,se),[h,c,u,p]=l;if(l[0]=e,l[1]=s,l[2]=i,l[3]=r,Math.abs(u-a)<=M.PRECISION||Math.abs(p-o)<=M.PRECISION){const v=e+i/2-(h+u/2),_=s+r/2-(c+p/2);for(const{line:E,points:S}of n(this,xe))M._translate(E,v,_,E),M._translate(S,v,_,S);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${M.svgRound(e)} ${M.svgRound(s)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}const b=(i-2*a)/(u-2*a),A=(r-2*o)/(p-2*o),y=-b*(h+a)+e+a,w=-A*(c+o)+s+o;if(b!==1||A!==1||y!==0||w!==0)for(const{line:v,points:_}of n(this,xe))M._rescale(v,y,w,b,A,v),M._rescale(_,y,w,b,A,_);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${M.svgRound(e)} ${M.svgRound(s)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([e,s],i){const[r,a]=i,o=n(this,se),l=e-o[0],h=s-o[1];if(n(this,Ss)===r&&n(this,Es)===a)for(const{line:c,points:u}of n(this,xe))M._translate(c,l,h,c),M._translate(u,l,h,u);else{const c=n(this,Ss)/r,u=n(this,Es)/a;f(this,Ss,r),f(this,Es,a);for(const{line:p,points:b}of n(this,xe))M._rescale(p,l,h,c,u,p),M._rescale(b,l,h,c,u,b);o[2]*=c,o[3]*=u}return o[0]=e,o[1]=s,{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${M.svgRound(e)} ${M.svgRound(s)}`}}}get defaultSVGProperties(){const e=n(this,se);return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${M.svgRound(e[0])} ${M.svgRound(e[1])}`,transform:this.rotationTransform||null},bbox:e}}};se=new WeakMap,_l=new WeakMap,Sl=new WeakMap,xe=new WeakMap,Ss=new WeakMap,Es=new WeakMap,ya=new WeakMap,va=new WeakMap,wa=new WeakMap,$t=new WeakSet,Ls=function(e=n(this,wa)){const s=n(this,Sl)+e/2*n(this,ya);return n(this,va)%180===0?[s/n(this,Ss),s/n(this,Es)]:[s/n(this,Es),s/n(this,Ss)]},Bf=function(){const[e,s,i,r]=n(this,se),[a,o]=m(this,$t,Ls).call(this,0);return[e+a,s+o,i-2*a,r-2*o]},$f=function(){const e=f(this,se,new Float32Array([1/0,1/0,-1/0,-1/0]));for(const{line:r}of n(this,xe)){if(r.length<=12){for(let l=4,h=r.length;l<h;l+=6){const[c,u]=r.subarray(l,l+2);e[0]=Math.min(e[0],c),e[1]=Math.min(e[1],u),e[2]=Math.max(e[2],c),e[3]=Math.max(e[3],u)}continue}let a=r[4],o=r[5];for(let l=6,h=r.length;l<h;l+=6){const[c,u,p,b,A,y]=r.subarray(l,l+6);D.bezierBoundingBox(a,o,c,u,p,b,A,y,e),a=A,o=y}}const[s,i]=m(this,$t,Ls).call(this);e[0]=Math.min(1,Math.max(0,e[0]-s)),e[1]=Math.min(1,Math.max(0,e[1]-i)),e[2]=Math.min(1,Math.max(0,e[2]+s)),e[3]=Math.min(1,Math.max(0,e[3]+i)),e[2]-=e[0],e[3]-=e[1]},Gf=function(e){const[s,i]=m(this,$t,Ls).call(this);f(this,wa,e);const[r,a]=m(this,$t,Ls).call(this),[o,l]=[r-s,a-i],h=n(this,se);return h[0]-=o,h[1]-=l,h[2]+=2*o,h[3]+=2*l,h};let gh=Cd;var _a;const xd=class xd extends Xg{constructor(e){super();g(this,_a);f(this,_a,e),super.updateProperties({fill:"none",stroke:gt._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(e,s){e==="stroke-width"&&(s??(s=this["stroke-width"]),s*=n(this,_a).realScale),super.updateSVGProperty(e,s)}clone(){const e=new xd(n(this,_a));return e.updateAll(this),e}};_a=new WeakMap;let td=xd;var Ih,zf;const yr=class yr extends Kc{constructor(e){super({...e,name:"inkEditor"});g(this,Ih);this._willKeepAspectRatio=!0}static initialize(e,s){gt.initialize(e,s),this._defaultDrawingOptions=new td(s.viewParameters)}static getDefaultDrawingOptions(e){const s=this._defaultDrawingOptions.clone();return s.updateProperties(e),s}static get supportMultipleDrawings(){return!0}static get typesMap(){return q(this,"typesMap",new Map([[Y.INK_THICKNESS,"stroke-width"],[Y.INK_COLOR,"stroke"],[Y.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(e,s,i,r,a){return new qg(e,s,i,r,a,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(e,s,i,r,a,o){return gh.deserialize(e,s,i,r,a,o)}static async deserialize(e,s,i){let r=null;if(e instanceof vd){const{data:{inkLists:o,rect:l,rotation:h,id:c,color:u,opacity:p,borderStyle:{rawWidth:b},popupRef:A},parent:{page:{pageNumber:y}}}=e;r=e={annotationType:j.INK,color:Array.from(u),thickness:b,opacity:p,paths:{points:o},boxes:null,pageIndex:y-1,rect:l.slice(0),rotation:h,id:c,deleted:!1,popupRef:A}}const a=await super.deserialize(e,s,i);return a.annotationElementId=e.id||null,a._initialData=r,a}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();const{_drawId:e,_drawingOptions:s,parent:i}=this;s.updateSVGProperty("stroke-width"),i.drawLayer.updateProperties(e,s.toSVGProperties())}static onScaleChangingWhenDrawing(){const e=this._currentParent;e&&(super.onScaleChangingWhenDrawing(),this._defaultDrawingOptions.updateSVGProperty("stroke-width"),e.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}createDrawingOptions({color:e,thickness:s,opacity:i}){this._drawingOptions=yr.getDefaultDrawingOptions({stroke:D.makeHexColor(...e),"stroke-width":s,"stroke-opacity":i})}serialize(e=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const{lines:s,points:i,rect:r}=this.serializeDraw(e),{_drawingOptions:{stroke:a,"stroke-opacity":o,"stroke-width":l}}=this,h={annotationType:j.INK,color:gt._colorManager.convert(a),opacity:o,thickness:l,paths:{lines:s,points:i},pageIndex:this.pageIndex,rect:r,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?h:this.annotationElementId&&!m(this,Ih,zf).call(this,h)?null:(h.id=this.annotationElementId,h)}renderAnnotationElement(e){const{points:s,rect:i}=this.serializeDraw(!1);return e.updateEdited({rect:i,thickness:this._drawingOptions["stroke-width"],points:s}),null}};Ih=new WeakSet,zf=function(e){const{color:s,thickness:i,opacity:r,pageIndex:a}=this._initialData;return this._hasBeenMoved||this._hasBeenResized||e.color.some((o,l)=>o!==s[l])||e.thickness!==i||e.opacity!==r||e.pageIndex!==a},O(yr,"_type","ink"),O(yr,"_editorType",j.INK),O(yr,"_defaultDrawingOptions",null);let ed=yr;var pt,Bt,Hi,si,Bi,Sa,Cs,xs,Te,Ea,Q,Wa,Xa,nh,id,rh,nd,ah,Uf;const Ya=class Ya extends gt{constructor(e){super({...e,name:"stampEditor"});g(this,Q);g(this,pt,null);g(this,Bt,null);g(this,Hi,null);g(this,si,null);g(this,Bi,null);g(this,Sa,"");g(this,Cs,null);g(this,xs,null);g(this,Te,!1);g(this,Ea,!1);f(this,si,e.bitmapUrl),f(this,Bi,e.bitmapFile)}static initialize(e,s){gt.initialize(e,s)}static get supportedTypes(){return q(this,"supportedTypes",["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"].map(s=>`image/${s}`))}static get supportedTypesStr(){return q(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(e){return this.supportedTypes.includes(e)}static paste(e,s){s.pasteEditor(j.STAMP,{bitmapFile:e.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1),super.altTextFinish()}get telemetryFinalData(){return{type:"stamp",hasAltText:!!this.altTextData?.altText}}static computeTelemetryFinalData(e){const s=e.get("hasAltText");return{hasAltText:s.get(!0)??0,hasNoAltText:s.get(!1)??0}}async mlGuessAltText(e=null,s=!0){if(this.hasAltTextData())return null;const{mlManager:i}=this._uiManager;if(!i)throw new Error("No ML.");if(!await i.isEnabledFor("altText"))throw new Error("ML isn't enabled for alt text.");const{data:r,width:a,height:o}=e||this.copyCanvas(null,null,!0).imageData,l=await i.guess({name:"altText",request:{data:r,width:a,height:o,channels:r.length/(a*o)}});if(!l)throw new Error("No response from the AI service.");if(l.error)throw new Error("Error from the AI service.");if(l.cancel)return null;if(!l.output)throw new Error("No valid response from the AI service.");const h=l.output;return await this.setGuessedAltText(h),s&&!this.hasAltTextData()&&(this.altTextData={alt:h,decorative:!1}),h}remove(){n(this,Bt)&&(f(this,pt,null),this._uiManager.imageManager.deleteId(n(this,Bt)),n(this,Cs)?.remove(),f(this,Cs,null),n(this,xs)&&(clearTimeout(n(this,xs)),f(this,xs,null))),super.remove()}rebuild(){if(!this.parent){n(this,Bt)&&m(this,Q,nh).call(this);return}super.rebuild(),this.div!==null&&(n(this,Bt)&&n(this,Cs)===null&&m(this,Q,nh).call(this),this.isAttachedToDOM||this.parent.add(this))}onceAdded(e){this._isDraggable=!0,e&&this.div.focus()}isEmpty(){return!(n(this,Hi)||n(this,pt)||n(this,si)||n(this,Bi)||n(this,Bt))}get isResizable(){return!0}render(){if(this.div)return this.div;let e,s;if(this.width&&(e=this.x,s=this.y),super.render(),this.div.hidden=!0,this.div.setAttribute("role","figure"),this.addAltTextButton(),n(this,pt)?m(this,Q,id).call(this):m(this,Q,nh).call(this),this.width&&!this.annotationElementId){const[i,r]=this.parentDimensions;this.setAt(e*i,s*r,this.width*i,this.height*r)}return this._uiManager.addShouldRescale(this),this.div}_onResized(){this.onScaleChanging()}onScaleChanging(){if(!this.parent)return;n(this,xs)!==null&&clearTimeout(n(this,xs)),f(this,xs,setTimeout(()=>{f(this,xs,null),m(this,Q,nd).call(this)},200))}copyCanvas(e,s,i=!1){e||(e=224);const{width:r,height:a}=n(this,pt),o=new tc;let l=n(this,pt),h=r,c=a,u=null;if(s){if(r>s||a>s){const x=Math.min(s/r,s/a);h=Math.floor(r*x),c=Math.floor(a*x)}u=document.createElement("canvas");const b=u.width=Math.ceil(h*o.sx),A=u.height=Math.ceil(c*o.sy);n(this,Te)||(l=m(this,Q,rh).call(this,b,A));const y=u.getContext("2d");y.filter=this._uiManager.hcmFilter;let w="white",v="#cfcfd8";this._uiManager.hcmFilter!=="none"?v="black":window.matchMedia?.("(prefers-color-scheme: dark)").matches&&(w="#8f8f9d",v="#42414d");const _=15,E=_*o.sx,S=_*o.sy,C=new OffscreenCanvas(E*2,S*2),T=C.getContext("2d");T.fillStyle=w,T.fillRect(0,0,E*2,S*2),T.fillStyle=v,T.fillRect(0,0,E,S),T.fillRect(E,S,E,S),y.fillStyle=y.createPattern(C,"repeat"),y.fillRect(0,0,b,A),y.drawImage(l,0,0,l.width,l.height,0,0,b,A)}let p=null;if(i){let b,A;if(o.symmetric&&l.width<e&&l.height<e)b=l.width,A=l.height;else if(l=n(this,pt),r>e||a>e){const v=Math.min(e/r,e/a);b=Math.floor(r*v),A=Math.floor(a*v),n(this,Te)||(l=m(this,Q,rh).call(this,b,A))}const w=new OffscreenCanvas(b,A).getContext("2d",{willReadFrequently:!0});w.drawImage(l,0,0,l.width,l.height,0,0,b,A),p={width:b,height:A,data:w.getImageData(0,0,b,A).data}}return{canvas:u,width:h,height:c,imageData:p}}getImageForAltText(){return n(this,Cs)}static async deserialize(e,s,i){let r=null;if(e instanceof cf){const{data:{rect:y,rotation:w,id:v,structParent:_,popupRef:E},container:S,parent:{page:{pageNumber:C}}}=e,T=S.querySelector("canvas"),x=i.imageManager.getFromCanvas(S.id,T);T.remove();const F=(await s._structTree.getAriaAttributes(`${ud}${v}`))?.get("aria-label")||"";r=e={annotationType:j.STAMP,bitmapId:x.id,bitmap:x.bitmap,pageIndex:C-1,rect:y.slice(0),rotation:w,id:v,deleted:!1,accessibilityData:{decorative:!1,altText:F},isSvg:!1,structParent:_,popupRef:E}}const a=await super.deserialize(e,s,i),{rect:o,bitmap:l,bitmapUrl:h,bitmapId:c,isSvg:u,accessibilityData:p}=e;c&&i.imageManager.isValidId(c)?(f(a,Bt,c),l&&f(a,pt,l)):f(a,si,h),f(a,Te,u);const[b,A]=a.pageDimensions;return a.width=(o[2]-o[0])/b,a.height=(o[3]-o[1])/A,a.annotationElementId=e.id||null,p&&(a.altTextData=p),a._initialData=r,f(a,Ea,!!r),a}serialize(e=!1,s=null){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const i={annotationType:j.STAMP,bitmapId:n(this,Bt),pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:n(this,Te),structTreeParentId:this._structTreeParentId};if(e)return i.bitmapUrl=m(this,Q,ah).call(this,!0),i.accessibilityData=this.serializeAltText(!0),i;const{decorative:r,altText:a}=this.serializeAltText(!1);if(!r&&a&&(i.accessibilityData={type:"Figure",alt:a}),this.annotationElementId){const l=m(this,Q,Uf).call(this,i);if(l.isSame)return null;l.isSameAltText?delete i.accessibilityData:i.accessibilityData.structParent=this._initialData.structParent??-1}if(i.id=this.annotationElementId,s===null)return i;s.stamps||(s.stamps=new Map);const o=n(this,Te)?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(!s.stamps.has(n(this,Bt)))s.stamps.set(n(this,Bt),{area:o,serialized:i}),i.bitmap=m(this,Q,ah).call(this,!1);else if(n(this,Te)){const l=s.stamps.get(n(this,Bt));o>l.area&&(l.area=o,l.serialized.bitmap.close(),l.serialized.bitmap=m(this,Q,ah).call(this,!1))}return i}renderAnnotationElement(e){return e.updateEdited({rect:this.getRect(0,0)}),null}};pt=new WeakMap,Bt=new WeakMap,Hi=new WeakMap,si=new WeakMap,Bi=new WeakMap,Sa=new WeakMap,Cs=new WeakMap,xs=new WeakMap,Te=new WeakMap,Ea=new WeakMap,Q=new WeakSet,Wa=function(e,s=!1){if(!e){this.remove();return}f(this,pt,e.bitmap),s||(f(this,Bt,e.id),f(this,Te,e.isSvg)),e.file&&f(this,Sa,e.file.name),m(this,Q,id).call(this)},Xa=function(){if(f(this,Hi,null),this._uiManager.enableWaiting(!1),!!n(this,Cs)){if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&n(this,pt)){this._editToolbar.hide(),this._uiManager.editAltText(this,!0);return}if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&n(this,pt)){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch{}}this.div.focus()}},nh=function(){if(n(this,Bt)){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(n(this,Bt)).then(i=>m(this,Q,Wa).call(this,i,!0)).finally(()=>m(this,Q,Xa).call(this));return}if(n(this,si)){const i=n(this,si);f(this,si,null),this._uiManager.enableWaiting(!0),f(this,Hi,this._uiManager.imageManager.getFromUrl(i).then(r=>m(this,Q,Wa).call(this,r)).finally(()=>m(this,Q,Xa).call(this)));return}if(n(this,Bi)){const i=n(this,Bi);f(this,Bi,null),this._uiManager.enableWaiting(!0),f(this,Hi,this._uiManager.imageManager.getFromFile(i).then(r=>m(this,Q,Wa).call(this,r)).finally(()=>m(this,Q,Xa).call(this)));return}const e=document.createElement("input");e.type="file",e.accept=Ya.supportedTypesStr;const s=this._uiManager._signal;f(this,Hi,new Promise(i=>{e.addEventListener("change",async()=>{if(!e.files||e.files.length===0)this.remove();else{this._uiManager.enableWaiting(!0);const r=await this._uiManager.imageManager.getFromFile(e.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}}),m(this,Q,Wa).call(this,r)}i()},{signal:s}),e.addEventListener("cancel",()=>{this.remove(),i()},{signal:s})}).finally(()=>m(this,Q,Xa).call(this))),e.click()},id=function(){const{div:e}=this;let{width:s,height:i}=n(this,pt);const[r,a]=this.pageDimensions,o=.75;if(this.width)s=this.width*r,i=this.height*a;else if(s>o*r||i>o*a){const u=Math.min(o*r/s,o*a/i);s*=u,i*=u}const[l,h]=this.parentDimensions;this.setDims(s*l/r,i*h/a),this._uiManager.enableWaiting(!1);const c=f(this,Cs,document.createElement("canvas"));c.setAttribute("role","img"),this.addContainer(c),this.width=s/r,this.height=i/a,this._initialOptions?.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,(!this._uiManager.useNewAltTextWhenAddingImage||!this._uiManager.useNewAltTextFlow||this.annotationElementId)&&(e.hidden=!1),m(this,Q,nd).call(this),n(this,Ea)||(this.parent.addUndoableEditor(this),f(this,Ea,!0)),this._reportTelemetry({action:"inserted_image"}),n(this,Sa)&&c.setAttribute("aria-label",n(this,Sa))},rh=function(e,s){const{width:i,height:r}=n(this,pt);let a=i,o=r,l=n(this,pt);for(;a>2*e||o>2*s;){const h=a,c=o;a>2*e&&(a=a>=16384?Math.floor(a/2)-1:Math.ceil(a/2)),o>2*s&&(o=o>=16384?Math.floor(o/2)-1:Math.ceil(o/2));const u=new OffscreenCanvas(a,o);u.getContext("2d").drawImage(l,0,0,h,c,0,0,a,o),l=u.transferToImageBitmap()}return l},nd=function(){const[e,s]=this.parentDimensions,{width:i,height:r}=this,a=new tc,o=Math.ceil(i*e*a.sx),l=Math.ceil(r*s*a.sy),h=n(this,Cs);if(!h||h.width===o&&h.height===l)return;h.width=o,h.height=l;const c=n(this,Te)?n(this,pt):m(this,Q,rh).call(this,o,l),u=h.getContext("2d");u.filter=this._uiManager.hcmFilter,u.drawImage(c,0,0,c.width,c.height,0,0,o,l)},ah=function(e){if(e){if(n(this,Te)){const r=this._uiManager.imageManager.getSvgUrl(n(this,Bt));if(r)return r}const s=document.createElement("canvas");return{width:s.width,height:s.height}=n(this,pt),s.getContext("2d").drawImage(n(this,pt),0,0),s.toDataURL()}if(n(this,Te)){const[s,i]=this.pageDimensions,r=Math.round(this.width*s*Vi.PDF_TO_CSS_UNITS),a=Math.round(this.height*i*Vi.PDF_TO_CSS_UNITS),o=new OffscreenCanvas(r,a);return o.getContext("2d").drawImage(n(this,pt),0,0,n(this,pt).width,n(this,pt).height,0,0,r,a),o.transferToImageBitmap()}return structuredClone(n(this,pt))},Uf=function(e){const{pageIndex:s,accessibilityData:{altText:i}}=this._initialData,r=e.pageIndex===s,a=(e.accessibilityData?.alt||"")===i;return{isSame:!this._hasBeenMoved&&!this._hasBeenResized&&r&&a,isSameAltText:a}},O(Ya,"_type","stamp"),O(Ya,"_editorType",j.STAMP);let sd=Ya;var er,Ca,Ts,$i,ii,$e,Gi,xa,sr,ss,ni,qt,ri,I,zi,ct,jf,cs,ad,od,oh;const je=class je{constructor({uiManager:t,pageIndex:e,div:s,structTreeLayer:i,accessibilityManager:r,annotationLayer:a,drawLayer:o,textLayer:l,viewport:h,l10n:c}){g(this,ct);g(this,er);g(this,Ca,!1);g(this,Ts,null);g(this,$i,null);g(this,ii,null);g(this,$e,new Map);g(this,Gi,!1);g(this,xa,!1);g(this,sr,!1);g(this,ss,null);g(this,ni,null);g(this,qt,null);g(this,ri,null);g(this,I);const u=[...n(je,zi).values()];if(!je._initialized){je._initialized=!0;for(const p of u)p.initialize(c,t)}t.registerEditorTypes(u),f(this,I,t),this.pageIndex=e,this.div=s,f(this,er,r),f(this,Ts,a),this.viewport=h,f(this,qt,l),this.drawLayer=o,this._structTree=i,n(this,I).addLayer(this)}get isEmpty(){return n(this,$e).size===0}get isInvisible(){return this.isEmpty&&n(this,I).getMode()===j.NONE}updateToolbar(t){n(this,I).updateToolbar(t)}updateMode(t=n(this,I).getMode()){switch(m(this,ct,oh).call(this),t){case j.NONE:this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),this.disableClick();return;case j.INK:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick();break;case j.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const s of n(je,zi).values())e.toggle(`${s._type}Editing`,t===s._editorType);this.div.hidden=!1}hasTextLayer(t){return t===n(this,qt)?.div}setEditingState(t){n(this,I).setEditingState(t)}addCommands(t){n(this,I).addCommands(t)}cleanUndoStack(t){n(this,I).cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){n(this,Ts)?.div.classList.toggle("disabled",!t)}async enable(){f(this,sr,!0),this.div.tabIndex=0,this.togglePointerEvents(!0);const t=new Set;for(const s of n(this,$e).values())s.enableEditing(),s.show(!0),s.annotationElementId&&(n(this,I).removeChangedExistingAnnotation(s),t.add(s.annotationElementId));if(!n(this,Ts)){f(this,sr,!1);return}const e=n(this,Ts).getEditableAnnotations();for(const s of e){if(s.hide(),n(this,I).isDeletedAnnotationElement(s.data.id)||t.has(s.data.id))continue;const i=await this.deserialize(s);i&&(this.addOrRebuild(i),i.enableEditing())}f(this,sr,!1)}disable(){f(this,xa,!0),this.div.tabIndex=-1,this.togglePointerEvents(!1);const t=new Map,e=new Map;for(const i of n(this,$e).values())if(i.disableEditing(),!!i.annotationElementId){if(i.serialize()!==null){t.set(i.annotationElementId,i);continue}else e.set(i.annotationElementId,i);this.getEditableAnnotation(i.annotationElementId)?.show(),i.remove()}if(n(this,Ts)){const i=n(this,Ts).getEditableAnnotations();for(const r of i){const{id:a}=r.data;if(n(this,I).isDeletedAnnotationElement(a))continue;let o=e.get(a);if(o){o.resetAnnotationElement(r),o.show(!1),r.show();continue}o=t.get(a),o&&(n(this,I).addChangedExistingAnnotation(o),o.renderAnnotationElement(r)&&o.show(!1)),r.show()}}m(this,ct,oh).call(this),this.isEmpty&&(this.div.hidden=!0);const{classList:s}=this.div;for(const i of n(je,zi).values())s.remove(`${i._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),f(this,xa,!1)}getEditableAnnotation(t){return n(this,Ts)?.getEditableAnnotation(t)||null}setActiveEditor(t){n(this,I).getActive()!==t&&n(this,I).setActiveEditor(t)}enableTextSelection(){if(this.div.tabIndex=-1,n(this,qt)?.div&&!n(this,ri)){f(this,ri,new AbortController);const t=n(this,I).combinedSignal(n(this,ri));n(this,qt).div.addEventListener("pointerdown",m(this,ct,jf).bind(this),{signal:t}),n(this,qt).div.classList.add("highlighting")}}disableTextSelection(){this.div.tabIndex=0,n(this,qt)?.div&&n(this,ri)&&(n(this,ri).abort(),f(this,ri,null),n(this,qt).div.classList.remove("highlighting"))}enableClick(){if(n(this,$i))return;f(this,$i,new AbortController);const t=n(this,I).combinedSignal(n(this,$i));this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});const e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t}),this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){n(this,$i)?.abort(),f(this,$i,null)}attach(t){n(this,$e).set(t.id,t);const{annotationElementId:e}=t;e&&n(this,I).isDeletedAnnotationElement(e)&&n(this,I).removeDeletedAnnotationElement(t)}detach(t){n(this,$e).delete(t.id),n(this,er)?.removePointerInTextLayer(t.contentDiv),!n(this,xa)&&t.annotationElementId&&n(this,I).addDeletedAnnotationElement(t)}remove(t){this.detach(t),n(this,I).removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1}changeParent(t){t.parent!==this&&(t.parent&&t.annotationElementId&&(n(this,I).addDeletedAnnotationElement(t.annotationElementId),gt.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),t.parent?.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(!(t.parent===this&&t.isAttachedToDOM)){if(this.changeParent(t),n(this,I).addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(!n(this,sr)),n(this,I).addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;t.div.contains(e)&&!n(this,ii)&&(t._focusEventsAllowed=!1,f(this,ii,setTimeout(()=>{f(this,ii,null),t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:n(this,I)._signal}),e.focus())},0))),t._structTreeParentId=n(this,er)?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||(t.parent=this),t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){const e=()=>t._uiManager.rebuild(t),s=()=>{t.remove()};this.addCommands({cmd:e,undo:s,mustExec:!1})}getNextId(){return n(this,I).getId()}combinedSignal(t){return n(this,I).combinedSignal(t)}canCreateNewEmptyEditor(){return n(this,ct,cs)?.canCreateNewEmptyEditor()}pasteEditor(t,e){n(this,I).updateToolbar(t),n(this,I).updateMode(t);const{offsetX:s,offsetY:i}=m(this,ct,od).call(this),r=this.getNextId(),a=m(this,ct,ad).call(this,{parent:this,id:r,x:s,y:i,uiManager:n(this,I),isCentered:!0,...e});a&&this.add(a)}async deserialize(t){return await n(je,zi).get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,n(this,I))||null}createAndAddNewEditor(t,e,s={}){const i=this.getNextId(),r=m(this,ct,ad).call(this,{parent:this,id:i,x:t.offsetX,y:t.offsetY,uiManager:n(this,I),isCentered:e,...s});return r&&this.add(r),r}addNewEditor(){this.createAndAddNewEditor(m(this,ct,od).call(this),!0)}setSelected(t){n(this,I).setSelected(t)}toggleSelected(t){n(this,I).toggleSelected(t)}unselect(t){n(this,I).unselect(t)}pointerup(t){const{isMac:e}=ne.platform;if(!(t.button!==0||t.ctrlKey&&e)&&t.target===this.div&&n(this,Gi)&&(f(this,Gi,!1),!(n(this,ct,cs)?.isDrawer&&n(this,ct,cs).supportMultipleDrawings))){if(!n(this,Ca)){f(this,Ca,!0);return}if(n(this,I).getMode()===j.STAMP){n(this,I).unselectAll();return}this.createAndAddNewEditor(t,!1)}}pointerdown(t){if(n(this,I).getMode()===j.HIGHLIGHT&&this.enableTextSelection(),n(this,Gi)){f(this,Gi,!1);return}const{isMac:e}=ne.platform;if(t.button!==0||t.ctrlKey&&e||t.target!==this.div)return;if(f(this,Gi,!0),n(this,ct,cs)?.isDrawer){this.startDrawingSession(t);return}const s=n(this,I).getActive();f(this,Ca,!s||s.isEmpty())}startDrawingSession(t){if(this.div.focus(),n(this,ss)){n(this,ct,cs).startDrawing(this,n(this,I),!1,t);return}n(this,I).setCurrentDrawingSession(this),f(this,ss,new AbortController);const e=n(this,I).combinedSignal(n(this,ss));this.div.addEventListener("blur",({relatedTarget:s})=>{s&&!this.div.contains(s)&&(f(this,ni,null),this.commitOrRemove())},{signal:e}),n(this,ct,cs).startDrawing(this,n(this,I),!1,t)}pause(t){if(t){const{activeElement:e}=document;this.div.contains(e)&&f(this,ni,e);return}n(this,ni)&&setTimeout(()=>{n(this,ni)?.focus(),f(this,ni,null)},0)}endDrawingSession(t=!1){return n(this,ss)?(n(this,I).setCurrentDrawingSession(null),n(this,ss).abort(),f(this,ss,null),f(this,ni,null),n(this,ct,cs).endDrawing(t)):null}findNewParent(t,e,s){const i=n(this,I).findParent(e,s);return i===null||i===this?!1:(i.changeParent(t),!0)}commitOrRemove(){return n(this,ss)?(this.endDrawingSession(),!0):!1}onScaleChanging(){n(this,ss)&&n(this,ct,cs).onScaleChangingWhenDrawing(this)}destroy(){this.commitOrRemove(),n(this,I).getActive()?.parent===this&&(n(this,I).commitOrRemove(),n(this,I).setActiveEditor(null)),n(this,ii)&&(clearTimeout(n(this,ii)),f(this,ii,null));for(const t of n(this,$e).values())n(this,er)?.removePointerInTextLayer(t.contentDiv),t.setParent(null),t.isAttachedToDOM=!1,t.div.remove();this.div=null,n(this,$e).clear(),n(this,I).removeLayer(this)}render({viewport:t}){this.viewport=t,nr(this.div,t);for(const e of n(this,I).getEditors(this.pageIndex))this.add(e),e.rebuild();this.updateMode()}update({viewport:t}){n(this,I).commitOrRemove(),m(this,ct,oh).call(this);const e=this.viewport.rotation,s=t.rotation;if(this.viewport=t,nr(this.div,{rotation:s}),e!==s)for(const i of n(this,$e).values())i.rotate(s)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return n(this,I).viewParameters.realScale}};er=new WeakMap,Ca=new WeakMap,Ts=new WeakMap,$i=new WeakMap,ii=new WeakMap,$e=new WeakMap,Gi=new WeakMap,xa=new WeakMap,sr=new WeakMap,ss=new WeakMap,ni=new WeakMap,qt=new WeakMap,ri=new WeakMap,I=new WeakMap,zi=new WeakMap,ct=new WeakSet,jf=function(t){n(this,I).unselectAll();const{target:e}=t;if(e===n(this,qt).div||(e.getAttribute("role")==="img"||e.classList.contains("endOfContent"))&&n(this,qt).div.contains(e)){const{isMac:s}=ne.platform;if(t.button!==0||t.ctrlKey&&s)return;n(this,I).showAllEditors("highlight",!0,!0),n(this,qt).div.classList.add("free"),this.toggleDrawing(),ph.startHighlighting(this,n(this,I).direction==="ltr",{target:n(this,qt).div,x:t.x,y:t.y}),n(this,qt).div.addEventListener("pointerup",()=>{n(this,qt).div.classList.remove("free"),this.toggleDrawing(!0)},{once:!0,signal:n(this,I)._signal}),t.preventDefault()}},cs=function(){return n(je,zi).get(n(this,I).getMode())},ad=function(t){const e=n(this,ct,cs);return e?new e.prototype.constructor(t):null},od=function(){const{x:t,y:e,width:s,height:i}=this.div.getBoundingClientRect(),r=Math.max(0,t),a=Math.max(0,e),o=Math.min(window.innerWidth,t+s),l=Math.min(window.innerHeight,e+i),h=(r+o)/2-t,c=(a+l)/2-e,[u,p]=this.viewport.rotation%180===0?[h,c]:[c,h];return{offsetX:u,offsetY:p}},oh=function(){for(const t of n(this,$e).values())t.isEmpty()&&t.remove()},O(je,"_initialized",!1),g(je,zi,new Map([$c,ed,sd,ph].map(t=>[t._editorType,t])));let rd=je;var is,El,ie,ir,Dh,Vf,Ps,hd,Wf,cd;const jt=class jt{constructor({pageIndex:t}){g(this,Ps);g(this,is,null);g(this,El,0);g(this,ie,new Map);g(this,ir,new Map);this.pageIndex=t}setParent(t){if(!n(this,is)){f(this,is,t);return}if(n(this,is)!==t){if(n(this,ie).size>0)for(const e of n(this,ie).values())e.remove(),t.append(e);f(this,is,t)}}static get _svgFactory(){return q(this,"_svgFactory",new yd)}draw(t,e=!1,s=!1){const i=Jt(this,El)._++,r=m(this,Ps,hd).call(this),a=jt._svgFactory.createElement("defs");r.append(a);const o=jt._svgFactory.createElement("path");a.append(o);const l=`path_p${this.pageIndex}_${i}`;o.setAttribute("id",l),o.setAttribute("vector-effect","non-scaling-stroke"),e&&n(this,ir).set(i,o);const h=s?m(this,Ps,Wf).call(this,a,l):null,c=jt._svgFactory.createElement("use");return r.append(c),c.setAttribute("href",`#${l}`),this.updateProperties(r,t),n(this,ie).set(i,r),{id:i,clipPathId:`url(#${h})`}}drawOutline(t,e){const s=Jt(this,El)._++,i=m(this,Ps,hd).call(this),r=jt._svgFactory.createElement("defs");i.append(r);const a=jt._svgFactory.createElement("path");r.append(a);const o=`path_p${this.pageIndex}_${s}`;a.setAttribute("id",o),a.setAttribute("vector-effect","non-scaling-stroke");let l;if(e){const u=jt._svgFactory.createElement("mask");r.append(u),l=`mask_p${this.pageIndex}_${s}`,u.setAttribute("id",l),u.setAttribute("maskUnits","objectBoundingBox");const p=jt._svgFactory.createElement("rect");u.append(p),p.setAttribute("width","1"),p.setAttribute("height","1"),p.setAttribute("fill","white");const b=jt._svgFactory.createElement("use");u.append(b),b.setAttribute("href",`#${o}`),b.setAttribute("stroke","none"),b.setAttribute("fill","black"),b.setAttribute("fill-rule","nonzero"),b.classList.add("mask")}const h=jt._svgFactory.createElement("use");i.append(h),h.setAttribute("href",`#${o}`),l&&h.setAttribute("mask",`url(#${l})`);const c=h.cloneNode();return i.append(c),h.classList.add("mainOutline"),c.classList.add("secondaryOutline"),this.updateProperties(i,t),n(this,ie).set(s,i),s}finalizeDraw(t,e){n(this,ir).delete(t),this.updateProperties(t,e)}updateProperties(t,e){var l;if(!e)return;const{root:s,bbox:i,rootClass:r,path:a}=e,o=typeof t=="number"?n(this,ie).get(t):t;if(o){if(s&&m(this,Ps,cd).call(this,o,s),i&&m(l=jt,Dh,Vf).call(l,o,i),r){const{classList:h}=o;for(const[c,u]of Object.entries(r))h.toggle(c,u)}if(a){const c=o.firstChild.firstChild;m(this,Ps,cd).call(this,c,a)}}}updateParent(t,e){if(e===this)return;const s=n(this,ie).get(t);s&&(n(e,is).append(s),n(this,ie).delete(t),n(e,ie).set(t,s))}remove(t){n(this,ir).delete(t),n(this,is)!==null&&(n(this,ie).get(t).remove(),n(this,ie).delete(t))}destroy(){f(this,is,null);for(const t of n(this,ie).values())t.remove();n(this,ie).clear(),n(this,ir).clear()}};is=new WeakMap,El=new WeakMap,ie=new WeakMap,ir=new WeakMap,Dh=new WeakSet,Vf=function(t,[e,s,i,r]){const{style:a}=t;a.top=`${100*s}%`,a.left=`${100*e}%`,a.width=`${100*i}%`,a.height=`${100*r}%`},Ps=new WeakSet,hd=function(){const t=jt._svgFactory.create(1,1,!0);return n(this,is).append(t),t.setAttribute("aria-hidden",!0),t},Wf=function(t,e){const s=jt._svgFactory.createElement("clipPath");t.append(s);const i=`clip_${e}`;s.setAttribute("id",i),s.setAttribute("clipPathUnits","objectBoundingBox");const r=jt._svgFactory.createElement("use");return s.append(r),r.setAttribute("href",`#${e}`),r.classList.add("clip"),i},cd=function(t,e){for(const[s,i]of Object.entries(e))i===null?t.removeAttribute(s):t.setAttribute(s,i)},g(jt,Dh);let ld=jt;globalThis.pdfjsTestingUtils={HighlightOutliner:zc};$.AbortException;$.AnnotationEditorLayer;$.AnnotationEditorParamsType;$.AnnotationEditorType;$.AnnotationEditorUIManager;$.AnnotationLayer;$.AnnotationMode;$.ColorPicker;$.DOMSVGFactory;$.DrawLayer;$.FeatureTest;var Kg=$.GlobalWorkerOptions;$.ImageKind;$.InvalidPDFException;$.MissingPDFException;$.OPS;$.OutputScale;$.PDFDataRangeTransport;$.PDFDateString;$.PDFWorker;$.PasswordResponses;$.PermissionFlag;$.PixelsPerInch;$.RenderingCancelledException;$.TextLayer;$.TouchManager;$.UnexpectedResponseException;$.Util;$.VerbosityLevel;$.XfaLayer;$.build;$.createValidAbsoluteUrl;$.fetchData;var Qg=$.getDocument;$.getFilenameFromUrl;$.getPdfFilenameFromUrl;$.getXfaPageViewport;$.isDataScheme;$.isPdfFile;$.noContextMenu;$.normalizeUnicode;$.setLayerDimensions;$.shadow;$.stopEvent;$.version;export{Kg as _,Qg as a};
