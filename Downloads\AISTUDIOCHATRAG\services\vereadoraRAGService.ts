// Serviço Integrado RAG para a Vereadora Rafaela de Nilda
import { GoogleGenAI } from "@google/genai";
import { RAGPipeline, DocumentChunk, DEFAULT_RAG_CONFIG } from './ragPipeline';
import { IngestionService, ProcessedDocument } from './ingestionService';
import { VectorStore, SearchQuery } from './vectorStore';
import { RerankingService } from './rerankingService';
import { MonitoringService, PerformanceMetrics } from './monitoringService';
import { gerarInstrucaoPersonalizada } from '../config/vereadora';

export interface VereadoraRAGConfig {
  geminiApiKey: string;
  supabaseUrl: string;
  supabaseKey: string;
  enableMonitoring: boolean;
  enableFeedback: boolean;
  maxDocuments: number;
  maxTokensPerResponse: number;
}

export interface RAGResponse {
  answer: string;
  sources: Array<{
    title: string;
    source: string;
    type: string;
    relevance: number;
  }>;
  confidence: number;
  processingTime: number;
  metadata: {
    chunksUsed: number;
    retrievalStrategy: string;
    rerankingApplied: boolean;
    contextOptimized: boolean;
    tokensUsed: number;
  };
}

export interface DocumentProcessingResult {
  success: boolean;
  documentId: string;
  chunksCreated: number;
  processingTime: number;
  error?: string;
}

export class VereadoraRAGService {
  private ragPipeline: RAGPipeline;
  private ingestionService: IngestionService;
  private vectorStore: VectorStore;
  private rerankingService: RerankingService;
  private monitoringService: MonitoringService | null = null;
  private aiClient: GoogleGenAI;
  private config: VereadoraRAGConfig;
  private initialized = false;

  constructor(config: VereadoraRAGConfig) {
    this.config = config;
    this.aiClient = new GoogleGenAI({ apiKey: config.geminiApiKey });
    
    // Inicializar serviços
    this.initializeServices();
  }

  private async initializeServices(): Promise<void> {
    try {
      console.log('[VereadoraRAG] Inicializando serviços...');

      // RAG Pipeline
      this.ragPipeline = new RAGPipeline(this.config.geminiApiKey, DEFAULT_RAG_CONFIG);

      // Ingestion Service
      this.ingestionService = new IngestionService({
        supportedFormats: ['pdf', 'txt', 'docx', 'xlsx', 'csv', 'image'],
        maxFileSize: 50,
        extractMetadata: true,
        cleanContent: true,
        detectLanguage: true
      });

      // Vector Store
      this.vectorStore = new VectorStore(
        this.config.supabaseUrl,
        this.config.supabaseKey,
        {
          dimensions: 768,
          indexType: 'hnsw',
          distanceMetric: 'cosine',
          enableCache: true,
          cacheSize: 1000,
          enableCompression: true
        }
      );

      // Reranking Service
      this.rerankingService = new RerankingService(
        this.config.geminiApiKey,
        {
          enabled: true,
          strategy: 'hybrid',
          maxCandidates: 20,
          diversityWeight: 0.3,
          recencyWeight: 0.2,
          authorityWeight: 0.3,
          relevanceThreshold: 0.5
        },
        {
          maxTokens: this.config.maxTokensPerResponse,
          compressionRatio: 0.7,
          preserveStructure: true,
          enableSummarization: true,
          prioritizeRecent: true,
          includeMetadata: true
        }
      );

      // Monitoring Service (opcional)
      if (this.config.enableMonitoring) {
        this.monitoringService = new MonitoringService(
          this.config.supabaseUrl,
          this.config.supabaseKey
        );
      }

      this.initialized = true;
      console.log('[VereadoraRAG] Serviços inicializados com sucesso!');

    } catch (error) {
      console.error('[VereadoraRAG] Erro na inicialização:', error);
      throw error;
    }
  }

  // PROCESSAMENTO DE DOCUMENTOS
  async processDocument(file: File): Promise<DocumentProcessingResult> {
    if (!this.initialized) {
      throw new Error('Serviço não inicializado');
    }

    const startTime = Date.now();
    
    try {
      console.log(`[VereadoraRAG] Processando documento: ${file.name}`);

      // 1. Ingestão e pré-processamento
      const processedDoc = await this.ingestionService.processFile(file);
      
      // 2. Chunking inteligente
      const chunks = await this.ragPipeline.ingestDocument(
        processedDoc.content,
        {
          source: processedDoc.originalName,
          type: processedDoc.metadata.type,
          title: processedDoc.metadata.title,
          category: processedDoc.metadata.category,
          tags: processedDoc.metadata.tags,
          date: processedDoc.metadata.createdDate?.toISOString()
        }
      );

      // 3. Armazenamento vetorial
      const documentChunks: DocumentChunk[] = chunks.map(chunkId => ({
        id: chunkId,
        content: processedDoc.content, // Simplificado - em produção, recuperar chunk específico
        metadata: {
          source: processedDoc.originalName,
          type: processedDoc.metadata.type,
          title: processedDoc.metadata.title,
          category: processedDoc.metadata.category,
          tags: processedDoc.metadata.tags,
          date: processedDoc.metadata.createdDate?.toISOString()
        }
      }));

      await this.vectorStore.storeChunks(documentChunks);

      const processingTime = Date.now() - startTime;

      // Registrar métricas
      if (this.monitoringService) {
        await this.monitoringService.recordMetric({
          timestamp: new Date(),
          metric: 'document_processed',
          value: 1,
          category: 'usage',
          metadata: {
            file_type: processedDoc.metadata.type,
            file_size: file.size,
            chunks_created: chunks.length,
            processing_time: processingTime
          }
        });
      }

      console.log(`[VereadoraRAG] Documento processado: ${chunks.length} chunks criados`);

      return {
        success: true,
        documentId: processedDoc.id,
        chunksCreated: chunks.length,
        processingTime
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error('[VereadoraRAG] Erro no processamento:', error);

      // Registrar erro
      if (this.monitoringService) {
        await this.monitoringService.recordMetric({
          timestamp: new Date(),
          metric: 'document_processing_error',
          value: 1,
          category: 'error',
          metadata: {
            error: error instanceof Error ? error.message : 'Unknown error',
            file_name: file.name,
            processing_time: processingTime
          }
        });
      }

      return {
        success: false,
        documentId: '',
        chunksCreated: 0,
        processingTime,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  // GERAÇÃO DE RESPOSTAS COM RAG
  async generateResponse(
    query: string,
    context: 'geral' | 'whatsapp' | 'formal' = 'geral',
    useDocuments = true
  ): Promise<RAGResponse> {
    if (!this.initialized) {
      throw new Error('Serviço não inicializado');
    }

    const startTime = Date.now();
    let performanceMetrics: Partial<PerformanceMetrics> = {};

    try {
      console.log(`[VereadoraRAG] Gerando resposta para: "${query}"`);

      let retrievedChunks: DocumentChunk[] = [];
      let sources: RAGResponse['sources'] = [];
      let confidence = 0.8; // Base confidence

      if (useDocuments) {
        // 1. Retrieval híbrido
        const retrievalStart = Date.now();
        const searchQuery: SearchQuery = {
          text: query,
          limit: 10,
          threshold: 0.6
        };

        const searchResults = await this.vectorStore.hybridSearch(searchQuery);
        performanceMetrics.retrievalTime = Date.now() - retrievalStart;

        // 2. Reranking
        const rerankingStart = Date.now();
        const rerankingResult = await this.rerankingService.rerankResults(
          query,
          searchResults,
          5
        );
        performanceMetrics.rerankingTime = Date.now() - rerankingStart;

        // 3. Otimização de contexto
        const optimizationStart = Date.now();
        const optimizedContext = await this.rerankingService.optimizeContext(
          rerankingResult.rankedChunks,
          query
        );
        performanceMetrics.contextOptimizationTime = Date.now() - optimizationStart;

        retrievedChunks = optimizedContext.chunks;
        confidence = Math.min(confidence + (retrievedChunks.length * 0.1), 1.0);

        // Preparar fontes
        sources = retrievedChunks.map(chunk => ({
          title: chunk.metadata.title || 'Documento',
          source: chunk.metadata.source || 'Desconhecido',
          type: chunk.metadata.type || 'document',
          relevance: chunk.score || 0
        }));
      }

      // 4. Geração da resposta
      const generationStart = Date.now();
      const systemInstruction = gerarInstrucaoPersonalizada(
        useDocuments && retrievedChunks.length > 0 ? 'rag' : context
      );

      const contextText = retrievedChunks.length > 0
        ? retrievedChunks.map(chunk => chunk.content).join('\n\n')
        : null;

      const response = await this.aiClient.models.generateContent({
        model: 'gemini-2.5-flash',
        contents: contextText 
          ? `CONTEXTO DOS DOCUMENTOS:\n${contextText}\n\nPERGUNTA DO CIDADÃO:\n${query}`
          : query,
        systemInstruction,
        generationConfig: {
          temperature: 0.7,
          topP: 0.95,
          topK: 64,
          maxOutputTokens: 2048
        }
      });

      const generationTime = Date.now() - generationStart;
      const totalTime = Date.now() - startTime;

      // Estimar tokens usados
      const tokensUsed = this.estimateTokens(query + (response.text || ''));
      performanceMetrics.totalTokensUsed = tokensUsed;

      // Registrar métricas de performance
      if (this.monitoringService) {
        await this.monitoringService.recordPerformanceMetrics({
          responseTime: totalTime,
          ragProcessingTime: totalTime - generationTime,
          retrievalTime: performanceMetrics.retrievalTime || 0,
          rerankingTime: performanceMetrics.rerankingTime || 0,
          contextOptimizationTime: performanceMetrics.contextOptimizationTime || 0,
          totalTokensUsed: tokensUsed,
          chunksRetrieved: retrievedChunks.length,
          documentsProcessed: new Set(retrievedChunks.map(c => c.metadata.source)).size
        });

        // Registrar query
        await this.monitoringService.recordMetric({
          timestamp: new Date(),
          metric: 'query_processed',
          value: 1,
          category: 'usage',
          metadata: {
            context,
            use_documents: useDocuments,
            chunks_used: retrievedChunks.length,
            response_time: totalTime
          }
        });
      }

      console.log(`[VereadoraRAG] Resposta gerada em ${totalTime}ms`);

      return {
        answer: response.text || 'Desculpe, não consegui gerar uma resposta adequada.',
        sources,
        confidence,
        processingTime: totalTime,
        metadata: {
          chunksUsed: retrievedChunks.length,
          retrievalStrategy: 'hybrid',
          rerankingApplied: true,
          contextOptimized: true,
          tokensUsed
        }
      };

    } catch (error) {
      const totalTime = Date.now() - startTime;
      console.error('[VereadoraRAG] Erro na geração de resposta:', error);

      // Registrar erro
      if (this.monitoringService) {
        await this.monitoringService.recordMetric({
          timestamp: new Date(),
          metric: 'response_generation_error',
          value: 1,
          category: 'error',
          metadata: {
            error: error instanceof Error ? error.message : 'Unknown error',
            query: query.substring(0, 100),
            processing_time: totalTime
          }
        });
      }

      // Resposta de fallback
      return {
        answer: 'Desculpe, ocorreu um erro ao processar sua solicitação. Por favor, tente novamente ou entre em contato diretamente com o gabinete.',
        sources: [],
        confidence: 0,
        processingTime: totalTime,
        metadata: {
          chunksUsed: 0,
          retrievalStrategy: 'none',
          rerankingApplied: false,
          contextOptimized: false,
          tokensUsed: 0
        }
      };
    }
  }

  // MÉTODOS DE UTILIDADE
  async getSystemStats() {
    if (!this.vectorStore) return null;
    
    try {
      return await this.vectorStore.getIndexStats();
    } catch (error) {
      console.error('[VereadoraRAG] Erro ao obter estatísticas:', error);
      return null;
    }
  }

  async recordFeedback(
    conversationId: string,
    messageId: string,
    rating: 1 | 2 | 3 | 4 | 5,
    feedback: string,
    comment?: string
  ): Promise<void> {
    if (!this.monitoringService) return;

    try {
      await this.monitoringService.recordFeedback({
        conversationId,
        messageId,
        rating,
        feedback: feedback as any,
        comment,
        userType: 'citizen'
      });
    } catch (error) {
      console.error('[VereadoraRAG] Erro ao registrar feedback:', error);
    }
  }

  getMonitoringService(): MonitoringService | null {
    return this.monitoringService;
  }

  isInitialized(): boolean {
    return this.initialized;
  }

  private estimateTokens(text: string): number {
    // Estimativa: 1 token ≈ 4 caracteres para português
    return Math.ceil(text.length / 4);
  }

  // MÉTODOS DE CONFIGURAÇÃO
  async addAPISource(name: string, config: any): Promise<void> {
    this.ingestionService.addAPISource(name, config);
  }

  async ingestFromAPI(sourceName: string): Promise<DocumentProcessingResult[]> {
    try {
      const documents = await this.ingestionService.ingestFromAPI(sourceName);
      const results: DocumentProcessingResult[] = [];

      for (const doc of documents) {
        // Simular processamento de documento da API
        const chunks = await this.ragPipeline.ingestDocument(
          doc.content,
          doc.metadata
        );

        results.push({
          success: true,
          documentId: doc.id,
          chunksCreated: chunks.length,
          processingTime: 0
        });
      }

      return results;

    } catch (error) {
      console.error('[VereadoraRAG] Erro na ingestão de API:', error);
      return [{
        success: false,
        documentId: '',
        chunksCreated: 0,
        processingTime: 0,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      }];
    }
  }
}

// Factory function para criar instância configurada
export function createVereadoraRAGService(config: VereadoraRAGConfig): VereadoraRAGService {
  return new VereadoraRAGService(config);
}
