<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- SEO Meta Tags -->
    <title>Vereadora Rafaela de Nilda - Atendimento Digital | Parnamirim/RN</title>
    <meta name="description" content="Sistema de atendimento digital da Vereadora Rafaela de Nilda. Tire suas dúvidas sobre serviços públicos, projetos e políticas de Parnamirim/RN." />
    <meta name="keywords" content="Vereadora Rafaela, Parnamirim, RN, atendimento digital, serviços públicos, políticas municipais" />
    <meta name="author" content="Gabinete da Vereadora Rafaela de Nilda" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Vereadora Rafaela de Nilda - Atendimento Digital" />
    <meta property="og:description" content="Sistema de atendimento digital da Vereadora Rafaela de Nilda de Parnamirim/RN" />
    <meta property="og:url" content="https://vereadora-rafaela.netlify.app" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="Vereadora Rafaela de Nilda - Atendimento Digital" />
    <meta property="twitter:description" content="Sistema de atendimento digital da Vereadora Rafaela de Nilda de Parnamirim/RN" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#1e40af" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Vereadora Rafaela" />

    <!-- Preconnect para performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://cdn.tailwindcss.com" />

    <!-- CSS - Removido Tailwind CDN para evitar conflitos -->
    <!-- Font Awesome via CDN será substituído por ícones inline -->
    <!-- Import Maps para módulos ES -->


    <!-- CSS customizado - DEVE vir DEPOIS do Tailwind -->

    <!-- Configuração removida - usando CSS puro -->

    <!-- CSS inline para garantir que funcione -->
    <style>
      /* Garantir que o design funcione mesmo se o Tailwind falhar */
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
        background-color: #f9fafb;
        margin: 0;
        padding: 0;
      }
      .loading-screen {
        position: fixed;
        inset: 0;
        background: #1e3a8a;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
    </style>
    <script type="importmap">
    {
      "imports": {
        "react": "https://esm.sh/react@^19.1.0",
        "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
        "react/": "https://esm.sh/react@^19.1.0/",
        "@google/genai": "https://esm.sh/@google/genai@^1.6.0",
        "pdfjs-dist/build/pdf.min.mjs": "https://esm.sh/pdfjs-dist@4.10.38/build/pdf.min.mjs",
        "pdfjs-dist/build/pdf.worker.min.mjs": "https://esm.sh/pdfjs-dist@4.10.38/build/pdf.worker.min.mjs"
      }
    }
    </script>
    <script type="module" crossorigin src="/assets/index-DAel1O8l.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-DrD-X_hS.js">
    <link rel="modulepreload" crossorigin href="/assets/supabase-zuWrSlgC.js">
    <link rel="modulepreload" crossorigin href="/assets/gemini-DcgyraGI.js">
    <link rel="modulepreload" crossorigin href="/assets/pdf-BCx2EAgV.js">
    <link rel="stylesheet" crossorigin href="/assets/index-CAvWnRwR.css">
  </head>

  <body class="bg-gray-50">
    <!-- Loading screen -->
    <div id="loading-screen" class="loading-screen">
      <div style="text-align: center; color: white;">
        <div style="width: 3rem; height: 3rem; border: 2px solid transparent; border-top: 2px solid white; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 1rem;"></div>
        <h2 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 0.5rem;">Carregando Sistema da Vereadora Rafaela</h2>
        <p style="color: #bfdbfe; font-size: 0.875rem;">Inicializando atendimento digital...</p>
      </div>
    </div>

    <!-- Animação de loading -->
    <style>
      @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
      }
    </style>

    <!-- App container -->
    <div id="root"></div>

    <!-- Scripts -->

    <!-- Script para remover loading screen -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.5s ease-out';
            setTimeout(function() {
              loadingScreen.remove();
            }, 500);
          }
        }, 1000);
      });
    </script>

    <!-- Service Worker para PWA (opcional) -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>