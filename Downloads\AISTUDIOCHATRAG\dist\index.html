<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- SEO Meta Tags -->
    <title>Vereadora Rafaela de Nilda - Atendimento Digital | Parnamirim/RN</title>
    <meta name="description" content="Sistema de atendimento digital da Vereadora Rafaela de Nilda. Tire suas dúvidas sobre serviços públicos, projetos e políticas de Parnamirim/RN." />
    <meta name="keywords" content="Vereadora Rafaela, Parnamirim, RN, atendimento digital, serviços públicos, políticas municipais" />
    <meta name="author" content="Gabinete da Vereadora Rafaela de Nilda" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:title" content="Vereadora Rafaela de Nilda - Atendimento Digital" />
    <meta property="og:description" content="Sistema de atendimento digital da Vereadora Rafaela de Nilda de Parnamirim/RN" />
    <meta property="og:url" content="https://vereadora-rafaela.netlify.app" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:title" content="Vereadora Rafaela de Nilda - Atendimento Digital" />
    <meta property="twitter:description" content="Sistema de atendimento digital da Vereadora Rafaela de Nilda de Parnamirim/RN" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#1e40af" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Vereadora Rafaela" />

    <!-- Preconnect para performance -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="preconnect" href="https://cdn.tailwindcss.com" />

    <!-- CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Import Maps para módulos ES -->


    <!-- CSS customizado -->

    <!-- Configuração do Tailwind -->
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'vereadora-blue': {
                50: '#eff6ff',
                500: '#3b82f6',
                600: '#2563eb',
                700: '#1d4ed8',
                800: '#1e40af',
                900: '#1e3a8a'
              }
            }
          }
        }
      }
    </script>
    <script type="importmap">
    {
      "imports": {
        "react": "https://esm.sh/react@^19.1.0",
        "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
        "react/": "https://esm.sh/react@^19.1.0/",
        "@google/genai": "https://esm.sh/@google/genai@^1.6.0",
        "pdfjs-dist/build/pdf.min.mjs": "https://esm.sh/pdfjs-dist@4.10.38/build/pdf.min.mjs",
        "pdfjs-dist/build/pdf.worker.min.mjs": "https://esm.sh/pdfjs-dist@4.10.38/build/pdf.worker.min.mjs"
      }
    }
    </script>
    <script type="module" crossorigin src="/assets/index-hT8N-aqm.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-DrD-X_hS.js">
    <link rel="modulepreload" crossorigin href="/assets/supabase-zuWrSlgC.js">
    <link rel="modulepreload" crossorigin href="/assets/gemini-DcgyraGI.js">
    <link rel="modulepreload" crossorigin href="/assets/pdf-BCx2EAgV.js">
    <link rel="stylesheet" crossorigin href="/assets/index-DrRLzZ3q.css">
  </head>

  <body class="bg-gray-50 font-sans antialiased">
    <!-- Loading screen -->
    <div id="loading-screen" class="fixed inset-0 bg-blue-900 flex items-center justify-center z-50">
      <div class="text-center text-white">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
        <h2 class="text-xl font-semibold">Carregando Sistema da Vereadora Rafaela</h2>
        <p class="text-blue-200">Inicializando atendimento digital...</p>
      </div>
    </div>

    <!-- App container -->
    <div id="root"></div>

    <!-- Scripts -->

    <!-- Script para remover loading screen -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.5s ease-out';
            setTimeout(function() {
              loadingScreen.remove();
            }, 500);
          }
        }, 1000);
      });
    </script>

    <!-- Service Worker para PWA (opcional) -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>