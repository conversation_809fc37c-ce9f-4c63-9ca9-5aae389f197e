// Dashboard de Monitoramento para o Sistema da Vereadora Rafaela
import React, { useState, useEffect } from 'react';
import { 
  MonitoringService, 
  PerformanceMetrics, 
  UsageMetrics, 
  QualityMetrics, 
  SystemHealth,
  Alert 
} from '../services/monitoringService';

interface MonitoringDashboardProps {
  monitoringService: MonitoringService;
}

export const MonitoringDashboard: React.FC<MonitoringDashboardProps> = ({ 
  monitoringService 
}) => {
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);
  const [usageMetrics, setUsageMetrics] = useState<UsageMetrics | null>(null);
  const [qualityMetrics, setQualityMetrics] = useState<QualityMetrics | null>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [timeRange, setTimeRange] = useState<'hour' | 'day' | 'week' | 'month'>('day');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadMetrics();
    const interval = setInterval(loadMetrics, 30000); // Atualizar a cada 30s
    return () => clearInterval(interval);
  }, [timeRange]);

  const loadMetrics = async () => {
    try {
      setLoading(true);
      
      const [performance, usage, quality, health, activeAlerts] = await Promise.all([
        monitoringService.getPerformanceMetrics(timeRange),
        monitoringService.getUsageMetrics(timeRange),
        monitoringService.getQualityMetrics(timeRange),
        monitoringService.getSystemHealth(),
        monitoringService.getActiveAlerts()
      ]);

      setPerformanceMetrics(performance);
      setUsageMetrics(usage);
      setQualityMetrics(quality);
      setSystemHealth(health);
      setAlerts(activeAlerts);
      
    } catch (error) {
      console.error('Erro ao carregar métricas:', error);
    } finally {
      setLoading(false);
    }
  };

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getAlertSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-blue-600 bg-blue-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            Dashboard de Monitoramento
          </h2>
          <p className="text-gray-600">
            Sistema da Vereadora Rafaela de Nilda
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="hour">Última Hora</option>
            <option value="day">Último Dia</option>
            <option value="week">Última Semana</option>
            <option value="month">Último Mês</option>
          </select>
          
          <button
            onClick={loadMetrics}
            className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700"
          >
            Atualizar
          </button>
        </div>
      </div>

      {/* Status do Sistema */}
      {systemHealth && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Status do Sistema</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getHealthStatusColor(systemHealth.status)}`}>
                {systemHealth.status.toUpperCase()}
              </div>
              <p className="text-gray-600 text-sm mt-1">Status Geral</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {systemHealth.apiLatency}ms
              </div>
              <p className="text-gray-600 text-sm">Latência API</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {systemHealth.errorRate.toFixed(1)}%
              </div>
              <p className="text-gray-600 text-sm">Taxa de Erro</p>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {Math.floor(systemHealth.uptime / 1000 / 60)}min
              </div>
              <p className="text-gray-600 text-sm">Uptime</p>
            </div>
          </div>
        </div>
      )}

      {/* Alertas Ativos */}
      {alerts.length > 0 && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Alertas Ativos ({alerts.length})</h3>
          <div className="space-y-3">
            {alerts.slice(0, 5).map(alert => (
              <div key={alert.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${getAlertSeverityColor(alert.severity)}`}>
                    {alert.severity.toUpperCase()}
                  </span>
                  <span className="text-sm text-gray-900">{alert.message}</span>
                </div>
                <span className="text-xs text-gray-500">
                  {alert.timestamp.toLocaleTimeString()}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Métricas de Performance */}
      {performanceMetrics && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Performance</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {performanceMetrics.responseTime.toFixed(0)}ms
              </div>
              <p className="text-blue-800 text-sm">Tempo de Resposta</p>
            </div>
            
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {performanceMetrics.ragProcessingTime.toFixed(0)}ms
              </div>
              <p className="text-green-800 text-sm">Processamento RAG</p>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {performanceMetrics.totalTokensUsed.toLocaleString()}
              </div>
              <p className="text-purple-800 text-sm">Tokens Utilizados</p>
            </div>
            
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">
                {performanceMetrics.chunksRetrieved}
              </div>
              <p className="text-orange-800 text-sm">Chunks Recuperados</p>
            </div>
          </div>
        </div>
      )}

      {/* Métricas de Uso */}
      {usageMetrics && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Uso do Sistema</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Total de Consultas</span>
                <span className="font-semibold">{usageMetrics.totalQueries.toLocaleString()}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Usuários Únicos</span>
                <span className="font-semibold">{usageMetrics.uniqueUsers.toLocaleString()}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Duração Média da Sessão</span>
                <span className="font-semibold">{usageMetrics.averageSessionDuration.toFixed(1)}min</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Documentos Enviados</span>
                <span className="font-semibold">{usageMetrics.documentUploads}</span>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Consultas Mais Comuns</h4>
              <div className="space-y-2">
                {usageMetrics.mostCommonQueries.map((query, index) => (
                  <div key={index} className="text-sm text-gray-600 p-2 bg-gray-50 rounded">
                    {query}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Métricas de Qualidade */}
      {qualityMetrics && (
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold mb-4">Qualidade das Respostas</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {qualityMetrics.userSatisfactionRating.toFixed(1)}/5
              </div>
              <p className="text-green-800 text-sm">Satisfação do Usuário</p>
            </div>
            
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">
                {(qualityMetrics.averageRelevanceScore * 100).toFixed(1)}%
              </div>
              <p className="text-blue-800 text-sm">Score de Relevância</p>
            </div>
            
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">
                {(qualityMetrics.accuracyScore * 100).toFixed(1)}%
              </div>
              <p className="text-purple-800 text-sm">Precisão</p>
            </div>
            
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">
                {(qualityMetrics.contextUtilization * 100).toFixed(1)}%
              </div>
              <p className="text-yellow-800 text-sm">Utilização de Contexto</p>
            </div>
            
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {(qualityMetrics.hallucinationRate * 100).toFixed(1)}%
              </div>
              <p className="text-red-800 text-sm">Taxa de Alucinação</p>
            </div>
            
            <div className="text-center p-4 bg-indigo-50 rounded-lg">
              <div className="text-2xl font-bold text-indigo-600">
                {(qualityMetrics.sourceReliability * 100).toFixed(1)}%
              </div>
              <p className="text-indigo-800 text-sm">Confiabilidade das Fontes</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Componente de Feedback do Usuário
interface FeedbackComponentProps {
  conversationId: string;
  messageId: string;
  monitoringService: MonitoringService;
  onFeedbackSubmitted?: () => void;
}

export const FeedbackComponent: React.FC<FeedbackComponentProps> = ({
  conversationId,
  messageId,
  monitoringService,
  onFeedbackSubmitted
}) => {
  const [rating, setRating] = useState<number>(0);
  const [feedback, setFeedback] = useState<'helpful' | 'not_helpful' | 'incorrect' | 'incomplete' | 'excellent' | ''>('');
  const [comment, setComment] = useState('');
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = async () => {
    if (rating === 0) return;

    try {
      await monitoringService.recordFeedback({
        conversationId,
        messageId,
        rating: rating as 1 | 2 | 3 | 4 | 5,
        feedback: feedback as any,
        comment: comment || undefined,
        userType: 'citizen'
      });

      setSubmitted(true);
      onFeedbackSubmitted?.();
      
    } catch (error) {
      console.error('Erro ao enviar feedback:', error);
    }
  };

  if (submitted) {
    return (
      <div className="text-center p-4 bg-green-50 rounded-lg">
        <div className="text-green-600 font-medium">Obrigada pelo seu feedback!</div>
        <div className="text-green-500 text-sm">Sua avaliação nos ajuda a melhorar o atendimento.</div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 rounded-lg p-4 space-y-4">
      <div className="text-sm font-medium text-gray-700">
        Esta resposta foi útil?
      </div>
      
      {/* Rating Stars */}
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map(star => (
          <button
            key={star}
            onClick={() => setRating(star)}
            className={`text-2xl ${star <= rating ? 'text-yellow-400' : 'text-gray-300'} hover:text-yellow-400`}
          >
            ⭐
          </button>
        ))}
      </div>
      
      {/* Feedback Type */}
      {rating > 0 && (
        <div className="space-y-2">
          <div className="text-sm text-gray-600">Como você classificaria esta resposta?</div>
          <div className="flex flex-wrap gap-2">
            {[
              { value: 'excellent', label: 'Excelente' },
              { value: 'helpful', label: 'Útil' },
              { value: 'incomplete', label: 'Incompleta' },
              { value: 'incorrect', label: 'Incorreta' },
              { value: 'not_helpful', label: 'Não útil' }
            ].map(option => (
              <button
                key={option.value}
                onClick={() => setFeedback(option.value as any)}
                className={`px-3 py-1 rounded-full text-xs ${
                  feedback === option.value 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      )}
      
      {/* Comment */}
      {rating > 0 && (
        <div className="space-y-2">
          <label className="text-sm text-gray-600">Comentário adicional (opcional):</label>
          <textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Compartilhe mais detalhes sobre sua experiência..."
            className="w-full p-2 border border-gray-300 rounded-md text-sm"
            rows={3}
          />
        </div>
      )}
      
      {/* Submit Button */}
      {rating > 0 && (
        <button
          onClick={handleSubmit}
          className="w-full bg-blue-600 text-white py-2 rounded-md text-sm hover:bg-blue-700"
        >
          Enviar Feedback
        </button>
      )}
    </div>
  );
};

export default MonitoringDashboard;
