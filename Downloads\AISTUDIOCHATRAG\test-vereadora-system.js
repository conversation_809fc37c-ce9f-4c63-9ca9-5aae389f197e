// Script de Teste Completo do Sistema da Vereadora Rafaela de Nilda
import { createVereadoraRAGService } from './services/vereadoraRAGService.js';
import { VEREADORA_CONFIG } from './config/vereadora.js';

// Configuração de teste
const TEST_CONFIG = {
  geminiApiKey: process.env.VITE_GEMINI_API_KEY || '',
  supabaseUrl: process.env.VITE_SUPABASE_URL || '',
  supabaseKey: process.env.VITE_SUPABASE_ANON_KEY || '',
  enableMonitoring: true,
  enableFeedback: true,
  maxDocuments: 100,
  maxTokensPerResponse: 4000
};

// Dados de teste
const TEST_QUERIES = [
  "Como posso solicitar uma consulta médica no posto de saúde?",
  "Quais são os projetos da vereadora para a educação em Parnamirim?",
  "Como funciona o programa de assistência social do município?",
  "Onde posso denunciar problemas de saneamento básico?",
  "Quais são os horários de atendimento do gabinete?",
  "Como participar das audiências públicas da Câmara?",
  "Existe algum programa para idosos em Parnamirim?",
  "Como solicitar melhorias na iluminação pública do meu bairro?",
  "Quais documentos preciso para me inscrever em programas sociais?",
  "A vereadora tem projetos voltados para o meio ambiente?"
];

const TEST_DOCUMENTS = [
  {
    name: "lei_municipal_exemplo.txt",
    content: `LEI MUNICIPAL Nº 001/2024

DISPÕE SOBRE O PROGRAMA DE ASSISTÊNCIA SOCIAL INTEGRADA DO MUNICÍPIO DE PARNAMIRIM

A Câmara Municipal de Parnamirim aprova e eu sanciono a seguinte Lei:

Art. 1º Fica instituído o Programa de Assistência Social Integrada de Parnamirim, destinado a promover o bem-estar social das famílias em situação de vulnerabilidade.

Art. 2º São objetivos do programa:
I - Garantir acesso aos serviços básicos de saúde, educação e assistência social;
II - Promover a inclusão social e produtiva das famílias beneficiárias;
III - Fortalecer os vínculos familiares e comunitários;
IV - Desenvolver ações de prevenção e proteção social.

Art. 3º O programa será executado pela Secretaria de Assistência Social em parceria com outras secretarias municipais.

Art. 4º Esta lei entra em vigor na data de sua publicação.

Parnamirim, 15 de janeiro de 2024.
Prefeito Municipal`
  },
  {
    name: "projeto_educacao_vereadora.txt",
    content: `PROJETO DE LEI Nº 005/2024
Autoria: Vereadora Rafaela de Nilda

INSTITUI O PROGRAMA "EDUCAÇÃO PARA TODOS" NO MUNICÍPIO DE PARNAMIRIM

JUSTIFICATIVA:
O presente projeto visa ampliar o acesso à educação de qualidade em Parnamirim, especialmente nas áreas mais carentes do município. A educação é um direito fundamental e deve ser garantida a todos os cidadãos.

PROPOSTAS:
1. Criação de novas creches nos bairros periféricos
2. Ampliação do ensino técnico profissionalizante
3. Programa de alfabetização para adultos
4. Melhoria da infraestrutura das escolas existentes
5. Capacitação continuada dos professores

BENEFÍCIOS ESPERADOS:
- Redução da evasão escolar
- Melhoria dos índices educacionais do município
- Maior qualificação profissional da população
- Desenvolvimento socioeconômico local

O projeto prevê investimento de R$ 2 milhões anuais, com recursos do orçamento municipal e parcerias com o governo estadual.`
  },
  {
    name: "relatorio_atividades_gabinete.txt",
    content: `RELATÓRIO DE ATIVIDADES DO GABINETE
Vereadora Rafaela de Nilda
Período: Janeiro a Março 2024

ATENDIMENTOS REALIZADOS:
- 450 atendimentos presenciais
- 320 atendimentos via WhatsApp
- 180 visitas domiciliares
- 25 audiências públicas

PRINCIPAIS DEMANDAS:
1. Saúde pública (35%)
2. Infraestrutura urbana (28%)
3. Assistência social (20%)
4. Educação (12%)
5. Outros (5%)

PROJETOS APRESENTADOS:
- PL 005/2024 - Programa Educação para Todos
- PL 008/2024 - Criação do Conselho Municipal da Mulher
- PL 012/2024 - Política Municipal de Sustentabilidade

PARCERIAS ESTABELECIDAS:
- Associação de Moradores do Cohabinal
- ONG Amigos da Natureza
- Sindicato dos Professores
- Conselho Municipal de Saúde

PRÓXIMAS AÇÕES:
- Audiência pública sobre saneamento básico
- Visita às escolas municipais
- Reunião com secretários municipais
- Campanha de conscientização ambiental`
  }
];

class VereadoraSystemTester {
  constructor() {
    this.ragService = null;
    this.testResults = {
      initialization: false,
      documentProcessing: [],
      queryResponses: [],
      monitoring: false,
      feedback: false,
      performance: {}
    };
  }

  async runAllTests() {
    console.log('🚀 Iniciando testes do Sistema da Vereadora Rafaela de Nilda\n');
    
    try {
      await this.testInitialization();
      await this.testDocumentProcessing();
      await this.testQueryResponses();
      await this.testMonitoring();
      await this.testFeedback();
      await this.testPerformance();
      
      this.printResults();
      
    } catch (error) {
      console.error('❌ Erro durante os testes:', error);
    }
  }

  async testInitialization() {
    console.log('📋 Teste 1: Inicialização do Sistema');
    
    try {
      this.ragService = createVereadoraRAGService(TEST_CONFIG);
      
      // Aguardar inicialização
      let attempts = 0;
      while (!this.ragService.isInitialized() && attempts < 30) {
        await this.sleep(1000);
        attempts++;
      }
      
      if (this.ragService.isInitialized()) {
        console.log('✅ Sistema inicializado com sucesso');
        this.testResults.initialization = true;
      } else {
        console.log('❌ Falha na inicialização do sistema');
      }
      
    } catch (error) {
      console.log('❌ Erro na inicialização:', error.message);
    }
    
    console.log('');
  }

  async testDocumentProcessing() {
    console.log('📄 Teste 2: Processamento de Documentos');
    
    if (!this.ragService) {
      console.log('❌ Sistema não inicializado');
      return;
    }

    for (const testDoc of TEST_DOCUMENTS) {
      try {
        console.log(`   Processando: ${testDoc.name}`);
        
        // Criar arquivo mock
        const file = new File([testDoc.content], testDoc.name, { type: 'text/plain' });
        
        const result = await this.ragService.processDocument(file);
        
        if (result.success) {
          console.log(`   ✅ ${testDoc.name}: ${result.chunksCreated} chunks criados em ${result.processingTime}ms`);
          this.testResults.documentProcessing.push({
            name: testDoc.name,
            success: true,
            chunks: result.chunksCreated,
            time: result.processingTime
          });
        } else {
          console.log(`   ❌ ${testDoc.name}: ${result.error}`);
          this.testResults.documentProcessing.push({
            name: testDoc.name,
            success: false,
            error: result.error
          });
        }
        
      } catch (error) {
        console.log(`   ❌ ${testDoc.name}: ${error.message}`);
        this.testResults.documentProcessing.push({
          name: testDoc.name,
          success: false,
          error: error.message
        });
      }
    }
    
    console.log('');
  }

  async testQueryResponses() {
    console.log('💬 Teste 3: Geração de Respostas');
    
    if (!this.ragService) {
      console.log('❌ Sistema não inicializado');
      return;
    }

    for (let i = 0; i < Math.min(5, TEST_QUERIES.length); i++) {
      const query = TEST_QUERIES[i];
      
      try {
        console.log(`   Pergunta ${i + 1}: "${query.substring(0, 50)}..."`);
        
        const response = await this.ragService.generateResponse(query, 'geral', true);
        
        console.log(`   ✅ Resposta gerada em ${response.processingTime}ms`);
        console.log(`   📊 Confiança: ${(response.confidence * 100).toFixed(1)}%`);
        console.log(`   📚 Fontes: ${response.sources.length}`);
        console.log(`   🔤 Tokens: ${response.metadata.tokensUsed}`);
        console.log(`   📝 Resposta: "${response.answer.substring(0, 100)}..."`);
        
        this.testResults.queryResponses.push({
          query: query.substring(0, 50),
          success: true,
          processingTime: response.processingTime,
          confidence: response.confidence,
          sources: response.sources.length,
          tokensUsed: response.metadata.tokensUsed
        });
        
      } catch (error) {
        console.log(`   ❌ Erro: ${error.message}`);
        this.testResults.queryResponses.push({
          query: query.substring(0, 50),
          success: false,
          error: error.message
        });
      }
      
      console.log('');
    }
  }

  async testMonitoring() {
    console.log('📊 Teste 4: Sistema de Monitoramento');
    
    if (!this.ragService) {
      console.log('❌ Sistema não inicializado');
      return;
    }

    try {
      const monitoringService = this.ragService.getMonitoringService();
      
      if (monitoringService) {
        // Testar coleta de métricas
        const systemHealth = await monitoringService.getSystemHealth();
        const performanceMetrics = await monitoringService.getPerformanceMetrics('hour');
        
        console.log('✅ Monitoramento ativo');
        console.log(`   Status do sistema: ${systemHealth.status}`);
        console.log(`   Latência da API: ${systemHealth.apiLatency}ms`);
        console.log(`   Taxa de erro: ${systemHealth.errorRate.toFixed(2)}%`);
        
        this.testResults.monitoring = true;
        
      } else {
        console.log('⚠️  Monitoramento desabilitado');
      }
      
    } catch (error) {
      console.log('❌ Erro no monitoramento:', error.message);
    }
    
    console.log('');
  }

  async testFeedback() {
    console.log('👍 Teste 5: Sistema de Feedback');
    
    if (!this.ragService) {
      console.log('❌ Sistema não inicializado');
      return;
    }

    try {
      await this.ragService.recordFeedback(
        'test-conversation-123',
        'test-message-456',
        5,
        'excellent',
        'Resposta muito útil e completa!'
      );
      
      console.log('✅ Feedback registrado com sucesso');
      this.testResults.feedback = true;
      
    } catch (error) {
      console.log('❌ Erro no feedback:', error.message);
    }
    
    console.log('');
  }

  async testPerformance() {
    console.log('⚡ Teste 6: Performance do Sistema');
    
    if (!this.ragService) {
      console.log('❌ Sistema não inicializado');
      return;
    }

    try {
      const stats = await this.ragService.getSystemStats();
      
      if (stats) {
        console.log('✅ Estatísticas do sistema obtidas');
        console.log(`   Total de documentos: ${stats.totalDocuments}`);
        console.log(`   Total de chunks: ${stats.totalChunks}`);
        console.log(`   Tamanho do índice: ${(stats.indexSize / 1024 / 1024).toFixed(2)} MB`);
        console.log(`   Última atualização: ${stats.lastUpdated.toLocaleString()}`);
        
        this.testResults.performance = {
          totalDocuments: stats.totalDocuments,
          totalChunks: stats.totalChunks,
          indexSize: stats.indexSize,
          lastUpdated: stats.lastUpdated
        };
        
      } else {
        console.log('⚠️  Estatísticas não disponíveis');
      }
      
    } catch (error) {
      console.log('❌ Erro na performance:', error.message);
    }
    
    console.log('');
  }

  printResults() {
    console.log('📋 RESUMO DOS TESTES\n');
    console.log('='.repeat(50));
    
    // Inicialização
    console.log(`Inicialização: ${this.testResults.initialization ? '✅ PASSOU' : '❌ FALHOU'}`);
    
    // Processamento de documentos
    const docSuccess = this.testResults.documentProcessing.filter(d => d.success).length;
    const docTotal = this.testResults.documentProcessing.length;
    console.log(`Processamento de Documentos: ${docSuccess}/${docTotal} sucessos`);
    
    // Respostas
    const querySuccess = this.testResults.queryResponses.filter(q => q.success).length;
    const queryTotal = this.testResults.queryResponses.length;
    console.log(`Geração de Respostas: ${querySuccess}/${queryTotal} sucessos`);
    
    // Monitoramento
    console.log(`Monitoramento: ${this.testResults.monitoring ? '✅ ATIVO' : '❌ INATIVO'}`);
    
    // Feedback
    console.log(`Feedback: ${this.testResults.feedback ? '✅ FUNCIONANDO' : '❌ FALHOU'}`);
    
    console.log('='.repeat(50));
    
    // Performance média
    if (this.testResults.queryResponses.length > 0) {
      const avgTime = this.testResults.queryResponses
        .filter(q => q.success)
        .reduce((sum, q) => sum + q.processingTime, 0) / querySuccess;
      
      const avgConfidence = this.testResults.queryResponses
        .filter(q => q.success)
        .reduce((sum, q) => sum + q.confidence, 0) / querySuccess;
      
      console.log(`\n📊 MÉTRICAS DE PERFORMANCE:`);
      console.log(`   Tempo médio de resposta: ${avgTime.toFixed(0)}ms`);
      console.log(`   Confiança média: ${(avgConfidence * 100).toFixed(1)}%`);
    }
    
    console.log('\n🎉 Testes concluídos!');
    
    // Verificar se sistema está pronto para produção
    const readyForProduction = 
      this.testResults.initialization &&
      docSuccess === docTotal &&
      querySuccess >= queryTotal * 0.8 && // 80% de sucesso mínimo
      this.testResults.monitoring;
    
    console.log(`\n${readyForProduction ? '🚀 SISTEMA PRONTO PARA PRODUÇÃO!' : '⚠️  SISTEMA PRECISA DE AJUSTES'}`);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Executar testes se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  const tester = new VereadoraSystemTester();
  tester.runAllTests().catch(console.error);
}

export { VereadoraSystemTester };
