var Zp=Object.defineProperty;var Rd=f=>{throw TypeError(f)};var tf=(f,t,e)=>t in f?Zp(f,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):f[t]=e;var F=(f,t,e)=>tf(f,typeof t!="symbol"?t+"":t,e),zl=(f,t,e)=>t.has(f)||Rd("Cannot "+e);var n=(f,t,e)=>(zl(f,t,"read from private field"),e?e.call(f):t.get(f)),b=(f,t,e)=>t.has(f)?Rd("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(f):t.set(f,e),u=(f,t,e,s)=>(zl(f,t,"write to private field"),s?s.call(f,e):t.set(f,e),e),A=(f,t,e)=>(zl(f,t,"access private method"),e);var Kt=(f,t,e,s)=>({set _(i){u(f,t,i,e)},get _(){return n(f,t,s)}});var Kl={d:(f,t)=>{for(var e in t)Kl.o(t,e)&&!Kl.o(f,e)&&Object.defineProperty(f,e,{enumerable:!0,get:t[e]})},o:(f,t)=>Object.prototype.hasOwnProperty.call(f,t)},H=globalThis.pdfjsLib={};Kl.d(H,{AbortException:()=>zi,AnnotationEditorLayer:()=>ad,AnnotationEditorParamsType:()=>Y,AnnotationEditorType:()=>V,AnnotationEditorUIManager:()=>ir,AnnotationLayer:()=>Jg,AnnotationMode:()=>li,ColorPicker:()=>dl,DOMSVGFactory:()=>yd,DrawLayer:()=>ld,FeatureTest:()=>se,GlobalWorkerOptions:()=>ei,ImageKind:()=>kh,InvalidPDFException:()=>Jl,MissingPDFException:()=>Ua,OPS:()=>ze,OutputScale:()=>ec,PDFDataRangeTransport:()=>ep,PDFDateString:()=>bd,PDFWorker:()=>br,PasswordResponses:()=>Df,PermissionFlag:()=>hf,PixelsPerInch:()=>ji,RenderingCancelledException:()=>gd,TextLayer:()=>Wa,TouchManager:()=>hl,UnexpectedResponseException:()=>rl,Util:()=>D,VerbosityLevel:()=>Dl,XfaLayer:()=>ip,build:()=>Dg,createValidAbsoluteUrl:()=>Nf,fetchData:()=>Ol,getDocument:()=>Cg,getFilenameFromUrl:()=>Hf,getPdfFilenameFromUrl:()=>$f,getXfaPageViewport:()=>zf,isDataScheme:()=>Bl,isPdfFile:()=>md,noContextMenu:()=>es,normalizeUnicode:()=>Bf,setLayerDimensions:()=>sr,shadow:()=>X,stopEvent:()=>Ce,version:()=>Ig});const Xt=!(typeof process!="object"||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&process.type!=="browser"),eu=[1,0,0,1,0,0],Ql=[.001,0,0,.001,0,0],jl=1.35,ef=1,ud=2,nl=4,sf=16,nf=32,rf=64,af=128,of=256,li={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},V={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15},Y={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35,DRAW_STEP:41},hf={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},Mh=0,Gl=1,xa=2,lf=3,Td=3,cf=4,kh={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},df=1,uf=2,pf=3,ff=4,gf=5,mf=6,bf=7,Af=8,vf=9,yf=10,wf=11,_f=12,xf=13,Sf=14,Ef=15,su=16,Cf=17,Mf=20,kf=1,Rf=2,Tf=3,Pf=4,If=5,Dl={ERRORS:0,WARNINGS:1,INFOS:5},ze={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91,setStrokeTransparent:92,setFillTransparent:93},Df={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let Ll=Dl.WARNINGS;function Lf(f){Number.isInteger(f)&&(Ll=f)}function Ff(){return Ll}function Fl(f){Ll>=Dl.INFOS&&console.log(`Info: ${f}`)}function U(f){Ll>=Dl.WARNINGS&&console.log(`Warning: ${f}`)}function at(f){throw new Error(f)}function _t(f,t){f||at(t)}function Nf(f,t=null,e=null){if(!f)return null;try{if(e&&typeof f=="string"&&(e.addDefaultProtocol&&f.startsWith("www.")&&f.match(/\./g)?.length>=2&&(f=`http://${f}`),e.tryConvertEncoding))try{f=function(r){return decodeURIComponent(escape(r))}(f)}catch{}const s=t?new URL(f,t):new URL(f);if(function(r){switch(r?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(s))return s}catch{}return null}function X(f,t,e,s=!1){return Object.defineProperty(f,t,{value:e,enumerable:!s,configurable:!0,writable:!1}),e}const Vi=function(){function t(e,s){this.message=e,this.name=s}return t.prototype=new Error,t.constructor=t,t}();class Pd extends Vi{constructor(t,e){super(t,"PasswordException"),this.code=e}}class Vl extends Vi{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}class Jl extends Vi{constructor(t){super(t,"InvalidPDFException")}}class Ua extends Vi{constructor(t){super(t,"MissingPDFException")}}class rl extends Vi{constructor(t,e){super(t,"UnexpectedResponseException"),this.status=e}}class Of extends Vi{constructor(t){super(t,"FormatError")}}class zi extends Vi{constructor(t){super(t,"AbortException")}}function iu(f){typeof f=="object"&&f?.length!==void 0||at("Invalid argument for bytesToString");const t=f.length,e=8192;if(t<e)return String.fromCharCode.apply(null,f);const s=[];for(let i=0;i<t;i+=e){const r=Math.min(i+e,t),a=f.subarray(i,r);s.push(String.fromCharCode.apply(null,a))}return s.join("")}function Nl(f){typeof f!="string"&&at("Invalid argument for stringToBytes");const t=f.length,e=new Uint8Array(t);for(let s=0;s<t;++s)e[s]=255&f.charCodeAt(s);return e}function pd(f){const t=Object.create(null);for(const[e,s]of f)t[e]=s;return t}class se{static get isLittleEndian(){return X(this,"isLittleEndian",function(){const e=new Uint8Array(4);return e[0]=1,new Uint32Array(e.buffer,0,1)[0]===1}())}static get isEvalSupported(){return X(this,"isEvalSupported",function(){try{return new Function(""),!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return X(this,"isOffscreenCanvasSupported",typeof OffscreenCanvas<"u")}static get isImageDecoderSupported(){return X(this,"isImageDecoderSupported",typeof ImageDecoder<"u")}static get platform(){return typeof navigator<"u"&&typeof navigator?.platform=="string"?X(this,"platform",{isMac:navigator.platform.includes("Mac"),isWindows:navigator.platform.includes("Win"),isFirefox:typeof navigator?.userAgent=="string"&&navigator.userAgent.includes("Firefox")}):X(this,"platform",{isMac:!1,isWindows:!1,isFirefox:!1})}static get isCSSRoundSupported(){return X(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const Ul=Array.from(Array(256).keys(),f=>f.toString(16).padStart(2,"0"));var si,Rh,Zl;class D{static makeHexColor(t,e,s){return`#${Ul[t]}${Ul[e]}${Ul[s]}`}static scaleMinMax(t,e){let s;t[0]?(t[0]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[0],e[2]*=t[0],t[3]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[3],e[3]*=t[3]):(s=e[0],e[0]=e[1],e[1]=s,s=e[2],e[2]=e[3],e[3]=s,t[1]<0&&(s=e[1],e[1]=e[3],e[3]=s),e[1]*=t[1],e[3]*=t[1],t[2]<0&&(s=e[0],e[0]=e[2],e[2]=s),e[0]*=t[2],e[2]*=t[2]),e[0]+=t[4],e[1]+=t[5],e[2]+=t[4],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){return[t[0]*e[0]+t[1]*e[2]+e[4],t[0]*e[1]+t[1]*e[3]+e[5]]}static applyInverseTransform(t,e){const s=e[0]*e[3]-e[1]*e[2];return[(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/s,(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/s]}static getAxialAlignedBoundingBox(t,e){const s=this.applyTransform(t,e),i=this.applyTransform(t.slice(2,4),e),r=this.applyTransform([t[0],t[3]],e),a=this.applyTransform([t[2],t[1]],e);return[Math.min(s[0],i[0],r[0],a[0]),Math.min(s[1],i[1],r[1],a[1]),Math.max(s[0],i[0],r[0],a[0]),Math.max(s[1],i[1],r[1],a[1])]}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t){const e=[t[0],t[2],t[1],t[3]],s=t[0]*e[0]+t[1]*e[2],i=t[0]*e[1]+t[1]*e[3],r=t[2]*e[0]+t[3]*e[2],a=t[2]*e[1]+t[3]*e[3],o=(s+a)/2,h=Math.sqrt((s+a)**2-4*(s*a-r*i))/2,l=o+h||1,c=o-h||1;return[Math.sqrt(l),Math.sqrt(c)]}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const s=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),i=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(s>i)return null;const r=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),a=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return r>a?null:[s,r,i,a]}static bezierBoundingBox(t,e,s,i,r,a,o,h,l){return l?(l[0]=Math.min(l[0],t,o),l[1]=Math.min(l[1],e,h),l[2]=Math.max(l[2],t,o),l[3]=Math.max(l[3],e,h)):l=[Math.min(t,o),Math.min(e,h),Math.max(t,o),Math.max(e,h)],A(this,si,Zl).call(this,t,s,r,o,e,i,a,h,3*(3*(s-r)-t+o),6*(t-2*s+r),3*(s-t),l),A(this,si,Zl).call(this,t,s,r,o,e,i,a,h,3*(3*(i-a)-e+h),6*(e-2*i+a),3*(i-e),l),l}}si=new WeakSet,Rh=function(t,e,s,i,r,a,o,h,l,c){if(l<=0||l>=1)return;const d=1-l,p=l*l,g=p*l,m=d*(d*(d*t+3*l*e)+3*p*s)+g*i,v=d*(d*(d*r+3*l*a)+3*p*o)+g*h;c[0]=Math.min(c[0],m),c[1]=Math.min(c[1],v),c[2]=Math.max(c[2],m),c[3]=Math.max(c[3],v)},Zl=function(t,e,s,i,r,a,o,h,l,c,d,p){if(Math.abs(l)<1e-12){Math.abs(c)>=1e-12&&A(this,si,Rh).call(this,t,e,s,i,r,a,o,h,-d/c,p);return}const g=c**2-4*d*l;if(g<0)return;const m=Math.sqrt(g),v=2*l;A(this,si,Rh).call(this,t,e,s,i,r,a,o,h,(-c+m)/v,p),A(this,si,Rh).call(this,t,e,s,i,r,a,o,h,(-c-m)/v,p)},b(D,si);let Wl=null,Id=null;function Bf(f){return Wl||(Wl=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,Id=new Map([["ﬅ","ſt"]])),f.replaceAll(Wl,(t,e,s)=>e?e.normalize("NFKC"):Id.get(s))}const fd="pdfjs_internal_id_";typeof Promise.try!="function"&&(Promise.try=function(f,...t){return new Promise(e=>{e(f(...t))})});const Es="http://www.w3.org/2000/svg",Xi=class Xi{};F(Xi,"CSS",96),F(Xi,"PDF",72),F(Xi,"PDF_TO_CSS_UNITS",Xi.CSS/Xi.PDF);let ji=Xi;async function Ol(f,t="text"){if(Ma(f,document.baseURI)){const e=await fetch(f);if(!e.ok)throw new Error(e.statusText);switch(t){case"arraybuffer":return e.arrayBuffer();case"blob":return e.blob();case"json":return e.json()}return e.text()}return new Promise((e,s)=>{const i=new XMLHttpRequest;i.open("GET",f,!0),i.responseType=t,i.onreadystatechange=()=>{if(i.readyState===XMLHttpRequest.DONE)if(i.status!==200&&i.status!==0)s(new Error(i.statusText));else{switch(t){case"arraybuffer":case"blob":case"json":e(i.response);return}e(i.responseText)}},i.send(null)})}class yh{constructor({viewBox:t,userUnit:e,scale:s,rotation:i,offsetX:r=0,offsetY:a=0,dontFlip:o=!1}){this.viewBox=t,this.userUnit=e,this.scale=s,this.rotation=i,this.offsetX=r,this.offsetY=a,s*=e;const h=(t[2]+t[0])/2,l=(t[3]+t[1])/2;let c,d,p,g,m,v,y,w;switch((i%=360)<0&&(i+=360),i){case 180:c=-1,d=0,p=0,g=1;break;case 90:c=0,d=1,p=1,g=0;break;case 270:c=0,d=-1,p=-1,g=0;break;case 0:c=1,d=0,p=0,g=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}o&&(p=-p,g=-g),c===0?(m=Math.abs(l-t[1])*s+r,v=Math.abs(h-t[0])*s+a,y=(t[3]-t[1])*s,w=(t[2]-t[0])*s):(m=Math.abs(h-t[0])*s+r,v=Math.abs(l-t[1])*s+a,y=(t[2]-t[0])*s,w=(t[3]-t[1])*s),this.transform=[c*s,d*s,p*s,g*s,m-c*s*h-p*s*l,v-d*s*h-g*s*l],this.width=y,this.height=w}get rawDims(){const{userUnit:t,viewBox:e}=this,s=e.map(i=>i*t);return X(this,"rawDims",{pageWidth:s[2]-s[0],pageHeight:s[3]-s[1],pageX:s[0],pageY:s[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:s=this.offsetX,offsetY:i=this.offsetY,dontFlip:r=!1}={}){return new yh({viewBox:this.viewBox.slice(),userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:r})}convertToViewportPoint(t,e){return D.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){const e=D.applyTransform([t[0],t[1]],this.transform),s=D.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],s[0],s[1]]}convertToPdfPoint(t,e){return D.applyInverseTransform([t,e],this.transform)}}class gd extends Vi{constructor(t,e=0){super(t,"RenderingCancelledException"),this.extraDelay=e}}function Bl(f){const t=f.length;let e=0;for(;e<t&&f[e].trim()==="";)e++;return f.substring(e,e+5).toLowerCase()==="data:"}function md(f){return typeof f=="string"&&/\.pdf$/i.test(f)}function Hf(f){return[f]=f.split(/[#?]/,1),f.substring(f.lastIndexOf("/")+1)}function $f(f,t="document.pdf"){if(typeof f!="string")return t;if(Bl(f))return U('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),t;const e=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,s=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(f);let i=e.exec(s[1])||e.exec(s[2])||e.exec(s[3]);if(i&&(i=i[0],i.includes("%")))try{i=e.exec(decodeURIComponent(i))[0]}catch{}return i||t}class Dd{constructor(){F(this,"started",Object.create(null));F(this,"times",[])}time(t){t in this.started&&U(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){t in this.started||U(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const{name:s}of this.times)e=Math.max(s.length,e);for(const{name:s,start:i,end:r}of this.times)t.push(`${s.padEnd(e)} ${r-i}ms
`);return t.join("")}}function Ma(f,t){try{const{protocol:e}=t?new URL(f,t):new URL(f);return e==="http:"||e==="https:"}catch{return!1}}function es(f){f.preventDefault()}function Ce(f){f.preventDefault(),f.stopPropagation()}var Xa;class bd{static toDateObject(t){if(!t||typeof t!="string")return null;n(this,Xa)||u(this,Xa,new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const e=n(this,Xa).exec(t);if(!e)return null;const s=parseInt(e[1],10);let i=parseInt(e[2],10);i=i>=1&&i<=12?i-1:0;let r=parseInt(e[3],10);r=r>=1&&r<=31?r:1;let a=parseInt(e[4],10);a=a>=0&&a<=23?a:0;let o=parseInt(e[5],10);o=o>=0&&o<=59?o:0;let h=parseInt(e[6],10);h=h>=0&&h<=59?h:0;const l=e[7]||"Z";let c=parseInt(e[8],10);c=c>=0&&c<=23?c:0;let d=parseInt(e[9],10)||0;return d=d>=0&&d<=59?d:0,l==="-"?(a+=c,o+=d):l==="+"&&(a-=c,o-=d),new Date(Date.UTC(s,i,r,a,o,h))}}Xa=new WeakMap,b(bd,Xa);function zf(f,{scale:t=1,rotation:e=0}){const{width:s,height:i}=f.attributes.style,r=[0,0,parseInt(s),parseInt(i)];return new yh({viewBox:r,userUnit:1,scale:t,rotation:e})}function tc(f){if(f.startsWith("#")){const t=parseInt(f.slice(1),16);return[(16711680&t)>>16,(65280&t)>>8,255&t]}return f.startsWith("rgb(")?f.slice(4,-1).split(",").map(t=>parseInt(t)):f.startsWith("rgba(")?f.slice(5,-1).split(",").map(t=>parseInt(t)).slice(0,3):(U(`Not a valid color format: "${f}"`),[0,0,0])}function ct(f){const{a:t,b:e,c:s,d:i,e:r,f:a}=f.getTransform();return[t,e,s,i,r,a]}function is(f){const{a:t,b:e,c:s,d:i,e:r,f:a}=f.getTransform().invertSelf();return[t,e,s,i,r,a]}function sr(f,t,e=!1,s=!0){if(t instanceof yh){const{pageWidth:i,pageHeight:r}=t.rawDims,{style:a}=f,o=se.isCSSRoundSupported,h=`var(--scale-factor) * ${i}px`,l=`var(--scale-factor) * ${r}px`,c=o?`round(down, ${h}, var(--scale-round-x, 1px))`:`calc(${h})`,d=o?`round(down, ${l}, var(--scale-round-y, 1px))`:`calc(${l})`;e&&t.rotation%180!=0?(a.width=d,a.height=c):(a.width=c,a.height=d)}s&&f.setAttribute("data-main-rotation",t.rotation)}class ec{constructor(){const t=window.devicePixelRatio||1;this.sx=t,this.sy=t}get scaled(){return this.sx!==1||this.sy!==1}get symmetric(){return this.sx===this.sy}}var ci,Qi,je,Ji,Ya,Ka,fl,nu,ie,ru,au,Th,ou,ic;const ks=class ks{constructor(t){b(this,ie);b(this,ci,null);b(this,Qi,null);b(this,je);b(this,Ji,null);b(this,Ya,null);u(this,je,t),n(ks,Ka)||u(ks,Ka,Object.freeze({freetext:"pdfjs-editor-remove-freetext-button",highlight:"pdfjs-editor-remove-highlight-button",ink:"pdfjs-editor-remove-ink-button",stamp:"pdfjs-editor-remove-stamp-button"}))}render(){const t=u(this,ci,document.createElement("div"));t.classList.add("editToolbar","hidden"),t.setAttribute("role","toolbar");const e=n(this,je)._uiManager._signal;t.addEventListener("contextmenu",es,{signal:e}),t.addEventListener("pointerdown",A(ks,fl,nu),{signal:e});const s=u(this,Ji,document.createElement("div"));s.className="buttons",t.append(s);const i=n(this,je).toolbarPosition;if(i){const{style:r}=t,a=n(this,je)._uiManager.direction==="ltr"?1-i[0]:i[0];r.insetInlineEnd=100*a+"%",r.top=`calc(${100*i[1]}% + var(--editor-toolbar-vert-offset))`}return A(this,ie,ou).call(this),t}get div(){return n(this,ci)}hide(){n(this,ci).classList.add("hidden"),n(this,Qi)?.hideDropdown()}show(){n(this,ci).classList.remove("hidden"),n(this,Ya)?.shown()}async addAltText(t){const e=await t.render();A(this,ie,Th).call(this,e),n(this,Ji).prepend(e,n(this,ie,ic)),u(this,Ya,t)}addColorPicker(t){u(this,Qi,t);const e=t.renderButton();A(this,ie,Th).call(this,e),n(this,Ji).prepend(e,n(this,ie,ic))}remove(){n(this,ci).remove(),n(this,Qi)?.destroy(),u(this,Qi,null)}};ci=new WeakMap,Qi=new WeakMap,je=new WeakMap,Ji=new WeakMap,Ya=new WeakMap,Ka=new WeakMap,fl=new WeakSet,nu=function(t){t.stopPropagation()},ie=new WeakSet,ru=function(t){n(this,je)._focusEventsAllowed=!1,Ce(t)},au=function(t){n(this,je)._focusEventsAllowed=!0,Ce(t)},Th=function(t){const e=n(this,je)._uiManager._signal;t.addEventListener("focusin",A(this,ie,ru).bind(this),{capture:!0,signal:e}),t.addEventListener("focusout",A(this,ie,au).bind(this),{capture:!0,signal:e}),t.addEventListener("contextmenu",es,{signal:e})},ou=function(){const{editorType:t,_uiManager:e}=n(this,je),s=document.createElement("button");s.className="delete",s.tabIndex=0,s.setAttribute("data-l10n-id",n(ks,Ka)[t]),A(this,ie,Th).call(this,s),s.addEventListener("click",i=>{e.delete()},{signal:e._signal}),n(this,Ji).append(s)},ic=function(){const t=document.createElement("div");return t.className="divider",t},b(ks,fl),b(ks,Ka,null);let sc=ks;var Qa,Zi,tn,Gi,hu,lu,cu;class jf{constructor(t){b(this,Gi);b(this,Qa,null);b(this,Zi,null);b(this,tn);u(this,tn,t)}show(t,e,s){const[i,r]=A(this,Gi,lu).call(this,e,s),{style:a}=n(this,Zi)||u(this,Zi,A(this,Gi,hu).call(this));t.append(n(this,Zi)),a.insetInlineEnd=100*i+"%",a.top=`calc(${100*r}% + var(--editor-toolbar-vert-offset))`}hide(){n(this,Zi).remove()}}Qa=new WeakMap,Zi=new WeakMap,tn=new WeakMap,Gi=new WeakSet,hu=function(){const t=u(this,Zi,document.createElement("div"));t.className="editToolbar",t.setAttribute("role","toolbar"),t.addEventListener("contextmenu",es,{signal:n(this,tn)._signal});const e=u(this,Qa,document.createElement("div"));return e.className="buttons",t.append(e),A(this,Gi,cu).call(this),t},lu=function(t,e){let s=0,i=0;for(const r of t){const a=r.y+r.height;if(a<s)continue;const o=r.x+(e?r.width:0);a>s?(i=o,s=a):e?o>i&&(i=o):o<i&&(i=o)}return[e?1-i:i,s]},cu=function(){const t=document.createElement("button");t.className="highlightButton",t.tabIndex=0,t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e),e.className="visuallyHidden",e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const s=n(this,tn)._signal;t.addEventListener("contextmenu",es,{signal:s}),t.addEventListener("click",()=>{n(this,tn).highlightSelection("floating_button")},{signal:s}),n(this,Qa).append(t)};function al(f,t,e){for(const s of e)t.addEventListener(s,f[s].bind(f))}var gl;class Gf{constructor(){b(this,gl,0)}get id(){return"pdfjs_internal_editor_"+Kt(this,gl)._++}}gl=new WeakMap;var Ar,Ja,Vt,vr,Ph;const _d=class _d{constructor(){b(this,vr);b(this,Ar,function(){if(typeof crypto.randomUUID=="function")return crypto.randomUUID();const e=new Uint8Array(32);return crypto.getRandomValues(e),iu(e)}());b(this,Ja,0);b(this,Vt,null)}static get _isSVGFittingCanvas(){const t=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),e=new Image;return e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',X(this,"_isSVGFittingCanvas",e.decode().then(()=>(t.drawImage(e,0,0,1,1,0,0,1,3),new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0]===0)))}async getFromFile(t){const{lastModified:e,name:s,size:i,type:r}=t;return A(this,vr,Ph).call(this,`${e}_${s}_${i}_${r}`,t)}async getFromUrl(t){return A(this,vr,Ph).call(this,t,t)}async getFromBlob(t,e){const s=await e;return A(this,vr,Ph).call(this,t,s)}async getFromId(t){n(this,Vt)||u(this,Vt,new Map);const e=n(this,Vt).get(t);if(!e)return null;if(e.bitmap)return e.refCounter+=1,e;if(e.file)return this.getFromFile(e.file);if(e.blobPromise){const{blobPromise:s}=e;return delete e.blobPromise,this.getFromBlob(e.id,s)}return this.getFromUrl(e.url)}getFromCanvas(t,e){n(this,Vt)||u(this,Vt,new Map);let s=n(this,Vt).get(t);if(s?.bitmap)return s.refCounter+=1,s;const i=new OffscreenCanvas(e.width,e.height);return i.getContext("2d").drawImage(e,0,0),s={bitmap:i.transferToImageBitmap(),id:`image_${n(this,Ar)}_${Kt(this,Ja)._++}`,refCounter:1,isSvg:!1},n(this,Vt).set(t,s),n(this,Vt).set(s.id,s),s}getSvgUrl(t){const e=n(this,Vt).get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){n(this,Vt)||u(this,Vt,new Map);const e=n(this,Vt).get(t);if(!e||(e.refCounter-=1,e.refCounter!==0))return;const{bitmap:s}=e;if(!e.url&&!e.file){const i=new OffscreenCanvas(s.width,s.height);i.getContext("bitmaprenderer").transferFromImageBitmap(s),e.blobPromise=i.convertToBlob()}s.close?.(),e.bitmap=null}isValidId(t){return t.startsWith(`image_${n(this,Ar)}_`)}};Ar=new WeakMap,Ja=new WeakMap,Vt=new WeakMap,vr=new WeakSet,Ph=async function(t,e){n(this,Vt)||u(this,Vt,new Map);let s=n(this,Vt).get(t);if(s===null)return null;if(s?.bitmap)return s.refCounter+=1,s;try{s||(s={bitmap:null,id:`image_${n(this,Ar)}_${Kt(this,Ja)._++}`,refCounter:0,isSvg:!1});let i;if(typeof e=="string"?(s.url=e,i=await Ol(e,"blob")):e instanceof File?i=s.file=e:e instanceof Blob&&(i=e),i.type==="image/svg+xml"){const r=_d._isSVGFittingCanvas,a=new FileReader,o=new Image,h=new Promise((l,c)=>{o.onload=()=>{s.bitmap=o,s.isSvg=!0,l()},a.onload=async()=>{const d=s.svgUrl=a.result;o.src=await r?`${d}#svgView(preserveAspectRatio(none))`:d},o.onerror=a.onerror=c});a.readAsDataURL(i),await h}else s.bitmap=await createImageBitmap(i);s.refCounter=1}catch(i){U(i),s=null}return n(this,Vt).set(t,s),s&&n(this,Vt).set(s.id,s),s};let nc=_d;var vt,di,Za,pt;class Vf{constructor(t=128){b(this,vt,[]);b(this,di,!1);b(this,Za);b(this,pt,-1);u(this,Za,t)}add({cmd:t,undo:e,post:s,mustExec:i,type:r=NaN,overwriteIfSameType:a=!1,keepUndo:o=!1}){if(i&&t(),n(this,di))return;const h={cmd:t,undo:e,post:s,type:r};if(n(this,pt)===-1){n(this,vt).length>0&&(n(this,vt).length=0),u(this,pt,0),n(this,vt).push(h);return}if(a&&n(this,vt)[n(this,pt)].type===r){o&&(h.undo=n(this,vt)[n(this,pt)].undo),n(this,vt)[n(this,pt)]=h;return}const l=n(this,pt)+1;l===n(this,Za)?n(this,vt).splice(0,1):(u(this,pt,l),l<n(this,vt).length&&n(this,vt).splice(l)),n(this,vt).push(h)}undo(){if(n(this,pt)===-1)return;u(this,di,!0);const{undo:t,post:e}=n(this,vt)[n(this,pt)];t(),e?.(),u(this,di,!1),u(this,pt,n(this,pt)-1)}redo(){if(n(this,pt)<n(this,vt).length-1){u(this,pt,n(this,pt)+1),u(this,di,!0);const{cmd:t,post:e}=n(this,vt)[n(this,pt)];t(),e?.(),u(this,di,!1)}}hasSomethingToUndo(){return n(this,pt)!==-1}hasSomethingToRedo(){return n(this,pt)<n(this,vt).length-1}cleanType(t){if(n(this,pt)!==-1){for(let e=n(this,pt);e>=0;e--)if(n(this,vt)[e].type!==t){n(this,vt).splice(e+1,n(this,pt)-e),u(this,pt,e);return}n(this,vt).length=0,u(this,pt,-1)}}destroy(){u(this,vt,null)}}vt=new WeakMap,di=new WeakMap,Za=new WeakMap,pt=new WeakMap;var ml,du;class wh{constructor(t){b(this,ml);this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:e}=se.platform;for(const[s,i,r={}]of t)for(const a of s){const o=a.startsWith("mac+");e&&o?(this.callbacks.set(a.slice(4),{callback:i,options:r}),this.allKeys.add(a.split("+").at(-1))):!e&&!o&&(this.callbacks.set(a,{callback:i,options:r}),this.allKeys.add(a.split("+").at(-1)))}}exec(t,e){if(!this.allKeys.has(e.key))return;const s=this.callbacks.get(A(this,ml,du).call(this,e));if(!s)return;const{callback:i,options:{bubbles:r=!1,args:a=[],checker:o=null}}=s;(!o||o(t,e))&&(i.bind(t,...a,e)(),r||Ce(e))}}ml=new WeakSet,du=function(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e};const bl=class bl{get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return function(s){const i=document.createElement("span");i.style.visibility="hidden",document.body.append(i);for(const r of s.keys()){i.style.color=r;const a=window.getComputedStyle(i).color;s.set(r,tc(a))}i.remove()}(t),X(this,"_colors",t)}convert(t){const e=tc(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[s,i]of this._colors)if(i.every((r,a)=>r===e[a]))return bl._colorsMapping.get(s);return e}getHexCode(t){const e=this._colors.get(t);return e?D.makeHexColor(...e):t}};F(bl,"_colorsMapping",new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]));let rc=bl;var yr,pe,Et,Nt,wr,Ts,_r,Te,ui,en,xr,sn,rs,Ge,nn,to,eo,Sr,so,as,pi,Er,fi,os,Al,gi,io,mi,rn,no,ro,Rt,tt,Ps,an,ao,oo,bi,hs,Is,ho,Pe,M,Ih,ac,uu,pu,Dh,fu,gu,mu,oc,bu,hc,lc,Au,Qt,Cs,vu,yu,cc,wu,ka,dc;const fr=class fr{constructor(t,e,s,i,r,a,o,h,l,c,d,p,g){b(this,M);b(this,yr,new AbortController);b(this,pe,null);b(this,Et,new Map);b(this,Nt,new Map);b(this,wr,null);b(this,Ts,null);b(this,_r,null);b(this,Te,new Vf);b(this,ui,null);b(this,en,null);b(this,xr,0);b(this,sn,new Set);b(this,rs,null);b(this,Ge,null);b(this,nn,new Set);F(this,"_editorUndoBar",null);b(this,to,!1);b(this,eo,!1);b(this,Sr,!1);b(this,so,null);b(this,as,null);b(this,pi,null);b(this,Er,null);b(this,fi,!1);b(this,os,null);b(this,Al,new Gf);b(this,gi,!1);b(this,io,!1);b(this,mi,null);b(this,rn,null);b(this,no,null);b(this,ro,null);b(this,Rt,V.NONE);b(this,tt,new Set);b(this,Ps,null);b(this,an,null);b(this,ao,null);b(this,oo,{isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1});b(this,bi,[0,0]);b(this,hs,null);b(this,Is,null);b(this,ho,null);b(this,Pe,null);const m=this._signal=n(this,yr).signal;u(this,Is,t),u(this,ho,e),u(this,wr,s),this._eventBus=i,i._on("editingaction",this.onEditingAction.bind(this),{signal:m}),i._on("pagechanging",this.onPageChanging.bind(this),{signal:m}),i._on("scalechanging",this.onScaleChanging.bind(this),{signal:m}),i._on("rotationchanging",this.onRotationChanging.bind(this),{signal:m}),i._on("setpreference",this.onSetPreference.bind(this),{signal:m}),i._on("switchannotationeditorparams",v=>this.updateParams(v.type,v.value),{signal:m}),A(this,M,fu).call(this),A(this,M,Au).call(this),A(this,M,oc).call(this),u(this,Ts,r.annotationStorage),u(this,so,r.filterFactory),u(this,an,a),u(this,Er,o||null),u(this,to,h),u(this,eo,l),u(this,Sr,c),u(this,ro,d||null),this.viewParameters={realScale:ji.PDF_TO_CSS_UNITS,rotation:0},this.isShiftKeyDown=!1,this._editorUndoBar=p||null,this._supportsPinchToZoom=g!==!1}static get _keyboardManager(){const t=fr.prototype,e=a=>n(a,Is).contains(document.activeElement)&&document.activeElement.tagName!=="BUTTON"&&a.hasSomethingToControl(),s=(a,{target:o})=>{if(o instanceof HTMLInputElement){const{type:h}=o;return h!=="text"&&h!=="number"}return!0},i=this.TRANSLATE_SMALL,r=this.TRANSLATE_BIG;return X(this,"_keyboardManager",new wh([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:s}],[["ctrl+z","mac+meta+z"],t.undo,{checker:s}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:s}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:s}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(a,{target:o})=>!(o instanceof HTMLButtonElement)&&n(a,Is).contains(o)&&!a.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(a,{target:o})=>!(o instanceof HTMLButtonElement)&&n(a,Is).contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-r,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[r,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-r],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,r],checker:e}]]))}destroy(){n(this,Pe)?.resolve(),u(this,Pe,null),n(this,yr)?.abort(),u(this,yr,null),this._signal=null;for(const t of n(this,Nt).values())t.destroy();n(this,Nt).clear(),n(this,Et).clear(),n(this,nn).clear(),u(this,pe,null),n(this,tt).clear(),n(this,Te).destroy(),n(this,wr)?.destroy(),n(this,os)?.hide(),u(this,os,null),n(this,as)&&(clearTimeout(n(this,as)),u(this,as,null)),n(this,hs)&&(clearTimeout(n(this,hs)),u(this,hs,null)),this._editorUndoBar?.destroy()}combinedSignal(t){return AbortSignal.any([this._signal,t.signal])}get mlManager(){return n(this,ro)}get useNewAltTextFlow(){return n(this,eo)}get useNewAltTextWhenAddingImage(){return n(this,Sr)}get hcmFilter(){return X(this,"hcmFilter",n(this,an)?n(this,so).addHCMFilter(n(this,an).foreground,n(this,an).background):"none")}get direction(){return X(this,"direction",getComputedStyle(n(this,Is)).direction)}get highlightColors(){return X(this,"highlightColors",n(this,Er)?new Map(n(this,Er).split(",").map(t=>t.split("=").map(e=>e.trim()))):null)}get highlightColorNames(){return X(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,t=>t.reverse())):null)}setCurrentDrawingSession(t){t?(this.unselectAll(),this.disableUserSelect(!0)):this.disableUserSelect(!1),u(this,en,t)}setMainHighlightColorPicker(t){u(this,no,t)}editAltText(t,e=!1){n(this,wr)?.editAltText(this,t,e)}switchToMode(t,e){this._eventBus.on("annotationeditormodechanged",e,{once:!0,signal:this._signal}),this._eventBus.dispatch("showannotationeditorui",{source:this,mode:t})}setPreference(t,e){this._eventBus.dispatch("setpreference",{source:this,name:t,value:e})}onSetPreference({name:t,value:e}){t==="enableNewAltTextWhenAddingImage"&&u(this,Sr,e)}onPageChanging({pageNumber:t}){u(this,xr,t-1)}focusMainContainer(){n(this,Is).focus()}findParent(t,e){for(const s of n(this,Nt).values()){const{x:i,y:r,width:a,height:o}=s.div.getBoundingClientRect();if(t>=i&&t<=i+a&&e>=r&&e<=r+o)return s}return null}disableUserSelect(t=!1){n(this,ho).classList.toggle("noUserSelect",t)}addShouldRescale(t){n(this,nn).add(t)}removeShouldRescale(t){n(this,nn).delete(t)}onScaleChanging({scale:t}){this.commitOrRemove(),this.viewParameters.realScale=t*ji.PDF_TO_CSS_UNITS;for(const e of n(this,nn))e.onScaleChanging();n(this,en)?.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove(),this.viewParameters.rotation=t}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:s,anchorOffset:i,focusNode:r,focusOffset:a}=e,o=e.toString(),h=A(this,M,Ih).call(this,e).closest(".textLayer"),l=this.getSelectionBoxes(h);if(!l)return;e.empty();const c=A(this,M,ac).call(this,h),d=n(this,Rt)===V.NONE,p=()=>{c?.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:l,anchorNode:s,anchorOffset:i,focusNode:r,focusOffset:a,text:o}),d&&this.showAllEditors("highlight",!0,!0)};d?this.switchToMode(V.HIGHLIGHT,p):p()}addToAnnotationStorage(t){t.isEmpty()||!n(this,Ts)||n(this,Ts).has(t.id)||n(this,Ts).setValue(t.id,t)}blur(){if(this.isShiftKeyDown=!1,n(this,fi)&&(u(this,fi,!1),A(this,M,Dh).call(this,"main_toolbar")),!this.hasSelection)return;const{activeElement:t}=document;for(const e of n(this,tt))if(e.div.contains(t)){u(this,rn,[e,t]),e._focusEventsAllowed=!1;break}}focus(){if(!n(this,rn))return;const[t,e]=n(this,rn);u(this,rn,null),e.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:this._signal}),e.focus()}addEditListeners(){A(this,M,oc).call(this),A(this,M,hc).call(this)}removeEditListeners(){A(this,M,bu).call(this),A(this,M,lc).call(this)}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const s of n(this,Ge))if(s.isHandlingMimeForPasting(e)){t.dataTransfer.dropEffect="copy",t.preventDefault();return}}drop(t){for(const e of t.dataTransfer.items)for(const s of n(this,Ge))if(s.isHandlingMimeForPasting(e.type)){s.paste(e,this.currentLayer),t.preventDefault();return}}copy(t){if(t.preventDefault(),n(this,pe)?.commitOrRemove(),!this.hasSelection)return;const e=[];for(const s of n(this,tt)){const i=s.serialize(!0);i&&e.push(i)}e.length!==0&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}async paste(t){t.preventDefault();const{clipboardData:e}=t;for(const r of e.items)for(const a of n(this,Ge))if(a.isHandlingMimeForPasting(r.type)){a.paste(r,this.currentLayer);return}let s=e.getData("application/pdfjs");if(!s)return;try{s=JSON.parse(s)}catch(r){U(`paste: "${r.message}".`);return}if(!Array.isArray(s))return;this.unselectAll();const i=this.currentLayer;try{const r=[];for(const h of s){const l=await i.deserialize(h);if(!l)return;r.push(l)}const a=()=>{for(const h of r)A(this,M,cc).call(this,h);A(this,M,dc).call(this,r)},o=()=>{for(const h of r)h.remove()};this.addCommands({cmd:a,undo:o,mustExec:!0})}catch(r){U(`paste: "${r.message}".`)}}keydown(t){this.isShiftKeyDown||t.key!=="Shift"||(this.isShiftKeyDown=!0),n(this,Rt)===V.NONE||this.isEditorHandlingKeyboard||fr._keyboardManager.exec(this,t)}keyup(t){this.isShiftKeyDown&&t.key==="Shift"&&(this.isShiftKeyDown=!1,n(this,fi)&&(u(this,fi,!1),A(this,M,Dh).call(this,"main_toolbar")))}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu")}}setEditingState(t){t?(A(this,M,gu).call(this),A(this,M,hc).call(this),A(this,M,Qt).call(this,{isEditing:n(this,Rt)!==V.NONE,isEmpty:A(this,M,ka).call(this),hasSomethingToUndo:n(this,Te).hasSomethingToUndo(),hasSomethingToRedo:n(this,Te).hasSomethingToRedo(),hasSelectedEditor:!1})):(A(this,M,mu).call(this),A(this,M,lc).call(this),A(this,M,Qt).call(this,{isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!n(this,Ge)){u(this,Ge,t);for(const e of n(this,Ge))A(this,M,Cs).call(this,e.defaultPropertiesToUpdate)}}getId(){return n(this,Al).id}get currentLayer(){return n(this,Nt).get(n(this,xr))}getLayer(t){return n(this,Nt).get(t)}get currentPageIndex(){return n(this,xr)}addLayer(t){n(this,Nt).set(t.pageIndex,t),n(this,gi)?t.enable():t.disable()}removeLayer(t){n(this,Nt).delete(t.pageIndex)}async updateMode(t,e=null,s=!1){if(n(this,Rt)!==t){if(n(this,Pe)&&(await n(this,Pe).promise,!n(this,Pe)))return;if(u(this,Pe,Promise.withResolvers()),u(this,Rt,t),t!==V.NONE){this.setEditingState(!0),await A(this,M,vu).call(this),this.unselectAll();for(const i of n(this,Nt).values())i.updateMode(t);if(e){for(const i of n(this,Et).values())i.annotationElementId===e?(this.setSelected(i),i.enterInEditMode()):i.unselect();n(this,Pe).resolve()}else s&&this.addNewEditorFromKeyboard(),n(this,Pe).resolve()}else this.setEditingState(!1),A(this,M,yu).call(this),this._editorUndoBar?.hide(),n(this,Pe).resolve()}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t!==n(this,Rt)&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){if(n(this,Ge)){switch(t){case Y.CREATE:this.currentLayer.addNewEditor();return;case Y.HIGHLIGHT_DEFAULT_COLOR:n(this,no)?.updateColor(e);break;case Y.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}}),(n(this,ao)||u(this,ao,new Map)).set(t,e),this.showAllEditors("highlight",e)}for(const s of n(this,tt))s.updateParams(t,e);for(const s of n(this,Ge))s.updateDefaultParams(t,e)}}showAllEditors(t,e,s=!1){for(const i of n(this,Et).values())i.editorType===t&&i.show(e);(n(this,ao)?.get(Y.HIGHLIGHT_SHOW_ALL)??!0)!==e&&A(this,M,Cs).call(this,[[Y.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(n(this,io)!==t){u(this,io,t);for(const e of n(this,Nt).values())t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}}getEditors(t){const e=[];for(const s of n(this,Et).values())s.pageIndex===t&&e.push(s);return e}getEditor(t){return n(this,Et).get(t)}addEditor(t){n(this,Et).set(t.id,t)}removeEditor(t){t.div.contains(document.activeElement)&&(n(this,as)&&clearTimeout(n(this,as)),u(this,as,setTimeout(()=>{this.focusMainContainer(),u(this,as,null)},0))),n(this,Et).delete(t.id),this.unselect(t),t.annotationElementId&&n(this,sn).has(t.annotationElementId)||n(this,Ts)?.remove(t.id)}addDeletedAnnotationElement(t){n(this,sn).add(t.annotationElementId),this.addChangedExistingAnnotation(t),t.deleted=!0}isDeletedAnnotationElement(t){return n(this,sn).has(t)}removeDeletedAnnotationElement(t){n(this,sn).delete(t.annotationElementId),this.removeChangedExistingAnnotation(t),t.deleted=!1}setActiveEditor(t){n(this,pe)!==t&&(u(this,pe,t),t&&A(this,M,Cs).call(this,t.propertiesToUpdate))}updateUI(t){n(this,M,wu)===t&&A(this,M,Cs).call(this,t.propertiesToUpdate)}updateUIForDefaultProperties(t){A(this,M,Cs).call(this,t.defaultPropertiesToUpdate)}toggleSelected(t){n(this,tt).has(t)?(n(this,tt).delete(t),t.unselect(),A(this,M,Qt).call(this,{hasSelectedEditor:this.hasSelection})):(n(this,tt).add(t),t.select(),A(this,M,Cs).call(this,t.propertiesToUpdate),A(this,M,Qt).call(this,{hasSelectedEditor:!0}))}setSelected(t){n(this,en)?.commitOrRemove();for(const e of n(this,tt))e!==t&&e.unselect();n(this,tt).clear(),n(this,tt).add(t),t.select(),A(this,M,Cs).call(this,t.propertiesToUpdate),A(this,M,Qt).call(this,{hasSelectedEditor:!0})}isSelected(t){return n(this,tt).has(t)}get firstSelectedEditor(){return n(this,tt).values().next().value}unselect(t){t.unselect(),n(this,tt).delete(t),A(this,M,Qt).call(this,{hasSelectedEditor:this.hasSelection})}get hasSelection(){return n(this,tt).size!==0}get isEnterHandled(){return n(this,tt).size===1&&this.firstSelectedEditor.isEnterHandled}undo(){n(this,Te).undo(),A(this,M,Qt).call(this,{hasSomethingToUndo:n(this,Te).hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:A(this,M,ka).call(this)}),this._editorUndoBar?.hide()}redo(){n(this,Te).redo(),A(this,M,Qt).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:n(this,Te).hasSomethingToRedo(),isEmpty:A(this,M,ka).call(this)})}addCommands(t){n(this,Te).add(t),A(this,M,Qt).call(this,{hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:A(this,M,ka).call(this)})}cleanUndoStack(t){n(this,Te).cleanType(t)}delete(){this.commitOrRemove();const t=this.currentLayer?.endDrawingSession(!0);if(!this.hasSelection&&!t)return;const e=t?[t]:[...n(this,tt)],s=()=>{for(const i of e)A(this,M,cc).call(this,i)};this.addCommands({cmd:()=>{this._editorUndoBar?.show(s,e.length===1?e[0].editorType:e.length);for(const i of e)i.remove()},undo:s,mustExec:!0})}commitOrRemove(){n(this,pe)?.commitOrRemove()}hasSomethingToControl(){return n(this,pe)||this.hasSelection}selectAll(){for(const t of n(this,tt))t.commit();A(this,M,dc).call(this,n(this,Et).values())}unselectAll(){if(!(n(this,pe)&&(n(this,pe).commitOrRemove(),n(this,Rt)!==V.NONE))&&!n(this,en)?.commitOrRemove()&&this.hasSelection){for(const t of n(this,tt))t.unselect();n(this,tt).clear(),A(this,M,Qt).call(this,{hasSelectedEditor:!1})}}translateSelectedEditors(t,e,s=!1){if(s||this.commitOrRemove(),!this.hasSelection)return;n(this,bi)[0]+=t,n(this,bi)[1]+=e;const[i,r]=n(this,bi),a=[...n(this,tt)];n(this,hs)&&clearTimeout(n(this,hs)),u(this,hs,setTimeout(()=>{u(this,hs,null),n(this,bi)[0]=n(this,bi)[1]=0,this.addCommands({cmd:()=>{for(const o of a)n(this,Et).has(o.id)&&o.translateInPage(i,r)},undo:()=>{for(const o of a)n(this,Et).has(o.id)&&o.translateInPage(-i,-r)},mustExec:!1})},1e3));for(const o of a)o.translateInPage(t,e)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),u(this,rs,new Map);for(const t of n(this,tt))n(this,rs).set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!n(this,rs))return!1;this.disableUserSelect(!1);const t=n(this,rs);u(this,rs,null);let e=!1;for(const[{x:i,y:r,pageIndex:a},o]of t)o.newX=i,o.newY=r,o.newPageIndex=a,e||(e=i!==o.savedX||r!==o.savedY||a!==o.savedPageIndex);if(!e)return!1;const s=(i,r,a,o)=>{if(n(this,Et).has(i.id)){const h=n(this,Nt).get(o);h?i._setParentAndPosition(h,r,a):(i.pageIndex=o,i.x=r,i.y=a)}};return this.addCommands({cmd:()=>{for(const[i,{newX:r,newY:a,newPageIndex:o}]of t)s(i,r,a,o)},undo:()=>{for(const[i,{savedX:r,savedY:a,savedPageIndex:o}]of t)s(i,r,a,o)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(n(this,rs))for(const s of n(this,rs).keys())s.drag(t,e)}rebuild(t){if(t.parent===null){const e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||n(this,tt).size===1&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return n(this,pe)===t}getActive(){return n(this,pe)}getMode(){return n(this,Rt)}get imageManager(){return X(this,"imageManager",new nc)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let l=0,c=e.rangeCount;l<c;l++)if(!t.contains(e.getRangeAt(l).commonAncestorContainer))return null;const{x:s,y:i,width:r,height:a}=t.getBoundingClientRect();let o;switch(t.getAttribute("data-main-rotation")){case"90":o=(l,c,d,p)=>({x:(c-i)/a,y:1-(l+d-s)/r,width:p/a,height:d/r});break;case"180":o=(l,c,d,p)=>({x:1-(l+d-s)/r,y:1-(c+p-i)/a,width:d/r,height:p/a});break;case"270":o=(l,c,d,p)=>({x:1-(c+p-i)/a,y:(l-s)/r,width:p/a,height:d/r});break;default:o=(l,c,d,p)=>({x:(l-s)/r,y:(c-i)/a,width:d/r,height:p/a})}const h=[];for(let l=0,c=e.rangeCount;l<c;l++){const d=e.getRangeAt(l);if(!d.collapsed)for(const{x:p,y:g,width:m,height:v}of d.getClientRects())m!==0&&v!==0&&h.push(o(p,g,m,v))}return h.length===0?null:h}addChangedExistingAnnotation({annotationElementId:t,id:e}){(n(this,_r)||u(this,_r,new Map)).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){n(this,_r)?.delete(t)}renderAnnotationElement(t){const e=n(this,_r)?.get(t.data.id);if(!e)return;const s=n(this,Ts).getRawValue(e);s&&(n(this,Rt)!==V.NONE||s.hasBeenModified)&&s.renderAnnotationElement(t)}};yr=new WeakMap,pe=new WeakMap,Et=new WeakMap,Nt=new WeakMap,wr=new WeakMap,Ts=new WeakMap,_r=new WeakMap,Te=new WeakMap,ui=new WeakMap,en=new WeakMap,xr=new WeakMap,sn=new WeakMap,rs=new WeakMap,Ge=new WeakMap,nn=new WeakMap,to=new WeakMap,eo=new WeakMap,Sr=new WeakMap,so=new WeakMap,as=new WeakMap,pi=new WeakMap,Er=new WeakMap,fi=new WeakMap,os=new WeakMap,Al=new WeakMap,gi=new WeakMap,io=new WeakMap,mi=new WeakMap,rn=new WeakMap,no=new WeakMap,ro=new WeakMap,Rt=new WeakMap,tt=new WeakMap,Ps=new WeakMap,an=new WeakMap,ao=new WeakMap,oo=new WeakMap,bi=new WeakMap,hs=new WeakMap,Is=new WeakMap,ho=new WeakMap,Pe=new WeakMap,M=new WeakSet,Ih=function({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t},ac=function(t){const{currentLayer:e}=this;if(e.hasTextLayer(t))return e;for(const s of n(this,Nt).values())if(s.hasTextLayer(t))return s;return null},uu=function(){const t=document.getSelection();if(!t||t.isCollapsed)return;const e=A(this,M,Ih).call(this,t).closest(".textLayer"),s=this.getSelectionBoxes(e);s&&(n(this,os)||u(this,os,new jf(this)),n(this,os).show(e,s,this.direction==="ltr"))},pu=function(){const t=document.getSelection();if(!t||t.isCollapsed){n(this,Ps)&&(n(this,os)?.hide(),u(this,Ps,null),A(this,M,Qt).call(this,{hasSelectedText:!1}));return}const{anchorNode:e}=t;if(e===n(this,Ps))return;const s=A(this,M,Ih).call(this,t).closest(".textLayer");if(s){if(n(this,os)?.hide(),u(this,Ps,e),A(this,M,Qt).call(this,{hasSelectedText:!0}),(n(this,Rt)===V.HIGHLIGHT||n(this,Rt)===V.NONE)&&(n(this,Rt)===V.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0),u(this,fi,this.isShiftKeyDown),!this.isShiftKeyDown)){const i=n(this,Rt)===V.HIGHLIGHT?A(this,M,ac).call(this,s):null;i?.toggleDrawing();const r=new AbortController,a=this.combinedSignal(r),o=h=>{(h.type!=="pointerup"||h.button===0)&&(r.abort(),i?.toggleDrawing(!0),h.type==="pointerup"&&A(this,M,Dh).call(this,"main_toolbar"))};window.addEventListener("pointerup",o,{signal:a}),window.addEventListener("blur",o,{signal:a})}}else n(this,Ps)&&(n(this,os)?.hide(),u(this,Ps,null),A(this,M,Qt).call(this,{hasSelectedText:!1}))},Dh=function(t=""){n(this,Rt)===V.HIGHLIGHT?this.highlightSelection(t):n(this,to)&&A(this,M,uu).call(this)},fu=function(){document.addEventListener("selectionchange",A(this,M,pu).bind(this),{signal:this._signal})},gu=function(){if(n(this,pi))return;u(this,pi,new AbortController);const t=this.combinedSignal(n(this,pi));window.addEventListener("focus",this.focus.bind(this),{signal:t}),window.addEventListener("blur",this.blur.bind(this),{signal:t})},mu=function(){n(this,pi)?.abort(),u(this,pi,null)},oc=function(){if(n(this,mi))return;u(this,mi,new AbortController);const t=this.combinedSignal(n(this,mi));window.addEventListener("keydown",this.keydown.bind(this),{signal:t}),window.addEventListener("keyup",this.keyup.bind(this),{signal:t})},bu=function(){n(this,mi)?.abort(),u(this,mi,null)},hc=function(){if(n(this,ui))return;u(this,ui,new AbortController);const t=this.combinedSignal(n(this,ui));document.addEventListener("copy",this.copy.bind(this),{signal:t}),document.addEventListener("cut",this.cut.bind(this),{signal:t}),document.addEventListener("paste",this.paste.bind(this),{signal:t})},lc=function(){n(this,ui)?.abort(),u(this,ui,null)},Au=function(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t}),document.addEventListener("drop",this.drop.bind(this),{signal:t})},Qt=function(t){Object.entries(t).some(([e,s])=>n(this,oo)[e]!==s)&&(this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(n(this,oo),t)}),n(this,Rt)===V.HIGHLIGHT&&t.hasSelectedEditor===!1&&A(this,M,Cs).call(this,[[Y.HIGHLIGHT_FREE,!0]]))},Cs=function(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})},vu=async function(){if(!n(this,gi)){u(this,gi,!0);const t=[];for(const e of n(this,Nt).values())t.push(e.enable());await Promise.all(t);for(const e of n(this,Et).values())e.enable()}},yu=function(){if(this.unselectAll(),n(this,gi)){u(this,gi,!1);for(const t of n(this,Nt).values())t.disable();for(const t of n(this,Et).values())t.disable()}},cc=function(t){const e=n(this,Nt).get(t.pageIndex);e?e.addOrRebuild(t):(this.addEditor(t),this.addToAnnotationStorage(t))},wu=function(){let t=null;for(t of n(this,tt));return t},ka=function(){if(n(this,Et).size===0)return!0;if(n(this,Et).size===1)for(const t of n(this,Et).values())return t.isEmpty();return!1},dc=function(t){for(const e of n(this,tt))e.unselect();n(this,tt).clear();for(const e of t)e.isEmpty()||(n(this,tt).add(e),e.select());A(this,M,Qt).call(this,{hasSelectedEditor:this.hasSelection})},F(fr,"TRANSLATE_SMALL",1),F(fr,"TRANSLATE_BIG",10);let ir=fr;var Tt,ls,Ve,Cr,cs,fe,Mr,ds,he,Ds,on,us,Ai,ts,Ra,Lh;const Jt=class Jt{constructor(t){b(this,ts);b(this,Tt,null);b(this,ls,!1);b(this,Ve,null);b(this,Cr,null);b(this,cs,null);b(this,fe,null);b(this,Mr,!1);b(this,ds,null);b(this,he,null);b(this,Ds,null);b(this,on,null);b(this,us,!1);u(this,he,t),u(this,us,t._uiManager.useNewAltTextFlow),n(Jt,Ai)||u(Jt,Ai,Object.freeze({added:"pdfjs-editor-new-alt-text-added-button","added-label":"pdfjs-editor-new-alt-text-added-button-label",missing:"pdfjs-editor-new-alt-text-missing-button","missing-label":"pdfjs-editor-new-alt-text-missing-button-label",review:"pdfjs-editor-new-alt-text-to-review-button","review-label":"pdfjs-editor-new-alt-text-to-review-button-label"}))}static initialize(t){Jt._l10n??(Jt._l10n=t)}async render(){const t=u(this,Ve,document.createElement("button"));t.className="altText",t.tabIndex="0";const e=u(this,Cr,document.createElement("span"));t.append(e),n(this,us)?(t.classList.add("new"),t.setAttribute("data-l10n-id",n(Jt,Ai).missing),e.setAttribute("data-l10n-id",n(Jt,Ai)["missing-label"])):(t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button"),e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-button-label"));const s=n(this,he)._uiManager._signal;t.addEventListener("contextmenu",es,{signal:s}),t.addEventListener("pointerdown",r=>r.stopPropagation(),{signal:s});const i=r=>{r.preventDefault(),n(this,he)._uiManager.editAltText(n(this,he)),n(this,us)&&n(this,he)._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_clicked",data:{label:n(this,ts,Ra)}})};return t.addEventListener("click",i,{capture:!0,signal:s}),t.addEventListener("keydown",r=>{r.target===t&&r.key==="Enter"&&(u(this,Mr,!0),i(r))},{signal:s}),await A(this,ts,Lh).call(this),t}finish(){n(this,Ve)&&(n(this,Ve).focus({focusVisible:n(this,Mr)}),u(this,Mr,!1))}isEmpty(){return n(this,us)?n(this,Tt)===null:!n(this,Tt)&&!n(this,ls)}hasData(){return n(this,us)?n(this,Tt)!==null||!!n(this,Ds):this.isEmpty()}get guessedText(){return n(this,Ds)}async setGuessedText(t){n(this,Tt)===null&&(u(this,Ds,t),u(this,on,await Jt._l10n.get("pdfjs-editor-new-alt-text-generated-alt-text-with-disclaimer",{generatedAltText:t})),A(this,ts,Lh).call(this))}toggleAltTextBadge(t=!1){if(n(this,us)&&!n(this,Tt)){if(!n(this,ds)){const e=u(this,ds,document.createElement("div"));e.className="noAltTextBadge",n(this,he).div.append(e)}n(this,ds).classList.toggle("hidden",!t)}else n(this,ds)?.remove(),u(this,ds,null)}serialize(t){let e=n(this,Tt);return t||n(this,Ds)!==e||(e=n(this,on)),{altText:e,decorative:n(this,ls),guessedText:n(this,Ds),textWithDisclaimer:n(this,on)}}get data(){return{altText:n(this,Tt),decorative:n(this,ls)}}set data({altText:t,decorative:e,guessedText:s,textWithDisclaimer:i,cancel:r=!1}){s&&(u(this,Ds,s),u(this,on,i)),(n(this,Tt)!==t||n(this,ls)!==e)&&(r||(u(this,Tt,t),u(this,ls,e)),A(this,ts,Lh).call(this))}toggle(t=!1){n(this,Ve)&&(!t&&n(this,fe)&&(clearTimeout(n(this,fe)),u(this,fe,null)),n(this,Ve).disabled=!t)}shown(){n(this,he)._reportTelemetry({action:"pdfjs.image.alt_text.image_status_label_displayed",data:{label:n(this,ts,Ra)}})}destroy(){n(this,Ve)?.remove(),u(this,Ve,null),u(this,Cr,null),u(this,cs,null),n(this,ds)?.remove(),u(this,ds,null)}};Tt=new WeakMap,ls=new WeakMap,Ve=new WeakMap,Cr=new WeakMap,cs=new WeakMap,fe=new WeakMap,Mr=new WeakMap,ds=new WeakMap,he=new WeakMap,Ds=new WeakMap,on=new WeakMap,us=new WeakMap,Ai=new WeakMap,ts=new WeakSet,Ra=function(){return(n(this,Tt)?"added":n(this,Tt)===null&&this.guessedText&&"review")||"missing"},Lh=async function(){const t=n(this,Ve);if(!t)return;if(n(this,us)){if(t.classList.toggle("done",!!n(this,Tt)),t.setAttribute("data-l10n-id",n(Jt,Ai)[n(this,ts,Ra)]),n(this,Cr)?.setAttribute("data-l10n-id",n(Jt,Ai)[`${n(this,ts,Ra)}-label`]),!n(this,Tt)){n(this,cs)?.remove();return}}else{if(!n(this,Tt)&&!n(this,ls)){t.classList.remove("done"),n(this,cs)?.remove();return}t.classList.add("done"),t.setAttribute("data-l10n-id","pdfjs-editor-alt-text-edit-button")}let e=n(this,cs);if(!e){u(this,cs,e=document.createElement("span")),e.className="tooltip",e.setAttribute("role","tooltip"),e.id=`alt-text-tooltip-${n(this,he).id}`;const i=100,r=n(this,he)._uiManager._signal;r.addEventListener("abort",()=>{clearTimeout(n(this,fe)),u(this,fe,null)},{once:!0}),t.addEventListener("mouseenter",()=>{u(this,fe,setTimeout(()=>{u(this,fe,null),n(this,cs).classList.add("show"),n(this,he)._reportTelemetry({action:"alt_text_tooltip"})},i))},{signal:r}),t.addEventListener("mouseleave",()=>{n(this,fe)&&(clearTimeout(n(this,fe)),u(this,fe,null)),n(this,cs)?.classList.remove("show")},{signal:r})}n(this,ls)?e.setAttribute("data-l10n-id","pdfjs-editor-alt-text-decorative-tooltip"):(e.removeAttribute("data-l10n-id"),e.textContent=n(this,Tt)),e.parentNode||t.append(e),n(this,he).getImageForAltText()?.setAttribute("aria-describedby",e.id)},b(Jt,Ai,null),F(Jt,"_l10n",null);let ol=Jt;var lo,hn,co,uo,po,fo,go,kr,Ls,ln,vi,ni,_u,xu,uc;const xd=class xd{constructor({container:t,isPinchingDisabled:e=null,isPinchingStopped:s=null,onPinchStart:i=null,onPinching:r=null,onPinchEnd:a=null,signal:o}){b(this,ni);b(this,lo);b(this,hn,!1);b(this,co,null);b(this,uo);b(this,po);b(this,fo);b(this,go);b(this,kr);b(this,Ls,null);b(this,ln);b(this,vi,null);u(this,lo,t),u(this,co,s),u(this,uo,e),u(this,po,i),u(this,fo,r),u(this,go,a),u(this,ln,new AbortController),u(this,kr,AbortSignal.any([o,n(this,ln).signal])),t.addEventListener("touchstart",A(this,ni,_u).bind(this),{passive:!1,signal:n(this,kr)})}get MIN_TOUCH_DISTANCE_TO_PINCH(){return X(this,"MIN_TOUCH_DISTANCE_TO_PINCH",35/(window.devicePixelRatio||1))}destroy(){n(this,ln)?.abort(),u(this,ln,null)}};lo=new WeakMap,hn=new WeakMap,co=new WeakMap,uo=new WeakMap,po=new WeakMap,fo=new WeakMap,go=new WeakMap,kr=new WeakMap,Ls=new WeakMap,ln=new WeakMap,vi=new WeakMap,ni=new WeakSet,_u=function(t){var i,r,a;if((i=n(this,uo))!=null&&i.call(this)||t.touches.length<2)return;if(!n(this,vi)){u(this,vi,new AbortController);const o=AbortSignal.any([n(this,kr),n(this,vi).signal]),h=n(this,lo),l={signal:o,passive:!1};h.addEventListener("touchmove",A(this,ni,xu).bind(this),l),h.addEventListener("touchend",A(this,ni,uc).bind(this),l),h.addEventListener("touchcancel",A(this,ni,uc).bind(this),l),(r=n(this,po))==null||r.call(this)}if(Ce(t),t.touches.length!==2||(a=n(this,co))!=null&&a.call(this)){u(this,Ls,null);return}let[e,s]=t.touches;e.identifier>s.identifier&&([e,s]=[s,e]),u(this,Ls,{touch0X:e.screenX,touch0Y:e.screenY,touch1X:s.screenX,touch1Y:s.screenY})},xu=function(t){var S;if(!n(this,Ls)||t.touches.length!==2)return;let[e,s]=t.touches;e.identifier>s.identifier&&([e,s]=[s,e]);const{screenX:i,screenY:r}=e,{screenX:a,screenY:o}=s,h=n(this,Ls),{touch0X:l,touch0Y:c,touch1X:d,touch1Y:p}=h,g=d-l,m=p-c,v=a-i,y=o-r,w=Math.hypot(v,y)||1,_=Math.hypot(g,m)||1;if(!n(this,hn)&&Math.abs(_-w)<=xd.MIN_TOUCH_DISTANCE_TO_PINCH)return;if(h.touch0X=i,h.touch0Y=r,h.touch1X=a,h.touch1Y=o,t.preventDefault(),!n(this,hn)){u(this,hn,!0);return}const x=[(i+a)/2,(r+o)/2];(S=n(this,fo))==null||S.call(this,x,_,w)},uc=function(t){var e;n(this,vi).abort(),u(this,vi,null),(e=n(this,go))==null||e.call(this),n(this,Ls)&&(t.preventDefault(),u(this,Ls,null),u(this,hn,!1))};let hl=xd;var cn,Ue,ht,Rr,yi,mo,dn,Ot,un,Fs,wi,bo,pn,ge,Ao,fn,Ns,ps,Tr,Pr,Ie,gn,vo,vl,N,pc,yo,fc,Fh,Su,Eu,gc,Nh,mc,Cu,Mu,ku,bc,Ru,Ac,Tu,Pu,Iu,vc,Ta;const W=class W{constructor(t){b(this,N);b(this,cn,null);b(this,Ue,null);b(this,ht,null);b(this,Rr,!1);b(this,yi,null);b(this,mo,"");b(this,dn,!1);b(this,Ot,null);b(this,un,null);b(this,Fs,null);b(this,wi,null);b(this,bo,"");b(this,pn,!1);b(this,ge,null);b(this,Ao,!1);b(this,fn,!1);b(this,Ns,!1);b(this,ps,null);b(this,Tr,0);b(this,Pr,0);b(this,Ie,null);b(this,gn,null);F(this,"_editToolbar",null);F(this,"_initialOptions",Object.create(null));F(this,"_initialData",null);F(this,"_isVisible",!0);F(this,"_uiManager",null);F(this,"_focusEventsAllowed",!0);b(this,vo,!1);b(this,vl,W._zIndex++);this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:s,pageHeight:i,pageX:r,pageY:a}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[s,i],this.pageTranslation=[r,a];const[o,h]=this.parentDimensions;this.x=t.x/o,this.y=t.y/h,this.isAttachedToDOM=!1,this.deleted=!1}static get _resizerKeyboardManager(){const t=W.prototype._resizeWithKeyboard,e=ir.TRANSLATE_SMALL,s=ir.TRANSLATE_BIG;return X(this,"_resizerKeyboardManager",new wh([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-s,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[s,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-s]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,s]}],[["Escape","mac+Escape"],W.prototype._stopResizingWithKeyboard]]))}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get isDrawer(){return!1}static get _defaultLineColor(){return X(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new Uf({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t,e){if(W._l10n??(W._l10n=t),W._l10nResizer||(W._l10nResizer=Object.freeze({topLeft:"pdfjs-editor-resizer-top-left",topMiddle:"pdfjs-editor-resizer-top-middle",topRight:"pdfjs-editor-resizer-top-right",middleRight:"pdfjs-editor-resizer-middle-right",bottomRight:"pdfjs-editor-resizer-bottom-right",bottomMiddle:"pdfjs-editor-resizer-bottom-middle",bottomLeft:"pdfjs-editor-resizer-bottom-left",middleLeft:"pdfjs-editor-resizer-middle-left"})),W._borderLineWidth!==-1)return;const s=getComputedStyle(document.documentElement);W._borderLineWidth=parseFloat(s.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){at("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return n(this,vo)}set _isDraggable(t){u(this,vo,t),this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t),this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t),this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2,this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=n(this,vl)}setParent(t){t!==null?(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions):A(this,N,Ta).call(this),this.parent=t}focusin(t){this._focusEventsAllowed&&(n(this,pn)?u(this,pn,!1):this.parent.setSelected(this))}focusout(t){if(!this._focusEventsAllowed||!this.isAttachedToDOM)return;t.relatedTarget?.closest(`#${this.id}`)||(t.preventDefault(),this.parent?.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,s,i){const[r,a]=this.parentDimensions;[s,i]=this.screenToPageTranslation(s,i),this.x=(t+s)/r,this.y=(e+i)/a,this.fixAndSetPosition()}translate(t,e){A(this,N,pc).call(this,this.parentDimensions,t,e)}translateInPage(t,e){n(this,ge)||u(this,ge,[this.x,this.y,this.width,this.height]),A(this,N,pc).call(this,this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}drag(t,e){n(this,ge)||u(this,ge,[this.x,this.y,this.width,this.height]);const{div:s,parentDimensions:[i,r]}=this;if(this.x+=t/i,this.y+=e/r,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:d,y:p}=this.div.getBoundingClientRect();this.parent.findNewParent(this,d,p)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:a,y:o}=this;const[h,l]=this.getBaseTranslation();a+=h,o+=l;const{style:c}=s;c.left=`${(100*a).toFixed(2)}%`,c.top=`${(100*o).toFixed(2)}%`,this._onTranslating(a,o),s.scrollIntoView({block:"nearest"})}_onTranslating(t,e){}_onTranslated(t,e){}get _hasBeenMoved(){return!!n(this,ge)&&(n(this,ge)[0]!==this.x||n(this,ge)[1]!==this.y)}get _hasBeenResized(){return!!n(this,ge)&&(n(this,ge)[2]!==this.width||n(this,ge)[3]!==this.height)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:s}=W,i=s/t,r=s/e;switch(this.rotation){case 90:return[-i,r];case 180:return[i,r];case 270:return[i,-r];default:return[-i,-r]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const{div:{style:e},pageDimensions:[s,i]}=this;let{x:r,y:a,width:o,height:h}=this;if(o*=s,h*=i,r*=s,a*=i,this._mustFixPosition)switch(t){case 0:r=Math.max(0,Math.min(s-o,r)),a=Math.max(0,Math.min(i-h,a));break;case 90:r=Math.max(0,Math.min(s-h,r)),a=Math.min(i,Math.max(o,a));break;case 180:r=Math.min(s,Math.max(o,r)),a=Math.min(i,Math.max(h,a));break;case 270:r=Math.min(s,Math.max(h,r)),a=Math.max(0,Math.min(i-o,a))}this.x=r/=s,this.y=a/=i;const[l,c]=this.getBaseTranslation();r+=l,a+=c,e.left=`${(100*r).toFixed(2)}%`,e.top=`${(100*a).toFixed(2)}%`,this.moveInDOM()}screenToPageTranslation(t,e){var s;return A(s=W,yo,fc).call(s,t,e,this.parentRotation)}pageTranslationToScreen(t,e){var s;return A(s=W,yo,fc).call(s,t,e,360-this.parentRotation)}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,s]}=this;return[e*t,s*t]}setDims(t,e){const[s,i]=this.parentDimensions,{style:r}=this.div;r.width=`${(100*t/s).toFixed(2)}%`,n(this,dn)||(r.height=`${(100*e/i).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:s}=t,i=s.endsWith("%"),r=!n(this,dn)&&e.endsWith("%");if(i&&r)return;const[a,o]=this.parentDimensions;i||(t.width=`${(100*parseFloat(s)/a).toFixed(2)}%`),n(this,dn)||r||(t.height=`${(100*parseFloat(e)/o).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}_onResized(){}static _round(t){return Math.round(1e4*t)/1e4}_onResizing(){}altTextFinish(){n(this,ht)?.finish()}async addEditToolbar(){return this._editToolbar||n(this,fn)?this._editToolbar:(this._editToolbar=new sc(this),this.div.append(this._editToolbar.render()),n(this,ht)&&await this._editToolbar.addAltText(n(this,ht)),this._editToolbar)}removeEditToolbar(){this._editToolbar&&(this._editToolbar.remove(),this._editToolbar=null,n(this,ht)?.destroy())}addContainer(t){const e=this._editToolbar?.div;e?e.before(t):this.div.append(t)}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){n(this,ht)||(ol.initialize(W._l10n),u(this,ht,new ol(this)),n(this,cn)&&(n(this,ht).data=n(this,cn),u(this,cn,null)),await this.addEditToolbar())}get altTextData(){return n(this,ht)?.data}set altTextData(t){n(this,ht)&&(n(this,ht).data=t)}get guessedAltText(){return n(this,ht)?.guessedText}async setGuessedAltText(t){await n(this,ht)?.setGuessedText(t)}serializeAltText(t){return n(this,ht)?.serialize(t)}hasAltText(){return!!n(this,ht)&&!n(this,ht).isEmpty()}hasAltTextData(){return n(this,ht)?.hasData()??!1}render(){this.div=document.createElement("div"),this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360),this.div.className=this.name,this.div.setAttribute("id",this.id),this.div.tabIndex=n(this,Rr)?-1:0,this._isVisible||this.div.classList.add("hidden"),this.setInForeground(),A(this,N,Ac).call(this);const[t,e]=this.parentDimensions;this.parentRotation%180!=0&&(this.div.style.maxWidth=`${(100*e/t).toFixed(2)}%`,this.div.style.maxHeight=`${(100*t/e).toFixed(2)}%`);const[s,i]=this.getInitialTranslation();return this.translate(s,i),al(this,this.div,["pointerdown"]),this.isResizable&&this._uiManager._supportsPinchToZoom&&(n(this,gn)||u(this,gn,new hl({container:this.div,isPinchingDisabled:()=>!this.isSelected,onPinchStart:A(this,N,Cu).bind(this),onPinching:A(this,N,Mu).bind(this),onPinchEnd:A(this,N,ku).bind(this),signal:this._uiManager._signal}))),this._uiManager._editorUndoBar?.hide(),this.div}pointerdown(t){const{isMac:e}=se.platform;t.button!==0||t.ctrlKey&&e?t.preventDefault():(u(this,pn,!0),this._isDraggable?A(this,N,Ru).call(this,t):A(this,N,bc).call(this,t))}get isSelected(){return this._uiManager.isSelected(this)}_onStartDragging(){}_onStopDragging(){}moveInDOM(){n(this,ps)&&clearTimeout(n(this,ps)),u(this,ps,setTimeout(()=>{u(this,ps,null),this.parent?.moveEditorInDOM(this)},0))}_setParentAndPosition(t,e,s){t.changeParent(this),this.x=e,this.y=s,this.fixAndSetPosition(),this._onTranslated()}getRect(t,e,s=this.rotation){const i=this.parentScale,[r,a]=this.pageDimensions,[o,h]=this.pageTranslation,l=t/i,c=e/i,d=this.x*r,p=this.y*a,g=this.width*r,m=this.height*a;switch(s){case 0:return[d+l+o,a-p-c-m+h,d+l+g+o,a-p-c+h];case 90:return[d+c+o,a-p+l+h,d+c+m+o,a-p+l+g+h];case 180:return[d-l-g+o,a-p+c+h,d-l+o,a-p+c+m+h];case 270:return[d-c-m+o,a-p-l-g+h,d-c+o,a-p-l+h];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[s,i,r,a]=t,o=r-s,h=a-i;switch(this.rotation){case 0:return[s,e-a,o,h];case 90:return[s,e-i,h,o];case 180:return[r,e-i,o,h];case 270:return[r,e-a,h,o];default:throw new Error("Invalid rotation")}}onceAdded(t){}isEmpty(){return!1}enableEditMode(){u(this,fn,!0)}disableEditMode(){u(this,fn,!1)}isInEditMode(){return n(this,fn)}shouldGetKeyboardEvents(){return n(this,Ns)}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}get isOnScreen(){const{top:t,left:e,bottom:s,right:i}=this.getClientDimensions(),{innerHeight:r,innerWidth:a}=window;return e<a&&i>0&&t<r&&s>0}rebuild(){A(this,N,Ac).call(this)}rotate(t){}resize(){}serializeDeleted(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex,popupRef:this._initialData?.popupRef||""}}serialize(t=!1,e=null){at("An editor must be serializable")}static async deserialize(t,e,s){const i=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:s});i.rotation=t.rotation,u(i,cn,t.accessibilityData);const[r,a]=i.pageDimensions,[o,h,l,c]=i.getRectInCurrentCoords(t.rect,a);return i.x=o/r,i.y=h/a,i.width=l/r,i.height=c/a,i}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||this.serialize()!==null)}remove(){if(n(this,wi)?.abort(),u(this,wi,null),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),n(this,ps)&&(clearTimeout(n(this,ps)),u(this,ps,null)),A(this,N,Ta).call(this),this.removeEditToolbar(),n(this,Ie)){for(const t of n(this,Ie).values())clearTimeout(t);u(this,Ie,null)}this.parent=null,n(this,gn)?.destroy(),u(this,gn,null)}get isResizable(){return!1}makeResizable(){this.isResizable&&(A(this,N,Su).call(this),n(this,Ot).classList.remove("hidden"),al(this,this.div,["keydown"]))}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||t.key!=="Enter")return;this._uiManager.setSelected(this),u(this,Fs,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height});const e=n(this,Ot).children;if(!n(this,Ue)){u(this,Ue,Array.from(e));const a=A(this,N,Tu).bind(this),o=A(this,N,Pu).bind(this),h=this._uiManager._signal;for(const l of n(this,Ue)){const c=l.getAttribute("data-resizer-name");l.setAttribute("role","spinbutton"),l.addEventListener("keydown",a,{signal:h}),l.addEventListener("blur",o,{signal:h}),l.addEventListener("focus",A(this,N,Iu).bind(this,c),{signal:h}),l.setAttribute("data-l10n-id",W._l10nResizer[c])}}const s=n(this,Ue)[0];let i=0;for(const a of e){if(a===s)break;i++}const r=(360-this.rotation+this.parentRotation)%360/90*(n(this,Ue).length/4);if(r!==i){if(r<i)for(let o=0;o<i-r;o++)n(this,Ot).append(n(this,Ot).firstChild);else if(r>i)for(let o=0;o<r-i;o++)n(this,Ot).firstChild.before(n(this,Ot).lastChild);let a=0;for(const o of e){const h=n(this,Ue)[a++].getAttribute("data-resizer-name");o.setAttribute("data-l10n-id",W._l10nResizer[h])}}A(this,N,vc).call(this,0),u(this,Ns,!0),n(this,Ot).firstChild.focus({focusVisible:!0}),t.preventDefault(),t.stopImmediatePropagation()}_resizeWithKeyboard(t,e){n(this,Ns)&&A(this,N,mc).call(this,n(this,bo),{deltaX:t,deltaY:e,fromKeyboard:!0})}_stopResizingWithKeyboard(){A(this,N,Ta).call(this),this.div.focus()}select(){this.makeResizable(),this.div?.classList.add("selectedEditor"),this._editToolbar?(this._editToolbar?.show(),n(this,ht)?.toggleAltTextBadge(!1)):this.addEditToolbar().then(()=>{this.div?.classList.contains("selectedEditor")&&this._editToolbar?.show()})}unselect(){n(this,Ot)?.classList.add("hidden"),this.div?.classList.remove("selectedEditor"),this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0}),this._editToolbar?.hide(),n(this,ht)?.toggleAltTextBadge(!0)}updateParams(t,e){}disableEditing(){}enableEditing(){}enterInEditMode(){}getImageForAltText(){return null}get contentDiv(){return this.div}get isEditing(){return n(this,Ao)}set isEditing(t){u(this,Ao,t),this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){u(this,dn,!0);const s=t/e,{style:i}=this.div;i.aspectRatio=s,i.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){n(this,Ie)||u(this,Ie,new Map);const{action:s}=t;let i=n(this,Ie).get(s);i&&clearTimeout(i),i=setTimeout(()=>{this._reportTelemetry(t),n(this,Ie).delete(s),n(this,Ie).size===0&&u(this,Ie,null)},W._telemetryTimeout),n(this,Ie).set(s,i)}else t.type||(t.type=this.editorType),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}show(t=this._isVisible){this.div.classList.toggle("hidden",!t),this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0),u(this,Rr,!1)}disable(){this.div&&(this.div.tabIndex=-1),u(this,Rr,!0)}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(e){if(e.nodeName==="CANVAS"){const s=e;e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),s.before(e)}}else e=document.createElement("div"),e.classList.add("annotationContent",this.editorType),t.container.prepend(e);return e}resetAnnotationElement(t){const{firstChild:e}=t.container;e?.nodeName==="DIV"&&e.classList.contains("annotationContent")&&e.remove()}};cn=new WeakMap,Ue=new WeakMap,ht=new WeakMap,Rr=new WeakMap,yi=new WeakMap,mo=new WeakMap,dn=new WeakMap,Ot=new WeakMap,un=new WeakMap,Fs=new WeakMap,wi=new WeakMap,bo=new WeakMap,pn=new WeakMap,ge=new WeakMap,Ao=new WeakMap,fn=new WeakMap,Ns=new WeakMap,ps=new WeakMap,Tr=new WeakMap,Pr=new WeakMap,Ie=new WeakMap,gn=new WeakMap,vo=new WeakMap,vl=new WeakMap,N=new WeakSet,pc=function([t,e],s,i){[s,i]=this.screenToPageTranslation(s,i),this.x+=s/t,this.y+=i/e,this._onTranslating(this.x,this.y),this.fixAndSetPosition()},yo=new WeakSet,fc=function(t,e,s){switch(s){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}},Fh=function(t){switch(t){case 90:{const[e,s]=this.pageDimensions;return[0,-e/s,s/e,0]}case 180:return[-1,0,0,-1];case 270:{const[e,s]=this.pageDimensions;return[0,e/s,-s/e,0]}default:return[1,0,0,1]}},Su=function(){if(n(this,Ot))return;u(this,Ot,document.createElement("div")),n(this,Ot).classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const s of t){const i=document.createElement("div");n(this,Ot).append(i),i.classList.add("resizer",s),i.setAttribute("data-resizer-name",s),i.addEventListener("pointerdown",A(this,N,Eu).bind(this,s),{signal:e}),i.addEventListener("contextmenu",es,{signal:e}),i.tabIndex=-1}this.div.prepend(n(this,Ot))},Eu=function(t,e){e.preventDefault();const{isMac:s}=se.platform;if(e.button!==0||e.ctrlKey&&s)return;n(this,ht)?.toggle(!1);const i=this._isDraggable;this._isDraggable=!1,u(this,un,[e.screenX,e.screenY]);const r=new AbortController,a=this._uiManager.combinedSignal(r);this.parent.togglePointerEvents(!1),window.addEventListener("pointermove",A(this,N,mc).bind(this,t),{passive:!0,capture:!0,signal:a}),window.addEventListener("touchmove",Ce,{passive:!1,signal:a}),window.addEventListener("contextmenu",es,{signal:a}),u(this,Fs,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height});const o=this.parent.div.style.cursor,h=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const l=()=>{r.abort(),this.parent.togglePointerEvents(!0),n(this,ht)?.toggle(!0),this._isDraggable=i,this.parent.div.style.cursor=o,this.div.style.cursor=h,A(this,N,Nh).call(this)};window.addEventListener("pointerup",l,{signal:a}),window.addEventListener("blur",l,{signal:a})},gc=function(t,e,s,i){this.width=s,this.height=i,this.x=t,this.y=e;const[r,a]=this.parentDimensions;this.setDims(r*s,a*i),this.fixAndSetPosition(),this._onResized()},Nh=function(){if(!n(this,Fs))return;const{savedX:t,savedY:e,savedWidth:s,savedHeight:i}=n(this,Fs);u(this,Fs,null);const r=this.x,a=this.y,o=this.width,h=this.height;r===t&&a===e&&o===s&&h===i||this.addCommands({cmd:A(this,N,gc).bind(this,r,a,o,h),undo:A(this,N,gc).bind(this,t,e,s,i),mustExec:!0})},mc=function(t,e){const[s,i]=this.parentDimensions,r=this.x,a=this.y,o=this.width,h=this.height,l=W.MIN_SIZE/s,c=W.MIN_SIZE/i,d=A(this,N,Fh).call(this,this.rotation),p=($,z)=>[d[0]*$+d[2]*z,d[1]*$+d[3]*z],g=A(this,N,Fh).call(this,360-this.rotation);let m,v,y=!1,w=!1;switch(t){case"topLeft":y=!0,m=($,z)=>[0,0],v=($,z)=>[$,z];break;case"topMiddle":m=($,z)=>[$/2,0],v=($,z)=>[$/2,z];break;case"topRight":y=!0,m=($,z)=>[$,0],v=($,z)=>[0,z];break;case"middleRight":w=!0,m=($,z)=>[$,z/2],v=($,z)=>[0,z/2];break;case"bottomRight":y=!0,m=($,z)=>[$,z],v=($,z)=>[0,0];break;case"bottomMiddle":m=($,z)=>[$/2,z],v=($,z)=>[$/2,0];break;case"bottomLeft":y=!0,m=($,z)=>[0,z],v=($,z)=>[$,0];break;case"middleLeft":w=!0,m=($,z)=>[0,z/2],v=($,z)=>[$,z/2]}const _=m(o,h),x=v(o,h);let S=p(...x);const C=W._round(r+S[0]),k=W._round(a+S[1]);let E,L,O=1,B=1;if(e.fromKeyboard)({deltaX:E,deltaY:L}=e);else{const{screenX:$,screenY:z}=e,[ke,xs]=n(this,un);[E,L]=this.screenToPageTranslation($-ke,z-xs),n(this,un)[0]=$,n(this,un)[1]=z}[E,L]=(G=E/s,nt=L/i,[g[0]*G+g[2]*nt,g[1]*G+g[3]*nt]);var G,nt;if(y){const $=Math.hypot(o,h);O=B=Math.max(Math.min(Math.hypot(x[0]-_[0]-E,x[1]-_[1]-L)/$,1/o,1/h),l/o,c/h)}else w?O=Math.max(l,Math.min(1,Math.abs(x[0]-_[0]-E)))/o:B=Math.max(c,Math.min(1,Math.abs(x[1]-_[1]-L)))/h;const ut=W._round(o*O),K=W._round(h*B);S=p(...v(ut,K));const mt=C-S[0],kt=k-S[1];n(this,ge)||u(this,ge,[this.x,this.y,this.width,this.height]),this.width=ut,this.height=K,this.x=mt,this.y=kt,this.setDims(s*ut,i*K),this.fixAndSetPosition(),this._onResizing()},Cu=function(){u(this,Fs,{savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height}),n(this,ht)?.toggle(!1),this.parent.togglePointerEvents(!1)},Mu=function(t,e,s){let i=s/e*.7+1-.7;if(i===1)return;const r=A(this,N,Fh).call(this,this.rotation),a=(C,k)=>[r[0]*C+r[2]*k,r[1]*C+r[3]*k],[o,h]=this.parentDimensions,l=this.x,c=this.y,d=this.width,p=this.height,g=W.MIN_SIZE/o,m=W.MIN_SIZE/h;i=Math.max(Math.min(i,1/d,1/p),g/d,m/p);const v=W._round(d*i),y=W._round(p*i);if(v===d&&y===p)return;n(this,ge)||u(this,ge,[l,c,d,p]);const w=a(d/2,p/2),_=W._round(l+w[0]),x=W._round(c+w[1]),S=a(v/2,y/2);this.x=_-S[0],this.y=x-S[1],this.width=v,this.height=y,this.setDims(o*v,h*y),this.fixAndSetPosition(),this._onResizing()},ku=function(){n(this,ht)?.toggle(!0),this.parent.togglePointerEvents(!0),A(this,N,Nh).call(this)},bc=function(t){const{isMac:e}=se.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)},Ru=function(t){const{isSelected:e}=this;this._uiManager.setUpDragSession();let s=!1;const i=new AbortController,r=this._uiManager.combinedSignal(i),a={capture:!0,passive:!1,signal:r},o=l=>{i.abort(),u(this,yi,null),u(this,pn,!1),this._uiManager.endDragSession()||A(this,N,bc).call(this,l),s&&this._onStopDragging()};e&&(u(this,Tr,t.clientX),u(this,Pr,t.clientY),u(this,yi,t.pointerId),u(this,mo,t.pointerType),window.addEventListener("pointermove",l=>{s||(s=!0,this._onStartDragging());const{clientX:c,clientY:d,pointerId:p}=l;if(p!==n(this,yi)){Ce(l);return}const[g,m]=this.screenToPageTranslation(c-n(this,Tr),d-n(this,Pr));u(this,Tr,c),u(this,Pr,d),this._uiManager.dragSelectedEditors(g,m)},a),window.addEventListener("touchmove",Ce,a),window.addEventListener("pointerdown",l=>{l.pointerType===n(this,mo)&&(n(this,gn)||l.isPrimary)&&o(l),Ce(l)},a));const h=l=>{n(this,yi)&&n(this,yi)!==l.pointerId?Ce(l):o(l)};window.addEventListener("pointerup",h,{signal:r}),window.addEventListener("blur",h,{signal:r})},Ac=function(){if(n(this,wi)||!this.div)return;u(this,wi,new AbortController);const t=this._uiManager.combinedSignal(n(this,wi));this.div.addEventListener("focusin",this.focusin.bind(this),{signal:t}),this.div.addEventListener("focusout",this.focusout.bind(this),{signal:t})},Tu=function(t){W._resizerKeyboardManager.exec(this,t)},Pu=function(t){n(this,Ns)&&t.relatedTarget?.parentNode!==n(this,Ot)&&A(this,N,Ta).call(this)},Iu=function(t){u(this,bo,n(this,Ns)?t:"")},vc=function(t){if(n(this,Ue))for(const e of n(this,Ue))e.tabIndex=t},Ta=function(){u(this,Ns,!1),A(this,N,vc).call(this,-1),A(this,N,Nh).call(this)},b(W,yo),F(W,"_l10n",null),F(W,"_l10nResizer",null),F(W,"_borderLineWidth",-1),F(W,"_colorManager",new rc),F(W,"_zIndex",1),F(W,"_telemetryTimeout",1e3);let gt=W;class Uf extends gt{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return this.serializeDeleted()}}const Ld=3285377520,Re=4294901760,oi=65535;class Du{constructor(t){this.h1=t?4294967295&t:Ld,this.h2=t?4294967295&t:Ld}update(t){let e,s;if(typeof t=="string"){e=new Uint8Array(2*t.length),s=0;for(let v=0,y=t.length;v<y;v++){const w=t.charCodeAt(v);w<=255?e[s++]=w:(e[s++]=w>>>8,e[s++]=255&w)}}else{if(!ArrayBuffer.isView(t))throw new Error("Invalid data format, must be a string or TypedArray.");e=t.slice(),s=e.byteLength}const i=s>>2,r=s-4*i,a=new Uint32Array(e.buffer,0,i);let o=0,h=0,l=this.h1,c=this.h2;const d=3432918353,p=461845907,g=11601,m=13715;for(let v=0;v<i;v++)1&v?(o=a[v],o=o*d&Re|o*g&oi,o=o<<15|o>>>17,o=o*p&Re|o*m&oi,l^=o,l=l<<13|l>>>19,l=5*l+3864292196):(h=a[v],h=h*d&Re|h*g&oi,h=h<<15|h>>>17,h=h*p&Re|h*m&oi,c^=h,c=c<<13|c>>>19,c=5*c+3864292196);switch(o=0,r){case 3:o^=e[4*i+2]<<16;case 2:o^=e[4*i+1]<<8;case 1:o^=e[4*i],o=o*d&Re|o*g&oi,o=o<<15|o>>>17,o=o*p&Re|o*m&oi,1&i?l^=o:c^=o}this.h1=l,this.h2=c}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,t=3981806797*t&Re|36045*t&oi,e=4283543511*e&Re|(2950163797*(e<<16|t>>>16)&Re)>>>16,t^=e>>>1,t=444984403*t&Re|60499*t&oi,e=3301882366*e&Re|(3120437893*(e<<16|t>>>16)&Re)>>>16,t^=e>>>1,(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const yc=Object.freeze({map:null,hash:"",transfer:void 0});var mn,bn,Pt,yl,Lu;class Ad{constructor(){b(this,yl);b(this,mn,!1);b(this,bn,null);b(this,Pt,new Map);this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){const s=n(this,Pt).get(t);return s===void 0?e:Object.assign(e,s)}getRawValue(t){return n(this,Pt).get(t)}remove(t){if(n(this,Pt).delete(t),n(this,Pt).size===0&&this.resetModified(),typeof this.onAnnotationEditor=="function"){for(const e of n(this,Pt).values())if(e instanceof gt)return;this.onAnnotationEditor(null)}}setValue(t,e){const s=n(this,Pt).get(t);let i=!1;if(s!==void 0)for(const[r,a]of Object.entries(e))s[r]!==a&&(i=!0,s[r]=a);else i=!0,n(this,Pt).set(t,e);i&&A(this,yl,Lu).call(this),e instanceof gt&&typeof this.onAnnotationEditor=="function"&&this.onAnnotationEditor(e.constructor._type)}has(t){return n(this,Pt).has(t)}getAll(){return n(this,Pt).size>0?pd(n(this,Pt)):null}setAll(t){for(const[e,s]of Object.entries(t))this.setValue(e,s)}get size(){return n(this,Pt).size}resetModified(){n(this,mn)&&(u(this,mn,!1),typeof this.onResetModified=="function"&&this.onResetModified())}get print(){return new Fu(this)}get serializable(){if(n(this,Pt).size===0)return yc;const t=new Map,e=new Du,s=[],i=Object.create(null);let r=!1;for(const[a,o]of n(this,Pt)){const h=o instanceof gt?o.serialize(!1,i):o;h&&(t.set(a,h),e.update(`${a}:${JSON.stringify(h)}`),r||(r=!!h.bitmap))}if(r)for(const a of t.values())a.bitmap&&s.push(a.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:s}:yc}get editorStats(){let t=null;const e=new Map;for(const s of n(this,Pt).values()){if(!(s instanceof gt))continue;const i=s.telemetryFinalData;if(!i)continue;const{type:r}=i;e.has(r)||e.set(r,Object.getPrototypeOf(s).constructor),t||(t=Object.create(null));const a=t[r]||(t[r]=new Map);for(const[o,h]of Object.entries(i)){if(o==="type")continue;let l=a.get(o);l||(l=new Map,a.set(o,l));const c=l.get(h)??0;l.set(h,c+1)}}for(const[s,i]of e)t[s]=i.computeTelemetryFinalData(t[s]);return t}resetModifiedIds(){u(this,bn,null)}get modifiedIds(){if(n(this,bn))return n(this,bn);const t=[];for(const e of n(this,Pt).values())e instanceof gt&&e.annotationElementId&&e.serialize()&&t.push(e.annotationElementId);return u(this,bn,{ids:new Set(t),hash:t.join(",")})}}mn=new WeakMap,bn=new WeakMap,Pt=new WeakMap,yl=new WeakSet,Lu=function(){n(this,mn)||(u(this,mn,!0),typeof this.onSetModified=="function"&&this.onSetModified())};var wo;class Fu extends Ad{constructor(e){super();b(this,wo);const{map:s,hash:i,transfer:r}=e.serializable,a=structuredClone(s,r?{transfer:r}:null);u(this,wo,{map:a,hash:i,transfer:r})}get print(){at("Should not call PrintAnnotationStorage.print")}get serializable(){return n(this,wo)}get modifiedIds(){return X(this,"modifiedIds",{ids:new Set,hash:""})}}wo=new WeakMap;var Ir;class Wf{constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){b(this,Ir,new Set);this._document=t,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),n(this,Ir).clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont({systemFontInfo:t,_inspectFont:e}){if(t&&!n(this,Ir).has(t.loadedName))if(_t(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:s,src:i,style:r}=t,a=new FontFace(s,i,r);this.addNativeFontFace(a);try{await a.load(),n(this,Ir).add(s),e?.(t)}catch{U(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(a)}}else at("Not implemented: loadSystemFont without the Font Loading API.")}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo){await this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){const s=t.createNativeFontFace();if(s){this.addNativeFontFace(s);try{await s.loaded}catch(i){throw U(`Failed to load font '${s.family}': '${i}'.`),t.disableFontFace=!0,i}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise(s=>{const i=this._queueLoadingCallback(s);this._prepareFontLoadEvent(t,i)})}}get isFontLoadingAPISupported(){return X(this,"isFontLoadingAPISupported",!!this._document?.fonts)}get isSyncFontLoadingSupported(){let t=!1;return(Xt||typeof navigator<"u"&&typeof navigator?.userAgent=="string"&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(t=!0),X(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){const{loadingRequests:e}=this,s={done:!1,complete:function(){for(_t(!s.done,"completeRequest() cannot be called twice."),s.done=!0;e.length>0&&e[0].done;){const r=e.shift();setTimeout(r.callback,0)}},callback:t};return e.push(s),s}get _loadTestFont(){return X(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e){function s(y,w){return y.charCodeAt(w)<<24|y.charCodeAt(w+1)<<16|y.charCodeAt(w+2)<<8|255&y.charCodeAt(w+3)}function i(y,w,_,x){return y.substring(0,w)+x+y.substring(w+_)}let r,a;const o=this._document.createElement("canvas");o.width=1,o.height=1;const h=o.getContext("2d");let l=0;const c=`lt${Date.now()}${this.loadTestFontId++}`;let d=this._loadTestFont;d=i(d,976,c.length,c);const p=1482184792;let g=s(d,16);for(r=0,a=c.length-3;r<a;r+=4)g=g-p+s(c,r)|0;r<c.length&&(g=g-p+s(c+"XXX",r)|0),d=i(d,16,4,function(w){return String.fromCharCode(w>>24&255,w>>16&255,w>>8&255,255&w)}(g));const m=`@font-face {font-family:"${c}";src:${`url(data:font/opentype;base64,${btoa(d)});`}}`;this.insertRule(m);const v=this._document.createElement("div");v.style.visibility="hidden",v.style.width=v.style.height="10px",v.style.position="absolute",v.style.top=v.style.left="0px";for(const y of[t.loadedName,c]){const w=this._document.createElement("span");w.textContent="Hi",w.style.fontFamily=y,v.append(w)}this._document.body.append(v),function y(w,_){if(++l>30){U("Load test font never loaded."),_();return}h.font="30px "+w,h.fillText(".",0,20),h.getImageData(0,0,1,1).data[3]>0?_():setTimeout(y.bind(null,w,_))}(c,()=>{v.remove(),e.complete()})}}Ir=new WeakMap;class qf{constructor(t,{disableFontFace:e=!1,fontExtraProperties:s=!1,inspectFont:i=null}){this.compiledGlyphs=Object.create(null);for(const r in t)this[r]=t[r];this.disableFontFace=e===!0,this.fontExtraProperties=s===!0,this._inspectFont=i}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});return this._inspectFont?.(this),t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=`url(data:${this.mimetype};base64,${function(i){return Uint8Array.prototype.toBase64?i.toBase64():btoa(iu(i))}(this.data)});`;let e;if(this.cssFontInfo){let s=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(s+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),e=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${s}src:${t}}`}else e=`@font-face {font-family:"${this.loadedName}";src:${t}}`;return this._inspectFont?.(this,t),e}getPathGenerator(t,e){if(this.compiledGlyphs[e]!==void 0)return this.compiledGlyphs[e];const s=this.loadedName+"_path_"+e;let i;try{i=t.get(s)}catch(a){U(`getPathGenerator - ignoring character: "${a}".`)}const r=new Path2D(i||"");return this.fontExtraProperties||t.delete(s),this.compiledGlyphs[e]=r}}const Fd=1,Nd=2,Od=1,ql=2,Bd=3,Hd=4,$d=5,zd=6,xh=7,Xl=8;function jd(){}function ae(f){if(f instanceof zi||f instanceof Jl||f instanceof Ua||f instanceof Pd||f instanceof rl||f instanceof Vl)return f;switch(f instanceof Error||typeof f=="object"&&f!==null||at('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),f.name){case"AbortException":return new zi(f.message);case"InvalidPDFException":return new Jl(f.message);case"MissingPDFException":return new Ua(f.message);case"PasswordException":return new Pd(f.message,f.code);case"UnexpectedResponseException":return new rl(f.message,f.status);case"UnknownErrorException":return new Vl(f.message,f.details)}return new Vl(f.message,f.toString())}var Dr,He,Nu,Ou,Bu,Oh;class Pa{constructor(t,e,s){b(this,He);b(this,Dr,new AbortController);this.sourceName=t,this.targetName=e,this.comObj=s,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),s.addEventListener("message",A(this,He,Nu).bind(this),{signal:n(this,Dr).signal})}on(t,e){const s=this.actionHandler;if(s[t])throw new Error(`There is already an actionName called "${t}"`);s[t]=e}send(t,e,s){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},s)}sendWithPromise(t,e,s){const i=this.callbackId++,r=Promise.withResolvers();this.callbackCapabilities[i]=r;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:i,data:e},s)}catch(a){r.reject(a)}return r.promise}sendWithStream(t,e,s,i){const r=this.streamId++,a=this.sourceName,o=this.targetName,h=this.comObj;return new ReadableStream({start:l=>{const c=Promise.withResolvers();return this.streamControllers[r]={controller:l,startCall:c,pullCall:null,cancelCall:null,isClosed:!1},h.postMessage({sourceName:a,targetName:o,action:t,streamId:r,data:e,desiredSize:l.desiredSize},i),c.promise},pull:l=>{const c=Promise.withResolvers();return this.streamControllers[r].pullCall=c,h.postMessage({sourceName:a,targetName:o,stream:zd,streamId:r,desiredSize:l.desiredSize}),c.promise},cancel:l=>{_t(l instanceof Error,"cancel must have a valid reason");const c=Promise.withResolvers();return this.streamControllers[r].cancelCall=c,this.streamControllers[r].isClosed=!0,h.postMessage({sourceName:a,targetName:o,stream:Od,streamId:r,reason:ae(l)}),c.promise}},s)}destroy(){n(this,Dr)?.abort(),u(this,Dr,null)}}Dr=new WeakMap,He=new WeakSet,Nu=function({data:t}){if(t.targetName!==this.sourceName)return;if(t.stream){A(this,He,Bu).call(this,t);return}if(t.callback){const s=t.callbackId,i=this.callbackCapabilities[s];if(!i)throw new Error(`Cannot resolve callback ${s}`);if(delete this.callbackCapabilities[s],t.callback===Fd)i.resolve(t.data);else{if(t.callback!==Nd)throw new Error("Unexpected callback case");i.reject(ae(t.reason))}return}const e=this.actionHandler[t.action];if(!e)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const s=this.sourceName,i=t.sourceName,r=this.comObj;Promise.try(e,t.data).then(function(a){r.postMessage({sourceName:s,targetName:i,callback:Fd,callbackId:t.callbackId,data:a})},function(a){r.postMessage({sourceName:s,targetName:i,callback:Nd,callbackId:t.callbackId,reason:ae(a)})})}else t.streamId?A(this,He,Ou).call(this,t):e(t.data)},Ou=function(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,r=this.comObj,a=this,o=this.actionHandler[t.action],h={enqueue(l,c=1,d){if(this.isCancelled)return;const p=this.desiredSize;this.desiredSize-=c,p>0&&this.desiredSize<=0&&(this.sinkCapability=Promise.withResolvers(),this.ready=this.sinkCapability.promise),r.postMessage({sourceName:s,targetName:i,stream:Hd,streamId:e,chunk:l},d)},close(){this.isCancelled||(this.isCancelled=!0,r.postMessage({sourceName:s,targetName:i,stream:Bd,streamId:e}),delete a.streamSinks[e])},error(l){_t(l instanceof Error,"error must have a valid reason"),this.isCancelled||(this.isCancelled=!0,r.postMessage({sourceName:s,targetName:i,stream:$d,streamId:e,reason:ae(l)}))},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};h.sinkCapability.resolve(),h.ready=h.sinkCapability.promise,this.streamSinks[e]=h,Promise.try(o,t.data,h).then(function(){r.postMessage({sourceName:s,targetName:i,stream:Xl,streamId:e,success:!0})},function(l){r.postMessage({sourceName:s,targetName:i,stream:Xl,streamId:e,reason:ae(l)})})},Bu=function(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,r=this.comObj,a=this.streamControllers[e],o=this.streamSinks[e];switch(t.stream){case Xl:t.success?a.startCall.resolve():a.startCall.reject(ae(t.reason));break;case xh:t.success?a.pullCall.resolve():a.pullCall.reject(ae(t.reason));break;case zd:if(!o){r.postMessage({sourceName:s,targetName:i,stream:xh,streamId:e,success:!0});break}o.desiredSize<=0&&t.desiredSize>0&&o.sinkCapability.resolve(),o.desiredSize=t.desiredSize,Promise.try(o.onPull||jd).then(function(){r.postMessage({sourceName:s,targetName:i,stream:xh,streamId:e,success:!0})},function(l){r.postMessage({sourceName:s,targetName:i,stream:xh,streamId:e,reason:ae(l)})});break;case Hd:if(_t(a,"enqueue should have stream controller"),a.isClosed)break;a.controller.enqueue(t.chunk);break;case Bd:if(_t(a,"close should have stream controller"),a.isClosed)break;a.isClosed=!0,a.controller.close(),A(this,He,Oh).call(this,a,e);break;case $d:_t(a,"error should have stream controller"),a.controller.error(ae(t.reason)),A(this,He,Oh).call(this,a,e);break;case ql:t.success?a.cancelCall.resolve():a.cancelCall.reject(ae(t.reason)),A(this,He,Oh).call(this,a,e);break;case Od:if(!o)break;const h=ae(t.reason);Promise.try(o.onCancel||jd,h).then(function(){r.postMessage({sourceName:s,targetName:i,stream:ql,streamId:e,success:!0})},function(l){r.postMessage({sourceName:s,targetName:i,stream:ql,streamId:e,reason:ae(l)})}),o.sinkCapability.reject(h),o.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}},Oh=async function(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]),delete this.streamControllers[e]};var _o;class Gd{constructor({enableHWA:t=!1}){b(this,_o,!1);u(this,_o,t)}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const s=this._createCanvas(t,e);return{canvas:s,context:s.getContext("2d",{willReadFrequently:!n(this,_o)})}}reset(t,e,s){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||s<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=s}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){at("Abstract method `_createCanvas` called.")}}_o=new WeakMap;class Hu{constructor({baseUrl:t=null,isCompressed:e=!0}){this.baseUrl=t,this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error("Ensure that the `cMapUrl` and `cMapPacked` API parameters are provided.");if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":"");return this._fetch(e).then(s=>({cMapData:s,isCompressed:this.isCompressed})).catch(s=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)})}async _fetch(t){at("Abstract method `_fetch` called.")}}class $u extends Hu{async _fetch(t){const e=await Ol(t,this.isCompressed?"arraybuffer":"text");return e instanceof ArrayBuffer?new Uint8Array(e):Nl(e)}}class Vd{addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,s,i,r){return"none"}destroy(t=!1){}}class zu{constructor({baseUrl:t=null}){this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error("Ensure that the `standardFontDataUrl` API parameter is provided.");if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetch(e).catch(s=>{throw new Error(`Unable to load font data at: ${e}`)})}async _fetch(t){at("Abstract method `_fetch` called.")}}class ju extends zu{async _fetch(t){const e=await Ol(t,"arraybuffer");return new Uint8Array(e)}}Xt&&U("Please use the `legacy` build in Node.js environments.");async function Gu(f){const t=process.getBuiltinModule("fs"),e=await t.promises.readFile(f);return new Uint8Array(e)}const Ki="Fill",ll="Stroke",Ga="Shading";function wc(f,t){if(!t)return;const e=t[2]-t[0],s=t[3]-t[1],i=new Path2D;i.rect(t[0],t[1],e,s),f.clip(i)}class vd{getPattern(){at("Abstract method `getPattern` called.")}}class Xf extends vd{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;this._type==="axial"?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):this._type==="radial"&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const s of this._colorStops)e.addColorStop(s[0],s[1]);return e}getPattern(t,e,s,i){let r;if(i===ll||i===Ki){const a=e.current.getClippedPathBoundingBox(i,ct(t))||[0,0,0,0],o=Math.ceil(a[2]-a[0])||1,h=Math.ceil(a[3]-a[1])||1,l=e.cachedCanvases.getCanvas("pattern",o,h),c=l.context;c.clearRect(0,0,c.canvas.width,c.canvas.height),c.beginPath(),c.rect(0,0,c.canvas.width,c.canvas.height),c.translate(-a[0],-a[1]),s=D.transform(s,[1,0,0,1,a[0],a[1]]),c.transform(...e.baseTransform),this.matrix&&c.transform(...this.matrix),wc(c,this._bbox),c.fillStyle=this._createGradient(c),c.fill(),r=t.createPattern(l.canvas,"no-repeat");const d=new DOMMatrix(s);r.setTransform(d)}else wc(t,this._bbox),r=this._createGradient(t);return r}}function Yl(f,t,e,s,i,r,a,o){const h=t.coords,l=t.colors,c=f.data,d=4*f.width;let p;h[e+1]>h[s+1]&&(p=e,e=s,s=p,p=r,r=a,a=p),h[s+1]>h[i+1]&&(p=s,s=i,i=p,p=a,a=o,o=p),h[e+1]>h[s+1]&&(p=e,e=s,s=p,p=r,r=a,a=p);const g=(h[e]+t.offsetX)*t.scaleX,m=(h[e+1]+t.offsetY)*t.scaleY,v=(h[s]+t.offsetX)*t.scaleX,y=(h[s+1]+t.offsetY)*t.scaleY,w=(h[i]+t.offsetX)*t.scaleX,_=(h[i+1]+t.offsetY)*t.scaleY;if(m>=_)return;const x=l[r],S=l[r+1],C=l[r+2],k=l[a],E=l[a+1],L=l[a+2],O=l[o],B=l[o+1],G=l[o+2],nt=Math.round(m),ut=Math.round(_);let K,mt,kt,$,z,ke,xs,Ss;for(let Yt=nt;Yt<=ut;Yt++){if(Yt<y){const At=Yt<m?0:(m-Yt)/(m-y);K=g-(g-v)*At,mt=x-(x-k)*At,kt=S-(S-E)*At,$=C-(C-L)*At}else{let At;At=Yt>_?1:y===_?0:(y-Yt)/(y-_),K=v-(v-w)*At,mt=k-(k-O)*At,kt=E-(E-B)*At,$=L-(L-G)*At}let Ft;Ft=Yt<m?0:Yt>_?1:(m-Yt)/(m-_),z=g-(g-w)*Ft,ke=x-(x-O)*Ft,xs=S-(S-B)*Ft,Ss=C-(C-G)*Ft;const ai=Math.round(Math.min(K,z)),ue=Math.round(Math.max(K,z));let rt=d*Yt+4*ai;for(let At=ai;At<=ue;At++)Ft=(K-At)/(K-z),Ft<0?Ft=0:Ft>1&&(Ft=1),c[rt++]=mt-(mt-ke)*Ft|0,c[rt++]=kt-(kt-xs)*Ft|0,c[rt++]=$-($-Ss)*Ft|0,c[rt++]=255}}function Yf(f,t,e){const s=t.coords,i=t.colors;let r,a;switch(t.type){case"lattice":const o=t.verticesPerRow,h=Math.floor(s.length/o)-1,l=o-1;for(r=0;r<h;r++){let c=r*o;for(let d=0;d<l;d++,c++)Yl(f,e,s[c],s[c+1],s[c+o],i[c],i[c+1],i[c+o]),Yl(f,e,s[c+o+1],s[c+1],s[c+o],i[c+o+1],i[c+1],i[c+o])}break;case"triangles":for(r=0,a=s.length;r<a;r+=3)Yl(f,e,s[r],s[r+1],s[r+2],i[r],i[r+1],i[r+2]);break;default:throw new Error("illegal figure")}}class Kf extends vd{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[7],this._background=t[8],this.matrix=null}_createMeshCanvas(t,e,s){const i=Math.floor(this._bounds[0]),r=Math.floor(this._bounds[1]),a=Math.ceil(this._bounds[2])-i,o=Math.ceil(this._bounds[3])-r,h=Math.min(Math.ceil(Math.abs(a*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(o*t[1]*1.1)),3e3),c=a/h,d=o/l,p={coords:this._coords,colors:this._colors,offsetX:-i,offsetY:-r,scaleX:1/c,scaleY:1/d},g=h+4,m=l+4,v=s.getCanvas("mesh",g,m),y=v.context,w=y.createImageData(h,l);if(e){const _=w.data;for(let x=0,S=_.length;x<S;x+=4)_[x]=e[0],_[x+1]=e[1],_[x+2]=e[2],_[x+3]=255}for(const _ of this._figures)Yf(w,_,p);return y.putImageData(w,2,2),{canvas:v.canvas,offsetX:i-2*c,offsetY:r-2*d,scaleX:c,scaleY:d}}getPattern(t,e,s,i){wc(t,this._bbox);let r;if(i===Ga)r=D.singularValueDecompose2dScale(ct(t));else if(r=D.singularValueDecompose2dScale(e.baseTransform),this.matrix){const o=D.singularValueDecompose2dScale(this.matrix);r=[r[0]*o[0],r[1]*o[1]]}const a=this._createMeshCanvas(r,i===Ga?null:this._background,e.cachedCanvases);return i!==Ga&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(a.offsetX,a.offsetY),t.scale(a.scaleX,a.scaleY),t.createPattern(a.canvas,"no-repeat")}}class Qf extends vd{getPattern(){return"hotpink"}}const Jf=1,Zf=2,wl=class wl{constructor(t,e,s,i,r){this.operatorList=t[2],this.matrix=t[3],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.color=e,this.ctx=s,this.canvasGraphicsFactory=i,this.baseTransform=r}createPatternCanvas(t){const{bbox:e,operatorList:s,paintType:i,tilingType:r,color:a,canvasGraphicsFactory:o}=this;let{xstep:h,ystep:l}=this;h=Math.abs(h),l=Math.abs(l),Fl("TilingType: "+r);const c=e[0],d=e[1],p=e[2],g=e[3],m=p-c,v=g-d,y=D.singularValueDecompose2dScale(this.matrix),w=D.singularValueDecompose2dScale(this.baseTransform),_=y[0]*w[0],x=y[1]*w[1];let S=m,C=v,k=!1,E=!1;const L=Math.ceil(h*_),O=Math.ceil(l*x);L>=Math.ceil(m*_)?S=h:k=!0,O>=Math.ceil(v*x)?C=l:E=!0;const B=this.getSizeAndScale(S,this.ctx.canvas.width,_),G=this.getSizeAndScale(C,this.ctx.canvas.height,x),nt=t.cachedCanvases.getCanvas("pattern",B.size,G.size),ut=nt.context,K=o.createCanvasGraphics(ut);if(K.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(K,i,a),ut.translate(-B.scale*c,-G.scale*d),K.transform(B.scale,0,0,G.scale,0,0),ut.save(),this.clipBbox(K,c,d,p,g),K.baseTransform=ct(K.ctx),K.executeOperatorList(s),K.endDrawing(),ut.restore(),k||E){const mt=nt.canvas;k&&(S=h),E&&(C=l);const kt=this.getSizeAndScale(S,this.ctx.canvas.width,_),$=this.getSizeAndScale(C,this.ctx.canvas.height,x),z=kt.size,ke=$.size,xs=t.cachedCanvases.getCanvas("pattern-workaround",z,ke),Ss=xs.context,Yt=k?Math.floor(m/h):0,Ft=E?Math.floor(v/l):0;for(let ai=0;ai<=Yt;ai++)for(let ue=0;ue<=Ft;ue++)Ss.drawImage(mt,z*ai,ke*ue,z,ke,0,0,z,ke);return{canvas:xs.canvas,scaleX:kt.scale,scaleY:$.scale,offsetX:c,offsetY:d}}return{canvas:nt.canvas,scaleX:B.scale,scaleY:G.scale,offsetX:c,offsetY:d}}getSizeAndScale(t,e,s){const i=Math.max(wl.MAX_PATTERN_SIZE,e);let r=Math.ceil(t*s);return r>=i?r=i:s=r/t,{scale:s,size:r}}clipBbox(t,e,s,i,r){const a=i-e,o=r-s;t.ctx.rect(e,s,a,o),t.current.updateRectMinMax(ct(t.ctx),[e,s,i,r]),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,s){const i=t.ctx,r=t.current;switch(e){case Jf:const a=this.ctx;i.fillStyle=a.fillStyle,i.strokeStyle=a.strokeStyle,r.fillColor=a.fillStyle,r.strokeColor=a.strokeStyle;break;case Zf:const o=D.makeHexColor(s[0],s[1],s[2]);i.fillStyle=o,i.strokeStyle=o,r.fillColor=o,r.strokeColor=o;break;default:throw new Of(`Unsupported paint type: ${e}`)}}getPattern(t,e,s,i){let r=s;i!==Ga&&(r=D.transform(r,e.baseTransform),this.matrix&&(r=D.transform(r,this.matrix)));const a=this.createPatternCanvas(e);let o=new DOMMatrix(r);o=o.translate(a.offsetX,a.offsetY),o=o.scale(1/a.scaleX,1/a.scaleY);const h=t.createPattern(a.canvas,"repeat");return h.setTransform(o),h}};F(wl,"MAX_PATTERN_SIZE",3e3);let _c=wl;function tg({src:f,srcPos:t=0,dest:e,width:s,height:i,nonBlackColor:r=4294967295,inverseDecode:a=!1}){const o=se.isLittleEndian?4278190080:255,[h,l]=a?[r,o]:[o,r],c=s>>3,d=7&s,p=f.length;e=new Uint32Array(e.buffer);let g=0;for(let m=0;m<i;m++){for(const y=t+c;t<y;t++){const w=t<p?f[t]:255;e[g++]=128&w?l:h,e[g++]=64&w?l:h,e[g++]=32&w?l:h,e[g++]=16&w?l:h,e[g++]=8&w?l:h,e[g++]=4&w?l:h,e[g++]=2&w?l:h,e[g++]=1&w?l:h}if(d===0)continue;const v=t<p?f[t++]:255;for(let y=0;y<d;y++)e[g++]=v&1<<7-y?l:h}return{srcPos:t,destPos:g}}const de=16;class eg{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,s){let i;return this.cache[t]!==void 0?(i=this.cache[t],this.canvasFactory.reset(i,e,s)):(i=this.canvasFactory.create(e,s),this.cache[t]=i),i}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function Sh(f,t,e,s,i,r,a,o,h,l){const[c,d,p,g,m,v]=ct(f);if(d===0&&p===0){const y=a*c+m,w=Math.round(y),_=o*g+v,x=Math.round(_),S=(a+h)*c+m,C=Math.abs(Math.round(S)-w)||1,k=(o+l)*g+v,E=Math.abs(Math.round(k)-x)||1;return f.setTransform(Math.sign(c),0,0,Math.sign(g),w,x),f.drawImage(t,e,s,i,r,0,0,C,E),f.setTransform(c,d,p,g,m,v),[C,E]}if(c===0&&g===0){const y=o*p+m,w=Math.round(y),_=a*d+v,x=Math.round(_),S=(o+l)*p+m,C=Math.abs(Math.round(S)-w)||1,k=(a+h)*d+v,E=Math.abs(Math.round(k)-x)||1;return f.setTransform(0,Math.sign(d),Math.sign(p),0,w,x),f.drawImage(t,e,s,i,r,0,0,E,C),f.setTransform(c,d,p,g,m,v),[E,C]}return f.drawImage(t,e,s,i,r,a,o,h,l),[Math.hypot(c,d)*h,Math.hypot(p,g)*l]}class Ud{constructor(t,e){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=eu,this.textMatrixScale=1,this.fontMatrix=Ql,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=Mh,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.patternStroke=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.transferMaps="none",this.startNewPathAndClipBox([0,0,t,e])}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t}setCurrentPoint(t,e){this.x=t,this.y=e}updatePathMinMax(t,e,s){[e,s]=D.applyTransform([e,s],t),this.minX=Math.min(this.minX,e),this.minY=Math.min(this.minY,s),this.maxX=Math.max(this.maxX,e),this.maxY=Math.max(this.maxY,s)}updateRectMinMax(t,e){const s=D.applyTransform(e,t),i=D.applyTransform(e.slice(2),t),r=D.applyTransform([e[0],e[3]],t),a=D.applyTransform([e[2],e[1]],t);this.minX=Math.min(this.minX,s[0],i[0],r[0],a[0]),this.minY=Math.min(this.minY,s[1],i[1],r[1],a[1]),this.maxX=Math.max(this.maxX,s[0],i[0],r[0],a[0]),this.maxY=Math.max(this.maxY,s[1],i[1],r[1],a[1])}updateScalingPathMinMax(t,e){D.scaleMinMax(t,e),this.minX=Math.min(this.minX,e[0]),this.minY=Math.min(this.minY,e[1]),this.maxX=Math.max(this.maxX,e[2]),this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,s,i,r,a,o,h,l,c){const d=D.bezierBoundingBox(e,s,i,r,a,o,h,l,c);c||this.updateRectMinMax(t,d)}getPathBoundingBox(t=Ki,e=null){const s=[this.minX,this.minY,this.maxX,this.maxY];if(t===ll){e||at("Stroke bounding box must include transform.");const i=D.singularValueDecompose2dScale(e),r=i[0]*this.lineWidth/2,a=i[1]*this.lineWidth/2;s[0]-=r,s[1]-=a,s[2]+=r,s[3]+=a}return s}updateClipFromPath(){const t=D.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t,this.minX=1/0,this.minY=1/0,this.maxX=0,this.maxY=0}getClippedPathBoundingBox(t=Ki,e=null){return D.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function Wd(f,t){if(t instanceof ImageData){f.putImageData(t,0,0);return}const e=t.height,s=t.width,i=e%de,r=(e-i)/de,a=i===0?r:r+1,o=f.createImageData(s,de);let h,l=0;const c=t.data,d=o.data;let p,g,m,v;if(t.kind===kh.GRAYSCALE_1BPP){const y=c.byteLength,w=new Uint32Array(d.buffer,0,d.byteLength>>2),_=w.length,x=s+7>>3,S=4294967295,C=se.isLittleEndian?4278190080:255;for(p=0;p<a;p++){for(m=p<r?de:i,h=0,g=0;g<m;g++){const k=y-l;let E=0;const L=k>x?s:8*k-7,O=-8&L;let B=0,G=0;for(;E<O;E+=8)G=c[l++],w[h++]=128&G?S:C,w[h++]=64&G?S:C,w[h++]=32&G?S:C,w[h++]=16&G?S:C,w[h++]=8&G?S:C,w[h++]=4&G?S:C,w[h++]=2&G?S:C,w[h++]=1&G?S:C;for(;E<L;E++)B===0&&(G=c[l++],B=128),w[h++]=G&B?S:C,B>>=1}for(;h<_;)w[h++]=0;f.putImageData(o,0,p*de)}}else if(t.kind===kh.RGBA_32BPP){for(g=0,v=s*de*4,p=0;p<r;p++)d.set(c.subarray(l,l+v)),l+=v,f.putImageData(o,0,g),g+=de;p<a&&(v=s*i*4,d.set(c.subarray(l,l+v)),f.putImageData(o,0,g))}else{if(t.kind!==kh.RGB_24BPP)throw new Error(`bad image kind: ${t.kind}`);for(m=de,v=s*m,p=0;p<a;p++){for(p>=r&&(m=i,v=s*m),h=0,g=v;g--;)d[h++]=c[l++],d[h++]=c[l++],d[h++]=c[l++],d[h++]=255;f.putImageData(o,0,p*de)}}}function qd(f,t){if(t.bitmap){f.drawImage(t.bitmap,0,0);return}const e=t.height,s=t.width,i=e%de,r=(e-i)/de,a=i===0?r:r+1,o=f.createImageData(s,de);let h=0;const l=t.data,c=o.data;for(let d=0;d<a;d++){const p=d<r?de:i;({srcPos:h}=tg({src:l,srcPos:h,dest:c,width:s,height:p,nonBlackColor:0})),f.putImageData(o,0,d*de)}}function Sa(f,t){const e=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of e)f[s]!==void 0&&(t[s]=f[s]);f.setLineDash!==void 0&&(t.setLineDash(f.getLineDash()),t.lineDashOffset=f.lineDashOffset)}function Eh(f){if(f.strokeStyle=f.fillStyle="#000000",f.fillRule="nonzero",f.globalAlpha=1,f.lineWidth=1,f.lineCap="butt",f.lineJoin="miter",f.miterLimit=10,f.globalCompositeOperation="source-over",f.font="10px sans-serif",f.setLineDash!==void 0&&(f.setLineDash([]),f.lineDashOffset=0),!Xt){const{filter:t}=f;t!=="none"&&t!==""&&(f.filter="none")}}function Xd(f,t){if(t)return!0;const e=D.singularValueDecompose2dScale(f);e[0]=Math.fround(e[0]),e[1]=Math.fround(e[1]);const s=Math.fround((globalThis.devicePixelRatio||1)*ji.PDF_TO_CSS_UNITS);return e[0]<=s&&e[1]<=s}const sg=["butt","round","square"],ig=["miter","round","bevel"],ng={},Yd={};var ss,xc,Sc,Ec;const Sd=class Sd{constructor(t,e,s,i,r,{optionalContentConfig:a,markedContentStack:o=null},h,l){b(this,ss);this.ctx=t,this.current=new Ud(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=s,this.canvasFactory=i,this.filterFactory=r,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=o||[],this.optionalContentConfig=a,this.cachedCanvases=new eg(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=h,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=l,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t,e=null){return typeof t=="string"?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:s=!1,background:i=null}){const r=this.ctx.canvas.width,a=this.ctx.canvas.height,o=this.ctx.fillStyle;if(this.ctx.fillStyle=i||"#ffffff",this.ctx.fillRect(0,0,r,a),this.ctx.fillStyle=o,s){const h=this.cachedCanvases.getCanvas("transparent",r,a);this.compositeCtx=this.ctx,this.transparentCanvas=h.canvas,this.ctx=h.context,this.ctx.save(),this.ctx.transform(...ct(this.compositeCtx))}this.ctx.save(),Eh(this.ctx),t&&(this.ctx.transform(...t),this.outputScaleX=t[0],this.outputScaleY=t[0]),this.ctx.transform(...e.transform),this.viewportScale=e.scale,this.baseTransform=ct(this.ctx)}executeOperatorList(t,e,s,i){const r=t.argsArray,a=t.fnArray;let o=e||0;const h=r.length;if(h===o)return o;const l=h-o>10&&typeof s=="function",c=l?Date.now()+15:0;let d=0;const p=this.commonObjs,g=this.objs;let m;for(;;){if(i!==void 0&&o===i.nextBreakPoint)return i.breakIt(o,s),o;if(m=a[o],m!==ze.dependency)this[m].apply(this,r[o]);else for(const v of r[o]){const y=v.startsWith("g_")?p:g;if(!y.has(v))return y.get(v,s),o}if(o++,o===h)return o;if(l&&++d>10){if(Date.now()>c)return s(),o;d=0}}}endDrawing(){A(this,ss,xc).call(this),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())typeof HTMLCanvasElement<"u"&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),A(this,ss,Sc).call(this)}_scaleImage(t,e){const s=t.width??t.displayWidth,i=t.height??t.displayHeight;let r,a,o=Math.max(Math.hypot(e[0],e[1]),1),h=Math.max(Math.hypot(e[2],e[3]),1),l=s,c=i,d="prescale1";for(;o>2&&l>1||h>2&&c>1;){let p=l,g=c;o>2&&l>1&&(p=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2),o/=l/p),h>2&&c>1&&(g=c>=16384?Math.floor(c/2)-1||1:Math.ceil(c)/2,h/=c/g),r=this.cachedCanvases.getCanvas(d,p,g),a=r.context,a.clearRect(0,0,p,g),a.drawImage(t,0,0,l,c,0,0,p,g),t=r.canvas,l=p,c=g,d=d==="prescale1"?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:c}}_createMaskCanvas(t){const e=this.ctx,{width:s,height:i}=t,r=this.current.fillColor,a=this.current.patternFill,o=ct(e);let h,l,c,d;if((t.bitmap||t.data)&&t.count>1){const L=t.bitmap||t.data.buffer;l=JSON.stringify(a?o:[o.slice(0,4),r]),h=this._cachedBitmapsMap.get(L),h||(h=new Map,this._cachedBitmapsMap.set(L,h));const O=h.get(l);if(O&&!a)return{canvas:O,offsetX:Math.round(Math.min(o[0],o[2])+o[4]),offsetY:Math.round(Math.min(o[1],o[3])+o[5])};c=O}c||(d=this.cachedCanvases.getCanvas("maskCanvas",s,i),qd(d.context,t));let p=D.transform(o,[1/s,0,0,-1/i,0,0]);p=D.transform(p,[1,0,0,1,0,-i]);const[g,m,v,y]=D.getAxialAlignedBoundingBox([0,0,s,i],p),w=Math.round(v-g)||1,_=Math.round(y-m)||1,x=this.cachedCanvases.getCanvas("fillCanvas",w,_),S=x.context,C=g,k=m;S.translate(-C,-k),S.transform(...p),c||(c=this._scaleImage(d.canvas,is(S)),c=c.img,h&&a&&h.set(l,c)),S.imageSmoothingEnabled=Xd(ct(S),t.interpolate),Sh(S,c,0,0,c.width,c.height,0,0,s,i),S.globalCompositeOperation="source-in";const E=D.transform(is(S),[1,0,0,1,-C,-k]);return S.fillStyle=a?r.getPattern(e,this,E,Ki):r,S.fillRect(0,0,s,i),h&&!a&&(this.cachedCanvases.delete("fillCanvas"),h.set(l,x.canvas)),{canvas:x.canvas,offsetX:Math.round(C),offsetY:Math.round(k)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=sg[t]}setLineJoin(t){this.ctx.lineJoin=ig[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const s=this.ctx;s.setLineDash!==void 0&&(s.setLineDash(t),s.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,s]of t)switch(e){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s[0],s[1]);break;case"CA":this.current.strokeAlpha=s;break;case"ca":this.current.fillAlpha=s,this.ctx.globalAlpha=s;break;case"BM":this.ctx.globalCompositeOperation=s;break;case"SMask":this.current.activeSMask=s?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(s)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,s="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(s,t,e);this.suspendedCtx=this.ctx,this.ctx=i.context;const r=this.ctx;r.setTransform(...ct(this.suspendedCtx)),Sa(this.suspendedCtx,r),function(o,h){if(o._removeMirroring)throw new Error("Context is already forwarding operations.");o.__originalSave=o.save,o.__originalRestore=o.restore,o.__originalRotate=o.rotate,o.__originalScale=o.scale,o.__originalTranslate=o.translate,o.__originalTransform=o.transform,o.__originalSetTransform=o.setTransform,o.__originalResetTransform=o.resetTransform,o.__originalClip=o.clip,o.__originalMoveTo=o.moveTo,o.__originalLineTo=o.lineTo,o.__originalBezierCurveTo=o.bezierCurveTo,o.__originalRect=o.rect,o.__originalClosePath=o.closePath,o.__originalBeginPath=o.beginPath,o._removeMirroring=()=>{o.save=o.__originalSave,o.restore=o.__originalRestore,o.rotate=o.__originalRotate,o.scale=o.__originalScale,o.translate=o.__originalTranslate,o.transform=o.__originalTransform,o.setTransform=o.__originalSetTransform,o.resetTransform=o.__originalResetTransform,o.clip=o.__originalClip,o.moveTo=o.__originalMoveTo,o.lineTo=o.__originalLineTo,o.bezierCurveTo=o.__originalBezierCurveTo,o.rect=o.__originalRect,o.closePath=o.__originalClosePath,o.beginPath=o.__originalBeginPath,delete o._removeMirroring},o.save=function(){h.save(),this.__originalSave()},o.restore=function(){h.restore(),this.__originalRestore()},o.translate=function(c,d){h.translate(c,d),this.__originalTranslate(c,d)},o.scale=function(c,d){h.scale(c,d),this.__originalScale(c,d)},o.transform=function(c,d,p,g,m,v){h.transform(c,d,p,g,m,v),this.__originalTransform(c,d,p,g,m,v)},o.setTransform=function(c,d,p,g,m,v){h.setTransform(c,d,p,g,m,v),this.__originalSetTransform(c,d,p,g,m,v)},o.resetTransform=function(){h.resetTransform(),this.__originalResetTransform()},o.rotate=function(c){h.rotate(c),this.__originalRotate(c)},o.clip=function(c){h.clip(c),this.__originalClip(c)},o.moveTo=function(l,c){h.moveTo(l,c),this.__originalMoveTo(l,c)},o.lineTo=function(l,c){h.lineTo(l,c),this.__originalLineTo(l,c)},o.bezierCurveTo=function(l,c,d,p,g,m){h.bezierCurveTo(l,c,d,p,g,m),this.__originalBezierCurveTo(l,c,d,p,g,m)},o.rect=function(l,c,d,p){h.rect(l,c,d,p),this.__originalRect(l,c,d,p)},o.closePath=function(){h.closePath(),this.__originalClosePath()},o.beginPath=function(){h.beginPath(),this.__originalBeginPath()}}(r,this.suspendedCtx),this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),Sa(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,s=this.suspendedCtx;this.composeSMask(s,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}composeSMask(t,e,s,i){const r=i[0],a=i[1],o=i[2]-r,h=i[3]-a;o!==0&&h!==0&&(this.genericComposeSMask(e.context,s,o,h,e.subtype,e.backdrop,e.transferMap,r,a,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(s.canvas,0,0),t.restore())}genericComposeSMask(t,e,s,i,r,a,o,h,l,c,d){let p=t.canvas,g=h-c,m=l-d;if(a){const y=D.makeHexColor(...a);if(g<0||m<0||g+s>p.width||m+i>p.height){const w=this.cachedCanvases.getCanvas("maskExtension",s,i),_=w.context;_.drawImage(p,-g,-m),_.globalCompositeOperation="destination-atop",_.fillStyle=y,_.fillRect(0,0,s,i),_.globalCompositeOperation="source-over",p=w.canvas,g=m=0}else{t.save(),t.globalAlpha=1,t.setTransform(1,0,0,1,0,0);const w=new Path2D;w.rect(g,m,s,i),t.clip(w),t.globalCompositeOperation="destination-atop",t.fillStyle=y,t.fillRect(g,m,s,i),t.restore()}}e.save(),e.globalAlpha=1,e.setTransform(1,0,0,1,0,0),r==="Alpha"&&o?e.filter=this.filterFactory.addAlphaFilter(o):r==="Luminosity"&&(e.filter=this.filterFactory.addLuminosityFilter(o));const v=new Path2D;v.rect(h,l,s,i),e.clip(v),e.globalCompositeOperation="destination-in",e.drawImage(p,g,m,s,i,h,l,s,i),e.restore()}save(){this.inSMaskMode?(Sa(this.ctx,this.suspendedCtx),this.suspendedCtx.save()):this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){this.stateStack.length===0&&this.inSMaskMode&&this.endSMaskMode(),this.stateStack.length!==0&&(this.current=this.stateStack.pop(),this.inSMaskMode?(this.suspendedCtx.restore(),Sa(this.suspendedCtx,this.ctx)):this.ctx.restore(),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null)}transform(t,e,s,i,r,a){this.ctx.transform(t,e,s,i,r,a),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,s){const i=this.ctx,r=this.current;let a,o,h=r.x,l=r.y;const c=ct(i),d=c[0]===0&&c[3]===0||c[1]===0&&c[2]===0,p=d?s.slice(0):null;for(let g=0,m=0,v=t.length;g<v;g++)switch(0|t[g]){case ze.rectangle:h=e[m++],l=e[m++];const y=e[m++],w=e[m++],_=h+y,x=l+w;i.moveTo(h,l),y===0||w===0?i.lineTo(_,x):(i.lineTo(_,l),i.lineTo(_,x),i.lineTo(h,x)),d||r.updateRectMinMax(c,[h,l,_,x]),i.closePath();break;case ze.moveTo:h=e[m++],l=e[m++],i.moveTo(h,l),d||r.updatePathMinMax(c,h,l);break;case ze.lineTo:h=e[m++],l=e[m++],i.lineTo(h,l),d||r.updatePathMinMax(c,h,l);break;case ze.curveTo:a=h,o=l,h=e[m+4],l=e[m+5],i.bezierCurveTo(e[m],e[m+1],e[m+2],e[m+3],h,l),r.updateCurvePathMinMax(c,a,o,e[m],e[m+1],e[m+2],e[m+3],h,l,p),m+=6;break;case ze.curveTo2:a=h,o=l,i.bezierCurveTo(h,l,e[m],e[m+1],e[m+2],e[m+3]),r.updateCurvePathMinMax(c,a,o,h,l,e[m],e[m+1],e[m+2],e[m+3],p),h=e[m+2],l=e[m+3],m+=4;break;case ze.curveTo3:a=h,o=l,h=e[m+2],l=e[m+3],i.bezierCurveTo(e[m],e[m+1],h,l,h,l),r.updateCurvePathMinMax(c,a,o,e[m],e[m+1],h,l,h,l,p),m+=4;break;case ze.closePath:i.closePath()}d&&r.updateScalingPathMinMax(c,p),r.setCurrentPoint(h,l)}closePath(){this.ctx.closePath()}stroke(t=!0){const e=this.ctx,s=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha,this.contentVisible&&(typeof s=="object"&&s?.getPattern?(e.save(),e.strokeStyle=s.getPattern(e,this,is(e),ll),this.rescaleAndStroke(!1),e.restore()):this.rescaleAndStroke(!0)),t&&this.consumePath(this.current.getClippedPathBoundingBox()),e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath(),this.stroke()}fill(t=!0){const e=this.ctx,s=this.current.fillColor;let i=!1;this.current.patternFill&&(e.save(),e.fillStyle=s.getPattern(e,this,is(e),Ki),i=!0);const r=this.current.getClippedPathBoundingBox();this.contentVisible&&r!==null&&(this.pendingEOFill?(e.fill("evenodd"),this.pendingEOFill=!1):e.fill()),i&&e.restore(),t&&this.consumePath(r)}eoFill(){this.pendingEOFill=!0,this.fill()}fillStroke(){this.fill(!1),this.stroke(!1),this.consumePath()}eoFillStroke(){this.pendingEOFill=!0,this.fillStroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=ng}eoClip(){this.pendingClip=Yd}beginText(){this.current.textMatrix=eu,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(t===void 0){e.beginPath();return}const s=new Path2D,i=e.getTransform().invertSelf();for(const{transform:r,x:a,y:o,fontSize:h,path:l}of t)s.addPath(l,new DOMMatrix(r).preMultiplySelf(i).translate(a,o).scale(h,-h));e.clip(s),e.beginPath(),delete this.pendingTextPaths}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const s=this.commonObjs.get(t),i=this.current;if(!s)throw new Error(`Can't find font for ${t}`);if(i.fontMatrix=s.fontMatrix||Ql,i.fontMatrix[0]!==0&&i.fontMatrix[3]!==0||U("Invalid font matrix for font "+t),e<0?(e=-e,i.fontDirection=-1):i.fontDirection=1,this.current.font=s,this.current.fontSize=e,s.isType3Font)return;const r=s.loadedName||"sans-serif",a=s.systemFontInfo?.css||`"${r}", ${s.fallbackName}`;let o="normal";s.black?o="900":s.bold&&(o="bold");const h=s.italic?"italic":"normal";let l=e;e<16?l=16:e>100&&(l=100),this.current.fontSizeScale=e/l,this.ctx.font=`${h} ${o} ${l}px ${a}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t,e,s,i,r,a){this.current.textMatrix=[t,e,s,i,r,a],this.current.textMatrixScale=Math.hypot(t,e),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,s,i,r){const a=this.ctx,o=this.current,h=o.font,l=o.textRenderingMode,c=o.fontSize/o.fontSizeScale,d=l&Td,p=!!(l&cf),g=o.patternFill&&!h.missingFile,m=o.patternStroke&&!h.missingFile;let v;if((h.disableFontFace||p||g||m)&&(v=h.getPathGenerator(this.commonObjs,t)),h.disableFontFace||g||m){if(a.save(),a.translate(e,s),a.scale(c,-c),d===Mh||d===xa)if(i){const y=a.getTransform();a.setTransform(...i),a.fill(A(this,ss,Ec).call(this,v,y,i))}else a.fill(v);if(d===Gl||d===xa)if(r){const y=a.getTransform();a.setTransform(...r),a.stroke(A(this,ss,Ec).call(this,v,y,r))}else a.lineWidth/=c,a.stroke(v);a.restore()}else d!==Mh&&d!==xa||a.fillText(t,e,s),d!==Gl&&d!==xa||a.strokeText(t,e,s);p&&(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:ct(a),x:e,y:s,fontSize:c,path:v})}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let s=!1;for(let i=3;i<e.length;i+=4)if(e[i]>0&&e[i]<255){s=!0;break}return X(this,"isFontSubpixelAAEnabled",s)}showText(t){const e=this.current,s=e.font;if(s.isType3Font)return this.showType3Text(t);const i=e.fontSize;if(i===0)return;const r=this.ctx,a=e.fontSizeScale,o=e.charSpacing,h=e.wordSpacing,l=e.fontDirection,c=e.textHScale*l,d=t.length,p=s.vertical,g=p?1:-1,m=s.defaultVMetrics,v=i*e.fontMatrix[0],y=e.textRenderingMode===Mh&&!s.disableFontFace&&!e.patternFill;r.save(),r.transform(...e.textMatrix),r.translate(e.x,e.y+e.textRise),l>0?r.scale(c,-1):r.scale(c,1);let w,_;if(e.patternFill){r.save();const E=e.fillColor.getPattern(r,this,is(r),Ki);w=ct(r),r.restore(),r.fillStyle=E}if(e.patternStroke){r.save();const E=e.strokeColor.getPattern(r,this,is(r),ll);_=ct(r),r.restore(),r.strokeStyle=E}let x=e.lineWidth;const S=e.textMatrixScale;if(S===0||x===0){const E=e.textRenderingMode&Td;E!==Gl&&E!==xa||(x=this.getSinglePixelWidth())}else x/=S;if(a!==1&&(r.scale(a,a),x/=a),r.lineWidth=x,s.isInvalidPDFjsFont){const E=[];let L=0;for(const O of t)E.push(O.unicode),L+=O.width;r.fillText(E.join(""),0,0),e.x+=L*v*c,r.restore(),this.compose();return}let C,k=0;for(C=0;C<d;++C){const E=t[C];if(typeof E=="number"){k+=g*E*i/1e3;continue}let L=!1;const O=(E.isSpace?h:0)+o,B=E.fontChar,G=E.accent;let nt,ut,K=E.width;if(p){const mt=E.vmetric||m,kt=-(E.vmetric?mt[1]:.5*K)*v,$=mt[2]*v;K=mt?-mt[0]:K,nt=kt/a,ut=(k+$)/a}else nt=k/a,ut=0;if(s.remeasure&&K>0){const mt=1e3*r.measureText(B).width/i*a;if(K<mt&&this.isFontSubpixelAAEnabled){const kt=K/mt;L=!0,r.save(),r.scale(kt,1),nt/=kt}else K!==mt&&(nt+=(K-mt)/2e3*i/a)}if(this.contentVisible&&(E.isInFont||s.missingFile)){if(y&&!G)r.fillText(B,nt,ut);else if(this.paintChar(B,nt,ut,w,_),G){const mt=nt+i*G.offset.x/a,kt=ut-i*G.offset.y/a;this.paintChar(G.fontChar,mt,kt,w,_)}}k+=p?K*v-O*l:K*v+O*l,L&&r.restore()}p?e.y-=k:e.x+=k*c,r.restore(),this.compose()}showType3Text(t){const e=this.ctx,s=this.current,i=s.font,r=s.fontSize,a=s.fontDirection,o=i.vertical?1:-1,h=s.charSpacing,l=s.wordSpacing,c=s.textHScale*a,d=s.fontMatrix||Ql,p=t.length;let g,m,v,y;if(s.textRenderingMode!==lf&&r!==0){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,e.save(),e.transform(...s.textMatrix),e.translate(s.x,s.y),e.scale(c,a),g=0;g<p;++g){if(m=t[g],typeof m=="number"){y=o*m*r/1e3,this.ctx.translate(y,0),s.x+=y*c;continue}const w=(m.isSpace?l:0)+h,_=i.charProcOperatorList[m.operatorListId];if(!_){U(`Type3 character "${m.operatorListId}" is not available.`);continue}this.contentVisible&&(this.processingType3=m,this.save(),e.scale(r,r),e.transform(...d),this.executeOperatorList(_),this.restore()),v=D.applyTransform([m.width,0],d)[0]*r+w,e.translate(v,0),s.x+=v*c}e.restore(),this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,s,i,r,a){this.ctx.rect(s,i,r-s,a-i),this.ctx.clip(),this.endPath()}getColorN_Pattern(t){let e;if(t[0]==="TilingPattern"){const s=t[1],i=this.baseTransform||ct(this.ctx),r={createCanvasGraphics:a=>new Sd(a,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new _c(t,s,this.ctx,r,i)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments),this.current.patternStroke=!0}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t,e,s){this.ctx.strokeStyle=this.current.strokeColor=D.makeHexColor(t,e,s),this.current.patternStroke=!1}setStrokeTransparent(){this.ctx.strokeStyle=this.current.strokeColor="transparent",this.current.patternStroke=!1}setFillRGBColor(t,e,s){this.ctx.fillStyle=this.current.fillColor=D.makeHexColor(t,e,s),this.current.patternFill=!1}setFillTransparent(){this.ctx.fillStyle=this.current.fillColor="transparent",this.current.patternFill=!1}_getPattern(t,e=null){let s;return this.cachedPatterns.has(t)?s=this.cachedPatterns.get(t):(s=function(r){switch(r[0]){case"RadialAxial":return new Xf(r);case"Mesh":return new Kf(r);case"Dummy":return new Qf}throw new Error(`Unknown IR type: ${r[0]}`)}(this.getObject(t)),this.cachedPatterns.set(t,s)),e&&(s.matrix=e),s}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const s=this._getPattern(t);e.fillStyle=s.getPattern(e,this,is(e),Ga);const i=is(e);if(i){const{width:r,height:a}=e.canvas,[o,h,l,c]=D.getAxialAlignedBoundingBox([0,0,r,a],i);this.ctx.fillRect(o,h,l-o,c-h)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){at("Should not call beginInlineImage")}beginImageData(){at("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),t&&this.transform(...t),this.baseTransform=ct(this.ctx),e)){const s=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],s,i),this.current.updateRectMinMax(ct(this.ctx),e),this.clip(),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||Fl("TODO: Support non-isolated groups."),t.knockout&&U("Knockout groups not supported.");const s=ct(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw new Error("Bounding box is required.");let i=D.getAxialAlignedBoundingBox(t.bbox,ct(e));const r=[0,0,e.canvas.width,e.canvas.height];i=D.intersect(i,r)||[0,0,0,0];const a=Math.floor(i[0]),o=Math.floor(i[1]),h=Math.max(Math.ceil(i[2])-a,1),l=Math.max(Math.ceil(i[3])-o,1);this.current.startNewPathAndClipBox([0,0,h,l]);let c="groupAt"+this.groupLevel;t.smask&&(c+="_smask_"+this.smaskCounter++%2);const d=this.cachedCanvases.getCanvas(c,h,l),p=d.context;p.translate(-a,-o),p.transform(...s),t.smask?this.smaskStack.push({canvas:d.canvas,context:p,offsetX:a,offsetY:o,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(a,o),e.save()),Sa(e,p),this.ctx=p,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,s=this.groupStack.pop();if(this.ctx=s,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const i=ct(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...i);const r=D.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],i);this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(r)}}beginAnnotation(t,e,s,i,r){if(A(this,ss,xc).call(this),Eh(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),e){const a=e[2]-e[0],o=e[3]-e[1];if(r&&this.annotationCanvasMap){(s=s.slice())[4]-=e[0],s[5]-=e[1],(e=e.slice())[0]=e[1]=0,e[2]=a,e[3]=o;const[h,l]=D.singularValueDecompose2dScale(ct(this.ctx)),{viewportScale:c}=this,d=Math.ceil(a*this.outputScaleX*c),p=Math.ceil(o*this.outputScaleY*c);this.annotationCanvas=this.canvasFactory.create(d,p);const{canvas:g,context:m}=this.annotationCanvas;this.annotationCanvasMap.set(t,g),this.annotationCanvas.savedCtx=this.ctx,this.ctx=m,this.ctx.save(),this.ctx.setTransform(h,0,0,-l,0,o*l),Eh(this.ctx)}else Eh(this.ctx),this.endPath(),this.ctx.rect(e[0],e[1],a,o),this.ctx.clip(),this.ctx.beginPath()}this.current=new Ud(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...s),this.transform(...i)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),A(this,ss,Sc).call(this),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const s=this.ctx,i=this.processingType3;if(i&&(i.compiled===void 0&&(i.compiled=function(h){const{width:l,height:c}=h;if(l>1e3||c>1e3)return null;const d=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),p=l+1;let g,m,v,y=new Uint8Array(p*(c+1));const w=l+7&-8;let _=new Uint8Array(w*c),x=0;for(const E of h.data){let L=128;for(;L>0;)_[x++]=E&L?0:255,L>>=1}let S=0;for(x=0,_[x]!==0&&(y[0]=1,++S),m=1;m<l;m++)_[x]!==_[x+1]&&(y[m]=_[x]?2:1,++S),x++;for(_[x]!==0&&(y[m]=2,++S),g=1;g<c;g++){x=g*w,v=g*p,_[x-w]!==_[x]&&(y[v]=_[x]?1:8,++S);let E=(_[x]?4:0)+(_[x-w]?8:0);for(m=1;m<l;m++)E=(E>>2)+(_[x+1]?4:0)+(_[x-w+1]?8:0),d[E]&&(y[v+m]=d[E],++S),x++;if(_[x-w]!==_[x]&&(y[v+m]=_[x]?2:4,++S),S>1e3)return null}for(x=w*(c-1),v=g*p,_[x]!==0&&(y[v]=8,++S),m=1;m<l;m++)_[x]!==_[x+1]&&(y[v+m]=_[x]?4:8,++S),x++;if(_[x]!==0&&(y[v+m]=4,++S),S>1e3)return null;const C=new Int32Array([0,p,-1,0,-p,0,0,0,1]),k=new Path2D;for(g=0;S&&g<=c;g++){let E=g*p;const L=E+l;for(;E<L&&!y[E];)E++;if(E===L)continue;k.moveTo(E%p,g);const O=E;let B=y[E];do{const G=C[B];do E+=G;while(!y[E]);const nt=y[E];nt!==5&&nt!==10?(B=nt,y[E]=0):(B=nt&51*B>>4,y[E]&=B>>2|B<<2),k.lineTo(E%p,E/p|0),y[E]||--S}while(O!==E);--g}return _=null,y=null,function(E){E.save(),E.scale(1/l,-1/c),E.translate(0,-c),E.fill(k),E.beginPath(),E.restore()}}(t)),i.compiled)){i.compiled(s);return}const r=this._createMaskCanvas(t),a=r.canvas;s.save(),s.setTransform(1,0,0,1,0,0),s.drawImage(a,r.offsetX,r.offsetY),s.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e,s=0,i=0,r,a){if(!this.contentVisible)return;t=this.getObject(t.data,t);const o=this.ctx;o.save();const h=ct(o);o.transform(e,s,i,r,0,0);const l=this._createMaskCanvas(t);o.setTransform(1,0,0,1,l.offsetX-h[4],l.offsetY-h[5]);for(let c=0,d=a.length;c<d;c+=2){const p=D.transform(h,[e,s,i,r,a[c],a[c+1]]),[g,m]=D.applyTransform([0,0],p);o.drawImage(l.canvas,g,m)}o.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,s=this.current.fillColor,i=this.current.patternFill;for(const r of t){const{data:a,width:o,height:h,transform:l}=r,c=this.cachedCanvases.getCanvas("maskCanvas",o,h),d=c.context;d.save(),qd(d,this.getObject(a,r)),d.globalCompositeOperation="source-in",d.fillStyle=i?s.getPattern(d,this,is(e),Ki):s,d.fillRect(0,0,o,h),d.restore(),e.save(),e.transform(...l),e.scale(1,-1),Sh(e,c.canvas,0,0,o,h,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):U("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,s,i){if(!this.contentVisible)return;const r=this.getObject(t);if(!r){U("Dependent image isn't ready yet");return}const a=r.width,o=r.height,h=[];for(let l=0,c=i.length;l<c;l+=2)h.push({transform:[e,0,0,s,i[l],i[l+1]],x:0,y:0,w:a,h:o});this.paintInlineImageXObjectGroup(r,h)}applyTransferMapsToCanvas(t){return this.current.transferMaps!=="none"&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if(this.current.transferMaps==="none")return t.bitmap;const{bitmap:e,width:s,height:i}=t,r=this.cachedCanvases.getCanvas("inlineImage",s,i),a=r.context;return a.filter=this.current.transferMaps,a.drawImage(e,0,0),a.filter="none",r.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,s=t.height,i=this.ctx;if(this.save(),!Xt){const{filter:o}=i;o!=="none"&&o!==""&&(i.filter="none")}i.scale(1/e,-1/s);let r;if(t.bitmap)r=this.applyTransferMapsToBitmap(t);else if(typeof HTMLElement=="function"&&t instanceof HTMLElement||!t.data)r=t;else{const o=this.cachedCanvases.getCanvas("inlineImage",e,s).context;Wd(o,t),r=this.applyTransferMapsToCanvas(o)}const a=this._scaleImage(r,is(i));i.imageSmoothingEnabled=Xd(ct(i),t.interpolate),Sh(i,a.img,0,0,a.paintWidth,a.paintHeight,0,-s,e,s),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const s=this.ctx;let i;if(t.bitmap)i=t.bitmap;else{const r=t.width,a=t.height,o=this.cachedCanvases.getCanvas("inlineImage",r,a).context;Wd(o,t),i=this.applyTransferMapsToCanvas(o)}for(const r of e)s.save(),s.transform(...r.transform),s.scale(1,-1),Sh(s,i,r.x,r.y,r.w,r.h,0,-1,1,1),s.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){t==="OC"?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){const e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(t);const s=this.ctx;this.pendingClip&&(e||(this.pendingClip===Yd?s.clip("evenodd"):s.clip()),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox),s.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=ct(this.ctx);if(t[1]===0&&t[2]===0)this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),s=Math.hypot(t[0],t[2]),i=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(s,i)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(this._cachedScaleForStroking[0]===-1){const{lineWidth:t}=this.current,{a:e,b:s,c:i,d:r}=this.ctx.getTransform();let a,o;if(s===0&&i===0){const h=Math.abs(e),l=Math.abs(r);if(h===l)if(t===0)a=o=1/h;else{const c=h*t;a=o=c<1?1/c:1}else if(t===0)a=1/h,o=1/l;else{const c=h*t,d=l*t;a=c<1?1/c:1,o=d<1?1/d:1}}else{const h=Math.abs(e*r-s*i),l=Math.hypot(e,s),c=Math.hypot(i,r);if(t===0)a=c/h,o=l/h;else{const d=t*h;a=c>d?c/d:1,o=l>d?l/d:1}}this._cachedScaleForStroking[0]=a,this._cachedScaleForStroking[1]=o}return this._cachedScaleForStroking}rescaleAndStroke(t){const{ctx:e}=this,{lineWidth:s}=this.current,[i,r]=this.getScaleForStroking();if(e.lineWidth=s||1,i===1&&r===1){e.stroke();return}const a=e.getLineDash();if(t&&e.save(),e.scale(i,r),a.length>0){const o=Math.max(i,r);e.setLineDash(a.map(h=>h/o)),e.lineDashOffset/=o}e.stroke(),t&&e.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}};ss=new WeakSet,xc=function(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.current.activeSMask=null,this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)},Sc=function(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if(t!=="none"){const e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}},Ec=function(t,e,s){const i=new Path2D;return i.addPath(t,new DOMMatrix(s).invertSelf().multiplySelf(e)),i};let mr=Sd;for(const f in ze)mr.prototype[f]!==void 0&&(mr.prototype[ze[f]]=mr.prototype[f]);var xo,So;class ei{static get workerPort(){return n(this,xo)}static set workerPort(t){if(!(typeof Worker<"u"&&t instanceof Worker)&&t!==null)throw new Error("Invalid `workerPort` type.");u(this,xo,t)}static get workerSrc(){return n(this,So)}static set workerSrc(t){if(typeof t!="string")throw new Error("Invalid `workerSrc` type.");u(this,So,t)}}xo=new WeakMap,So=new WeakMap,b(ei,xo,null),b(ei,So,"");var An,Eo;class rg{constructor({parsedData:t,rawData:e}){b(this,An);b(this,Eo);u(this,An,t),u(this,Eo,e)}getRaw(){return n(this,Eo)}get(t){return n(this,An).get(t)??null}getAll(){return pd(n(this,An))}has(t){return n(this,An).has(t)}}An=new WeakMap,Eo=new WeakMap;const or=Symbol("INTERNAL");var Co,Mo,ko,Lr;class ag{constructor(t,{name:e,intent:s,usage:i,rbGroups:r}){b(this,Co,!1);b(this,Mo,!1);b(this,ko,!1);b(this,Lr,!0);u(this,Co,!!(t&ud)),u(this,Mo,!!(t&nl)),this.name=e,this.intent=s,this.usage=i,this.rbGroups=r}get visible(){if(n(this,ko))return n(this,Lr);if(!n(this,Lr))return!1;const{print:t,view:e}=this.usage;return n(this,Co)?e?.viewState!=="OFF":!n(this,Mo)||t?.printState!=="OFF"}_setVisible(t,e,s=!1){t!==or&&at("Internal method `_setVisible` called."),u(this,ko,s),u(this,Lr,e)}}Co=new WeakMap,Mo=new WeakMap,ko=new WeakMap,Lr=new WeakMap;var _i,et,Fr,Nr,Ro,Cc;class og{constructor(t,e=ud){b(this,Ro);b(this,_i,null);b(this,et,new Map);b(this,Fr,null);b(this,Nr,null);if(this.renderingIntent=e,this.name=null,this.creator=null,t!==null){this.name=t.name,this.creator=t.creator,u(this,Nr,t.order);for(const s of t.groups)n(this,et).set(s.id,new ag(e,s));if(t.baseState==="OFF")for(const s of n(this,et).values())s._setVisible(or,!1);for(const s of t.on)n(this,et).get(s)._setVisible(or,!0);for(const s of t.off)n(this,et).get(s)._setVisible(or,!1);u(this,Fr,this.getHash())}}isVisible(t){if(n(this,et).size===0)return!0;if(!t)return Fl("Optional content group not defined."),!0;if(t.type==="OCG")return n(this,et).has(t.id)?n(this,et).get(t.id).visible:(U(`Optional content group not found: ${t.id}`),!0);if(t.type==="OCMD"){if(t.expression)return A(this,Ro,Cc).call(this,t.expression);if(!t.policy||t.policy==="AnyOn"){for(const e of t.ids){if(!n(this,et).has(e))return U(`Optional content group not found: ${e}`),!0;if(n(this,et).get(e).visible)return!0}return!1}if(t.policy==="AllOn"){for(const e of t.ids){if(!n(this,et).has(e))return U(`Optional content group not found: ${e}`),!0;if(!n(this,et).get(e).visible)return!1}return!0}if(t.policy==="AnyOff"){for(const e of t.ids){if(!n(this,et).has(e))return U(`Optional content group not found: ${e}`),!0;if(!n(this,et).get(e).visible)return!0}return!1}if(t.policy==="AllOff"){for(const e of t.ids){if(!n(this,et).has(e))return U(`Optional content group not found: ${e}`),!0;if(n(this,et).get(e).visible)return!1}return!0}return U(`Unknown optional content policy ${t.policy}.`),!0}return U(`Unknown group type ${t.type}.`),!0}setVisibility(t,e=!0,s=!0){const i=n(this,et).get(t);if(i){if(s&&e&&i.rbGroups.length)for(const r of i.rbGroups)for(const a of r)a!==t&&n(this,et).get(a)?._setVisible(or,!1,!0);i._setVisible(or,!!e,!0),u(this,_i,null)}else U(`Optional content group not found: ${t}`)}setOCGState({state:t,preserveRB:e}){let s;for(const i of t){switch(i){case"ON":case"OFF":case"Toggle":s=i;continue}const r=n(this,et).get(i);if(r)switch(s){case"ON":this.setVisibility(i,!0,e);break;case"OFF":this.setVisibility(i,!1,e);break;case"Toggle":this.setVisibility(i,!r.visible,e)}}u(this,_i,null)}get hasInitialVisibility(){return n(this,Fr)===null||this.getHash()===n(this,Fr)}getOrder(){return n(this,et).size?n(this,Nr)?n(this,Nr).slice():[...n(this,et).keys()]:null}getGroups(){return n(this,et).size>0?pd(n(this,et)):null}getGroup(t){return n(this,et).get(t)||null}getHash(){if(n(this,_i)!==null)return n(this,_i);const t=new Du;for(const[e,s]of n(this,et))t.update(`${e}:${s.visible}`);return u(this,_i,t.hexdigest())}}_i=new WeakMap,et=new WeakMap,Fr=new WeakMap,Nr=new WeakMap,Ro=new WeakSet,Cc=function(t){const e=t.length;if(e<2)return!0;const s=t[0];for(let i=1;i<e;i++){const r=t[i];let a;if(Array.isArray(r))a=A(this,Ro,Cc).call(this,r);else{if(!n(this,et).has(r))return U(`Optional content group not found: ${r}`),!0;a=n(this,et).get(r).visible}switch(s){case"And":if(!a)return!1;break;case"Or":if(a)return!0;break;case"Not":return!a;default:return!0}}return s==="And"};class hg{constructor(t,{disableRange:e=!1,disableStream:s=!1}){_t(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:i,initialData:r,progressiveDone:a,contentDispositionFilename:o}=t;if(this._queuedChunks=[],this._progressiveDone=a,this._contentDispositionFilename=o,r?.length>0){const h=r instanceof Uint8Array&&r.byteLength===r.buffer.byteLength?r.buffer:new Uint8Array(r).buffer;this._queuedChunks.push(h)}this._pdfDataRangeTransport=t,this._isStreamingSupported=!s,this._isRangeSupported=!e,this._contentLength=i,this._fullRequestReader=null,this._rangeReaders=[],t.addRangeListener((h,l)=>{this._onReceiveData({begin:h,chunk:l})}),t.addProgressListener((h,l)=>{this._onProgress({loaded:h,total:l})}),t.addProgressiveReadListener(h=>{this._onReceiveData({chunk:h})}),t.addProgressiveDoneListener(()=>{this._onProgressiveDone()}),t.transportReady()}_onReceiveData({begin:t,chunk:e}){const s=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;t===void 0?this._fullRequestReader?this._fullRequestReader._enqueue(s):this._queuedChunks.push(s):_t(this._rangeReaders.some(function(i){return i._begin!==t?!1:(i._enqueue(s),!0)}),"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){t.total===void 0?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){_t(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new lg(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new cg(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}class lg{constructor(t,e,s=!1,i=null){this._stream=t,this._done=s||!1,this._filename=md(i)?i:null,this._queuedChunks=e||[],this._loaded=0;for(const r of this._queuedChunks)this._loaded+=r.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){this._done||(this._requests.length>0?this._requests.shift().resolve({value:t,done:!1}):this._queuedChunks.push(t),this._loaded+=t.byteLength)}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0)return{value:this._queuedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class cg{constructor(t,e,s){this._stream=t,this._begin=e,this._end=s,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length===0)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}function Vu(f,t){const e=new Headers;if(!f||!t||typeof t!="object")return e;for(const s in t){const i=t[s];i!==void 0&&e.append(s,i)}return e}function Hl(f){try{return new URL(f).origin}catch{}return null}function Uu({responseHeaders:f,isHttp:t,rangeChunkSize:e,disableRange:s}){const i={allowRangeRequests:!1,suggestedLength:void 0},r=parseInt(f.get("Content-Length"),10);return!Number.isInteger(r)||(i.suggestedLength=r,r<=2*e)||s||!t||f.get("Accept-Ranges")!=="bytes"||(f.get("Content-Encoding")||"identity")!=="identity"||(i.allowRangeRequests=!0),i}function Wu(f){const t=f.get("Content-Disposition");if(t){let e=function(i){let r=!0,a=o("filename\\*","i").exec(i);if(a){a=a[1];let g=c(a);return g=unescape(g),g=d(g),g=p(g),l(g)}if(a=function(m){const v=[];let y;const w=o("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;(y=w.exec(m))!==null;){let[,x,S,C]=y;if(x=parseInt(x,10),x in v){if(x===0)break}else v[x]=[S,C]}const _=[];for(let x=0;x<v.length&&x in v;++x){let[S,C]=v[x];C=c(C),S&&(C=unescape(C),x===0&&(C=d(C))),_.push(C)}return _.join("")}(i),a)return l(p(a));if(a=o("filename","i").exec(i),a){a=a[1];let g=c(a);return g=p(g),l(g)}function o(g,m){return new RegExp("(?:^|;)\\s*"+g+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',m)}function h(g,m){if(g){if(!/^[\x00-\xFF]+$/.test(m))return m;try{const v=new TextDecoder(g,{fatal:!0}),y=Nl(m);m=v.decode(y),r=!1}catch{}}return m}function l(g){return r&&/[\x80-\xff]/.test(g)&&(g=h("utf-8",g),r&&(g=h("iso-8859-1",g))),g}function c(g){if(g.startsWith('"')){const m=g.slice(1).split('\\"');for(let v=0;v<m.length;++v){const y=m[v].indexOf('"');y!==-1&&(m[v]=m[v].slice(0,y),m.length=v+1),m[v]=m[v].replaceAll(/\\(.)/g,"$1")}g=m.join('"')}return g}function d(g){const m=g.indexOf("'");return m===-1?g:h(g.slice(0,m),g.slice(m+1).replace(/^[^']*'/,""))}function p(g){return!g.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(g)?g:g.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,function(m,v,y,w){if(y==="q"||y==="Q")return h(v,w=(w=w.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,function(_,x){return String.fromCharCode(parseInt(x,16))}));try{w=atob(w)}catch{}return h(v,w)})}return""}(t);if(e.includes("%"))try{e=decodeURIComponent(e)}catch{}if(md(e))return e}return null}function $l(f,t){return f===404||f===0&&t.startsWith("file:")?new Ua('Missing PDF "'+t+'".'):new rl(`Unexpected server response (${f}) while retrieving PDF "${t}".`,f)}function qu(f){return f===200||f===206}function Xu(f,t,e){return{method:"GET",headers:f,signal:e.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}function Yu(f){return f instanceof Uint8Array?f.buffer:f instanceof ArrayBuffer?f:(U(`getArrayBuffer - unexpected data format: ${f}`),new Uint8Array(f).buffer)}class Kd{constructor(t){F(this,"_responseOrigin",null);this.source=t,this.isHttp=/^https?:/i.test(t.url),this.headers=Vu(this.isHttp,t.httpHeaders),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return _t(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new dg(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new ug(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class dg{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange;const s=new Headers(t.headers),i=e.url;fetch(i,Xu(s,this._withCredentials,this._abortController)).then(r=>{if(t._responseOrigin=Hl(r.url),!qu(r.status))throw $l(r.status,i);this._reader=r.body.getReader(),this._headersCapability.resolve();const a=r.headers,{allowRangeRequests:o,suggestedLength:h}=Uu({responseHeaders:a,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=o,this._contentLength=h||this._contentLength,this._filename=Wu(a),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new zi("Streaming is disabled."))}).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:Yu(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class ug{constructor(t,e,s){this._stream=t,this._reader=null,this._loaded=0;const i=t.source;this._withCredentials=i.withCredentials||!1,this._readCapability=Promise.withResolvers(),this._isStreamingSupported=!i.disableStream,this._abortController=new AbortController;const r=new Headers(t.headers);r.append("Range",`bytes=${e}-${s-1}`);const a=i.url;fetch(a,Xu(r,this._withCredentials,this._abortController)).then(o=>{const h=Hl(o.url);if(h!==t._responseOrigin)throw new Error(`Expected range response-origin "${h}" to match "${t._responseOrigin}".`);if(!qu(o.status))throw $l(o.status,a);this._readCapability.resolve(),this._reader=o.body.getReader()}).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded}),{value:Yu(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class pg{constructor({url:t,httpHeaders:e,withCredentials:s}){F(this,"_responseOrigin",null);this.url=t,this.isHttp=/^https?:/i.test(t),this.headers=Vu(this.isHttp,e),this.withCredentials=s||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}request(t){const e=new XMLHttpRequest,s=this.currXhrId++,i=this.pendingRequests[s]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const[r,a]of this.headers)e.setRequestHeader(r,a);return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),i.expectedStatus=206):i.expectedStatus=200,e.responseType="arraybuffer",_t(t.onError,"Expected `onError` callback to be provided."),e.onerror=()=>{t.onError(e.status)},e.onreadystatechange=this.onStateChange.bind(this,s),e.onprogress=this.onProgress.bind(this,s),i.onHeadersReceived=t.onHeadersReceived,i.onDone=t.onDone,i.onError=t.onError,i.onProgress=t.onProgress,e.send(null),s}onProgress(t,e){const s=this.pendingRequests[t];s&&s.onProgress?.(e)}onStateChange(t,e){const s=this.pendingRequests[t];if(!s)return;const i=s.xhr;if(i.readyState>=2&&s.onHeadersReceived&&(s.onHeadersReceived(),delete s.onHeadersReceived),i.readyState!==4||!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],i.status===0&&this.isHttp){s.onError(i.status);return}const r=i.status||200;if(!(r===200&&s.expectedStatus===206)&&r!==s.expectedStatus){s.onError(i.status);return}const a=function(h){const l=h.response;return typeof l!="string"?l:Nl(l).buffer}(i);if(r===206){const o=i.getResponseHeader("Content-Range"),h=/bytes (\d+)-(\d+)\/(\d+)/.exec(o);h?s.onDone({begin:parseInt(h[1],10),chunk:a}):(U('Missing or invalid "Content-Range" header.'),s.onError(0))}else a?s.onDone({begin:0,chunk:a}):s.onError(i.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class fg{constructor(t){this._source=t,this._manager=new pg(t),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return _t(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new gg(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const s=new mg(this._manager,t,e);return s.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class gg{constructor(t,e){this._manager=t,this._url=e.url,this._fullRequestId=t.request({onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._headersCapability=Promise.withResolvers(),this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t);this._manager._responseOrigin=Hl(e.responseURL);const s=e.getAllResponseHeaders(),i=new Headers(s?s.trimStart().replace(/[^\S ]+$/,"").split(/[\r\n]+/).map(o=>{const[h,...l]=o.split(": ");return[h,l.join(": ")]}):[]),{allowRangeRequests:r,suggestedLength:a}=Uu({responseHeaders:i,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});r&&(this._isRangeSupported=!0),this._contentLength=a||this._contentLength,this._filename=Wu(i),this._isRangeSupported&&this._manager.abortRequest(t),this._headersCapability.resolve()}_onDone(t){if(t&&(this._requests.length>0?this._requests.shift().resolve({value:t.chunk,done:!1}):this._cachedChunks.push(t.chunk)),this._done=!0,!(this._cachedChunks.length>0)){for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=$l(t,this._url),this._headersCapability.reject(this._storedError);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersCapability.promise}async read(){if(await this._headersCapability.promise,this._storedError)throw this._storedError;if(this._cachedChunks.length>0)return{value:this._cachedChunks.shift(),done:!1};if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0,this._headersCapability.reject(t);for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class mg{constructor(t,e,s){this._manager=t,this._url=t.url,this._requestId=t.request({begin:e,end:s,onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)}),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_onHeadersReceived(){const t=Hl(this._manager.getRequestXhr(this._requestId)?.responseURL);t!==this._manager._responseOrigin&&(this._storedError=new Error(`Expected range response-origin "${t}" to match "${this._manager._responseOrigin}".`),this._onError(0))}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;this._requests.length>0?this._requests.shift().resolve({value:e,done:!1}):this._queuedChunk=e,this._done=!0;for(const s of this._requests)s.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){this._storedError??(this._storedError=$l(t,this._url));for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(this._queuedChunk!==null){const e=this._queuedChunk;return this._queuedChunk=null,{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}const bg=/^[a-z][a-z0-9\-+.]+:/i;class Ag{constructor(t){this.source=t,this.url=function(s){if(bg.test(s))return new URL(s);const i=process.getBuiltinModule("url");return new URL(i.pathToFileURL(s))}(t.url),_t(this.url.protocol==="file:","PDFNodeStream only supports file:// URLs."),this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return _t(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=new vg(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new yg(this,t,e);return this._rangeRequestReaders.push(s),s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class vg{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=Promise.withResolvers(),this._headersCapability=Promise.withResolvers();const s=process.getBuiltinModule("fs");s.promises.lstat(this._url).then(i=>{this._contentLength=i.size,this._setReadableStream(s.createReadStream(this._url)),this._headersCapability.resolve()},i=>{i.code==="ENOENT"&&(i=new Ua(`Missing PDF "${this._url}".`)),this._storedError=i,this._headersCapability.reject(i)})}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new zi("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class yg{constructor(t,e,s){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=Promise.withResolvers();const i=t.source;this._isStreamingSupported=!i.disableStream;const r=process.getBuiltinModule("fs");this._setReadableStream(r.createReadStream(this._url,{start:e,end:s-1}))}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();return t===null?(this._readCapability=Promise.withResolvers(),this.read()):(this._loaded+=t.length,this.onProgress?.({loaded:this._loaded}),{value:new Uint8Array(t).buffer,done:!1})}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",()=>{this._readCapability.resolve()}),t.on("end",()=>{t.destroy(),this._done=!0,this._readCapability.resolve()}),t.on("error",e=>{this._error(e)}),this._storedError&&this._readableStream.destroy(this._storedError)}}const re=30;var xi,le,To,Po,vn,Os,Io,Do,yn,Or,Br,Si,Hr,Lo,$r,wn,Fo,No,_n,xn,Oo,Ei,zr,ri,Ku,Qu,Mc,Me,Bh,kc,Ju,Zu;const St=class St{constructor({textContentSource:t,container:e,viewport:s}){b(this,ri);b(this,xi,Promise.withResolvers());b(this,le,null);b(this,To,!1);b(this,Po,!!globalThis.FontInspector?.enabled);b(this,vn,null);b(this,Os,null);b(this,Io,0);b(this,Do,0);b(this,yn,null);b(this,Or,null);b(this,Br,0);b(this,Si,0);b(this,Hr,Object.create(null));b(this,Lo,[]);b(this,$r,null);b(this,wn,[]);b(this,Fo,new WeakMap);b(this,No,null);var h;if(t instanceof ReadableStream)u(this,$r,t);else{if(typeof t!="object")throw new Error('No "textContentSource" parameter specified.');u(this,$r,new ReadableStream({start(l){l.enqueue(t),l.close()}}))}u(this,le,u(this,Or,e)),u(this,Si,s.scale*(globalThis.devicePixelRatio||1)),u(this,Br,s.rotation),u(this,Os,{div:null,properties:null,ctx:null});const{pageWidth:i,pageHeight:r,pageX:a,pageY:o}=s.rawDims;u(this,No,[1,0,0,-1,-a,o+r]),u(this,Do,i),u(this,Io,r),A(h=St,Me,Ju).call(h),sr(e,s),n(this,xi).promise.finally(()=>{n(St,zr).delete(this),u(this,Os,null),u(this,Hr,null)}).catch(()=>{})}static get fontFamilyMap(){const{isWindows:t,isFirefox:e}=se.platform;return X(this,"fontFamilyMap",new Map([["sans-serif",(t&&e?"Calibri, ":"")+"sans-serif"],["monospace",(t&&e?"Lucida Console, ":"")+"monospace"]]))}render(){const t=()=>{n(this,yn).read().then(({value:e,done:s})=>{s?n(this,xi).resolve():(n(this,vn)??u(this,vn,e.lang),Object.assign(n(this,Hr),e.styles),A(this,ri,Ku).call(this,e.items),t())},n(this,xi).reject)};return u(this,yn,n(this,$r).getReader()),n(St,zr).add(this),t(),n(this,xi).promise}update({viewport:t,onBefore:e=null}){var r;const s=t.scale*(globalThis.devicePixelRatio||1),i=t.rotation;if(i!==n(this,Br)&&(e?.(),u(this,Br,i),sr(n(this,Or),{rotation:i})),s!==n(this,Si)){e?.(),u(this,Si,s);const a={div:null,properties:null,ctx:A(r=St,Me,Bh).call(r,n(this,vn))};for(const o of n(this,wn))a.properties=n(this,Fo).get(o),a.div=o,A(this,ri,Mc).call(this,a)}}cancel(){const t=new zi("TextLayer task cancelled.");n(this,yn)?.cancel(t).catch(()=>{}),u(this,yn,null),n(this,xi).reject(t)}get textDivs(){return n(this,wn)}get textContentItemsStr(){return n(this,Lo)}static cleanup(){if(!(n(this,zr).size>0)){n(this,_n).clear();for(const{canvas:t}of n(this,xn).values())t.remove();n(this,xn).clear()}}};xi=new WeakMap,le=new WeakMap,To=new WeakMap,Po=new WeakMap,vn=new WeakMap,Os=new WeakMap,Io=new WeakMap,Do=new WeakMap,yn=new WeakMap,Or=new WeakMap,Br=new WeakMap,Si=new WeakMap,Hr=new WeakMap,Lo=new WeakMap,$r=new WeakMap,wn=new WeakMap,Fo=new WeakMap,No=new WeakMap,_n=new WeakMap,xn=new WeakMap,Oo=new WeakMap,Ei=new WeakMap,zr=new WeakMap,ri=new WeakSet,Ku=function(t){var i,r;if(n(this,To))return;(r=n(this,Os)).ctx??(r.ctx=A(i=St,Me,Bh).call(i,n(this,vn)));const e=n(this,wn),s=n(this,Lo);for(const a of t){if(e.length>1e5){U("Ignoring additional textDivs for performance reasons."),u(this,To,!0);return}if(a.str!==void 0)s.push(a.str),A(this,ri,Qu).call(this,a);else if(a.type==="beginMarkedContentProps"||a.type==="beginMarkedContent"){const o=n(this,le);u(this,le,document.createElement("span")),n(this,le).classList.add("markedContent"),a.id!==null&&n(this,le).setAttribute("id",`${a.id}`),o.append(n(this,le))}else a.type==="endMarkedContent"&&u(this,le,n(this,le).parentNode)}},Qu=function(t){var v;const e=document.createElement("span"),s={angle:0,canvasWidth:0,hasText:t.str!=="",hasEOL:t.hasEOL,fontSize:0};n(this,wn).push(e);const i=D.transform(n(this,No),t.transform);let r=Math.atan2(i[1],i[0]);const a=n(this,Hr)[t.fontName];a.vertical&&(r+=Math.PI/2);let o=n(this,Po)&&a.fontSubstitution||a.fontFamily;o=St.fontFamilyMap.get(o)||o;const h=Math.hypot(i[2],i[3]),l=h*A(v=St,Me,Zu).call(v,o,n(this,vn));let c,d;r===0?(c=i[4],d=i[5]-l):(c=i[4]+l*Math.sin(r),d=i[5]-l*Math.cos(r));const p="calc(var(--scale-factor)*",g=e.style;n(this,le)===n(this,Or)?(g.left=`${(100*c/n(this,Do)).toFixed(2)}%`,g.top=`${(100*d/n(this,Io)).toFixed(2)}%`):(g.left=`${p}${c.toFixed(2)}px)`,g.top=`${p}${d.toFixed(2)}px)`),g.fontSize=`${p}${(n(St,Ei)*h).toFixed(2)}px)`,g.fontFamily=o,s.fontSize=h,e.setAttribute("role","presentation"),e.textContent=t.str,e.dir=t.dir,n(this,Po)&&(e.dataset.fontName=a.fontSubstitutionLoadedName||t.fontName),r!==0&&(s.angle=r*(180/Math.PI));let m=!1;if(t.str.length>1)m=!0;else if(t.str!==" "&&t.transform[0]!==t.transform[3]){const y=Math.abs(t.transform[0]),w=Math.abs(t.transform[3]);y!==w&&Math.max(y,w)/Math.min(y,w)>1.5&&(m=!0)}if(m&&(s.canvasWidth=a.vertical?t.height:t.width),n(this,Fo).set(e,s),n(this,Os).div=e,n(this,Os).properties=s,A(this,ri,Mc).call(this,n(this,Os)),s.hasText&&n(this,le).append(e),s.hasEOL){const y=document.createElement("br");y.setAttribute("role","presentation"),n(this,le).append(y)}},Mc=function(t){var o;const{div:e,properties:s,ctx:i}=t,{style:r}=e;let a="";if(n(St,Ei)>1&&(a=`scale(${1/n(St,Ei)})`),s.canvasWidth!==0&&s.hasText){const{fontFamily:h}=r,{canvasWidth:l,fontSize:c}=s;A(o=St,Me,kc).call(o,i,c*n(this,Si),h);const{width:d}=i.measureText(e.textContent);d>0&&(a=`scaleX(${l*n(this,Si)/d}) ${a}`)}s.angle!==0&&(a=`rotate(${s.angle}deg) ${a}`),a.length>0&&(r.transform=a)},Me=new WeakSet,Bh=function(t=null){let e=n(this,xn).get(t||(t=""));if(!e){const s=document.createElement("canvas");s.className="hiddenCanvasElement",s.lang=t,document.body.append(s),e=s.getContext("2d",{alpha:!1,willReadFrequently:!0}),n(this,xn).set(t,e),n(this,Oo).set(e,{size:0,family:""})}return e},kc=function(t,e,s){const i=n(this,Oo).get(t);(e!==i.size||s!==i.family)&&(t.font=`${e}px ${s}`,i.size=e,i.family=s)},Ju=function(){if(n(this,Ei)!==null)return;const t=document.createElement("div");t.style.opacity=0,t.style.lineHeight=1,t.style.fontSize="1px",t.style.position="absolute",t.textContent="X",document.body.append(t),u(this,Ei,t.getBoundingClientRect().height),t.remove()},Zu=function(t,e){const s=n(this,_n).get(t);if(s)return s;const i=A(this,Me,Bh).call(this,e);i.canvas.width=i.canvas.height=re,A(this,Me,kc).call(this,i,re,t);const r=i.measureText("");let a=r.fontBoundingBoxAscent,o=Math.abs(r.fontBoundingBoxDescent);if(a){const c=a/(a+o);return n(this,_n).set(t,c),i.canvas.width=i.canvas.height=0,c}i.strokeStyle="red",i.clearRect(0,0,re,re),i.strokeText("g",0,0);let h=i.getImageData(0,0,re,re).data;o=0;for(let c=h.length-1-3;c>=0;c-=4)if(h[c]>0){o=Math.ceil(c/4/re);break}i.clearRect(0,0,re,re),i.strokeText("A",0,re),h=i.getImageData(0,0,re,re).data,a=0;for(let c=0,d=h.length;c<d;c+=4)if(h[c]>0){a=re-Math.floor(c/4/re);break}i.canvas.width=i.canvas.height=0;const l=a?a/(a+o):.8;return n(this,_n).set(t,l),l},b(St,Me),b(St,_n,new Map),b(St,xn,new Map),b(St,Oo,new WeakMap),b(St,Ei,null),b(St,zr,new Set);let Wa=St;class qa{static textContent(t){const e=[],s={items:e,styles:Object.create(null)};return function i(r){if(!r)return;let a=null;const o=r.name;if(o==="#text")a=r.value;else{if(!qa.shouldBuildText(o))return;r?.attributes?.textContent?a=r.attributes.textContent:r.value&&(a=r.value)}if(a!==null&&e.push({str:a}),r.children)for(const h of r.children)i(h)}(t),s}static shouldBuildText(t){return!(t==="textarea"||t==="input"||t==="option"||t==="select")}}const wg=65536,_g=Xt?class extends Gd{_createCanvas(t,e){return process.getBuiltinModule("module").createRequire(import.meta.url)("@napi-rs/canvas").createCanvas(t,e)}}:class extends Gd{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}){super({enableHWA:e}),this._document=t}_createCanvas(t,e){const s=this._document.createElement("canvas");return s.width=t,s.height=e,s}},xg=Xt?class extends Hu{async _fetch(t){return Gu(t)}}:$u;var Sn,jr,Bs,Hs,Ut,En,Cn,R,jt,Ia,hr,Hh,lr,tp,Rc,cr,Da,La,Tc,Fa,tu;const Sg=Xt?class extends Vd{}:(tu=class extends Vd{constructor({docId:e,ownerDocument:s=globalThis.document}){super();b(this,R);b(this,Sn);b(this,jr);b(this,Bs);b(this,Hs);b(this,Ut);b(this,En);b(this,Cn,0);u(this,Hs,e),u(this,Ut,s)}addFilter(e){if(!e)return"none";let s=n(this,R,jt).get(e);if(s)return s;const[i,r,a]=A(this,R,Hh).call(this,e),o=e.length===1?i:`${i}${r}${a}`;if(s=n(this,R,jt).get(o),s)return n(this,R,jt).set(e,s),s;const h=`g_${n(this,Hs)}_transfer_map_${Kt(this,Cn)._++}`,l=A(this,R,lr).call(this,h);n(this,R,jt).set(e,l),n(this,R,jt).set(o,l);const c=A(this,R,cr).call(this,h);return A(this,R,La).call(this,i,r,a,c),l}addHCMFilter(e,s){const i=`${e}-${s}`,r="base";let a=n(this,R,Ia).get(r);if(a?.key===i||(a?(a.filter?.remove(),a.key=i,a.url="none",a.filter=null):(a={key:i,url:"none",filter:null},n(this,R,Ia).set(r,a)),!e||!s))return a.url;const o=A(this,R,Fa).call(this,e);e=D.makeHexColor(...o);const h=A(this,R,Fa).call(this,s);if(s=D.makeHexColor(...h),n(this,R,hr).style.color="",e==="#000000"&&s==="#ffffff"||e===s)return a.url;const l=new Array(256);for(let m=0;m<=255;m++){const v=m/255;l[m]=v<=.03928?v/12.92:((v+.055)/1.055)**2.4}const c=l.join(","),d=`g_${n(this,Hs)}_hcm_filter`,p=a.filter=A(this,R,cr).call(this,d);A(this,R,La).call(this,c,c,c,p),A(this,R,Rc).call(this,p);const g=(m,v)=>{const y=o[m]/255,w=h[m]/255,_=new Array(v+1);for(let x=0;x<=v;x++)_[x]=y+x/v*(w-y);return _.join(",")};return A(this,R,La).call(this,g(0,5),g(1,5),g(2,5),p),a.url=A(this,R,lr).call(this,d),a.url}addAlphaFilter(e){let s=n(this,R,jt).get(e);if(s)return s;const[i]=A(this,R,Hh).call(this,[e]),r=`alpha_${i}`;if(s=n(this,R,jt).get(r),s)return n(this,R,jt).set(e,s),s;const a=`g_${n(this,Hs)}_alpha_map_${Kt(this,Cn)._++}`,o=A(this,R,lr).call(this,a);n(this,R,jt).set(e,o),n(this,R,jt).set(r,o);const h=A(this,R,cr).call(this,a);return A(this,R,Tc).call(this,i,h),o}addLuminosityFilter(e){let s,i,r=n(this,R,jt).get(e||"luminosity");if(r)return r;if(e?([s]=A(this,R,Hh).call(this,[e]),i=`luminosity_${s}`):i="luminosity",r=n(this,R,jt).get(i),r)return n(this,R,jt).set(e,r),r;const a=`g_${n(this,Hs)}_luminosity_map_${Kt(this,Cn)._++}`,o=A(this,R,lr).call(this,a);n(this,R,jt).set(e,o),n(this,R,jt).set(i,o);const h=A(this,R,cr).call(this,a);return A(this,R,tp).call(this,h),e&&A(this,R,Tc).call(this,s,h),o}addHighlightHCMFilter(e,s,i,r,a){const o=`${s}-${i}-${r}-${a}`;let h=n(this,R,Ia).get(e);if(h?.key===o||(h?(h.filter?.remove(),h.key=o,h.url="none",h.filter=null):(h={key:o,url:"none",filter:null},n(this,R,Ia).set(e,h)),!s||!i))return h.url;const[l,c]=[s,i].map(A(this,R,Fa).bind(this));let d=Math.round(.2126*l[0]+.7152*l[1]+.0722*l[2]),p=Math.round(.2126*c[0]+.7152*c[1]+.0722*c[2]),[g,m]=[r,a].map(A(this,R,Fa).bind(this));p<d&&([d,p,g,m]=[p,d,m,g]),n(this,R,hr).style.color="";const v=(_,x,S)=>{const C=new Array(256),k=(p-d)/S,E=_/255,L=(x-_)/(255*S);let O=0;for(let B=0;B<=S;B++){const G=Math.round(d+B*k),nt=E+B*L;for(let ut=O;ut<=G;ut++)C[ut]=nt;O=G+1}for(let B=O;B<256;B++)C[B]=C[O-1];return C.join(",")},y=`g_${n(this,Hs)}_hcm_${e}_filter`,w=h.filter=A(this,R,cr).call(this,y);return A(this,R,Rc).call(this,w),A(this,R,La).call(this,v(g[0],m[0],5),v(g[1],m[1],5),v(g[2],m[2],5),w),h.url=A(this,R,lr).call(this,y),h.url}destroy(e=!1){(!e||!n(this,En)?.size)&&(n(this,Bs)?.parentNode.parentNode.remove(),u(this,Bs,null),n(this,jr)?.clear(),u(this,jr,null),n(this,En)?.clear(),u(this,En,null),u(this,Cn,0))}},Sn=new WeakMap,jr=new WeakMap,Bs=new WeakMap,Hs=new WeakMap,Ut=new WeakMap,En=new WeakMap,Cn=new WeakMap,R=new WeakSet,jt=function(){return n(this,jr)||u(this,jr,new Map)},Ia=function(){return n(this,En)||u(this,En,new Map)},hr=function(){if(!n(this,Bs)){const e=n(this,Ut).createElement("div"),{style:s}=e;s.visibility="hidden",s.contain="strict",s.width=s.height=0,s.position="absolute",s.top=s.left=0,s.zIndex=-1;const i=n(this,Ut).createElementNS(Es,"svg");i.setAttribute("width",0),i.setAttribute("height",0),u(this,Bs,n(this,Ut).createElementNS(Es,"defs")),e.append(i),i.append(n(this,Bs)),n(this,Ut).body.append(e)}return n(this,Bs)},Hh=function(e){if(e.length===1){const l=e[0],c=new Array(256);for(let p=0;p<256;p++)c[p]=l[p]/255;const d=c.join(",");return[d,d,d]}const[s,i,r]=e,a=new Array(256),o=new Array(256),h=new Array(256);for(let l=0;l<256;l++)a[l]=s[l]/255,o[l]=i[l]/255,h[l]=r[l]/255;return[a.join(","),o.join(","),h.join(",")]},lr=function(e){if(n(this,Sn)===void 0){u(this,Sn,"");const s=n(this,Ut).URL;s!==n(this,Ut).baseURI&&(Bl(s)?U('#createUrl: ignore "data:"-URL for performance reasons.'):u(this,Sn,s.split("#",1)[0]))}return`url(${n(this,Sn)}#${e})`},tp=function(e){const s=n(this,Ut).createElementNS(Es,"feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0"),e.append(s)},Rc=function(e){const s=n(this,Ut).createElementNS(Es,"feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),e.append(s)},cr=function(e){const s=n(this,Ut).createElementNS(Es,"filter");return s.setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("id",e),n(this,R,hr).append(s),s},Da=function(e,s,i){const r=n(this,Ut).createElementNS(Es,s);r.setAttribute("type","discrete"),r.setAttribute("tableValues",i),e.append(r)},La=function(e,s,i,r){const a=n(this,Ut).createElementNS(Es,"feComponentTransfer");r.append(a),A(this,R,Da).call(this,a,"feFuncR",e),A(this,R,Da).call(this,a,"feFuncG",s),A(this,R,Da).call(this,a,"feFuncB",i)},Tc=function(e,s){const i=n(this,Ut).createElementNS(Es,"feComponentTransfer");s.append(i),A(this,R,Da).call(this,i,"feFuncA",e)},Fa=function(e){return n(this,R,hr).style.color=e,tc(getComputedStyle(n(this,R,hr)).getPropertyValue("color"))},tu),Eg=Xt?class extends zu{async _fetch(t){return Gu(t)}}:ju;function Cg(f={}){typeof f=="string"||f instanceof URL?f={url:f}:(f instanceof ArrayBuffer||ArrayBuffer.isView(f))&&(f={data:f});const t=new Pc,{docId:e}=t,s=f.url?function(rt){if(rt instanceof URL)return rt.href;try{return new URL(rt,window.location).href}catch{if(Xt&&typeof rt=="string")return rt}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}(f.url):null,i=f.data?function(rt){if(Xt&&typeof Buffer<"u"&&rt instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(rt instanceof Uint8Array&&rt.byteLength===rt.buffer.byteLength)return rt;if(typeof rt=="string")return Nl(rt);if(rt instanceof ArrayBuffer||ArrayBuffer.isView(rt)||typeof rt=="object"&&!isNaN(rt?.length))return new Uint8Array(rt);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}(f.data):null,r=f.httpHeaders||null,a=f.withCredentials===!0,o=f.password??null,h=f.range instanceof ep?f.range:null,l=Number.isInteger(f.rangeChunkSize)&&f.rangeChunkSize>0?f.rangeChunkSize:wg;let c=f.worker instanceof br?f.worker:null;const d=f.verbosity,p=typeof f.docBaseUrl!="string"||Bl(f.docBaseUrl)?null:f.docBaseUrl,g=typeof f.cMapUrl=="string"?f.cMapUrl:null,m=f.cMapPacked!==!1,v=f.CMapReaderFactory||xg,y=typeof f.standardFontDataUrl=="string"?f.standardFontDataUrl:null,w=f.StandardFontDataFactory||Eg,_=f.stopAtErrors!==!0,x=Number.isInteger(f.maxImageSize)&&f.maxImageSize>-1?f.maxImageSize:-1,S=f.isEvalSupported!==!1,C=typeof f.isOffscreenCanvasSupported=="boolean"?f.isOffscreenCanvasSupported:!Xt,k=typeof f.isImageDecoderSupported=="boolean"?f.isImageDecoderSupported:!Xt&&(se.platform.isFirefox||!globalThis.chrome),E=Number.isInteger(f.canvasMaxAreaInBytes)?f.canvasMaxAreaInBytes:-1,L=typeof f.disableFontFace=="boolean"?f.disableFontFace:Xt,O=f.fontExtraProperties===!0,B=f.enableXfa===!0,G=f.ownerDocument||globalThis.document,nt=f.disableRange===!0,ut=f.disableStream===!0,K=f.disableAutoFetch===!0,mt=f.pdfBug===!0,kt=f.CanvasFactory||_g,$=f.FilterFactory||Sg,z=f.enableHWA===!0,ke=h?h.length:f.length??NaN,xs=typeof f.useSystemFonts=="boolean"?f.useSystemFonts:!Xt&&!L,Ss=typeof f.useWorkerFetch=="boolean"?f.useWorkerFetch:v===$u&&w===ju&&g&&y&&Ma(g,document.baseURI)&&Ma(y,document.baseURI);Lf(d);const Yt={canvasFactory:new kt({ownerDocument:G,enableHWA:z}),filterFactory:new $({docId:e,ownerDocument:G}),cMapReaderFactory:Ss?null:new v({baseUrl:g,isCompressed:m}),standardFontDataFactory:Ss?null:new w({baseUrl:y})};if(!c){const ue={verbosity:d,port:ei.workerPort};c=ue.port?br.fromPort(ue):new br(ue),t._worker=c}const Ft={docId:e,apiVersion:"4.10.38",data:i,password:o,disableAutoFetch:K,rangeChunkSize:l,length:ke,docBaseUrl:p,enableXfa:B,evaluatorOptions:{maxImageSize:x,disableFontFace:L,ignoreErrors:_,isEvalSupported:S,isOffscreenCanvasSupported:C,isImageDecoderSupported:k,canvasMaxAreaInBytes:E,fontExtraProperties:O,useSystemFonts:xs,cMapUrl:Ss?g:null,standardFontDataUrl:Ss?y:null}},ai={disableFontFace:L,fontExtraProperties:O,ownerDocument:G,pdfBug:mt,styleElement:null,loadingParams:{disableAutoFetch:K,enableXfa:B}};return c.promise.then(function(){if(t.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const ue=c.messageHandler.sendWithPromise("GetDocRequest",Ft,i?[i.buffer]:null);let rt;if(h)rt=new hg(h,{disableRange:nt,disableStream:ut});else if(!i){if(!s)throw new Error("getDocument - no `url` parameter provided.");let At;if(Xt)if(Ma(s)){if(typeof fetch>"u"||typeof Response>"u"||!("body"in Response.prototype))throw new Error("getDocument - the Fetch API was disabled in Node.js, see `--no-experimental-fetch`.");At=Kd}else At=Ag;else At=Ma(s)?Kd:fg;rt=new At({url:s,length:ke,httpHeaders:r,withCredentials:a,rangeChunkSize:l,disableRange:nt,disableStream:ut})}return ue.then(At=>{if(t.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const kd=new Pa(e,At,c.port),Jp=new Tg(kd,t,rt,ai,Yt);t._transport=Jp,kd.send("Ready",null)})}).catch(t._capability.reject),t}function Qd(f){return typeof f=="object"&&Number.isInteger(f?.num)&&f.num>=0&&Number.isInteger(f?.gen)&&f.gen>=0}var _l;const xl=class xl{constructor(){this._capability=Promise.withResolvers(),this._transport=null,this._worker=null,this.docId="d"+Kt(xl,_l)._++,this.destroyed=!1,this.onPassword=null,this.onProgress=null}get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0),await this._transport?.destroy()}catch(t){throw this._worker?.port&&delete this._worker._pendingDestroy,t}this._transport=null,this._worker?.destroy(),this._worker=null}};_l=new WeakMap,b(xl,_l,0);let Pc=xl;class ep{constructor(t,e,s=!1,i=null){this.length=t,this.initialData=e,this.progressiveDone=s,this.contentDispositionFilename=i,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=Promise.withResolvers()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const s of this._rangeListeners)s(t,e)}onDataProgress(t,e){this._readyCapability.promise.then(()=>{for(const s of this._progressListeners)s(t,e)})}onDataProgressiveRead(t){this._readyCapability.promise.then(()=>{for(const e of this._progressiveReadListeners)e(t)})}onDataProgressiveDone(){this._readyCapability.promise.then(()=>{for(const t of this._progressiveDoneListeners)t()})}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){at("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class Mg{constructor(t,e){this._pdfInfo=t,this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get canvasFactory(){return this._transport.canvasFactory}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return X(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}var Ci,$s,Oe,dr,$h;class kg{constructor(t,e,s,i=!1){b(this,Oe);b(this,Ci,null);b(this,$s,!1);this._pageIndex=t,this._pageInfo=e,this._transport=s,this._stats=i?new Dd:null,this._pdfBug=i,this.commonObjs=s.commonObjs,this.objs=new sp,this._maybeCleanupAfterRender=!1,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:s=0,offsetY:i=0,dontFlip:r=!1}={}){return new yh({viewBox:this.view,userUnit:this.userUnit,scale:t,rotation:e,offsetX:s,offsetY:i,dontFlip:r})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return X(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:s="display",annotationMode:i=li.ENABLE,transform:r=null,background:a=null,optionalContentConfigPromise:o=null,annotationCanvasMap:h=null,pageColors:l=null,printAnnotationStorage:c=null,isEditing:d=!1}){this._stats?.time("Overall");const p=this._transport.getRenderingIntent(s,i,c,d),{renderingIntent:g,cacheKey:m}=p;u(this,$s,!1),A(this,Oe,$h).call(this),o||(o=this._transport.getOptionalContentConfig(g));let v=this._intentStates.get(m);v||(v=Object.create(null),this._intentStates.set(m,v)),v.streamReaderCancelTimeout&&(clearTimeout(v.streamReaderCancelTimeout),v.streamReaderCancelTimeout=null);const y=!!(g&nl);v.displayReadyCapability||(v.displayReadyCapability=Promise.withResolvers(),v.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(p));const w=S=>{v.renderTasks.delete(_),(this._maybeCleanupAfterRender||y)&&u(this,$s,!0),A(this,Oe,dr).call(this,!y),S?(_.capability.reject(S),this._abortOperatorList({intentState:v,reason:S instanceof Error?S:new Error(S)})):_.capability.resolve(),this._stats&&(this._stats.timeEnd("Rendering"),this._stats.timeEnd("Overall"),globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats))},_=new Dc({callback:w,params:{canvasContext:t,viewport:e,transform:r,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:h,operatorList:v.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!y,pdfBug:this._pdfBug,pageColors:l});(v.renderTasks||(v.renderTasks=new Set)).add(_);const x=_.task;return Promise.all([v.displayReadyCapability.promise,o]).then(([S,C])=>{if(this.destroyed)w();else{if(this._stats?.time("Rendering"),!(C.renderingIntent&g))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");_.initializeGraphics({transparency:S,optionalContentConfig:C}),_.operatorListChanged()}}).catch(w),x}getOperatorList({intent:t="display",annotationMode:e=li.ENABLE,printAnnotationStorage:s=null,isEditing:i=!1}={}){const r=this._transport.getRenderingIntent(t,e,s,i,!0);let a,o=this._intentStates.get(r.cacheKey);return o||(o=Object.create(null),this._intentStates.set(r.cacheKey,o)),o.opListReadCapability||(a=Object.create(null),a.operatorListChanged=function(){o.operatorList.lastChunk&&(o.opListReadCapability.resolve(o.operatorList),o.renderTasks.delete(a))},o.opListReadCapability=Promise.withResolvers(),(o.renderTasks||(o.renderTasks=new Set)).add(a),o.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(r)),o.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:t===!0,disableNormalization:e===!0},{highWaterMark:100,size:s=>s.items.length})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then(s=>qa.textContent(s));const e=this.streamTextContent(t);return new Promise(function(s,i){const r=e.getReader(),a={items:[],styles:Object.create(null),lang:null};(function o(){r.read().then(function({value:h,done:l}){l?s(a):(a.lang??(a.lang=h.lang),Object.assign(a.styles,h.styles),a.items.push(...h.items),o())},i)})()})}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const s of e.renderTasks)t.push(s.completed),s.cancel();return this.objs.clear(),u(this,$s,!1),A(this,Oe,$h).call(this),Promise.all(t)}cleanup(t=!1){u(this,$s,!0);const e=A(this,Oe,dr).call(this,!1);return t&&e&&this._stats&&(this._stats=new Dd),e}_startRenderPage(t,e){const s=this._intentStates.get(e);s&&(this._stats?.timeEnd("Page Request"),s.displayReadyCapability?.resolve(t))}_renderPageChunk(t,e){for(let s=0,i=t.length;s<i;s++)e.operatorList.fnArray.push(t.fnArray[s]),e.operatorList.argsArray.push(t.argsArray[s]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const s of e.renderTasks)s.operatorListChanged();t.lastChunk&&A(this,Oe,dr).call(this,!0)}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:s,modifiedIds:i}){const{map:r,transfer:a}=s,o=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:r,modifiedIds:i},a).getReader(),h=this._intentStates.get(e);h.streamReader=o;const l=()=>{o.read().then(({value:c,done:d})=>{d?h.streamReader=null:this._transport.destroyed||(this._renderPageChunk(c,h),l())},c=>{if(h.streamReader=null,!this._transport.destroyed){if(h.operatorList){h.operatorList.lastChunk=!0;for(const d of h.renderTasks)d.operatorListChanged();A(this,Oe,dr).call(this,!0)}if(h.displayReadyCapability)h.displayReadyCapability.reject(c);else{if(!h.opListReadCapability)throw c;h.opListReadCapability.reject(c)}}})};l()}_abortOperatorList({intentState:t,reason:e,force:s=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout&&(clearTimeout(t.streamReaderCancelTimeout),t.streamReaderCancelTimeout=null),!s){if(t.renderTasks.size>0)return;if(e instanceof gd){let i=100;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay),t.streamReaderCancelTimeout=setTimeout(()=>{t.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:t,reason:e,force:!0})},i);return}}if(t.streamReader.cancel(new zi(e.message)).catch(()=>{}),t.streamReader=null,!this._transport.destroyed){for(const[i,r]of this._intentStates)if(r===t){this._intentStates.delete(i);break}this.cleanup()}}}get stats(){return this._stats}}Ci=new WeakMap,$s=new WeakMap,Oe=new WeakSet,dr=function(t=!1){if(A(this,Oe,$h).call(this),!n(this,$s)||this.destroyed)return!1;if(t)return u(this,Ci,setTimeout(()=>{u(this,Ci,null),A(this,Oe,dr).call(this,!1)},5e3)),!1;for(const{renderTasks:e,operatorList:s}of this._intentStates.values())if(e.size>0||!s.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),u(this,$s,!1),!0},$h=function(){n(this,Ci)&&(clearTimeout(n(this,Ci)),u(this,Ci,null))};var zs,Sl;class Rg{constructor(){b(this,zs,new Map);b(this,Sl,Promise.resolve())}postMessage(t,e){const s={data:structuredClone(t,e?{transfer:e}:null)};n(this,Sl).then(()=>{for(const[i]of n(this,zs))i.call(this,s)})}addEventListener(t,e,s=null){let i=null;if(s?.signal instanceof AbortSignal){const{signal:r}=s;if(r.aborted){U("LoopbackPort - cannot use an `aborted` signal.");return}const a=()=>this.removeEventListener(t,e);i=()=>r.removeEventListener("abort",a),r.addEventListener("abort",a)}n(this,zs).set(e,i)}removeEventListener(t,e){n(this,zs).get(e)?.(),n(this,zs).delete(e)}terminate(){for(const[,t]of n(this,zs))t?.();n(this,zs).clear()}}zs=new WeakMap,Sl=new WeakMap;var El,Mn,kn,Gr,zh,Vr,jh;const lt=class lt{constructor({name:t=null,port:e=null,verbosity:s=Ff()}={}){b(this,Gr);if(this.name=t,this.destroyed=!1,this.verbosity=s,this._readyCapability=Promise.withResolvers(),this._port=null,this._webWorker=null,this._messageHandler=null,e){if(n(lt,kn)?.has(e))throw new Error("Cannot use more than one PDFWorker per port.");(n(lt,kn)||u(lt,kn,new WeakMap)).set(e,this),this._initializeFromPort(e)}else this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t,this._messageHandler=new Pa("main","worker",t),this._messageHandler.on("ready",function(){}),A(this,Gr,zh).call(this)}_initialize(){if(n(lt,Mn)||n(lt,Vr,jh)){this._setupFakeWorker();return}let{workerSrc:t}=lt;try{lt._isSameOrigin(window.location.href,t)||(t=lt._createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),s=new Pa("main","worker",e),i=()=>{r.abort(),s.destroy(),e.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},r=new AbortController;e.addEventListener("error",()=>{this._webWorker||i()},{signal:r.signal}),s.on("test",o=>{r.abort(),!this.destroyed&&o?(this._messageHandler=s,this._port=e,this._webWorker=e,A(this,Gr,zh).call(this)):i()}),s.on("ready",o=>{if(r.abort(),this.destroyed)i();else try{a()}catch{this._setupFakeWorker()}});const a=()=>{const o=new Uint8Array;s.send("test",o,[o.buffer])};a();return}catch{Fl("The worker has been disabled.")}this._setupFakeWorker()}_setupFakeWorker(){n(lt,Mn)||(U("Setting up fake worker."),u(lt,Mn,!0)),lt._setupFakeWorkerGlobal.then(t=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const e=new Rg;this._port=e;const s="fake"+Kt(lt,El)._++,i=new Pa(s+"_worker",s,e);t.setup(i,e),this._messageHandler=new Pa(s,s+"_worker",e),A(this,Gr,zh).call(this)}).catch(t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))})}destroy(){this.destroyed=!0,this._webWorker?.terminate(),this._webWorker=null,n(lt,kn)?.delete(this._port),this._port=null,this._messageHandler?.destroy(),this._messageHandler=null}static fromPort(t){if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");const e=n(this,kn)?.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new lt(t)}static get workerSrc(){if(ei.workerSrc)return ei.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _setupFakeWorkerGlobal(){return X(this,"_setupFakeWorkerGlobal",(async()=>n(this,Vr,jh)?n(this,Vr,jh):(await import(this.workerSrc)).WorkerMessageHandler)())}};El=new WeakMap,Mn=new WeakMap,kn=new WeakMap,Gr=new WeakSet,zh=function(){this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})},Vr=new WeakSet,jh=function(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}},b(lt,Vr),b(lt,El,0),b(lt,Mn,!1),b(lt,kn),Xt&&(u(lt,Mn,!0),ei.workerSrc||(ei.workerSrc="./pdf.worker.mjs")),lt._isSameOrigin=(t,e)=>{let s;try{if(s=new URL(t),!s.origin||s.origin==="null")return!1}catch{return!1}const i=new URL(e,s);return s.origin===i.origin},lt._createCDNWrapper=t=>{const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))};let br=lt;var js,fs,Ur,Wr,Gs,Rn,Na;class Tg{constructor(t,e,s,i,r){b(this,Rn);b(this,js,new Map);b(this,fs,new Map);b(this,Ur,new Map);b(this,Wr,new Map);b(this,Gs,null);this.messageHandler=t,this.loadingTask=e,this.commonObjs=new sp,this.fontLoader=new Wf({ownerDocument:i.ownerDocument,styleElement:i.styleElement}),this.loadingParams=i.loadingParams,this._params=i,this.canvasFactory=r.canvasFactory,this.filterFactory=r.filterFactory,this.cMapReaderFactory=r.cMapReaderFactory,this.standardFontDataFactory=r.standardFontDataFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=s,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=Promise.withResolvers(),this.setupMessageHandler()}get annotationStorage(){return X(this,"annotationStorage",new Ad)}getRenderingIntent(t,e=li.ENABLE,s=null,i=!1,r=!1){let a=ud,o=yc;switch(t){case"any":a=ef;break;case"display":break;case"print":a=nl;break;default:U(`getRenderingIntent - invalid intent: ${t}`)}const h=a&nl&&s instanceof Fu?s:this.annotationStorage;switch(e){case li.DISABLE:a+=rf;break;case li.ENABLE:break;case li.ENABLE_FORMS:a+=sf;break;case li.ENABLE_STORAGE:a+=nf,o=h.serializable;break;default:U(`getRenderingIntent - invalid annotationMode: ${e}`)}i&&(a+=af),r&&(a+=of);const{ids:l,hash:c}=h.modifiedIds;return{renderingIntent:a,cacheKey:[a,o.hash,c].join("_"),annotationStorageSerializable:o,modifiedIds:l}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=Promise.withResolvers(),n(this,Gs)?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const s of n(this,fs).values())t.push(s._destroy());n(this,fs).clear(),n(this,Ur).clear(),n(this,Wr).clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then(()=>{this.commonObjs.clear(),this.fontLoader.clear(),n(this,js).clear(),this.filterFactory.destroy(),Wa.cleanup(),this._networkStream?.cancelAllRequests(new zi("Worker was terminated.")),this.messageHandler?.destroy(),this.messageHandler=null,this.destroyCapability.resolve()},this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",(s,i)=>{_t(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=r=>{this._lastProgress={loaded:r.loaded,total:r.total}},i.onPull=()=>{this._fullReader.read().then(function({value:r,done:a}){a?i.close():(_t(r instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(r),1,[r]))}).catch(r=>{i.error(r)})},i.onCancel=r=>{this._fullReader.cancel(r),i.ready.catch(a=>{if(!this.destroyed)throw a})}}),t.on("ReaderHeadersReady",async s=>{await this._fullReader.headersReady;const{isStreamingSupported:i,isRangeSupported:r,contentLength:a}=this._fullReader;return(!i||!r)&&(this._lastProgress&&e.onProgress?.(this._lastProgress),this._fullReader.onProgress=o=>{e.onProgress?.({loaded:o.loaded,total:o.total})}),{isStreamingSupported:i,isRangeSupported:r,contentLength:a}}),t.on("GetRangeReader",(s,i)=>{_t(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const r=this._networkStream.getRangeReader(s.begin,s.end);r?(i.onPull=()=>{r.read().then(function({value:a,done:o}){o?i.close():(_t(a instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer."),i.enqueue(new Uint8Array(a),1,[a]))}).catch(a=>{i.error(a)})},i.onCancel=a=>{r.cancel(a),i.ready.catch(o=>{if(!this.destroyed)throw o})}):i.close()}),t.on("GetDoc",({pdfInfo:s})=>{this._numPages=s.numPages,this._htmlForXfa=s.htmlForXfa,delete s.htmlForXfa,e._capability.resolve(new Mg(s,this))}),t.on("DocException",s=>{e._capability.reject(ae(s))}),t.on("PasswordRequest",s=>{u(this,Gs,Promise.withResolvers());try{if(!e.onPassword)throw ae(s);const i=r=>{r instanceof Error?n(this,Gs).reject(r):n(this,Gs).resolve({password:r})};e.onPassword(i,s.code)}catch(i){n(this,Gs).reject(i)}return n(this,Gs).promise}),t.on("DataLoaded",s=>{e.onProgress?.({loaded:s.length,total:s.length}),this.downloadInfoCapability.resolve(s)}),t.on("StartRenderPage",s=>{this.destroyed||n(this,fs).get(s.pageIndex)._startRenderPage(s.transparency,s.cacheKey)}),t.on("commonobj",([s,i,r])=>{if(this.destroyed||this.commonObjs.has(s))return null;switch(i){case"Font":const{disableFontFace:a,fontExtraProperties:o,pdfBug:h}=this._params;if("error"in r){const p=r.error;U(`Error during font loading: ${p}`),this.commonObjs.resolve(s,p);break}const l=h&&globalThis.FontInspector?.enabled?(p,g)=>globalThis.FontInspector.fontAdded(p,g):null,c=new qf(r,{disableFontFace:a,fontExtraProperties:o,inspectFont:l});this.fontLoader.bind(c).catch(()=>t.sendWithPromise("FontFallback",{id:s})).finally(()=>{!o&&c.data&&(c.data=null),this.commonObjs.resolve(s,c)});break;case"CopyLocalImage":const{imageRef:d}=r;_t(d,"The imageRef must be defined.");for(const p of n(this,fs).values())for(const[,g]of p.objs)if(g?.ref===d)return g.dataLen?(this.commonObjs.resolve(s,structuredClone(g)),g.dataLen):null;break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(s,r);break;default:throw new Error(`Got unknown common object type ${i}`)}return null}),t.on("obj",([s,i,r,a])=>{if(this.destroyed)return;const o=n(this,fs).get(i);if(!o.objs.has(s))if(o._intentStates.size!==0)switch(r){case"Image":o.objs.resolve(s,a),a?.dataLen>1e7&&(o._maybeCleanupAfterRender=!0);break;case"Pattern":o.objs.resolve(s,a);break;default:throw new Error(`Got unknown object type ${r}`)}else a?.bitmap?.close()}),t.on("DocProgress",s=>{this.destroyed||e.onProgress?.({loaded:s.loaded,total:s.total})}),t.on("FetchBuiltInCMap",async s=>{if(this.destroyed)throw new Error("Worker was destroyed.");if(!this.cMapReaderFactory)throw new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter.");return this.cMapReaderFactory.fetch(s)}),t.on("FetchStandardFontData",async s=>{if(this.destroyed)throw new Error("Worker was destroyed.");if(!this.standardFontDataFactory)throw new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter.");return this.standardFontDataFactory.fetch(s)})}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&U("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally(()=>{this.annotationStorage.resetModified()})}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,s=n(this,Ur).get(e);if(s)return s;const i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then(r=>{if(this.destroyed)throw new Error("Transport destroyed");r.refStr&&n(this,Wr).set(r.refStr,t);const a=new kg(e,r,this,this._params.pdfBug);return n(this,fs).set(e,a),a});return n(this,Ur).set(e,i),i}getPageIndex(t){return Qd(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return A(this,Rn,Na).call(this,"GetFieldObjects")}hasJSActions(){return A(this,Rn,Na).call(this,"HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return typeof t!="string"?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return A(this,Rn,Na).call(this,"GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return A(this,Rn,Na).call(this,"GetOptionalContentConfig").then(e=>new og(e,t))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=n(this,js).get(t);if(e)return e;const s=this.messageHandler.sendWithPromise(t,null).then(i=>({info:i[0],metadata:i[1]?new rg(i[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null}));return n(this,js).set(t,s),s}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const e of n(this,fs).values())if(!e.cleanup())throw new Error(`startCleanup: Page ${e.pageNumber} is currently rendering.`);this.commonObjs.clear(),t||this.fontLoader.clear(),n(this,js).clear(),this.filterFactory.destroy(!0),Wa.cleanup()}}cachedPageNumber(t){if(!Qd(t))return null;const e=t.gen===0?`${t.num}R`:`${t.num}R${t.gen}`;return n(this,Wr).get(e)??null}}js=new WeakMap,fs=new WeakMap,Ur=new WeakMap,Wr=new WeakMap,Gs=new WeakMap,Rn=new WeakSet,Na=function(t,e=null){const s=n(this,js).get(t);if(s)return s;const i=this.messageHandler.sendWithPromise(t,e);return n(this,js).set(t,i),i};const Ea=Symbol("INITIAL_DATA");var me,Bo,Ic;class sp{constructor(){b(this,Bo);b(this,me,Object.create(null))}get(t,e=null){if(e){const i=A(this,Bo,Ic).call(this,t);return i.promise.then(()=>e(i.data)),null}const s=n(this,me)[t];if(!s||s.data===Ea)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return s.data}has(t){const e=n(this,me)[t];return!!e&&e.data!==Ea}delete(t){const e=n(this,me)[t];return!e||e.data===Ea?!1:(delete n(this,me)[t],!0)}resolve(t,e=null){const s=A(this,Bo,Ic).call(this,t);s.data=e,s.resolve()}clear(){for(const t in n(this,me)){const{data:e}=n(this,me)[t];e?.bitmap?.close()}u(this,me,Object.create(null))}*[Symbol.iterator](){for(const t in n(this,me)){const{data:e}=n(this,me)[t];e!==Ea&&(yield[t,e])}}}me=new WeakMap,Bo=new WeakSet,Ic=function(t){var e;return(e=n(this,me))[t]||(e[t]={...Promise.withResolvers(),data:Ea})};var Mi;class Pg{constructor(t){b(this,Mi,null);u(this,Mi,t),this.onContinue=null}get promise(){return n(this,Mi).capability.promise}cancel(t=0){n(this,Mi).cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=n(this,Mi).operatorList;if(!t)return!1;const{annotationCanvasMap:e}=n(this,Mi);return t.form||t.canvas&&e?.size>0}}Mi=new WeakMap;var ki,Tn;const Yi=class Yi{constructor({callback:t,params:e,objs:s,commonObjs:i,annotationCanvasMap:r,operatorList:a,pageIndex:o,canvasFactory:h,filterFactory:l,useRequestAnimationFrame:c=!1,pdfBug:d=!1,pageColors:p=null}){b(this,ki,null);this.callback=t,this.params=e,this.objs=s,this.commonObjs=i,this.annotationCanvasMap=r,this.operatorListIdx=null,this.operatorList=a,this._pageIndex=o,this.canvasFactory=h,this.filterFactory=l,this._pdfBug=d,this.pageColors=p,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=c===!0&&typeof window<"u",this.cancelled=!1,this.capability=Promise.withResolvers(),this.task=new Pg(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch(function(){})}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(n(Yi,Tn).has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");n(Yi,Tn).add(this._canvas)}this._pdfBug&&globalThis.StepperManager?.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:s,viewport:i,transform:r,background:a}=this.params;this.gfx=new mr(s,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:r,viewport:i,transparency:t,background:a}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1,this.cancelled=!0,this.gfx?.endDrawing(),n(this,ki)&&(window.cancelAnimationFrame(n(this,ki)),u(this,ki,null)),n(Yi,Tn).delete(this._canvas),this.callback(t||new gd(`Rendering cancelled, page ${this._pageIndex+1}`,e))}operatorListChanged(){this.graphicsReady?(this.stepper?.updateOperatorList(this.operatorList),this.running||this._continue()):this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound)}_continue(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?u(this,ki,window.requestAnimationFrame(()=>{u(this,ki,null),this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),n(Yi,Tn).delete(this._canvas),this.callback())))}};ki=new WeakMap,Tn=new WeakMap,b(Yi,Tn,new WeakSet);let Dc=Yi;const Ig="4.10.38",Dg="f9bea397f";function Jd(f){return Math.floor(255*Math.max(0,Math.min(1,f))).toString(16).padStart(2,"0")}function Ca(f){return Math.max(0,Math.min(255,255*f))}class Zd{static CMYK_G([t,e,s,i]){return["G",1-Math.min(1,.3*t+.59*s+.11*e+i)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return[t=Ca(t),t,t]}static G_HTML([t]){const e=Jd(t);return`#${e}${e}${e}`}static RGB_G([t,e,s]){return["G",.3*t+.59*e+.11*s]}static RGB_rgb(t){return t.map(Ca)}static RGB_HTML(t){return`#${t.map(Jd).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,s,i]){return["RGB",1-Math.min(1,t+i),1-Math.min(1,s+i),1-Math.min(1,e+i)]}static CMYK_rgb([t,e,s,i]){return[Ca(1-Math.min(1,t+i)),Ca(1-Math.min(1,s+i)),Ca(1-Math.min(1,e+i))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,s]){const i=1-t,r=1-e,a=1-s;return["CMYK",i,r,a,Math.min(i,r,a)]}}class Lg{create(t,e,s=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const i=this._createSVG("svg:svg");return i.setAttribute("version","1.1"),s||(i.setAttribute("width",`${t}px`),i.setAttribute("height",`${e}px`)),i.setAttribute("preserveAspectRatio","none"),i.setAttribute("viewBox",`0 0 ${t} ${e}`),i}createElement(t){if(typeof t!="string")throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){at("Abstract method `_createSVG` called.")}}class yd extends Lg{_createSVG(t){return document.createElementNS(Es,t)}}class ip{static setupStorage(t,e,s,i,r){const a=i.getValue(e,{value:null});switch(s.name){case"textarea":if(a.value!==null&&(t.textContent=a.value),r==="print")break;t.addEventListener("input",o=>{i.setValue(e,{value:o.target.value})});break;case"input":if(s.attributes.type==="radio"||s.attributes.type==="checkbox"){if(a.value===s.attributes.xfaOn?t.setAttribute("checked",!0):a.value===s.attributes.xfaOff&&t.removeAttribute("checked"),r==="print")break;t.addEventListener("change",o=>{i.setValue(e,{value:o.target.checked?o.target.getAttribute("xfaOn"):o.target.getAttribute("xfaOff")})})}else{if(a.value!==null&&t.setAttribute("value",a.value),r==="print")break;t.addEventListener("input",o=>{i.setValue(e,{value:o.target.value})})}break;case"select":if(a.value!==null){t.setAttribute("value",a.value);for(const o of s.children)o.attributes.value===a.value?o.attributes.selected=!0:o.attributes.hasOwnProperty("selected")&&delete o.attributes.selected}t.addEventListener("input",o=>{const h=o.target.options,l=h.selectedIndex===-1?"":h[h.selectedIndex].value;i.setValue(e,{value:l})})}}static setAttributes({html:t,element:e,storage:s=null,intent:i,linkService:r}){const{attributes:a}=e,o=t instanceof HTMLAnchorElement;a.type==="radio"&&(a.name=`${a.name}-${i}`);for(const[h,l]of Object.entries(a))if(l!=null)switch(h){case"class":l.length&&t.setAttribute(h,l.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",l);break;case"style":Object.assign(t.style,l);break;case"textContent":t.textContent=l;break;default:(!o||h!=="href"&&h!=="newWindow")&&t.setAttribute(h,l)}o&&r.addLinkAttributes(t,a.href,a.newWindow),s&&a.dataId&&this.setupStorage(t,a.dataId,e,s)}static render(t){const e=t.annotationStorage,s=t.linkService,i=t.xfaHtml,r=t.intent||"display",a=document.createElement(i.name);i.attributes&&this.setAttributes({html:a,element:i,intent:r,linkService:s});const o=r!=="richText",h=t.div;if(h.append(a),t.viewport){const d=`matrix(${t.viewport.transform.join(",")})`;h.style.transform=d}o&&h.setAttribute("class","xfaLayer xfaFont");const l=[];if(i.children.length===0){if(i.value){const d=document.createTextNode(i.value);a.append(d),o&&qa.shouldBuildText(i.name)&&l.push(d)}return{textDivs:l}}const c=[[i,-1,a]];for(;c.length>0;){const[d,p,g]=c.at(-1);if(p+1===d.children.length){c.pop();continue}const m=d.children[++c.at(-1)[1]];if(m===null)continue;const{name:v}=m;if(v==="#text"){const w=document.createTextNode(m.value);l.push(w),g.append(w);continue}const y=m?.attributes?.xmlns?document.createElementNS(m.attributes.xmlns,v):document.createElement(v);if(g.append(y),m.attributes&&this.setAttributes({html:y,element:m,storage:e,intent:r,linkService:s}),m.children?.length>0)c.push([m,-1,y]);else if(m.value){const w=document.createTextNode(m.value);o&&qa.shouldBuildText(v)&&l.push(w),y.append(w)}}for(const d of h.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))d.setAttribute("readOnly",!0);return{textDivs:l}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}const _h=1e3,nr=new WeakSet;function $i(f){return{width:f[2]-f[0],height:f[3]-f[1]}}class Fg{static create(t){switch(t.data.annotationType){case uf:return new np(t);case df:return new Ng(t);case Mf:switch(t.data.fieldType){case"Tx":return new Og(t);case"Btn":return t.data.radioButton?new op(t):t.data.checkBox?new Hg(t):new $g(t);case"Ch":return new zg(t);case"Sig":return new Bg(t)}return new ar(t);case su:return new Fc(t);case pf:return new up(t);case ff:return new Gg(t);case gf:return new Vg(t);case mf:return new Ug(t);case Af:return new pp(t);case Sf:return new qg(t);case Ef:return new wd(t);case bf:return new Wg(t);case vf:return new fp(t);case yf:return new Xg(t);case wf:return new Yg(t);case _f:return new Kg(t);case xf:return new gp(t);case Cf:return new Qg(t);default:return new wt(t)}}}var Pn,qr,Xr,Ho,Lc;const Ed=class Ed{constructor(t,{isRenderable:e=!1,ignoreBorder:s=!1,createQuadrilaterals:i=!1}={}){b(this,Ho);b(this,Pn,null);b(this,qr,!1);b(this,Xr,null);this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(s)),i&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:s}){return!!(t?.str||e?.str||s?.str)}get _isEditable(){return this.data.isEditable}get hasPopupData(){return Ed._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;n(this,Pn)||u(this,Pn,{rect:this.data.rect.slice(0)});const{rect:e}=t;e&&A(this,Ho,Lc).call(this,e),n(this,Xr)?.popup.updateEdited(t)}resetEdited(){n(this,Pn)&&(A(this,Ho,Lc).call(this,n(this,Pn).rect),n(this,Xr)?.popup.resetEdited(),u(this,Pn,null))}_createContainer(t){const{data:e,parent:{page:s,viewport:i}}=this,r=document.createElement("section");r.setAttribute("data-annotation-id",e.id),this instanceof ar||(r.tabIndex=_h);const{style:a}=r;if(a.zIndex=this.parent.zIndex++,e.alternativeText&&(r.title=e.alternativeText),e.noRotate&&r.classList.add("norotate"),!e.rect||this instanceof Fc){const{rotation:v}=e;return e.hasOwnCanvas||v===0||this.setRotation(v,r),r}const{width:o,height:h}=$i(e.rect);if(!t&&e.borderStyle.width>0){a.borderWidth=`${e.borderStyle.width}px`;const v=e.borderStyle.horizontalCornerRadius,y=e.borderStyle.verticalCornerRadius;if(v>0||y>0){const _=`calc(${v}px * var(--scale-factor)) / calc(${y}px * var(--scale-factor))`;a.borderRadius=_}else if(this instanceof op){const _=`calc(${o}px * var(--scale-factor)) / calc(${h}px * var(--scale-factor))`;a.borderRadius=_}switch(e.borderStyle.style){case kf:a.borderStyle="solid";break;case Rf:a.borderStyle="dashed";break;case Tf:U("Unimplemented border style: beveled");break;case Pf:U("Unimplemented border style: inset");break;case If:a.borderBottomStyle="solid"}const w=e.borderColor||null;w?(u(this,qr,!0),a.borderColor=D.makeHexColor(0|w[0],0|w[1],0|w[2])):a.borderWidth=0}const l=D.normalizeRect([e.rect[0],s.view[3]-e.rect[1]+s.view[1],e.rect[2],s.view[3]-e.rect[3]+s.view[1]]),{pageWidth:c,pageHeight:d,pageX:p,pageY:g}=i.rawDims;a.left=100*(l[0]-p)/c+"%",a.top=100*(l[1]-g)/d+"%";const{rotation:m}=e;return e.hasOwnCanvas||m===0?(a.width=100*o/c+"%",a.height=100*h/d+"%"):this.setRotation(m,r),r}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:s,pageHeight:i}=this.parent.viewport.rawDims,{width:r,height:a}=$i(this.data.rect);let o,h;t%180==0?(o=100*r/s,h=100*a/i):(o=100*a/s,h=100*r/i),e.style.width=`${o}%`,e.style.height=`${h}%`,e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(e,s,i)=>{const r=i.detail[e],a=r[0],o=r.slice(1);i.target.style[s]=Zd[`${a}_HTML`](o),this.annotationStorage.setValue(this.data.id,{[s]:Zd[`${a}_rgb`](o)})};return X(this,"_commonActions",{display:e=>{const{display:s}=e.detail,i=s%2==1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:s===1||s===2})},print:e=>{this.annotationStorage.setValue(this.data.id,{noPrint:!e.detail.print})},hidden:e=>{const{hidden:s}=e.detail;this.container.style.visibility=s?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:s,noView:s})},focus:e=>{setTimeout(()=>e.target.focus({preventScroll:!1}),0)},userName:e=>{e.target.title=e.detail.userName},readonly:e=>{e.target.disabled=e.detail.readonly},required:e=>{this._setRequired(e.target,e.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:e=>{const s=e.detail.rotation;this.setRotation(s),this.annotationStorage.setValue(this.data.id,{rotation:s})}})}_dispatchEventFromSandbox(t,e){const s=this._commonActions;for(const i of Object.keys(e.detail))(t[i]||s[i])?.(e)}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const s=this._commonActions;for(const[i,r]of Object.entries(e)){const a=s[i];a&&(a({detail:{[i]:r},target:t}),delete e[i])}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,s,i,r]=this.data.rect.map(v=>Math.fround(v));if(t.length===8){const[v,y,w,_]=t.subarray(2,6);if(i===v&&r===y&&e===w&&s===_)return}const{style:a}=this.container;let o;if(n(this,qr)){const{borderColor:v,borderWidth:y}=a;a.borderWidth=0,o=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${v}" stroke-width="${y}">`],this.container.classList.add("hasBorder")}const h=i-e,l=r-s,{svgFactory:c}=this,d=c.createElement("svg");d.classList.add("quadrilateralsContainer"),d.setAttribute("width",0),d.setAttribute("height",0);const p=c.createElement("defs");d.append(p);const g=c.createElement("clipPath"),m=`clippath_${this.data.id}`;g.setAttribute("id",m),g.setAttribute("clipPathUnits","objectBoundingBox"),p.append(g);for(let v=2,y=t.length;v<y;v+=8){const w=t[v],_=t[v+1],x=t[v+2],S=t[v+3],C=c.createElement("rect"),k=(x-e)/h,E=(r-_)/l,L=(w-x)/h,O=(_-S)/l;C.setAttribute("x",k),C.setAttribute("y",E),C.setAttribute("width",L),C.setAttribute("height",O),g.append(C),o?.push(`<rect vector-effect="non-scaling-stroke" x="${k}" y="${E}" width="${L}" height="${O}"/>`)}n(this,qr)&&(o.push("</g></svg>')"),a.backgroundImage=o.join("")),this.container.append(d),this.container.style.clipPath=`url(#${m})`}_createPopup(){const{data:t}=this,e=u(this,Xr,new Fc({data:{color:t.color,titleObj:t.titleObj,modificationDate:t.modificationDate,contentsObj:t.contentsObj,richText:t.richText,parentRect:t.rect,borderStyle:0,id:`popup_${t.id}`,rotation:t.rotation},parent:this.parent,elements:[this]}));this.parent.div.append(e.render())}render(){at("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const s=[];if(this._fieldObjects){const i=this._fieldObjects[t];if(i)for(const{page:r,id:a,exportValues:o}of i){if(r===-1||a===e)continue;const h=typeof o=="string"?o:null,l=document.querySelector(`[data-element-id="${a}"]`);!l||nr.has(l)?s.push({id:a,exportValue:h,domElement:l}):U(`_getElementsByName - element not allowed: ${a}`)}return s}for(const i of document.getElementsByName(t)){const{exportValue:r}=i,a=i.getAttribute("data-element-id");a!==e&&nr.has(i)&&s.push({id:a,exportValue:r,domElement:i})}return s}show(){this.container&&(this.container.hidden=!1),this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0),this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})})}};Pn=new WeakMap,qr=new WeakMap,Xr=new WeakMap,Ho=new WeakSet,Lc=function(t){const{container:{style:e},data:{rect:s,rotation:i},parent:{viewport:{rawDims:{pageWidth:r,pageHeight:a,pageX:o,pageY:h}}}}=this;s?.splice(0,4,...t);const{width:l,height:c}=$i(t);e.left=100*(t[0]-o)/r+"%",e.top=100*(a-t[3]+h)/a+"%",i===0?(e.width=100*l/r+"%",e.height=100*c/a+"%"):this.setRotation(i)};let wt=Ed;var Ee,Ui,rp,ap;class np extends wt{constructor(e,s=null){super(e,{isRenderable:!0,ignoreBorder:!!s?.ignoreBorder,createQuadrilaterals:!0});b(this,Ee);this.isTooltipOnly=e.data.isTooltipOnly}render(){const{data:e,linkService:s}=this,i=document.createElement("a");i.setAttribute("data-element-id",e.id);let r=!1;return e.url?(s.addLinkAttributes(i,e.url,e.newWindow),r=!0):e.action?(this._bindNamedAction(i,e.action),r=!0):e.attachment?(A(this,Ee,rp).call(this,i,e.attachment,e.attachmentDest),r=!0):e.setOCGState?(A(this,Ee,ap).call(this,i,e.setOCGState),r=!0):e.dest?(this._bindLink(i,e.dest),r=!0):(e.actions&&(e.actions.Action||e.actions["Mouse Up"]||e.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(i,e),r=!0),e.resetForm?(this._bindResetFormAction(i,e.resetForm),r=!0):this.isTooltipOnly&&!r&&(this._bindLink(i,""),r=!0)),this.container.classList.add("linkAnnotation"),r&&this.container.append(i),this.container}_bindLink(e,s){e.href=this.linkService.getDestinationHash(s),e.onclick=()=>(s&&this.linkService.goToDestination(s),!1),(s||s==="")&&A(this,Ee,Ui).call(this)}_bindNamedAction(e,s){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>(this.linkService.executeNamedAction(s),!1),A(this,Ee,Ui).call(this)}_bindJSAction(e,s){e.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const r of Object.keys(s.actions)){const a=i.get(r);a&&(e[a]=()=>(this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:s.id,name:r}}),!1))}e.onclick||(e.onclick=()=>!1),A(this,Ee,Ui).call(this)}_bindResetFormAction(e,s){const i=e.onclick;i||(e.href=this.linkService.getAnchorUrl("")),A(this,Ee,Ui).call(this),this._fieldObjects?e.onclick=()=>{i?.();const{fields:r,refs:a,include:o}=s,h=[];if(r.length!==0||a.length!==0){const d=new Set(a);for(const p of r){const g=this._fieldObjects[p]||[];for(const{id:m}of g)d.add(m)}for(const p of Object.values(this._fieldObjects))for(const g of p)d.has(g.id)===o&&h.push(g)}else for(const d of Object.values(this._fieldObjects))h.push(...d);const l=this.annotationStorage,c=[];for(const d of h){const{id:p}=d;switch(c.push(p),d.type){case"text":{const m=d.defaultValue||"";l.setValue(p,{value:m});break}case"checkbox":case"radiobutton":{const m=d.defaultValue===d.exportValues;l.setValue(p,{value:m});break}case"combobox":case"listbox":{const m=d.defaultValue||"";l.setValue(p,{value:m});break}default:continue}const g=document.querySelector(`[data-element-id="${p}"]`);g&&(nr.has(g)?g.dispatchEvent(new Event("resetform")):U(`_bindResetFormAction - element not allowed: ${p}`))}return this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:c,name:"ResetForm"}}),!1}:(U('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),i||(e.onclick=()=>!1))}}Ee=new WeakSet,Ui=function(){this.container.setAttribute("data-internal-link","")},rp=function(e,s,i=null){e.href=this.linkService.getAnchorUrl(""),s.description&&(e.title=s.description),e.onclick=()=>(this.downloadManager?.openOrDownloadData(s.content,s.filename,i),!1),A(this,Ee,Ui).call(this)},ap=function(e,s){e.href=this.linkService.getAnchorUrl(""),e.onclick=()=>(this.linkService.executeSetOCGState(s),!1),A(this,Ee,Ui).call(this)};class Ng extends wt{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.setAttribute("data-l10n-id","pdfjs-text-annotation-type"),t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name})),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class ar extends wt{render(){return this.container}showElementAndHideCanvas(t){this.data.hasOwnCanvas&&(t.previousSibling?.nodeName==="CANVAS"&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){return se.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,s,i,r){s.includes("mouse")?t.addEventListener(s,a=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:r(a),shift:a.shiftKey,modifier:this._getKeyModifier(a)}})}):t.addEventListener(s,a=>{if(s==="blur"){if(!e.focused||!a.relatedTarget)return;e.focused=!1}else if(s==="focus"){if(e.focused)return;e.focused=!0}r&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:r(a)}})})}_setEventListeners(t,e,s,i){for(const[r,a]of s)(a==="Action"||this.data.actions?.[a])&&(a!=="Focus"&&a!=="Blur"||e||(e={focused:!1}),this._setEventListener(t,e,r,a,i),a!=="Focus"||this.data.actions?.Blur?a!=="Blur"||this.data.actions?.Focus||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null))}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=e===null?"transparent":D.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:s}=this.data.defaultAppearanceData,i=this.data.defaultAppearanceData.fontSize||9,r=t.style;let a;const o=h=>Math.round(10*h)/10;if(this.data.multiLine){const h=Math.abs(this.data.rect[3]-this.data.rect[1]-2),l=h/(Math.round(h/(jl*i))||1);a=Math.min(i,o(l/jl))}else{const h=Math.abs(this.data.rect[3]-this.data.rect[1]-2);a=Math.min(i,o(h/jl))}r.fontSize=`calc(${a}px * var(--scale-factor))`,r.color=D.makeHexColor(s[0],s[1],s[2]),this.data.textAlignment!==null&&(r.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class Og extends ar{constructor(t){super(t,{isRenderable:t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,s,i){const r=this.annotationStorage;for(const a of this._getElementsByName(t.name,t.id))a.domElement&&(a.domElement[e]=s),r.setValue(a.id,{[i]:s})}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let s=null;if(this.renderForms){const i=t.getValue(e,{value:this.data.fieldValue});let r=i.value||"";const a=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;a&&r.length>a&&(r=r.slice(0,a));let o=i.formattedValue||this.data.textContent?.join(`
`)||null;o&&this.data.comb&&(o=o.replaceAll(/\s+/g,""));const h={userValue:r,formattedValue:o,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(s=document.createElement("textarea"),s.textContent=o??r,this.data.doNotScroll&&(s.style.overflowY="hidden")):(s=document.createElement("input"),s.type="text",s.setAttribute("value",o??r),this.data.doNotScroll&&(s.style.overflowX="hidden")),this.data.hasOwnCanvas&&(s.hidden=!0),nr.add(s),s.setAttribute("data-element-id",e),s.disabled=this.data.readOnly,s.name=this.data.fieldName,s.tabIndex=_h,this._setRequired(s,this.data.required),a&&(s.maxLength=a),s.addEventListener("input",c=>{t.setValue(e,{value:c.target.value}),this.setPropertyOnSiblings(s,"value",c.target.value,"value"),h.formattedValue=null}),s.addEventListener("resetform",c=>{const d=this.data.defaultFieldValue??"";s.value=h.userValue=d,h.formattedValue=null});let l=c=>{const{formattedValue:d}=h;d!=null&&(c.target.value=d),c.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){s.addEventListener("focus",d=>{if(h.focused)return;const{target:p}=d;h.userValue&&(p.value=h.userValue),h.lastCommittedValue=p.value,h.commitKey=1,this.data.actions?.Focus||(h.focused=!0)}),s.addEventListener("updatefromsandbox",d=>{this.showElementAndHideCanvas(d.target);const p={value(g){h.userValue=g.detail.value??"",t.setValue(e,{value:h.userValue.toString()}),g.target.value=h.userValue},formattedValue(g){const{formattedValue:m}=g.detail;h.formattedValue=m,m!=null&&g.target!==document.activeElement&&(g.target.value=m),t.setValue(e,{formattedValue:m})},selRange(g){g.target.setSelectionRange(...g.detail.selRange)},charLimit:g=>{const{charLimit:m}=g.detail,{target:v}=g;if(m===0){v.removeAttribute("maxLength");return}v.setAttribute("maxLength",m);let y=h.userValue;y&&!(y.length<=m)&&(y=y.slice(0,m),v.value=h.userValue=y,t.setValue(e,{value:y}),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:y,willCommit:!0,commitKey:1,selStart:v.selectionStart,selEnd:v.selectionEnd}}))}};this._dispatchEventFromSandbox(p,d)}),s.addEventListener("keydown",d=>{h.commitKey=1;let p=-1;if(d.key==="Escape"?p=0:d.key!=="Enter"||this.data.multiLine?d.key==="Tab"&&(h.commitKey=3):p=2,p===-1)return;const{value:g}=d.target;h.lastCommittedValue!==g&&(h.lastCommittedValue=g,h.userValue=g,this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:g,willCommit:!0,commitKey:p,selStart:d.target.selectionStart,selEnd:d.target.selectionEnd}}))});const c=l;l=null,s.addEventListener("blur",d=>{if(!h.focused||!d.relatedTarget)return;this.data.actions?.Blur||(h.focused=!1);const{value:p}=d.target;h.userValue=p,h.lastCommittedValue!==p&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:p,willCommit:!0,commitKey:h.commitKey,selStart:d.target.selectionStart,selEnd:d.target.selectionEnd}}),c(d)}),this.data.actions?.Keystroke&&s.addEventListener("beforeinput",d=>{h.lastCommittedValue=null;const{data:p,target:g}=d,{value:m,selectionStart:v,selectionEnd:y}=g;let w=v,_=y;switch(d.inputType){case"deleteWordBackward":{const x=m.substring(0,v).match(/\w*[^\w]*$/);x&&(w-=x[0].length);break}case"deleteWordForward":{const x=m.substring(v).match(/^[^\w]*\w*/);x&&(_+=x[0].length);break}case"deleteContentBackward":v===y&&(w-=1);break;case"deleteContentForward":v===y&&(_+=1)}d.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:m,change:p||"",willCommit:!1,selStart:w,selEnd:_}})}),this._setEventListeners(s,h,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],d=>d.target.value)}if(l&&s.addEventListener("blur",l),this.data.comb){const c=(this.data.rect[2]-this.data.rect[0])/a;s.classList.add("comb"),s.style.letterSpacing=`calc(${c}px * var(--scale-factor) - 1ch)`}}else s=document.createElement("div"),s.textContent=this.data.fieldValue,s.style.verticalAlign="middle",s.style.display="table-cell",this.data.hasOwnCanvas&&(s.hidden=!0);return this._setTextStyle(s),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class Bg extends ar{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class Hg extends ar{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.exportValue===e.fieldValue}).value;typeof i=="string"&&(i=i!=="Off",t.setValue(s,{value:i})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const r=document.createElement("input");return nr.add(r),r.setAttribute("data-element-id",s),r.disabled=e.readOnly,this._setRequired(r,this.data.required),r.type="checkbox",r.name=e.fieldName,i&&r.setAttribute("checked",!0),r.setAttribute("exportValue",e.exportValue),r.tabIndex=_h,r.addEventListener("change",a=>{const{name:o,checked:h}=a.target;for(const l of this._getElementsByName(o,s)){const c=h&&l.exportValue===e.exportValue;l.domElement&&(l.domElement.checked=c),t.setValue(l.id,{value:c})}t.setValue(s,{value:h})}),r.addEventListener("resetform",a=>{const o=e.defaultFieldValue||"Off";a.target.checked=o===e.exportValue}),this.enableScripting&&this.hasJSActions&&(r.addEventListener("updatefromsandbox",a=>{const o={value(h){h.target.checked=h.detail.value!=="Off",t.setValue(s,{value:h.target.checked})}};this._dispatchEventFromSandbox(o,a)}),this._setEventListeners(r,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],a=>a.target.checked)),this._setBackgroundColor(r),this._setDefaultPropertiesFromJS(r),this.container.append(r),this.container}}class op extends ar{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,s=e.id;let i=t.getValue(s,{value:e.fieldValue===e.buttonValue}).value;if(typeof i=="string"&&(i=i!==e.buttonValue,t.setValue(s,{value:i})),i)for(const a of this._getElementsByName(e.fieldName,s))t.setValue(a.id,{value:!1});const r=document.createElement("input");if(nr.add(r),r.setAttribute("data-element-id",s),r.disabled=e.readOnly,this._setRequired(r,this.data.required),r.type="radio",r.name=e.fieldName,i&&r.setAttribute("checked",!0),r.tabIndex=_h,r.addEventListener("change",a=>{const{name:o,checked:h}=a.target;for(const l of this._getElementsByName(o,s))t.setValue(l.id,{value:!1});t.setValue(s,{value:h})}),r.addEventListener("resetform",a=>{const o=e.defaultFieldValue;a.target.checked=o!=null&&o===e.buttonValue}),this.enableScripting&&this.hasJSActions){const a=e.buttonValue;r.addEventListener("updatefromsandbox",o=>{const h={value:l=>{const c=a===l.detail.value;for(const d of this._getElementsByName(l.target.name)){const p=c&&d.id===s;d.domElement&&(d.domElement.checked=p),t.setValue(d.id,{value:p})}}};this._dispatchEventFromSandbox(h,o)}),this._setEventListeners(r,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],o=>o.target.checked)}return this._setBackgroundColor(r),this._setDefaultPropertiesFromJS(r),this.container.append(r),this.container}}class $g extends np{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",s=>{this._dispatchEventFromSandbox({},s)})),t}}class zg extends ar{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,s=t.getValue(e,{value:this.data.fieldValue}),i=document.createElement("select");nr.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,this._setRequired(i,this.data.required),i.name=this.data.fieldName,i.tabIndex=_h;let r=this.data.combo&&this.data.options.length>0;this.data.combo||(i.size=this.data.options.length,this.data.multiSelect&&(i.multiple=!0)),i.addEventListener("resetform",c=>{const d=this.data.defaultFieldValue;for(const p of i.options)p.selected=p.value===d});for(const c of this.data.options){const d=document.createElement("option");d.textContent=c.displayValue,d.value=c.exportValue,s.value.includes(c.exportValue)&&(d.setAttribute("selected",!0),r=!1),i.append(d)}let a=null;if(r){const c=document.createElement("option");c.value=" ",c.setAttribute("hidden",!0),c.setAttribute("selected",!0),i.prepend(c),a=()=>{c.remove(),i.removeEventListener("input",a),a=null},i.addEventListener("input",a)}const o=c=>{const d=c?"value":"textContent",{options:p,multiple:g}=i;return g?Array.prototype.filter.call(p,m=>m.selected).map(m=>m[d]):p.selectedIndex===-1?null:p[p.selectedIndex][d]};let h=o(!1);const l=c=>{const d=c.target.options;return Array.prototype.map.call(d,p=>({displayValue:p.textContent,exportValue:p.value}))};return this.enableScripting&&this.hasJSActions?(i.addEventListener("updatefromsandbox",c=>{const d={value(p){a?.();const g=p.detail.value,m=new Set(Array.isArray(g)?g:[g]);for(const v of i.options)v.selected=m.has(v.value);t.setValue(e,{value:o(!0)}),h=o(!1)},multipleSelection(p){i.multiple=!0},remove(p){const g=i.options,m=p.detail.remove;g[m].selected=!1,i.remove(m),g.length>0&&Array.prototype.findIndex.call(g,v=>v.selected)===-1&&(g[0].selected=!0),t.setValue(e,{value:o(!0),items:l(p)}),h=o(!1)},clear(p){for(;i.length!==0;)i.remove(0);t.setValue(e,{value:null,items:[]}),h=o(!1)},insert(p){const{index:g,displayValue:m,exportValue:v}=p.detail.insert,y=i.children[g],w=document.createElement("option");w.textContent=m,w.value=v,y?y.before(w):i.append(w),t.setValue(e,{value:o(!0),items:l(p)}),h=o(!1)},items(p){const{items:g}=p.detail;for(;i.length!==0;)i.remove(0);for(const m of g){const{displayValue:v,exportValue:y}=m,w=document.createElement("option");w.textContent=v,w.value=y,i.append(w)}i.options.length>0&&(i.options[0].selected=!0),t.setValue(e,{value:o(!0),items:l(p)}),h=o(!1)},indices(p){const g=new Set(p.detail.indices);for(const m of p.target.options)m.selected=g.has(m.index);t.setValue(e,{value:o(!0)}),h=o(!1)},editable(p){p.target.disabled=!p.detail.editable}};this._dispatchEventFromSandbox(d,c)}),i.addEventListener("input",c=>{const d=o(!0),p=o(!1);t.setValue(e,{value:d}),c.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:h,change:p,changeEx:d,willCommit:!1,commitKey:1,keyDown:!1}})}),this._setEventListeners(i,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],c=>c.target.value)):i.addEventListener("input",function(c){t.setValue(e,{value:o(!0)})}),this.data.combo&&this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class Fc extends wt{constructor(t){const{data:e,elements:s}=t;super(t,{isRenderable:wt._hasPopupData(e)}),this.elements=s,this.popup=null}render(){this.container.classList.add("popupAnnotation");const t=this.popup=new jg({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const s of this.elements)s.popup=t,s.container.ariaHasPopup="dialog",e.push(s.data.id),s.addHighlightArea();return this.container.setAttribute("aria-controls",e.map(s=>`${fd}${s}`).join(",")),this.container}}var Yr,Cl,Ml,Kr,In,bt,Vs,Qr,$o,zo,Jr,Us,We,Ws,jo,qs,Go,Dn,Ln,it,Gh,Nc,hp,lp,cp,dp,Vh,Uh,Oc;class jg{constructor({container:t,color:e,elements:s,titleObj:i,modificationDate:r,contentsObj:a,richText:o,parent:h,rect:l,parentRect:c,open:d}){b(this,it);b(this,Yr,A(this,it,cp).bind(this));b(this,Cl,A(this,it,Oc).bind(this));b(this,Ml,A(this,it,Uh).bind(this));b(this,Kr,A(this,it,Vh).bind(this));b(this,In,null);b(this,bt,null);b(this,Vs,null);b(this,Qr,null);b(this,$o,null);b(this,zo,null);b(this,Jr,null);b(this,Us,!1);b(this,We,null);b(this,Ws,null);b(this,jo,null);b(this,qs,null);b(this,Go,null);b(this,Dn,null);b(this,Ln,!1);u(this,bt,t),u(this,Go,i),u(this,Vs,a),u(this,qs,o),u(this,zo,h),u(this,In,e),u(this,jo,l),u(this,Jr,c),u(this,$o,s),u(this,Qr,bd.toDateObject(r)),this.trigger=s.flatMap(p=>p.getElementsToTriggerPopup());for(const p of this.trigger)p.addEventListener("click",n(this,Kr)),p.addEventListener("mouseenter",n(this,Ml)),p.addEventListener("mouseleave",n(this,Cl)),p.classList.add("popupTriggerArea");for(const p of s)p.container?.addEventListener("keydown",n(this,Yr));n(this,bt).hidden=!0,d&&A(this,it,Vh).call(this)}render(){if(n(this,We))return;const t=u(this,We,document.createElement("div"));if(t.className="popup",n(this,In)){const r=t.style.outlineColor=D.makeHexColor(...n(this,In));CSS.supports("background-color","color-mix(in srgb, red 30%, white)")?t.style.backgroundColor=`color-mix(in srgb, ${r} 30%, white)`:t.style.backgroundColor=D.makeHexColor(...n(this,In).map(o=>Math.floor(.7*(255-o)+o)))}const e=document.createElement("span");e.className="header";const s=document.createElement("h1");if(e.append(s),{dir:s.dir,str:s.textContent}=n(this,Go),t.append(e),n(this,Qr)){const r=document.createElement("span");r.classList.add("popupDate"),r.setAttribute("data-l10n-id","pdfjs-annotation-date-time-string"),r.setAttribute("data-l10n-args",JSON.stringify({dateObj:n(this,Qr).valueOf()})),e.append(r)}const i=n(this,it,Gh);if(i)ip.render({xfaHtml:i,intent:"richText",div:t}),t.lastChild.classList.add("richText","popupContent");else{const r=this._formatContents(n(this,Vs));t.append(r)}n(this,bt).append(t)}_formatContents({str:t,dir:e}){const s=document.createElement("p");s.classList.add("popupContent"),s.dir=e;const i=t.split(/(?:\r\n?|\n)/);for(let r=0,a=i.length;r<a;++r){const o=i[r];s.append(document.createTextNode(o)),r<a-1&&s.append(document.createElement("br"))}return s}updateEdited({rect:t,popupContent:e}){n(this,Dn)||u(this,Dn,{contentsObj:n(this,Vs),richText:n(this,qs)}),t&&u(this,Ws,null),e&&(u(this,qs,A(this,it,lp).call(this,e)),u(this,Vs,null)),n(this,We)?.remove(),u(this,We,null)}resetEdited(){n(this,Dn)&&({contentsObj:Kt(this,Vs)._,richText:Kt(this,qs)._}=n(this,Dn),u(this,Dn,null),n(this,We)?.remove(),u(this,We,null),u(this,Ws,null))}forceHide(){u(this,Ln,this.isVisible),n(this,Ln)&&(n(this,bt).hidden=!0)}maybeShow(){n(this,Ln)&&(n(this,We)||A(this,it,Uh).call(this),u(this,Ln,!1),n(this,bt).hidden=!1)}get isVisible(){return n(this,bt).hidden===!1}}Yr=new WeakMap,Cl=new WeakMap,Ml=new WeakMap,Kr=new WeakMap,In=new WeakMap,bt=new WeakMap,Vs=new WeakMap,Qr=new WeakMap,$o=new WeakMap,zo=new WeakMap,Jr=new WeakMap,Us=new WeakMap,We=new WeakMap,Ws=new WeakMap,jo=new WeakMap,qs=new WeakMap,Go=new WeakMap,Dn=new WeakMap,Ln=new WeakMap,it=new WeakSet,Gh=function(){const t=n(this,qs),e=n(this,Vs);return!t?.str||e?.str&&e.str!==t.str?null:n(this,qs).html||null},Nc=function(){return n(this,it,Gh)?.attributes?.style?.fontSize||0},hp=function(){return n(this,it,Gh)?.attributes?.style?.color||null},lp=function(t){const e=[],s={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},i={style:{color:n(this,it,hp),fontSize:n(this,it,Nc)?`calc(${n(this,it,Nc)}px * var(--scale-factor))`:""}};for(const r of t.split(`
`))e.push({name:"span",value:r,attributes:i});return s},cp=function(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||(t.key==="Enter"||t.key==="Escape"&&n(this,Us))&&A(this,it,Vh).call(this)},dp=function(){if(n(this,Ws)!==null)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:s,pageX:i,pageY:r}}}=n(this,zo);let a=!!n(this,Jr),o=a?n(this,Jr):n(this,jo);for(const g of n(this,$o))if(!o||D.intersect(g.data.rect,o)!==null){o=g.data.rect,a=!0;break}const h=D.normalizeRect([o[0],t[3]-o[1]+t[1],o[2],t[3]-o[3]+t[1]]),l=a?o[2]-o[0]+5:0,c=h[0]+l,d=h[1];u(this,Ws,[100*(c-i)/e,100*(d-r)/s]);const{style:p}=n(this,bt);p.left=`${n(this,Ws)[0]}%`,p.top=`${n(this,Ws)[1]}%`},Vh=function(){u(this,Us,!n(this,Us)),n(this,Us)?(A(this,it,Uh).call(this),n(this,bt).addEventListener("click",n(this,Kr)),n(this,bt).addEventListener("keydown",n(this,Yr))):(A(this,it,Oc).call(this),n(this,bt).removeEventListener("click",n(this,Kr)),n(this,bt).removeEventListener("keydown",n(this,Yr)))},Uh=function(){n(this,We)||this.render(),this.isVisible?n(this,Us)&&n(this,bt).classList.add("focused"):(A(this,it,dp).call(this),n(this,bt).hidden=!1,n(this,bt).style.zIndex=parseInt(n(this,bt).style.zIndex)+1e3)},Oc=function(){n(this,bt).classList.remove("focused"),!n(this,Us)&&this.isVisible&&(n(this,bt).hidden=!0,n(this,bt).style.zIndex=parseInt(n(this,bt).style.zIndex)-1e3)};class up extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=V.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent"),t.setAttribute("role","comment");for(const e of this.textContent){const s=document.createElement("span");s.textContent=e,t.append(s)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}var Vo;class Gg extends wt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});b(this,Vo,null)}render(){this.container.classList.add("lineAnnotation");const e=this.data,{width:s,height:i}=$i(e.rect),r=this.svgFactory.create(s,i,!0),a=u(this,Vo,this.svgFactory.createElement("svg:line"));return a.setAttribute("x1",e.rect[2]-e.lineCoordinates[0]),a.setAttribute("y1",e.rect[3]-e.lineCoordinates[1]),a.setAttribute("x2",e.rect[2]-e.lineCoordinates[2]),a.setAttribute("y2",e.rect[3]-e.lineCoordinates[3]),a.setAttribute("stroke-width",e.borderStyle.width||1),a.setAttribute("stroke","transparent"),a.setAttribute("fill","transparent"),r.append(a),this.container.append(r),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return n(this,Vo)}addHighlightArea(){this.container.classList.add("highlightArea")}}Vo=new WeakMap;var Uo;class Vg extends wt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});b(this,Uo,null)}render(){this.container.classList.add("squareAnnotation");const e=this.data,{width:s,height:i}=$i(e.rect),r=this.svgFactory.create(s,i,!0),a=e.borderStyle.width,o=u(this,Uo,this.svgFactory.createElement("svg:rect"));return o.setAttribute("x",a/2),o.setAttribute("y",a/2),o.setAttribute("width",s-a),o.setAttribute("height",i-a),o.setAttribute("stroke-width",a||1),o.setAttribute("stroke","transparent"),o.setAttribute("fill","transparent"),r.append(o),this.container.append(r),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return n(this,Uo)}addHighlightArea(){this.container.classList.add("highlightArea")}}Uo=new WeakMap;var Wo;class Ug extends wt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});b(this,Wo,null)}render(){this.container.classList.add("circleAnnotation");const e=this.data,{width:s,height:i}=$i(e.rect),r=this.svgFactory.create(s,i,!0),a=e.borderStyle.width,o=u(this,Wo,this.svgFactory.createElement("svg:ellipse"));return o.setAttribute("cx",s/2),o.setAttribute("cy",i/2),o.setAttribute("rx",s/2-a/2),o.setAttribute("ry",i/2-a/2),o.setAttribute("stroke-width",a||1),o.setAttribute("stroke","transparent"),o.setAttribute("fill","transparent"),r.append(o),this.container.append(r),!e.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return n(this,Wo)}addHighlightArea(){this.container.classList.add("highlightArea")}}Wo=new WeakMap;var qo;class pp extends wt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});b(this,qo,null);this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:e,vertices:s,borderStyle:i,popupRef:r}}=this;if(!s)return this.container;const{width:a,height:o}=$i(e),h=this.svgFactory.create(a,o,!0);let l=[];for(let d=0,p=s.length;d<p;d+=2){const g=s[d]-e[0],m=e[3]-s[d+1];l.push(`${g},${m}`)}l=l.join(" ");const c=u(this,qo,this.svgFactory.createElement(this.svgElementName));return c.setAttribute("points",l),c.setAttribute("stroke-width",i.width||1),c.setAttribute("stroke","transparent"),c.setAttribute("fill","transparent"),h.append(c),this.container.append(h),!r&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return n(this,qo)}addHighlightArea(){this.container.classList.add("highlightArea")}}qo=new WeakMap;class Wg extends pp{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class qg extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}var Xo,Fn,Yo,Bc;class wd extends wt{constructor(e){super(e,{isRenderable:!0,ignoreBorder:!0});b(this,Yo);b(this,Xo,null);b(this,Fn,[]);this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=this.data.it==="InkHighlight"?V.HIGHLIGHT:V.INK}render(){this.container.classList.add(this.containerClassName);const{data:{rect:e,rotation:s,inkLists:i,borderStyle:r,popupRef:a}}=this,{transform:o,width:h,height:l}=A(this,Yo,Bc).call(this,s,e),c=this.svgFactory.create(h,l,!0),d=u(this,Xo,this.svgFactory.createElement("svg:g"));c.append(d),d.setAttribute("stroke-width",r.width||1),d.setAttribute("stroke-linecap","round"),d.setAttribute("stroke-linejoin","round"),d.setAttribute("stroke-miterlimit",10),d.setAttribute("stroke","transparent"),d.setAttribute("fill","transparent"),d.setAttribute("transform",o);for(let p=0,g=i.length;p<g;p++){const m=this.svgFactory.createElement(this.svgElementName);n(this,Fn).push(m),m.setAttribute("points",i[p].join(",")),d.append(m)}return!a&&this.hasPopupData&&this._createPopup(),this.container.append(c),this._editOnDoubleClick(),this.container}updateEdited(e){super.updateEdited(e);const{thickness:s,points:i,rect:r}=e,a=n(this,Xo);if(s>=0&&a.setAttribute("stroke-width",s||1),i)for(let o=0,h=n(this,Fn).length;o<h;o++)n(this,Fn)[o].setAttribute("points",i[o].join(","));if(r){const{transform:o,width:h,height:l}=A(this,Yo,Bc).call(this,this.data.rotation,r);a.parentElement.setAttribute("viewBox",`0 0 ${h} ${l}`),a.setAttribute("transform",o)}}getElementsToTriggerPopup(){return n(this,Fn)}addHighlightArea(){this.container.classList.add("highlightArea")}}Xo=new WeakMap,Fn=new WeakMap,Yo=new WeakSet,Bc=function(e,s){switch(e){case 90:return{transform:`rotate(90) translate(${-s[0]},${s[1]}) scale(1,-1)`,width:s[3]-s[1],height:s[2]-s[0]};case 180:return{transform:`rotate(180) translate(${-s[2]},${s[1]}) scale(1,-1)`,width:s[2]-s[0],height:s[3]-s[1]};case 270:return{transform:`rotate(270) translate(${-s[2]},${s[3]}) scale(1,-1)`,width:s[3]-s[1],height:s[2]-s[0]};default:return{transform:`translate(${-s[0]},${s[3]}) scale(1,-1)`,width:s[2]-s[0],height:s[3]-s[1]}}};class fp extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0}),this.annotationEditorType=V.HIGHLIGHT}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this._editOnDoubleClick(),this.container}}class Xg extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class Yg extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class Kg extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class gp extends wt{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.annotationEditorType=V.STAMP}render(){return this.container.classList.add("stampAnnotation"),this.container.setAttribute("role","img"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}var Ko,Qo,Hc;class Qg extends wt{constructor(e){super(e,{isRenderable:!0});b(this,Qo);b(this,Ko,null);const{file:s}=this.data;this.filename=s.filename,this.content=s.content,this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...s})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:e,data:s}=this;let i;s.hasAppearance||s.fillAlpha===0?i=document.createElement("div"):(i=document.createElement("img"),i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(s.name)?"paperclip":"pushpin"}.svg`,s.fillAlpha&&s.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(100*s.fillAlpha)}%);`)),i.addEventListener("dblclick",A(this,Qo,Hc).bind(this)),u(this,Ko,i);const{isMac:r}=se.platform;return e.addEventListener("keydown",a=>{a.key==="Enter"&&(r?a.metaKey:a.ctrlKey)&&A(this,Qo,Hc).call(this)}),!s.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea"),e.append(i),e}getElementsToTriggerPopup(){return n(this,Ko)}addHighlightArea(){this.container.classList.add("highlightArea")}}Ko=new WeakMap,Qo=new WeakSet,Hc=function(){this.downloadManager?.openOrDownloadData(this.content,this.filename)};var Jo,Nn,On,Zo,rr,mp,$c;class Jg{constructor({div:t,accessibilityManager:e,annotationCanvasMap:s,annotationEditorUIManager:i,page:r,viewport:a,structTreeLayer:o}){b(this,rr);b(this,Jo,null);b(this,Nn,null);b(this,On,new Map);b(this,Zo,null);this.div=t,u(this,Jo,e),u(this,Nn,s),u(this,Zo,o||null),this.page=r,this.viewport=a,this.zIndex=0,this._annotationEditorUIManager=i}hasEditableAnnotations(){return n(this,On).size>0}async render(t){const{annotations:e}=t,s=this.div;sr(s,this.viewport);const i=new Map,r={data:null,layer:s,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:t.renderForms!==!1,svgFactory:new yd,annotationStorage:t.annotationStorage||new Ad,enableScripting:t.enableScripting===!0,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const a of e){if(a.noHTML)continue;const o=a.annotationType===su;if(o){const c=i.get(a.id);if(!c)continue;r.elements=c}else{const{width:c,height:d}=$i(a.rect);if(c<=0||d<=0)continue}r.data=a;const h=Fg.create(r);if(!h.isRenderable)continue;if(!o&&a.popupRef){const c=i.get(a.popupRef);c?c.push(h):i.set(a.popupRef,[h])}const l=h.render();a.hidden&&(l.style.visibility="hidden"),await A(this,rr,mp).call(this,l,a.id),h._isEditable&&(n(this,On).set(h.data.id,h),this._annotationEditorUIManager?.renderAnnotationElement(h))}A(this,rr,$c).call(this)}update({viewport:t}){const e=this.div;this.viewport=t,sr(e,{rotation:t.rotation}),A(this,rr,$c).call(this),e.hidden=!1}getEditableAnnotations(){return Array.from(n(this,On).values())}getEditableAnnotation(t){return n(this,On).get(t)}}Jo=new WeakMap,Nn=new WeakMap,On=new WeakMap,Zo=new WeakMap,rr=new WeakSet,mp=async function(t,e){const s=t.firstChild||t,i=s.id=`${fd}${e}`,r=await n(this,Zo)?.getAriaAttributes(i);if(r)for(const[a,o]of r)s.setAttribute(a,o);this.div.append(t),n(this,Jo)?.moveElementInDOM(this.div,t,s,!1)},$c=function(){if(!n(this,Nn))return;const t=this.div;for(const[e,s]of n(this,Nn)){const i=t.querySelector(`[data-annotation-id="${e}"]`);if(!i)continue;s.className="annotationContent";const{firstChild:r}=i;r?r.nodeName==="CANVAS"?r.replaceWith(s):r.classList.contains("annotationContent")?r.after(s):r.before(s):i.append(s)}n(this,Nn).clear()};const Ch=/\r\n?|\n/g;var qe,be,th,Bn,Ae,xt,bp,Ap,vp,Wh,ii,qh,Xh,yp,jc,wp;const ot=class ot extends gt{constructor(e){super({...e,name:"freeTextEditor"});b(this,xt);b(this,qe);b(this,be,"");b(this,th,`${this.id}-editor`);b(this,Bn,null);b(this,Ae);u(this,qe,e.color||ot._defaultColor||gt._defaultLineColor),u(this,Ae,e.fontSize||ot._defaultFontSize)}static get _keyboardManager(){const e=ot.prototype,s=a=>a.isEmpty(),i=ir.TRANSLATE_SMALL,r=ir.TRANSLATE_BIG;return X(this,"_keyboardManager",new wh([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],e.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],e.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],e._translateEmpty,{args:[-i,0],checker:s}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],e._translateEmpty,{args:[-r,0],checker:s}],[["ArrowRight","mac+ArrowRight"],e._translateEmpty,{args:[i,0],checker:s}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],e._translateEmpty,{args:[r,0],checker:s}],[["ArrowUp","mac+ArrowUp"],e._translateEmpty,{args:[0,-i],checker:s}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],e._translateEmpty,{args:[0,-r],checker:s}],[["ArrowDown","mac+ArrowDown"],e._translateEmpty,{args:[0,i],checker:s}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],e._translateEmpty,{args:[0,r],checker:s}]]))}static initialize(e,s){gt.initialize(e,s);const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(e,s){switch(e){case Y.FREETEXT_SIZE:ot._defaultFontSize=s;break;case Y.FREETEXT_COLOR:ot._defaultColor=s}}updateParams(e,s){switch(e){case Y.FREETEXT_SIZE:A(this,xt,bp).call(this,s);break;case Y.FREETEXT_COLOR:A(this,xt,Ap).call(this,s)}}static get defaultPropertiesToUpdate(){return[[Y.FREETEXT_SIZE,ot._defaultFontSize],[Y.FREETEXT_COLOR,ot._defaultColor||gt._defaultLineColor]]}get propertiesToUpdate(){return[[Y.FREETEXT_SIZE,n(this,Ae)],[Y.FREETEXT_COLOR,n(this,qe)]]}_translateEmpty(e,s){this._uiManager.translateSelectedEditors(e,s,!0)}getInitialTranslation(){const e=this.parentScale;return[-ot._internalPadding*e,-(ot._internalPadding+n(this,Ae))*e]}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){if(this.isInEditMode())return;this.parent.setEditingState(!1),this.parent.updateToolbar(V.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),u(this,Bn,new AbortController);const e=this._uiManager.combinedSignal(n(this,Bn));this.editorDiv.addEventListener("keydown",this.editorDivKeydown.bind(this),{signal:e}),this.editorDiv.addEventListener("focus",this.editorDivFocus.bind(this),{signal:e}),this.editorDiv.addEventListener("blur",this.editorDivBlur.bind(this),{signal:e}),this.editorDiv.addEventListener("input",this.editorDivInput.bind(this),{signal:e}),this.editorDiv.addEventListener("paste",this.editorDivPaste.bind(this),{signal:e})}disableEditMode(){this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",n(this,th)),this._isDraggable=!0,n(this,Bn)?.abort(),u(this,Bn,null),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freetextEditing"))}focusin(e){this._focusEventsAllowed&&(super.focusin(e),e.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(e){this.width||(this.enableEditMode(),e&&this.editorDiv.focus(),this._initialOptions?.isCentered&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||this.editorDiv.innerText.trim()===""}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freetextEditing")),super.remove()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const e=n(this,be),s=u(this,be,A(this,xt,vp).call(this).trimEnd());if(e===s)return;const i=r=>{u(this,be,r),r?(A(this,xt,Xh).call(this),this._uiManager.rebuild(this),A(this,xt,Wh).call(this)):this.remove()};this.addCommands({cmd:()=>{i(s)},undo:()=>{i(e)},mustExec:!1}),A(this,xt,Wh).call(this)}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(e){this.enterInEditMode()}keydown(e){e.target===this.div&&e.key==="Enter"&&(this.enterInEditMode(),e.preventDefault())}editorDivKeydown(e){ot._keyboardManager.exec(this,e)}editorDivFocus(e){this.isEditing=!0}editorDivBlur(e){this.isEditing=!1}editorDivInput(e){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let e,s;this.width&&(e=this.x,s=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",n(this,th)),this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text2"),this.editorDiv.setAttribute("data-l10n-attrs","default-content"),this.enableEditing(),this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;if(i.fontSize=`calc(${n(this,Ae)}px * var(--scale-factor))`,i.color=n(this,qe),this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),al(this,this.div,["dblclick","keydown"]),this.width){const[r,a]=this.parentDimensions;if(this.annotationElementId){const{position:o}=this._initialData;let[h,l]=this.getInitialTranslation();[h,l]=this.pageTranslationToScreen(h,l);const[c,d]=this.pageDimensions,[p,g]=this.pageTranslation;let m,v;switch(this.rotation){case 0:m=e+(o[0]-p)/c,v=s+this.height-(o[1]-g)/d;break;case 90:m=e+(o[0]-p)/c,v=s-(o[1]-g)/d,[h,l]=[l,-h];break;case 180:m=e-this.width+(o[0]-p)/c,v=s-(o[1]-g)/d,[h,l]=[-h,-l];break;case 270:m=e+(o[0]-p-this.height*d)/c,v=s+(o[1]-g-this.width*c)/d,[h,l]=[-l,h]}this.setAt(m*r,v*a,h,l)}else this.setAt(e*r,s*a,this.width*r,this.height*a);A(this,xt,Xh).call(this),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}editorDivPaste(e){var m,v,y;const s=e.clipboardData||window.clipboardData,{types:i}=s;if(i.length===1&&i[0]==="text/plain")return;e.preventDefault();const r=A(m=ot,ii,jc).call(m,s.getData("text")||"").replaceAll(Ch,`
`);if(!r)return;const a=window.getSelection();if(!a.rangeCount)return;this.editorDiv.normalize(),a.deleteFromDocument();const o=a.getRangeAt(0);if(!r.includes(`
`)){o.insertNode(document.createTextNode(r)),this.editorDiv.normalize(),a.collapseToStart();return}const{startContainer:h,startOffset:l}=o,c=[],d=[];if(h.nodeType===Node.TEXT_NODE){const w=h.parentElement;if(d.push(h.nodeValue.slice(l).replaceAll(Ch,"")),w!==this.editorDiv){let _=c;for(const x of this.editorDiv.childNodes)x!==w?_.push(A(v=ot,ii,qh).call(v,x)):_=d}c.push(h.nodeValue.slice(0,l).replaceAll(Ch,""))}else if(h===this.editorDiv){let w=c,_=0;for(const x of this.editorDiv.childNodes)_++===l&&(w=d),w.push(A(y=ot,ii,qh).call(y,x))}u(this,be,`${c.join(`
`)}${r}${d.join(`
`)}`),A(this,xt,Xh).call(this);const p=new Range;let g=c.reduce((w,_)=>w+_.length,0);for(const{firstChild:w}of this.editorDiv.childNodes)if(w.nodeType===Node.TEXT_NODE){const _=w.nodeValue.length;if(g<=_){p.setStart(w,g),p.setEnd(w,g);break}g-=_}a.removeAllRanges(),a.addRange(p)}get contentDiv(){return this.editorDiv}static async deserialize(e,s,i){var o;let r=null;if(e instanceof up){const{data:{defaultAppearanceData:{fontSize:h,fontColor:l},rect:c,rotation:d,id:p,popupRef:g},textContent:m,textPosition:v,parent:{page:{pageNumber:y}}}=e;if(!m||m.length===0)return null;r=e={annotationType:V.FREETEXT,color:Array.from(l),fontSize:h,value:m.join(`
`),position:v,pageIndex:y-1,rect:c.slice(0),rotation:d,id:p,deleted:!1,popupRef:g}}const a=await super.deserialize(e,s,i);return u(a,Ae,e.fontSize),u(a,qe,D.makeHexColor(...e.color)),u(a,be,A(o=ot,ii,jc).call(o,e.value)),a.annotationElementId=e.id||null,a._initialData=r,a}serialize(e=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const s=ot._internalPadding*this.parentScale,i=this.getRect(s,s),r=gt._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:n(this,qe)),a={annotationType:V.FREETEXT,color:r,fontSize:n(this,Ae),value:A(this,xt,yp).call(this),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?a:this.annotationElementId&&!A(this,xt,wp).call(this,a)?null:(a.id=this.annotationElementId,a)}renderAnnotationElement(e){const s=super.renderAnnotationElement(e);if(this.deleted)return s;const{style:i}=s;i.fontSize=`calc(${n(this,Ae)}px * var(--scale-factor))`,i.color=n(this,qe),s.replaceChildren();for(const a of n(this,be).split(`
`)){const o=document.createElement("div");o.append(a?document.createTextNode(a):document.createElement("br")),s.append(o)}const r=ot._internalPadding*this.parentScale;return e.updateEdited({rect:this.getRect(r,r),popupContent:n(this,be)}),s}resetAnnotationElement(e){super.resetAnnotationElement(e),e.resetEdited()}};qe=new WeakMap,be=new WeakMap,th=new WeakMap,Bn=new WeakMap,Ae=new WeakMap,xt=new WeakSet,bp=function(e){const s=r=>{this.editorDiv.style.fontSize=`calc(${r}px * var(--scale-factor))`,this.translate(0,-(r-n(this,Ae))*this.parentScale),u(this,Ae,r),A(this,xt,Wh).call(this)},i=n(this,Ae);this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Y.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})},Ap=function(e){const s=r=>{u(this,qe,this.editorDiv.style.color=r)},i=n(this,qe);this.addCommands({cmd:s.bind(this,e),undo:s.bind(this,i),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Y.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})},vp=function(){var i;const e=[];this.editorDiv.normalize();let s=null;for(const r of this.editorDiv.childNodes)(s?.nodeType!==Node.TEXT_NODE||r.nodeName!=="BR")&&(e.push(A(i=ot,ii,qh).call(i,r)),s=r);return e.join(`
`)},Wh=function(){const[e,s]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:r,div:a}=this,o=a.style.display,h=a.classList.contains("hidden");a.classList.remove("hidden"),a.style.display="hidden",r.div.append(this.div),i=a.getBoundingClientRect(),a.remove(),a.style.display=o,a.classList.toggle("hidden",h)}this.rotation%180==this.parentRotation%180?(this.width=i.width/e,this.height=i.height/s):(this.width=i.height/e,this.height=i.width/s),this.fixAndSetPosition()},ii=new WeakSet,qh=function(e){return(e.nodeType===Node.TEXT_NODE?e.nodeValue:e.innerText).replaceAll(Ch,"")},Xh=function(){if(this.editorDiv.replaceChildren(),n(this,be))for(const e of n(this,be).split(`
`)){const s=document.createElement("div");s.append(e?document.createTextNode(e):document.createElement("br")),this.editorDiv.append(s)}},yp=function(){return n(this,be).replaceAll(" "," ")},jc=function(e){return e.replaceAll(" "," ")},wp=function(e){const{value:s,fontSize:i,color:r,pageIndex:a}=this._initialData;return this._hasBeenMoved||e.value!==s||e.fontSize!==i||e.color.some((o,h)=>o!==r[h])||e.pageIndex!==a},b(ot,ii),F(ot,"_freeTextDefaultContent",""),F(ot,"_internalPadding",0),F(ot,"_defaultColor",null),F(ot,"_defaultFontSize",10),F(ot,"_type","freetext"),F(ot,"_editorType",V.FREETEXT);let zc=ot;class T{toSVGPath(){at("Abstract method `toSVGPath` must be implemented.")}get box(){at("Abstract getter `box` must be implemented.")}serialize(t,e){at("Abstract method `serialize` must be implemented.")}static _rescale(t,e,s,i,r,a){a||(a=new Float32Array(t.length));for(let o=0,h=t.length;o<h;o+=2)a[o]=e+t[o]*i,a[o+1]=s+t[o+1]*r;return a}static _rescaleAndSwap(t,e,s,i,r,a){a||(a=new Float32Array(t.length));for(let o=0,h=t.length;o<h;o+=2)a[o]=e+t[o+1]*i,a[o+1]=s+t[o]*r;return a}static _translate(t,e,s,i){i||(i=new Float32Array(t.length));for(let r=0,a=t.length;r<a;r+=2)i[r]=e+t[r],i[r+1]=s+t[r+1];return i}static svgRound(t){return Math.round(1e4*t)}static _normalizePoint(t,e,s,i,r){switch(r){case 90:return[1-e/s,t/i];case 180:return[1-t/s,1-e/i];case 270:return[e/s,1-t/i];default:return[t/s,e/i]}}static _normalizePagePoint(t,e,s){switch(s){case 90:return[1-e,t];case 180:return[1-t,1-e];case 270:return[e,1-t];default:return[t,e]}}static createBezierPoints(t,e,s,i,r,a){return[(t+5*s)/6,(e+5*i)/6,(5*s+r)/6,(5*i+a)/6,(s+r)/2,(i+a)/2]}}F(T,"PRECISION",1e-4);var ve,Xe,Zr,ta,gs,q,Hn,$n,eh,sh,ea,sa,Ri,ih,kl,Rl,Mt,Oa,_p,xp,Sp,Ep,Cp,Mp;const Rs=class Rs{constructor({x:t,y:e},s,i,r,a,o=0){b(this,Mt);b(this,ve);b(this,Xe,[]);b(this,Zr);b(this,ta);b(this,gs,[]);b(this,q,new Float32Array(18));b(this,Hn);b(this,$n);b(this,eh);b(this,sh);b(this,ea);b(this,sa);b(this,Ri,[]);u(this,ve,s),u(this,sa,r*i),u(this,ta,a),n(this,q).set([NaN,NaN,NaN,NaN,t,e],6),u(this,Zr,o),u(this,sh,n(Rs,ih)*i),u(this,eh,n(Rs,Rl)*i),u(this,ea,i),n(this,Ri).push(t,e)}isEmpty(){return isNaN(n(this,q)[8])}add({x:t,y:e}){u(this,Hn,t),u(this,$n,e);const[s,i,r,a]=n(this,ve);let[o,h,l,c]=n(this,q).subarray(8,12);const d=t-l,p=e-c,g=Math.hypot(d,p);if(g<n(this,eh))return!1;const m=g-n(this,sh),v=m/g,y=v*d,w=v*p;let _=o,x=h;o=l,h=c,l+=y,c+=w,n(this,Ri)?.push(t,e);const S=y/m,C=-w/m*n(this,sa),k=S*n(this,sa);return n(this,q).set(n(this,q).subarray(2,8),0),n(this,q).set([l+C,c+k],4),n(this,q).set(n(this,q).subarray(14,18),12),n(this,q).set([l-C,c-k],16),isNaN(n(this,q)[6])?(n(this,gs).length===0&&(n(this,q).set([o+C,h+k],2),n(this,gs).push(NaN,NaN,NaN,NaN,(o+C-s)/r,(h+k-i)/a),n(this,q).set([o-C,h-k],14),n(this,Xe).push(NaN,NaN,NaN,NaN,(o-C-s)/r,(h-k-i)/a)),n(this,q).set([_,x,o,h,l,c],6),!this.isEmpty()):(n(this,q).set([_,x,o,h,l,c],6),Math.abs(Math.atan2(x-h,_-o)-Math.atan2(w,y))<Math.PI/2?([o,h,l,c]=n(this,q).subarray(2,6),n(this,gs).push(NaN,NaN,NaN,NaN,((o+l)/2-s)/r,((h+c)/2-i)/a),[o,h,_,x]=n(this,q).subarray(14,18),n(this,Xe).push(NaN,NaN,NaN,NaN,((_+o)/2-s)/r,((x+h)/2-i)/a),!0):([_,x,o,h,l,c]=n(this,q).subarray(0,6),n(this,gs).push(((_+5*o)/6-s)/r,((x+5*h)/6-i)/a,((5*o+l)/6-s)/r,((5*h+c)/6-i)/a,((o+l)/2-s)/r,((h+c)/2-i)/a),[l,c,o,h,_,x]=n(this,q).subarray(12,18),n(this,Xe).push(((_+5*o)/6-s)/r,((x+5*h)/6-i)/a,((5*o+l)/6-s)/r,((5*h+c)/6-i)/a,((o+l)/2-s)/r,((h+c)/2-i)/a),!0))}toSVGPath(){if(this.isEmpty())return"";const t=n(this,gs),e=n(this,Xe);if(isNaN(n(this,q)[6])&&!this.isEmpty())return A(this,Mt,_p).call(this);const s=[];s.push(`M${t[4]} ${t[5]}`);for(let i=6;i<t.length;i+=6)isNaN(t[i])?s.push(`L${t[i+4]} ${t[i+5]}`):s.push(`C${t[i]} ${t[i+1]} ${t[i+2]} ${t[i+3]} ${t[i+4]} ${t[i+5]}`);A(this,Mt,Sp).call(this,s);for(let i=e.length-6;i>=6;i-=6)isNaN(e[i])?s.push(`L${e[i+4]} ${e[i+5]}`):s.push(`C${e[i]} ${e[i+1]} ${e[i+2]} ${e[i+3]} ${e[i+4]} ${e[i+5]}`);return A(this,Mt,xp).call(this,s),s.join(" ")}newFreeDrawOutline(t,e,s,i,r,a){return new kp(t,e,s,i,r,a)}getOutlines(){const t=n(this,gs),e=n(this,Xe),s=n(this,q),[i,r,a,o]=n(this,ve),h=new Float32Array((n(this,Ri)?.length??0)+2);for(let d=0,p=h.length-2;d<p;d+=2)h[d]=(n(this,Ri)[d]-i)/a,h[d+1]=(n(this,Ri)[d+1]-r)/o;if(h[h.length-2]=(n(this,Hn)-i)/a,h[h.length-1]=(n(this,$n)-r)/o,isNaN(s[6])&&!this.isEmpty())return A(this,Mt,Ep).call(this,h);const l=new Float32Array(n(this,gs).length+24+n(this,Xe).length);let c=t.length;for(let d=0;d<c;d+=2)isNaN(t[d])?l[d]=l[d+1]=NaN:(l[d]=t[d],l[d+1]=t[d+1]);c=A(this,Mt,Mp).call(this,l,c);for(let d=e.length-6;d>=6;d-=6)for(let p=0;p<6;p+=2)isNaN(e[d+p])?(l[c]=l[c+1]=NaN,c+=2):(l[c]=e[d+p],l[c+1]=e[d+p+1],c+=2);return A(this,Mt,Cp).call(this,l,c),this.newFreeDrawOutline(l,h,n(this,ve),n(this,ea),n(this,Zr),n(this,ta))}};ve=new WeakMap,Xe=new WeakMap,Zr=new WeakMap,ta=new WeakMap,gs=new WeakMap,q=new WeakMap,Hn=new WeakMap,$n=new WeakMap,eh=new WeakMap,sh=new WeakMap,ea=new WeakMap,sa=new WeakMap,Ri=new WeakMap,ih=new WeakMap,kl=new WeakMap,Rl=new WeakMap,Mt=new WeakSet,Oa=function(){const t=n(this,q).subarray(4,6),e=n(this,q).subarray(16,18),[s,i,r,a]=n(this,ve);return[(n(this,Hn)+(t[0]-e[0])/2-s)/r,(n(this,$n)+(t[1]-e[1])/2-i)/a,(n(this,Hn)+(e[0]-t[0])/2-s)/r,(n(this,$n)+(e[1]-t[1])/2-i)/a]},_p=function(){const[t,e,s,i]=n(this,ve),[r,a,o,h]=A(this,Mt,Oa).call(this);return`M${(n(this,q)[2]-t)/s} ${(n(this,q)[3]-e)/i} L${(n(this,q)[4]-t)/s} ${(n(this,q)[5]-e)/i} L${r} ${a} L${o} ${h} L${(n(this,q)[16]-t)/s} ${(n(this,q)[17]-e)/i} L${(n(this,q)[14]-t)/s} ${(n(this,q)[15]-e)/i} Z`},xp=function(t){const e=n(this,Xe);t.push(`L${e[4]} ${e[5]} Z`)},Sp=function(t){const[e,s,i,r]=n(this,ve),a=n(this,q).subarray(4,6),o=n(this,q).subarray(16,18),[h,l,c,d]=A(this,Mt,Oa).call(this);t.push(`L${(a[0]-e)/i} ${(a[1]-s)/r} L${h} ${l} L${c} ${d} L${(o[0]-e)/i} ${(o[1]-s)/r}`)},Ep=function(t){const e=n(this,q),[s,i,r,a]=n(this,ve),[o,h,l,c]=A(this,Mt,Oa).call(this),d=new Float32Array(36);return d.set([NaN,NaN,NaN,NaN,(e[2]-s)/r,(e[3]-i)/a,NaN,NaN,NaN,NaN,(e[4]-s)/r,(e[5]-i)/a,NaN,NaN,NaN,NaN,o,h,NaN,NaN,NaN,NaN,l,c,NaN,NaN,NaN,NaN,(e[16]-s)/r,(e[17]-i)/a,NaN,NaN,NaN,NaN,(e[14]-s)/r,(e[15]-i)/a],0),this.newFreeDrawOutline(d,t,n(this,ve),n(this,ea),n(this,Zr),n(this,ta))},Cp=function(t,e){const s=n(this,Xe);return t.set([NaN,NaN,NaN,NaN,s[4],s[5]],e),e+6},Mp=function(t,e){const s=n(this,q).subarray(4,6),i=n(this,q).subarray(16,18),[r,a,o,h]=n(this,ve),[l,c,d,p]=A(this,Mt,Oa).call(this);return t.set([NaN,NaN,NaN,NaN,(s[0]-r)/o,(s[1]-a)/h,NaN,NaN,NaN,NaN,l,c,NaN,NaN,NaN,NaN,d,p,NaN,NaN,NaN,NaN,(i[0]-r)/o,(i[1]-a)/h],e),e+24},b(Rs,ih,8),b(Rs,kl,2),b(Rs,Rl,n(Rs,ih)+n(Rs,kl));let cl=Rs;var ia,zn,Xs,nh,ye,rh,yt,Tl,Rp;class kp extends T{constructor(e,s,i,r,a,o){super();b(this,Tl);b(this,ia);b(this,zn,new Float32Array(4));b(this,Xs);b(this,nh);b(this,ye);b(this,rh);b(this,yt);u(this,yt,e),u(this,ye,s),u(this,ia,i),u(this,rh,r),u(this,Xs,a),u(this,nh,o),this.lastPoint=[NaN,NaN],A(this,Tl,Rp).call(this,o);const[h,l,c,d]=n(this,zn);for(let p=0,g=e.length;p<g;p+=2)e[p]=(e[p]-h)/c,e[p+1]=(e[p+1]-l)/d;for(let p=0,g=s.length;p<g;p+=2)s[p]=(s[p]-h)/c,s[p+1]=(s[p+1]-l)/d}toSVGPath(){const e=[`M${n(this,yt)[4]} ${n(this,yt)[5]}`];for(let s=6,i=n(this,yt).length;s<i;s+=6)isNaN(n(this,yt)[s])?e.push(`L${n(this,yt)[s+4]} ${n(this,yt)[s+5]}`):e.push(`C${n(this,yt)[s]} ${n(this,yt)[s+1]} ${n(this,yt)[s+2]} ${n(this,yt)[s+3]} ${n(this,yt)[s+4]} ${n(this,yt)[s+5]}`);return e.push("Z"),e.join(" ")}serialize([e,s,i,r],a){const o=i-e,h=r-s;let l,c;switch(a){case 0:l=T._rescale(n(this,yt),e,r,o,-h),c=T._rescale(n(this,ye),e,r,o,-h);break;case 90:l=T._rescaleAndSwap(n(this,yt),e,s,o,h),c=T._rescaleAndSwap(n(this,ye),e,s,o,h);break;case 180:l=T._rescale(n(this,yt),i,s,-o,h),c=T._rescale(n(this,ye),i,s,-o,h);break;case 270:l=T._rescaleAndSwap(n(this,yt),i,r,-o,-h),c=T._rescaleAndSwap(n(this,ye),i,r,-o,-h)}return{outline:Array.from(l),points:[Array.from(c)]}}get box(){return n(this,zn)}newOutliner(e,s,i,r,a,o=0){return new cl(e,s,i,r,a,o)}getNewOutline(e,s){const[i,r,a,o]=n(this,zn),[h,l,c,d]=n(this,ia),p=a*c,g=o*d,m=i*c+h,v=r*d+l,y=this.newOutliner({x:n(this,ye)[0]*p+m,y:n(this,ye)[1]*g+v},n(this,ia),n(this,rh),e,n(this,nh),s??n(this,Xs));for(let w=2;w<n(this,ye).length;w+=2)y.add({x:n(this,ye)[w]*p+m,y:n(this,ye)[w+1]*g+v});return y.getOutlines()}}ia=new WeakMap,zn=new WeakMap,Xs=new WeakMap,nh=new WeakMap,ye=new WeakMap,rh=new WeakMap,yt=new WeakMap,Tl=new WeakSet,Rp=function(e){const s=n(this,yt);let i=s[4],r=s[5],a=i,o=r,h=i,l=r,c=i,d=r;const p=e?Math.max:Math.min;for(let m=6,v=s.length;m<v;m+=6){if(isNaN(s[m]))a=Math.min(a,s[m+4]),o=Math.min(o,s[m+5]),h=Math.max(h,s[m+4]),l=Math.max(l,s[m+5]),d<s[m+5]?(c=s[m+4],d=s[m+5]):d===s[m+5]&&(c=p(c,s[m+4]));else{const y=D.bezierBoundingBox(i,r,...s.slice(m,m+6));a=Math.min(a,y[0]),o=Math.min(o,y[1]),h=Math.max(h,y[2]),l=Math.max(l,y[3]),d<y[3]?(c=y[2],d=y[3]):d===y[3]&&(c=p(c,y[2]))}i=s[m+4],r=s[m+5]}const g=n(this,zn);g[0]=a-n(this,Xs),g[1]=o-n(this,Xs),g[2]=h-a+2*n(this,Xs),g[3]=l-o+2*n(this,Xs),this.lastPoint=[c,d]};var ah,oh,Ti,Ye,ne,Tp,Yh,Pp,Ip,Vc;class Gc{constructor(t,e=0,s=0,i=!0){b(this,ne);b(this,ah);b(this,oh);b(this,Ti,[]);b(this,Ye,[]);let r=1/0,a=-1/0,o=1/0,h=-1/0;const l=10**-4;for(const{x:y,y:w,width:_,height:x}of t){const S=Math.floor((y-e)/l)*l,C=Math.ceil((y+_+e)/l)*l,k=Math.floor((w-e)/l)*l,E=Math.ceil((w+x+e)/l)*l,L=[S,k,E,!0],O=[C,k,E,!1];n(this,Ti).push(L,O),r=Math.min(r,S),a=Math.max(a,C),o=Math.min(o,k),h=Math.max(h,E)}const c=a-r+2*s,d=h-o+2*s,p=r-s,g=o-s,m=n(this,Ti).at(i?-1:-2),v=[m[0],m[2]];for(const y of n(this,Ti)){const[w,_,x]=y;y[0]=(w-p)/c,y[1]=(_-g)/d,y[2]=(x-g)/d}u(this,ah,new Float32Array([p,g,c,d])),u(this,oh,v)}getOutlines(){n(this,Ti).sort((e,s)=>e[0]-s[0]||e[1]-s[1]||e[2]-s[2]);const t=[];for(const e of n(this,Ti))e[3]?(t.push(...A(this,ne,Vc).call(this,e)),A(this,ne,Pp).call(this,e)):(A(this,ne,Ip).call(this,e),t.push(...A(this,ne,Vc).call(this,e)));return A(this,ne,Tp).call(this,t)}}ah=new WeakMap,oh=new WeakMap,Ti=new WeakMap,Ye=new WeakMap,ne=new WeakSet,Tp=function(t){const e=[],s=new Set;for(const a of t){const[o,h,l]=a;e.push([o,h,a],[o,l,a])}e.sort((a,o)=>a[1]-o[1]||a[0]-o[0]);for(let a=0,o=e.length;a<o;a+=2){const h=e[a][2],l=e[a+1][2];h.push(l),l.push(h),s.add(h),s.add(l)}const i=[];let r;for(;s.size>0;){const a=s.values().next().value;let[o,h,l,c,d]=a;s.delete(a);let p=o,g=h;for(r=[o,l],i.push(r);;){let m;if(s.has(c))m=c;else{if(!s.has(d))break;m=d}s.delete(m),[o,h,l,c,d]=m,p!==o&&(r.push(p,g,o,g===h?h:l),p=o),g=g===h?l:h}r.push(p,g)}return new Zg(i,n(this,ah),n(this,oh))},Yh=function(t){const e=n(this,Ye);let s=0,i=e.length-1;for(;s<=i;){const r=s+i>>1,a=e[r][0];if(a===t)return r;a<t?s=r+1:i=r-1}return i+1},Pp=function([,t,e]){const s=A(this,ne,Yh).call(this,t);n(this,Ye).splice(s,0,[t,e])},Ip=function([,t,e]){const s=A(this,ne,Yh).call(this,t);for(let i=s;i<n(this,Ye).length;i++){const[r,a]=n(this,Ye)[i];if(r!==t)break;if(r===t&&a===e){n(this,Ye).splice(i,1);return}}for(let i=s-1;i>=0;i--){const[r,a]=n(this,Ye)[i];if(r!==t)break;if(r===t&&a===e){n(this,Ye).splice(i,1);return}}},Vc=function(t){const[e,s,i]=t,r=[[e,s,i]],a=A(this,ne,Yh).call(this,i);for(let o=0;o<a;o++){const[h,l]=n(this,Ye)[o];for(let c=0,d=r.length;c<d;c++){const[,p,g]=r[c];if(!(l<=p||g<=h))if(p>=h)if(g>l)r[c][1]=l;else{if(d===1)return[];r.splice(c,1),c--,d--}else r[c][2]=h,g>l&&r.push([e,l,g])}}return r};var hh,na;class Zg extends T{constructor(e,s,i){super();b(this,hh);b(this,na);u(this,na,e),u(this,hh,s),this.lastPoint=i}toSVGPath(){const e=[];for(const s of n(this,na)){let[i,r]=s;e.push(`M${i} ${r}`);for(let a=2;a<s.length;a+=2){const o=s[a],h=s[a+1];o===i?(e.push(`V${h}`),r=h):h===r&&(e.push(`H${o}`),i=o)}e.push("Z")}return e.join(" ")}serialize([e,s,i,r],a){const o=[],h=i-e,l=r-s;for(const c of n(this,na)){const d=new Array(c.length);for(let p=0;p<c.length;p+=2)d[p]=e+c[p]*h,d[p+1]=r-c[p+1]*l;o.push(d)}return o}get box(){return n(this,hh)}get classNamesForOutlining(){return["highlightOutline"]}}hh=new WeakMap,na=new WeakMap;class Uc extends cl{newFreeDrawOutline(t,e,s,i,r,a){return new tm(t,e,s,i,r,a)}}class tm extends kp{newOutliner(t,e,s,i,r,a=0){return new Uc(t,e,s,i,r,a)}}var Ke,jn,ra,Ct,lh,aa,ch,dh,Pi,Qe,oa,uh,st,Wc,qc,Xc,Wi,Dp,hi;const oe=class oe{constructor({editor:t=null,uiManager:e=null}){b(this,st);b(this,Ke,null);b(this,jn,null);b(this,ra);b(this,Ct,null);b(this,lh,!1);b(this,aa,!1);b(this,ch,null);b(this,dh);b(this,Pi,null);b(this,Qe,null);b(this,oa);t?(u(this,aa,!1),u(this,oa,Y.HIGHLIGHT_COLOR),u(this,ch,t)):(u(this,aa,!0),u(this,oa,Y.HIGHLIGHT_DEFAULT_COLOR)),u(this,Qe,t?._uiManager||e),u(this,dh,n(this,Qe)._eventBus),u(this,ra,t?.color||n(this,Qe)?.highlightColors.values().next().value||"#FFFF98"),n(oe,uh)||u(oe,uh,Object.freeze({blue:"pdfjs-editor-colorpicker-blue",green:"pdfjs-editor-colorpicker-green",pink:"pdfjs-editor-colorpicker-pink",red:"pdfjs-editor-colorpicker-red",yellow:"pdfjs-editor-colorpicker-yellow"}))}static get _keyboardManager(){return X(this,"_keyboardManager",new wh([[["Escape","mac+Escape"],oe.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],oe.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],oe.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],oe.prototype._moveToPrevious],[["Home","mac+Home"],oe.prototype._moveToBeginning],[["End","mac+End"],oe.prototype._moveToEnd]]))}renderButton(){const t=u(this,Ke,document.createElement("button"));t.className="colorPicker",t.tabIndex="0",t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button"),t.setAttribute("aria-haspopup",!0);const e=n(this,Qe)._signal;t.addEventListener("click",A(this,st,Wi).bind(this),{signal:e}),t.addEventListener("keydown",A(this,st,Xc).bind(this),{signal:e});const s=u(this,jn,document.createElement("span"));return s.className="swatch",s.setAttribute("aria-hidden",!0),s.style.backgroundColor=n(this,ra),t.append(s),t}renderMainDropdown(){const t=u(this,Ct,A(this,st,Wc).call(this));return t.setAttribute("aria-orientation","horizontal"),t.setAttribute("aria-labelledby","highlightColorPickerLabel"),t}_colorSelectFromKeyboard(t){if(t.target===n(this,Ke)){A(this,st,Wi).call(this,t);return}const e=t.target.getAttribute("data-color");e&&A(this,st,qc).call(this,e,t)}_moveToNext(t){n(this,st,hi)?t.target!==n(this,Ke)?t.target.nextSibling?.focus():n(this,Ct).firstChild?.focus():A(this,st,Wi).call(this,t)}_moveToPrevious(t){t.target!==n(this,Ct)?.firstChild&&t.target!==n(this,Ke)?(n(this,st,hi)||A(this,st,Wi).call(this,t),t.target.previousSibling?.focus()):n(this,st,hi)&&this._hideDropdownFromKeyboard()}_moveToBeginning(t){n(this,st,hi)?n(this,Ct).firstChild?.focus():A(this,st,Wi).call(this,t)}_moveToEnd(t){n(this,st,hi)?n(this,Ct).lastChild?.focus():A(this,st,Wi).call(this,t)}hideDropdown(){n(this,Ct)?.classList.add("hidden"),n(this,Pi)?.abort(),u(this,Pi,null)}_hideDropdownFromKeyboard(){n(this,aa)||(n(this,st,hi)?(this.hideDropdown(),n(this,Ke).focus({preventScroll:!0,focusVisible:n(this,lh)})):n(this,ch)?.unselect())}updateColor(t){if(n(this,jn)&&(n(this,jn).style.backgroundColor=t),!n(this,Ct))return;const e=n(this,Qe).highlightColors.values();for(const s of n(this,Ct).children)s.setAttribute("aria-selected",e.next().value===t)}destroy(){n(this,Ke)?.remove(),u(this,Ke,null),u(this,jn,null),n(this,Ct)?.remove(),u(this,Ct,null)}};Ke=new WeakMap,jn=new WeakMap,ra=new WeakMap,Ct=new WeakMap,lh=new WeakMap,aa=new WeakMap,ch=new WeakMap,dh=new WeakMap,Pi=new WeakMap,Qe=new WeakMap,oa=new WeakMap,uh=new WeakMap,st=new WeakSet,Wc=function(){const t=document.createElement("div"),e=n(this,Qe)._signal;t.addEventListener("contextmenu",es,{signal:e}),t.className="dropdown",t.role="listbox",t.setAttribute("aria-multiselectable",!1),t.setAttribute("aria-orientation","vertical"),t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");for(const[s,i]of n(this,Qe).highlightColors){const r=document.createElement("button");r.tabIndex="0",r.role="option",r.setAttribute("data-color",i),r.title=s,r.setAttribute("data-l10n-id",n(oe,uh)[s]);const a=document.createElement("span");r.append(a),a.className="swatch",a.style.backgroundColor=i,r.setAttribute("aria-selected",i===n(this,ra)),r.addEventListener("click",A(this,st,qc).bind(this,i),{signal:e}),t.append(r)}return t.addEventListener("keydown",A(this,st,Xc).bind(this),{signal:e}),t},qc=function(t,e){e.stopPropagation(),n(this,dh).dispatch("switchannotationeditorparams",{source:this,type:n(this,oa),value:t})},Xc=function(t){oe._keyboardManager.exec(this,t)},Wi=function(t){if(n(this,st,hi)){this.hideDropdown();return}if(u(this,lh,t.detail===0),n(this,Pi)||(u(this,Pi,new AbortController),window.addEventListener("pointerdown",A(this,st,Dp).bind(this),{signal:n(this,Qe).combinedSignal(n(this,Pi))})),n(this,Ct)){n(this,Ct).classList.remove("hidden");return}const e=u(this,Ct,A(this,st,Wc).call(this));n(this,Ke).append(e)},Dp=function(t){n(this,Ct)?.contains(t.target)||this.hideDropdown()},hi=function(){return n(this,Ct)&&!n(this,Ct).classList.contains("hidden")},b(oe,uh,null);let dl=oe;var ha,ph,Ys,Gn,la,ce,fh,gh,Vn,De,we,Bt,ca,Ks,Wt,da,Le,mh,j,Yc,Kh,Lp,Fp,Np,Kc,qi,Be,ur,Op,Qh,Ba,Bp,Hp,$p,zp,jp;const Z=class Z extends gt{constructor(e){super({...e,name:"highlightEditor"});b(this,j);b(this,ha,null);b(this,ph,0);b(this,Ys);b(this,Gn,null);b(this,la,null);b(this,ce,null);b(this,fh,null);b(this,gh,0);b(this,Vn,null);b(this,De,null);b(this,we,null);b(this,Bt,!1);b(this,ca,null);b(this,Ks);b(this,Wt,null);b(this,da,"");b(this,Le);b(this,mh,"");this.color=e.color||Z._defaultColor,u(this,Le,e.thickness||Z._defaultThickness),u(this,Ks,e.opacity||Z._defaultOpacity),u(this,Ys,e.boxes||null),u(this,mh,e.methodOfCreation||""),u(this,da,e.text||""),this._isDraggable=!1,e.highlightId>-1?(u(this,Bt,!0),A(this,j,Kh).call(this,e),A(this,j,qi).call(this)):n(this,Ys)&&(u(this,ha,e.anchorNode),u(this,ph,e.anchorOffset),u(this,fh,e.focusNode),u(this,gh,e.focusOffset),A(this,j,Yc).call(this),A(this,j,qi).call(this),this.rotate(this.rotation))}static get _keyboardManager(){const e=Z.prototype;return X(this,"_keyboardManager",new wh([[["ArrowLeft","mac+ArrowLeft"],e._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],e._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],e._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],e._moveCaret,{args:[3]}]]))}get telemetryInitialData(){return{action:"added",type:n(this,Bt)?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:n(this,Le),methodOfCreation:n(this,mh)}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(e){return{numberOfColors:e.get("color").size}}static initialize(e,s){gt.initialize(e,s),Z._defaultColor||(Z._defaultColor=s.highlightColors?.values().next().value||"#fff066")}static updateDefaultParams(e,s){switch(e){case Y.HIGHLIGHT_DEFAULT_COLOR:Z._defaultColor=s;break;case Y.HIGHLIGHT_THICKNESS:Z._defaultThickness=s}}translateInPage(e,s){}get toolbarPosition(){return n(this,ca)}updateParams(e,s){switch(e){case Y.HIGHLIGHT_COLOR:A(this,j,Lp).call(this,s);break;case Y.HIGHLIGHT_THICKNESS:A(this,j,Fp).call(this,s)}}static get defaultPropertiesToUpdate(){return[[Y.HIGHLIGHT_DEFAULT_COLOR,Z._defaultColor],[Y.HIGHLIGHT_THICKNESS,Z._defaultThickness]]}get propertiesToUpdate(){return[[Y.HIGHLIGHT_COLOR,this.color||Z._defaultColor],[Y.HIGHLIGHT_THICKNESS,n(this,Le)||Z._defaultThickness],[Y.HIGHLIGHT_FREE,n(this,Bt)]]}async addEditToolbar(){const e=await super.addEditToolbar();return e?(this._uiManager.highlightColors&&(u(this,la,new dl({editor:this})),e.addColorPicker(n(this,la))),e):null}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(A(this,j,Ba).call(this))}getBaseTranslation(){return[0,0]}getRect(e,s){return super.getRect(e,s,A(this,j,Ba).call(this))}onceAdded(e){this.annotationElementId||this.parent.addUndoableEditor(this),e&&this.div.focus()}remove(){A(this,j,Kc).call(this),this._reportTelemetry({action:"deleted"}),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(A(this,j,qi).call(this),this.isAttachedToDOM||this.parent.add(this)))}setParent(e){let s=!1;this.parent&&!e?A(this,j,Kc).call(this):e&&(A(this,j,qi).call(this,e),s=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(e),this.show(this._isVisible),s&&this.select()}rotate(e){var r,a,o;const{drawLayer:s}=this.parent;let i;n(this,Bt)?(e=(e-this.rotation+360)%360,i=A(r=Z,Be,ur).call(r,n(this,De).box,e)):i=A(a=Z,Be,ur).call(a,[this.x,this.y,this.width,this.height],e),s.updateProperties(n(this,we),{bbox:i,root:{"data-main-rotation":e}}),s.updateProperties(n(this,Wt),{bbox:A(o=Z,Be,ur).call(o,n(this,ce).box,e),root:{"data-main-rotation":e}})}render(){if(this.div)return this.div;const e=super.render();n(this,da)&&(e.setAttribute("aria-label",n(this,da)),e.setAttribute("role","mark")),n(this,Bt)?e.classList.add("free"):this.div.addEventListener("keydown",A(this,j,Op).bind(this),{signal:this._uiManager._signal});const s=u(this,Vn,document.createElement("div"));e.append(s),s.setAttribute("aria-hidden","true"),s.className="internal",s.style.clipPath=n(this,Gn);const[i,r]=this.parentDimensions;return this.setDims(this.width*i,this.height*r),al(this,n(this,Vn),["pointerover","pointerleave"]),this.enableEditing(),e}pointerover(){this.isSelected||this.parent?.drawLayer.updateProperties(n(this,Wt),{rootClass:{hovered:!0}})}pointerleave(){this.isSelected||this.parent?.drawLayer.updateProperties(n(this,Wt),{rootClass:{hovered:!1}})}_moveCaret(e){switch(this.parent.unselect(this),e){case 0:case 2:A(this,j,Qh).call(this,!0);break;case 1:case 3:A(this,j,Qh).call(this,!1)}}select(){super.select(),n(this,Wt)&&this.parent?.drawLayer.updateProperties(n(this,Wt),{rootClass:{hovered:!1,selected:!0}})}unselect(){super.unselect(),n(this,Wt)&&(this.parent?.drawLayer.updateProperties(n(this,Wt),{rootClass:{selected:!1}}),n(this,Bt)||A(this,j,Qh).call(this,!1))}get _mustFixPosition(){return!n(this,Bt)}show(e=this._isVisible){super.show(e),this.parent&&(this.parent.drawLayer.updateProperties(n(this,we),{rootClass:{hidden:!e}}),this.parent.drawLayer.updateProperties(n(this,Wt),{rootClass:{hidden:!e}}))}static startHighlighting(e,s,{target:i,x:r,y:a}){const{x:o,y:h,width:l,height:c}=i.getBoundingClientRect(),d=new AbortController,p=e.combinedSignal(d),g=m=>{d.abort(),A(this,Be,zp).call(this,e,m)};window.addEventListener("blur",g,{signal:p}),window.addEventListener("pointerup",g,{signal:p}),window.addEventListener("pointerdown",Ce,{capture:!0,passive:!1,signal:p}),window.addEventListener("contextmenu",es,{signal:p}),i.addEventListener("pointermove",A(this,Be,$p).bind(this,e),{signal:p}),this._freeHighlight=new Uc({x:r,y:a},[o,h,l,c],e.scale,this._defaultThickness/2,s,.001),{id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=e.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:this._defaultColor,"fill-opacity":this._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:this._freeHighlight.toSVGPath()}},!0,!0)}static async deserialize(e,s,i){var v,y,w,_;let r=null;if(e instanceof fp){const{data:{quadPoints:x,rect:S,rotation:C,id:k,color:E,opacity:L,popupRef:O},parent:{page:{pageNumber:B}}}=e;r=e={annotationType:V.HIGHLIGHT,color:Array.from(E),opacity:L,quadPoints:x,boxes:null,pageIndex:B-1,rect:S.slice(0),rotation:C,id:k,deleted:!1,popupRef:O}}else if(e instanceof wd){const{data:{inkLists:x,rect:S,rotation:C,id:k,color:E,borderStyle:{rawWidth:L},popupRef:O},parent:{page:{pageNumber:B}}}=e;r=e={annotationType:V.HIGHLIGHT,color:Array.from(E),thickness:L,inkLists:x,boxes:null,pageIndex:B-1,rect:S.slice(0),rotation:C,id:k,deleted:!1,popupRef:O}}const{color:a,quadPoints:o,inkLists:h,opacity:l}=e,c=await super.deserialize(e,s,i);c.color=D.makeHexColor(...a),u(c,Ks,l||1),h&&u(c,Le,e.thickness),c.annotationElementId=e.id||null,c._initialData=r;const[d,p]=c.pageDimensions,[g,m]=c.pageTranslation;if(o){const x=u(c,Ys,[]);for(let S=0;S<o.length;S+=8)x.push({x:(o[S]-g)/d,y:1-(o[S+1]-m)/p,width:(o[S+2]-o[S])/d,height:(o[S+1]-o[S+5])/p});A(v=c,j,Yc).call(v),A(y=c,j,qi).call(y),c.rotate(c.rotation)}else if(h){u(c,Bt,!0);const x=h[0],S={x:x[0]-g,y:p-(x[1]-m)},C=new Uc(S,[0,0,d,p],1,n(c,Le)/2,!0,.001);for(let L=0,O=x.length;L<O;L+=2)S.x=x[L]-g,S.y=p-(x[L+1]-m),C.add(S);const{id:k,clipPathId:E}=s.drawLayer.draw({bbox:[0,0,1,1],root:{viewBox:"0 0 1 1",fill:c.color,"fill-opacity":c._defaultOpacity},rootClass:{highlight:!0,free:!0},path:{d:C.toSVGPath()}},!0,!0);A(w=c,j,Kh).call(w,{highlightOutlines:C.getOutlines(),highlightId:k,clipPathId:E}),A(_=c,j,qi).call(_)}return c}serialize(e=!1){if(this.isEmpty()||e)return null;if(this.deleted)return this.serializeDeleted();const s=this.getRect(0,0),i=gt._colorManager.convert(this.color),r={annotationType:V.HIGHLIGHT,color:i,opacity:n(this,Ks),thickness:n(this,Le),quadPoints:A(this,j,Bp).call(this),outlines:A(this,j,Hp).call(this,s),pageIndex:this.pageIndex,rect:s,rotation:A(this,j,Ba).call(this),structTreeParentId:this._structTreeParentId};return this.annotationElementId&&!A(this,j,jp).call(this,r)?null:(r.id=this.annotationElementId,r)}renderAnnotationElement(e){return e.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}};ha=new WeakMap,ph=new WeakMap,Ys=new WeakMap,Gn=new WeakMap,la=new WeakMap,ce=new WeakMap,fh=new WeakMap,gh=new WeakMap,Vn=new WeakMap,De=new WeakMap,we=new WeakMap,Bt=new WeakMap,ca=new WeakMap,Ks=new WeakMap,Wt=new WeakMap,da=new WeakMap,Le=new WeakMap,mh=new WeakMap,j=new WeakSet,Yc=function(){const e=new Gc(n(this,Ys),.001);u(this,De,e.getOutlines()),[this.x,this.y,this.width,this.height]=n(this,De).box;const s=new Gc(n(this,Ys),.0025,.001,this._uiManager.direction==="ltr");u(this,ce,s.getOutlines());const{lastPoint:i}=n(this,ce);u(this,ca,[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height])},Kh=function({highlightOutlines:e,highlightId:s,clipPathId:i}){var c,d;if(u(this,De,e),u(this,ce,e.getNewOutline(n(this,Le)/2+1.5,.0025)),s>=0)u(this,we,s),u(this,Gn,i),this.parent.drawLayer.finalizeDraw(s,{bbox:e.box,path:{d:e.toSVGPath()}}),u(this,Wt,this.parent.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:!0},bbox:n(this,ce).box,path:{d:n(this,ce).toSVGPath()}},!0));else if(this.parent){const p=this.parent.viewport.rotation;this.parent.drawLayer.updateProperties(n(this,we),{bbox:A(c=Z,Be,ur).call(c,n(this,De).box,(p-this.rotation+360)%360),path:{d:e.toSVGPath()}}),this.parent.drawLayer.updateProperties(n(this,Wt),{bbox:A(d=Z,Be,ur).call(d,n(this,ce).box,p),path:{d:n(this,ce).toSVGPath()}})}const[r,a,o,h]=e.box;switch(this.rotation){case 0:this.x=r,this.y=a,this.width=o,this.height=h;break;case 90:{const[p,g]=this.parentDimensions;this.x=a,this.y=1-r,this.width=o*g/p,this.height=h*p/g;break}case 180:this.x=1-r,this.y=1-a,this.width=o,this.height=h;break;case 270:{const[p,g]=this.parentDimensions;this.x=1-a,this.y=r,this.width=o*g/p,this.height=h*p/g;break}}const{lastPoint:l}=n(this,ce);u(this,ca,[(l[0]-r)/o,(l[1]-a)/h])},Lp=function(e){const s=(a,o)=>{this.color=a,u(this,Ks,o),this.parent?.drawLayer.updateProperties(n(this,we),{root:{fill:a,"fill-opacity":o}}),n(this,la)?.updateColor(a)},i=this.color,r=n(this,Ks);this.addCommands({cmd:s.bind(this,e,Z._defaultOpacity),undo:s.bind(this,i,r),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Y.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(e)},!0)},Fp=function(e){const s=n(this,Le),i=r=>{u(this,Le,r),A(this,j,Np).call(this,r)};this.addCommands({cmd:i.bind(this,e),undo:i.bind(this,s),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:Y.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0}),this._reportTelemetry({action:"thickness_changed",thickness:e},!0)},Np=function(e){if(!n(this,Bt))return;A(this,j,Kh).call(this,{highlightOutlines:n(this,De).getNewOutline(e/2)}),this.fixAndSetPosition();const[s,i]=this.parentDimensions;this.setDims(this.width*s,this.height*i)},Kc=function(){n(this,we)!==null&&this.parent&&(this.parent.drawLayer.remove(n(this,we)),u(this,we,null),this.parent.drawLayer.remove(n(this,Wt)),u(this,Wt,null))},qi=function(e=this.parent){n(this,we)===null&&({id:Kt(this,we)._,clipPathId:Kt(this,Gn)._}=e.drawLayer.draw({bbox:n(this,De).box,root:{viewBox:"0 0 1 1",fill:this.color,"fill-opacity":n(this,Ks)},rootClass:{highlight:!0,free:n(this,Bt)},path:{d:n(this,De).toSVGPath()}},!1,!0),u(this,Wt,e.drawLayer.drawOutline({rootClass:{highlightOutline:!0,free:n(this,Bt)},bbox:n(this,ce).box,path:{d:n(this,ce).toSVGPath()}},n(this,Bt))),n(this,Vn)&&(n(this,Vn).style.clipPath=n(this,Gn)))},Be=new WeakSet,ur=function([e,s,i,r],a){switch(a){case 90:return[1-s-r,e,r,i];case 180:return[1-e-i,1-s-r,i,r];case 270:return[s,1-e-i,r,i]}return[e,s,i,r]},Op=function(e){Z._keyboardManager.exec(this,e)},Qh=function(e){if(!n(this,ha))return;const s=window.getSelection();e?s.setPosition(n(this,ha),n(this,ph)):s.setPosition(n(this,fh),n(this,gh))},Ba=function(){return n(this,Bt)?this.rotation:0},Bp=function(){if(n(this,Bt))return null;const[e,s]=this.pageDimensions,[i,r]=this.pageTranslation,a=n(this,Ys),o=new Float32Array(8*a.length);let h=0;for(const{x:l,y:c,width:d,height:p}of a){const g=l*e+i,m=(1-c)*s+r;o[h]=o[h+4]=g,o[h+1]=o[h+3]=m,o[h+2]=o[h+6]=g+d*e,o[h+5]=o[h+7]=m-p*s,h+=8}return o},Hp=function(e){return n(this,De).serialize(e,A(this,j,Ba).call(this))},$p=function(e,s){this._freeHighlight.add(s)&&e.drawLayer.updateProperties(this._freeHighlightId,{path:{d:this._freeHighlight.toSVGPath()}})},zp=function(e,s){this._freeHighlight.isEmpty()?e.drawLayer.remove(this._freeHighlightId):e.createAndAddNewEditor(s,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"}),this._freeHighlightId=-1,this._freeHighlight=null,this._freeHighlightClipId=""},jp=function(e){const{color:s}=this._initialData;return e.color.some((i,r)=>i!==s[r])},b(Z,Be),F(Z,"_defaultColor",null),F(Z,"_defaultOpacity",1),F(Z,"_defaultThickness",12),F(Z,"_type","highlight"),F(Z,"_editorType",V.HIGHLIGHT),F(Z,"_freeHighlightId",-1),F(Z,"_freeHighlight",null),F(Z,"_freeHighlightClipId","");let ul=Z;var Un;class em{constructor(){b(this,Un,Object.create(null))}updateProperty(t,e){this[t]=e,this.updateSVGProperty(t,e)}updateProperties(t){if(t)for(const[e,s]of Object.entries(t))this.updateProperty(e,s)}updateSVGProperty(t,e){n(this,Un)[t]=e}toSVGProperties(){const t=n(this,Un);return u(this,Un,Object.create(null)),{root:t}}reset(){u(this,Un,Object.create(null))}updateAll(t=this){this.updateProperties(t)}clone(){at("Not implemented")}}Un=new WeakMap;var _e,ua,It,Wn,qn,Ii,Di,Li,Xn,Q,Jc,Zc,td,Ha,Gp,Jh,$a,pr;const P=class P extends gt{constructor(e){super(e);b(this,Q);b(this,_e,null);b(this,ua);F(this,"_drawId",null);u(this,ua,e.mustBeCommitted||!1),e.drawOutlines&&(A(this,Q,Jc).call(this,e),A(this,Q,Ha).call(this))}static _mergeSVGProperties(e,s){const i=new Set(Object.keys(e));for(const[r,a]of Object.entries(s))i.has(r)?Object.assign(e[r],a):e[r]=a;return e}static getDefaultDrawingOptions(e){at("Not implemented")}static get typesMap(){at("Not implemented")}static get isDrawer(){return!0}static get supportMultipleDrawings(){return!1}static updateDefaultParams(e,s){const i=this.typesMap.get(e);i&&this._defaultDrawingOptions.updateProperty(i,s),this._currentParent&&(n(P,It).updateProperty(i,s),this._currentParent.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}updateParams(e,s){const i=this.constructor.typesMap.get(e);i&&this._updateProperty(e,i,s)}static get defaultPropertiesToUpdate(){const e=[],s=this._defaultDrawingOptions;for(const[i,r]of this.typesMap)e.push([i,s[r]]);return e}get propertiesToUpdate(){const e=[],{_drawingOptions:s}=this;for(const[i,r]of this.constructor.typesMap)e.push([i,s[r]]);return e}_updateProperty(e,s,i){const r=this._drawingOptions,a=r[s],o=h=>{r.updateProperty(s,h);const l=n(this,_e).updateProperty(s,h);l&&A(this,Q,$a).call(this,l),this.parent?.drawLayer.updateProperties(this._drawId,r.toSVGProperties())};this.addCommands({cmd:o.bind(this,i),undo:o.bind(this,a),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:e,overwriteIfSameType:!0,keepUndo:!0})}_onResizing(){this.parent?.drawLayer.updateProperties(this._drawId,P._mergeSVGProperties(n(this,_e).getPathResizingSVGProperties(A(this,Q,Jh).call(this)),{bbox:A(this,Q,pr).call(this)}))}_onResized(){this.parent?.drawLayer.updateProperties(this._drawId,P._mergeSVGProperties(n(this,_e).getPathResizedSVGProperties(A(this,Q,Jh).call(this)),{bbox:A(this,Q,pr).call(this)}))}_onTranslating(e,s){this.parent?.drawLayer.updateProperties(this._drawId,{bbox:A(this,Q,pr).call(this,e,s)})}_onTranslated(){this.parent?.drawLayer.updateProperties(this._drawId,P._mergeSVGProperties(n(this,_e).getPathTranslatedSVGProperties(A(this,Q,Jh).call(this),this.parentDimensions),{bbox:A(this,Q,pr).call(this)}))}_onStartDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!0}})}_onStopDragging(){this.parent?.drawLayer.updateProperties(this._drawId,{rootClass:{moving:!1}})}commit(){super.commit(),this.disableEditMode(),this.disableEditing()}disableEditing(){super.disableEditing(),this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing(),this.div.classList.toggle("disabled",!1)}getBaseTranslation(){return[0,0]}get isResizable(){return!0}onceAdded(e){this.annotationElementId||this.parent.addUndoableEditor(this),this._isDraggable=!0,n(this,ua)&&(u(this,ua,!1),this.commit(),this.parent.setSelected(this),e&&this.isOnScreen&&this.div.focus())}remove(){A(this,Q,td).call(this),super.remove()}rebuild(){this.parent&&(super.rebuild(),this.div!==null&&(A(this,Q,Ha).call(this),A(this,Q,$a).call(this,n(this,_e).box),this.isAttachedToDOM||this.parent.add(this)))}setParent(e){let s=!1;this.parent&&!e?(this._uiManager.removeShouldRescale(this),A(this,Q,td).call(this)):e&&(this._uiManager.addShouldRescale(this),A(this,Q,Ha).call(this,e),s=!this.parent&&this.div?.classList.contains("selectedEditor")),super.setParent(e),s&&this.select()}rotate(){this.parent&&this.parent.drawLayer.updateProperties(this._drawId,P._mergeSVGProperties({bbox:A(this,Q,pr).call(this)},n(this,_e).updateRotation((this.parentRotation-this.rotation+360)%360)))}onScaleChanging(){this.parent&&A(this,Q,$a).call(this,n(this,_e).updateParentDimensions(this.parentDimensions,this.parent.scale))}static onScaleChangingWhenDrawing(){}render(){if(this.div)return this.div;const e=super.render();e.classList.add("draw");const s=document.createElement("div");e.append(s),s.setAttribute("aria-hidden","true"),s.className="internal";const[i,r]=this.parentDimensions;return this.setDims(this.width*i,this.height*r),this._uiManager.addShouldRescale(this),this.disableEditing(),e}static createDrawerInstance(e,s,i,r,a){at("Not implemented")}static startDrawing(e,s,i,r){const{target:a,offsetX:o,offsetY:h,pointerId:l,pointerType:c}=r;if(n(P,Di)&&n(P,Di)!==c)return;const{viewport:{rotation:d}}=e,{width:p,height:g}=a.getBoundingClientRect(),m=u(P,Wn,new AbortController),v=e.combinedSignal(m);n(P,Ii)||u(P,Ii,l),n(P,Di)??u(P,Di,c),window.addEventListener("pointerup",y=>{n(P,Ii)===y.pointerId?this._endDraw(y):n(P,Li)?.delete(y.pointerId)},{signal:v}),window.addEventListener("pointercancel",y=>{n(P,Ii)===y.pointerId?this._currentParent.endDrawingSession():n(P,Li)?.delete(y.pointerId)},{signal:v}),window.addEventListener("pointerdown",y=>{n(P,Di)===y.pointerType&&((n(P,Li)||u(P,Li,new Set)).add(y.pointerId),n(P,It).isCancellable()&&(n(P,It).removeLastElement(),n(P,It).isEmpty()?this._currentParent.endDrawingSession(!0):this._endDraw(null)))},{capture:!0,passive:!1,signal:v}),window.addEventListener("contextmenu",es,{signal:v}),a.addEventListener("pointermove",this._drawMove.bind(this),{signal:v}),a.addEventListener("touchmove",y=>{y.timeStamp===n(P,Xn)&&Ce(y)},{signal:v}),e.toggleDrawing(),s._editorUndoBar?.hide(),n(P,It)?e.drawLayer.updateProperties(this._currentDrawId,n(P,It).startNew(o,h,p,g,d)):(s.updateUIForDefaultProperties(this),u(P,It,this.createDrawerInstance(o,h,p,g,d)),u(P,qn,this.getDefaultDrawingOptions()),this._currentParent=e,{id:this._currentDrawId}=e.drawLayer.draw(this._mergeSVGProperties(n(P,qn).toSVGProperties(),n(P,It).defaultSVGProperties),!0,!1))}static _drawMove(e){if(u(P,Xn,-1),!n(P,It))return;const{offsetX:s,offsetY:i,pointerId:r}=e;n(P,Ii)===r&&(n(P,Li)?.size>=1?this._endDraw(e):(this._currentParent.drawLayer.updateProperties(this._currentDrawId,n(P,It).add(s,i)),u(P,Xn,e.timeStamp),Ce(e)))}static _cleanup(e){e&&(this._currentDrawId=-1,this._currentParent=null,u(P,It,null),u(P,qn,null),u(P,Di,null),u(P,Xn,NaN)),n(P,Wn)&&(n(P,Wn).abort(),u(P,Wn,null),u(P,Ii,NaN),u(P,Li,null))}static _endDraw(e){const s=this._currentParent;if(s)if(s.toggleDrawing(!0),this._cleanup(!1),e&&s.drawLayer.updateProperties(this._currentDrawId,n(P,It).end(e.offsetX,e.offsetY)),this.supportMultipleDrawings){const i=n(P,It),r=this._currentDrawId,a=i.getLastElement();s.addCommands({cmd:()=>{s.drawLayer.updateProperties(r,i.setLastElement(a))},undo:()=>{s.drawLayer.updateProperties(r,i.removeLastElement())},mustExec:!1,type:Y.DRAW_STEP})}else this.endDrawing(!1)}static endDrawing(e){const s=this._currentParent;if(!s)return null;if(s.toggleDrawing(!0),s.cleanUndoStack(Y.DRAW_STEP),!n(P,It).isEmpty()){const{pageDimensions:[i,r],scale:a}=s,o=s.createAndAddNewEditor({offsetX:0,offsetY:0},!1,{drawId:this._currentDrawId,drawOutlines:n(P,It).getOutlines(i*a,r*a,a,this._INNER_MARGIN),drawingOptions:n(P,qn),mustBeCommitted:!e});return this._cleanup(!0),o}return s.drawLayer.remove(this._currentDrawId),this._cleanup(!0),null}createDrawingOptions(e){}static deserializeDraw(e,s,i,r,a,o){at("Not implemented")}static async deserialize(e,s,i){var d,p;const{rawDims:{pageWidth:r,pageHeight:a,pageX:o,pageY:h}}=s.viewport,l=this.deserializeDraw(o,h,r,a,this._INNER_MARGIN,e),c=await super.deserialize(e,s,i);return c.createDrawingOptions(e),A(d=c,Q,Jc).call(d,{drawOutlines:l}),A(p=c,Q,Ha).call(p),c.onScaleChanging(),c.rotate(),c}serializeDraw(e){const[s,i]=this.pageTranslation,[r,a]=this.pageDimensions;return n(this,_e).serialize([s,i,r,a],e)}renderAnnotationElement(e){return e.updateEdited({rect:this.getRect(0,0)}),null}static canCreateNewEmptyEditor(){return!1}};_e=new WeakMap,ua=new WeakMap,It=new WeakMap,Wn=new WeakMap,qn=new WeakMap,Ii=new WeakMap,Di=new WeakMap,Li=new WeakMap,Xn=new WeakMap,Q=new WeakSet,Jc=function({drawOutlines:e,drawId:s,drawingOptions:i}){u(this,_e,e),this._drawingOptions||(this._drawingOptions=i),s>=0?(this._drawId=s,this.parent.drawLayer.finalizeDraw(s,e.defaultProperties)):this._drawId=A(this,Q,Zc).call(this,e,this.parent),A(this,Q,$a).call(this,e.box)},Zc=function(e,s){const{id:i}=s.drawLayer.draw(P._mergeSVGProperties(this._drawingOptions.toSVGProperties(),e.defaultSVGProperties),!1,!1);return i},td=function(){this._drawId!==null&&this.parent&&(this.parent.drawLayer.remove(this._drawId),this._drawId=null,this._drawingOptions.reset())},Ha=function(e=this.parent){(this._drawId===null||this.parent!==e)&&(this._drawId===null?(this._drawingOptions.updateAll(),this._drawId=A(this,Q,Zc).call(this,n(this,_e),e)):this.parent.drawLayer.updateParent(this._drawId,e.drawLayer))},Gp=function([e,s,i,r]){const{parentDimensions:[a,o],rotation:h}=this;switch(h){case 90:return[s,1-e,i*(o/a),r*(a/o)];case 180:return[1-e,1-s,i,r];case 270:return[1-s,e,i*(o/a),r*(a/o)];default:return[e,s,i,r]}},Jh=function(){const{x:e,y:s,width:i,height:r,parentDimensions:[a,o],rotation:h}=this;switch(h){case 90:return[1-s,e,i*(a/o),r*(o/a)];case 180:return[1-e,1-s,i,r];case 270:return[s,1-e,i*(a/o),r*(o/a)];default:return[e,s,i,r]}},$a=function(e){if([this.x,this.y,this.width,this.height]=A(this,Q,Gp).call(this,e),this.div){this.fixAndSetPosition();const[s,i]=this.parentDimensions;this.setDims(this.width*s,this.height*i)}this._onResized()},pr=function(){const{x:e,y:s,width:i,height:r,rotation:a,parentRotation:o,parentDimensions:[h,l]}=this;switch((4*a+o)/90){case 1:return[1-s-r,e,r,i];case 2:return[1-e-i,1-s-r,i,r];case 3:return[s,1-e-i,r,i];case 4:return[e,s-i*(h/l),r*(l/h),i*(h/l)];case 5:return[1-s,e,i*(h/l),r*(l/h)];case 6:return[1-e-r*(l/h),1-s,r*(l/h),i*(h/l)];case 7:return[s-i*(h/l),1-e-r*(l/h),i*(h/l),r*(l/h)];case 8:return[e-i,s-r,i,r];case 9:return[1-s,e-i,r,i];case 10:return[1-e,1-s,i,r];case 11:return[s-r,1-e,r,i];case 12:return[e-r*(l/h),s,r*(l/h),i*(h/l)];case 13:return[1-s-i*(h/l),e-r*(l/h),i*(h/l),r*(l/h)];case 14:return[1-e,1-s-i*(h/l),r*(l/h),i*(h/l)];case 15:return[s,1-e,i*(h/l),r*(l/h)];default:return[e,s,i,r]}},F(P,"_currentDrawId",-1),F(P,"_currentParent",null),b(P,It,null),b(P,Wn,null),b(P,qn,null),b(P,Ii,NaN),b(P,Di,null),b(P,Li,null),b(P,Xn,NaN),F(P,"_INNER_MARGIN",3);let Qc=P;var ms,Dt,Lt,Yn,pa,Zt,Ht,Fe,Kn,Qn,Jn,fa,Zh;class sm{constructor(t,e,s,i,r,a){b(this,fa);b(this,ms,new Float64Array(6));b(this,Dt);b(this,Lt);b(this,Yn);b(this,pa);b(this,Zt);b(this,Ht,"");b(this,Fe,0);b(this,Kn,new pl);b(this,Qn);b(this,Jn);u(this,Qn,s),u(this,Jn,i),u(this,Yn,r),u(this,pa,a),[t,e]=A(this,fa,Zh).call(this,t,e);const o=u(this,Dt,[NaN,NaN,NaN,NaN,t,e]);u(this,Zt,[t,e]),u(this,Lt,[{line:o,points:n(this,Zt)}]),n(this,ms).set(o,0)}updateProperty(t,e){t==="stroke-width"&&u(this,pa,e)}isEmpty(){return!n(this,Lt)||n(this,Lt).length===0}isCancellable(){return n(this,Zt).length<=10}add(t,e){[t,e]=A(this,fa,Zh).call(this,t,e);const[s,i,r,a]=n(this,ms).subarray(2,6),o=t-r,h=e-a;return Math.hypot(n(this,Qn)*o,n(this,Jn)*h)<=2?null:(n(this,Zt).push(t,e),isNaN(s)?(n(this,ms).set([r,a,t,e],2),n(this,Dt).push(NaN,NaN,NaN,NaN,t,e),{path:{d:this.toSVGPath()}}):(isNaN(n(this,ms)[0])&&n(this,Dt).splice(6,6),n(this,ms).set([s,i,r,a,t,e],0),n(this,Dt).push(...T.createBezierPoints(s,i,r,a,t,e)),{path:{d:this.toSVGPath()}}))}end(t,e){return this.add(t,e)||(n(this,Zt).length===2?{path:{d:this.toSVGPath()}}:null)}startNew(t,e,s,i,r){u(this,Qn,s),u(this,Jn,i),u(this,Yn,r),[t,e]=A(this,fa,Zh).call(this,t,e);const a=u(this,Dt,[NaN,NaN,NaN,NaN,t,e]);u(this,Zt,[t,e]);const o=n(this,Lt).at(-1);return o&&(o.line=new Float32Array(o.line),o.points=new Float32Array(o.points)),n(this,Lt).push({line:a,points:n(this,Zt)}),n(this,ms).set(a,0),u(this,Fe,0),this.toSVGPath(),null}getLastElement(){return n(this,Lt).at(-1)}setLastElement(t){return n(this,Lt)?(n(this,Lt).push(t),u(this,Dt,t.line),u(this,Zt,t.points),u(this,Fe,0),{path:{d:this.toSVGPath()}}):n(this,Kn).setLastElement(t)}removeLastElement(){if(!n(this,Lt))return n(this,Kn).removeLastElement();n(this,Lt).pop(),u(this,Ht,"");for(let t=0,e=n(this,Lt).length;t<e;t++){const{line:s,points:i}=n(this,Lt)[t];u(this,Dt,s),u(this,Zt,i),u(this,Fe,0),this.toSVGPath()}return{path:{d:n(this,Ht)}}}toSVGPath(){const t=T.svgRound(n(this,Dt)[4]),e=T.svgRound(n(this,Dt)[5]);if(n(this,Zt).length===2)return u(this,Ht,`${n(this,Ht)} M ${t} ${e} Z`),n(this,Ht);if(n(this,Zt).length<=6){const i=n(this,Ht).lastIndexOf("M");u(this,Ht,`${n(this,Ht).slice(0,i)} M ${t} ${e}`),u(this,Fe,6)}if(n(this,Zt).length===4){const i=T.svgRound(n(this,Dt)[10]),r=T.svgRound(n(this,Dt)[11]);return u(this,Ht,`${n(this,Ht)} L ${i} ${r}`),u(this,Fe,12),n(this,Ht)}const s=[];n(this,Fe)===0&&(s.push(`M ${t} ${e}`),u(this,Fe,6));for(let i=n(this,Fe),r=n(this,Dt).length;i<r;i+=6){const[a,o,h,l,c,d]=n(this,Dt).slice(i,i+6).map(T.svgRound);s.push(`C${a} ${o} ${h} ${l} ${c} ${d}`)}return u(this,Ht,n(this,Ht)+s.join(" ")),u(this,Fe,n(this,Dt).length),n(this,Ht)}getOutlines(t,e,s,i){const r=n(this,Lt).at(-1);return r.line=new Float32Array(r.line),r.points=new Float32Array(r.points),n(this,Kn).build(n(this,Lt),t,e,s,n(this,Yn),n(this,pa),i),u(this,ms,null),u(this,Dt,null),u(this,Lt,null),u(this,Ht,null),n(this,Kn)}get defaultSVGProperties(){return{root:{viewBox:"0 0 10000 10000"},rootClass:{draw:!0},bbox:[0,0,1,1]}}}ms=new WeakMap,Dt=new WeakMap,Lt=new WeakMap,Yn=new WeakMap,pa=new WeakMap,Zt=new WeakMap,Ht=new WeakMap,Fe=new WeakMap,Kn=new WeakMap,Qn=new WeakMap,Jn=new WeakMap,fa=new WeakSet,Zh=function(t,e){return T._normalizePoint(t,e,n(this,Qn),n(this,Jn),n(this,Yn))};var te,bh,Ah,xe,bs,As,ga,ma,ba,zt,Ms,Vp,Up,Wp;const Cd=class Cd extends T{constructor(){super(...arguments);b(this,zt);b(this,te);b(this,bh,0);b(this,Ah);b(this,xe);b(this,bs);b(this,As);b(this,ga);b(this,ma);b(this,ba)}build(e,s,i,r,a,o,h){u(this,bs,s),u(this,As,i),u(this,ga,r),u(this,ma,a),u(this,ba,o),u(this,Ah,h??0),u(this,xe,e),A(this,zt,Up).call(this)}setLastElement(e){return n(this,xe).push(e),{path:{d:this.toSVGPath()}}}removeLastElement(){return n(this,xe).pop(),{path:{d:this.toSVGPath()}}}toSVGPath(){const e=[];for(const{line:s}of n(this,xe))if(e.push(`M${T.svgRound(s[4])} ${T.svgRound(s[5])}`),s.length!==6)if(s.length!==12)for(let i=6,r=s.length;i<r;i+=6){const[a,o,h,l,c,d]=s.subarray(i,i+6).map(T.svgRound);e.push(`C${a} ${o} ${h} ${l} ${c} ${d}`)}else e.push(`L${T.svgRound(s[10])} ${T.svgRound(s[11])}`);else e.push("Z");return e.join("")}serialize([e,s,i,r],a){const o=[],h=[],[l,c,d,p]=A(this,zt,Vp).call(this);let g,m,v,y,w,_,x,S,C;switch(n(this,ma)){case 0:C=T._rescale,g=e,m=s+r,v=i,y=-r,w=e+l*i,_=s+(1-c-p)*r,x=e+(l+d)*i,S=s+(1-c)*r;break;case 90:C=T._rescaleAndSwap,g=e,m=s,v=i,y=r,w=e+c*i,_=s+l*r,x=e+(c+p)*i,S=s+(l+d)*r;break;case 180:C=T._rescale,g=e+i,m=s,v=-i,y=r,w=e+(1-l-d)*i,_=s+c*r,x=e+(1-l)*i,S=s+(c+p)*r;break;case 270:C=T._rescaleAndSwap,g=e+i,m=s+r,v=-i,y=-r,w=e+(1-c-p)*i,_=s+(1-l-d)*r,x=e+(1-c)*i,S=s+(1-l)*r}for(const{line:k,points:E}of n(this,xe))o.push(C(k,g,m,v,y,a?new Array(k.length):null)),h.push(C(E,g,m,v,y,a?new Array(E.length):null));return{lines:o,points:h,rect:[w,_,x,S]}}static deserialize(e,s,i,r,a,{paths:{lines:o,points:h},rotation:l,thickness:c}){const d=[];let p,g,m,v,y;switch(l){case 0:y=T._rescale,p=-e/i,g=s/r+1,m=1/i,v=-1/r;break;case 90:y=T._rescaleAndSwap,p=-s/r,g=-e/i,m=1/r,v=1/i;break;case 180:y=T._rescale,p=e/i+1,g=-s/r,m=-1/i,v=1/r;break;case 270:y=T._rescaleAndSwap,p=s/r+1,g=e/i+1,m=-1/r,v=-1/i}if(!o){o=[];for(const _ of h){const x=_.length;if(x===2){o.push(new Float32Array([NaN,NaN,NaN,NaN,_[0],_[1]]));continue}if(x===4){o.push(new Float32Array([NaN,NaN,NaN,NaN,_[0],_[1],NaN,NaN,NaN,NaN,_[2],_[3]]));continue}const S=new Float32Array(3*(x-2));o.push(S);let[C,k,E,L]=_.subarray(0,4);S.set([NaN,NaN,NaN,NaN,C,k],0);for(let O=4;O<x;O+=2){const B=_[O],G=_[O+1];S.set(T.createBezierPoints(C,k,E,L,B,G),3*(O-2)),[C,k,E,L]=[E,L,B,G]}}}for(let _=0,x=o.length;_<x;_++)d.push({line:y(o[_].map(S=>S??NaN),p,g,m,v),points:y(h[_].map(S=>S??NaN),p,g,m,v)});const w=new Cd;return w.build(d,i,r,1,l,c,a),w}get box(){return n(this,te)}updateProperty(e,s){return e==="stroke-width"?A(this,zt,Wp).call(this,s):null}updateParentDimensions([e,s],i){const[r,a]=A(this,zt,Ms).call(this);u(this,bs,e),u(this,As,s),u(this,ga,i);const[o,h]=A(this,zt,Ms).call(this),l=o-r,c=h-a,d=n(this,te);return d[0]-=l,d[1]-=c,d[2]+=2*l,d[3]+=2*c,d}updateRotation(e){return u(this,bh,e),{path:{transform:this.rotationTransform}}}get viewBox(){return n(this,te).map(T.svgRound).join(" ")}get defaultProperties(){const[e,s]=n(this,te);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${T.svgRound(e)} ${T.svgRound(s)}`}}}get rotationTransform(){const[,,e,s]=n(this,te);let i=0,r=0,a=0,o=0,h=0,l=0;switch(n(this,bh)){case 90:r=s/e,a=-e/s,h=e;break;case 180:i=-1,o=-1,h=e,l=s;break;case 270:r=-s/e,a=e/s,l=s;break;default:return""}return`matrix(${i} ${r} ${a} ${o} ${T.svgRound(h)} ${T.svgRound(l)})`}getPathResizingSVGProperties([e,s,i,r]){const[a,o]=A(this,zt,Ms).call(this),[h,l,c,d]=n(this,te);if(Math.abs(c-a)<=T.PRECISION||Math.abs(d-o)<=T.PRECISION){const y=e+i/2-(h+c/2),w=s+r/2-(l+d/2);return{path:{"transform-origin":`${T.svgRound(e)} ${T.svgRound(s)}`,transform:`${this.rotationTransform} translate(${y} ${w})`}}}const p=(i-2*a)/(c-2*a),g=(r-2*o)/(d-2*o),m=c/i,v=d/r;return{path:{"transform-origin":`${T.svgRound(h)} ${T.svgRound(l)}`,transform:`${this.rotationTransform} scale(${m} ${v}) translate(${T.svgRound(a)} ${T.svgRound(o)}) scale(${p} ${g}) translate(${T.svgRound(-a)} ${T.svgRound(-o)})`}}}getPathResizedSVGProperties([e,s,i,r]){const[a,o]=A(this,zt,Ms).call(this),h=n(this,te),[l,c,d,p]=h;if(h[0]=e,h[1]=s,h[2]=i,h[3]=r,Math.abs(d-a)<=T.PRECISION||Math.abs(p-o)<=T.PRECISION){const w=e+i/2-(l+d/2),_=s+r/2-(c+p/2);for(const{line:x,points:S}of n(this,xe))T._translate(x,w,_,x),T._translate(S,w,_,S);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${T.svgRound(e)} ${T.svgRound(s)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}const g=(i-2*a)/(d-2*a),m=(r-2*o)/(p-2*o),v=-g*(l+a)+e+a,y=-m*(c+o)+s+o;if(g!==1||m!==1||v!==0||y!==0)for(const{line:w,points:_}of n(this,xe))T._rescale(w,v,y,g,m,w),T._rescale(_,v,y,g,m,_);return{root:{viewBox:this.viewBox},path:{"transform-origin":`${T.svgRound(e)} ${T.svgRound(s)}`,transform:this.rotationTransform||null,d:this.toSVGPath()}}}getPathTranslatedSVGProperties([e,s],i){const[r,a]=i,o=n(this,te),h=e-o[0],l=s-o[1];if(n(this,bs)===r&&n(this,As)===a)for(const{line:c,points:d}of n(this,xe))T._translate(c,h,l,c),T._translate(d,h,l,d);else{const c=n(this,bs)/r,d=n(this,As)/a;u(this,bs,r),u(this,As,a);for(const{line:p,points:g}of n(this,xe))T._rescale(p,h,l,c,d,p),T._rescale(g,h,l,c,d,g);o[2]*=c,o[3]*=d}return o[0]=e,o[1]=s,{root:{viewBox:this.viewBox},path:{d:this.toSVGPath(),"transform-origin":`${T.svgRound(e)} ${T.svgRound(s)}`}}}get defaultSVGProperties(){const e=n(this,te);return{root:{viewBox:this.viewBox},rootClass:{draw:!0},path:{d:this.toSVGPath(),"transform-origin":`${T.svgRound(e[0])} ${T.svgRound(e[1])}`,transform:this.rotationTransform||null},bbox:e}}};te=new WeakMap,bh=new WeakMap,Ah=new WeakMap,xe=new WeakMap,bs=new WeakMap,As=new WeakMap,ga=new WeakMap,ma=new WeakMap,ba=new WeakMap,zt=new WeakSet,Ms=function(e=n(this,ba)){const s=n(this,Ah)+e/2*n(this,ga);return n(this,ma)%180==0?[s/n(this,bs),s/n(this,As)]:[s/n(this,As),s/n(this,bs)]},Vp=function(){const[e,s,i,r]=n(this,te),[a,o]=A(this,zt,Ms).call(this,0);return[e+a,s+o,i-2*a,r-2*o]},Up=function(){const e=u(this,te,new Float32Array([1/0,1/0,-1/0,-1/0]));for(const{line:r}of n(this,xe)){if(r.length<=12){for(let h=4,l=r.length;h<l;h+=6){const[c,d]=r.subarray(h,h+2);e[0]=Math.min(e[0],c),e[1]=Math.min(e[1],d),e[2]=Math.max(e[2],c),e[3]=Math.max(e[3],d)}continue}let a=r[4],o=r[5];for(let h=6,l=r.length;h<l;h+=6){const[c,d,p,g,m,v]=r.subarray(h,h+6);D.bezierBoundingBox(a,o,c,d,p,g,m,v,e),a=m,o=v}}const[s,i]=A(this,zt,Ms).call(this);e[0]=Math.min(1,Math.max(0,e[0]-s)),e[1]=Math.min(1,Math.max(0,e[1]-i)),e[2]=Math.min(1,Math.max(0,e[2]+s)),e[3]=Math.min(1,Math.max(0,e[3]+i)),e[2]-=e[0],e[3]-=e[1]},Wp=function(e){const[s,i]=A(this,zt,Ms).call(this);u(this,ba,e);const[r,a]=A(this,zt,Ms).call(this),[o,h]=[r-s,a-i],l=n(this,te);return l[0]-=o,l[1]-=h,l[2]+=2*o,l[3]+=2*h,l};let pl=Cd;var Aa;const Md=class Md extends em{constructor(e){super();b(this,Aa);u(this,Aa,e),super.updateProperties({fill:"none",stroke:gt._defaultLineColor,"stroke-opacity":1,"stroke-width":1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-miterlimit":10})}updateSVGProperty(e,s){e==="stroke-width"&&(s??(s=this["stroke-width"]),s*=n(this,Aa).realScale),super.updateSVGProperty(e,s)}clone(){const e=new Md(n(this,Aa));return e.updateAll(this),e}};Aa=new WeakMap;let ed=Md;var Pl,qp;const gr=class gr extends Qc{constructor(e){super({...e,name:"inkEditor"});b(this,Pl);this._willKeepAspectRatio=!0}static initialize(e,s){gt.initialize(e,s),this._defaultDrawingOptions=new ed(s.viewParameters)}static getDefaultDrawingOptions(e){const s=this._defaultDrawingOptions.clone();return s.updateProperties(e),s}static get supportMultipleDrawings(){return!0}static get typesMap(){return X(this,"typesMap",new Map([[Y.INK_THICKNESS,"stroke-width"],[Y.INK_COLOR,"stroke"],[Y.INK_OPACITY,"stroke-opacity"]]))}static createDrawerInstance(e,s,i,r,a){return new sm(e,s,i,r,a,this._defaultDrawingOptions["stroke-width"])}static deserializeDraw(e,s,i,r,a,o){return pl.deserialize(e,s,i,r,a,o)}static async deserialize(e,s,i){let r=null;if(e instanceof wd){const{data:{inkLists:o,rect:h,rotation:l,id:c,color:d,opacity:p,borderStyle:{rawWidth:g},popupRef:m},parent:{page:{pageNumber:v}}}=e;r=e={annotationType:V.INK,color:Array.from(d),thickness:g,opacity:p,paths:{points:o},boxes:null,pageIndex:v-1,rect:h.slice(0),rotation:l,id:c,deleted:!1,popupRef:m}}const a=await super.deserialize(e,s,i);return a.annotationElementId=e.id||null,a._initialData=r,a}onScaleChanging(){if(!this.parent)return;super.onScaleChanging();const{_drawId:e,_drawingOptions:s,parent:i}=this;s.updateSVGProperty("stroke-width"),i.drawLayer.updateProperties(e,s.toSVGProperties())}static onScaleChangingWhenDrawing(){const e=this._currentParent;e&&(super.onScaleChangingWhenDrawing(),this._defaultDrawingOptions.updateSVGProperty("stroke-width"),e.drawLayer.updateProperties(this._currentDrawId,this._defaultDrawingOptions.toSVGProperties()))}createDrawingOptions({color:e,thickness:s,opacity:i}){this._drawingOptions=gr.getDefaultDrawingOptions({stroke:D.makeHexColor(...e),"stroke-width":s,"stroke-opacity":i})}serialize(e=!1){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const{lines:s,points:i,rect:r}=this.serializeDraw(e),{_drawingOptions:{stroke:a,"stroke-opacity":o,"stroke-width":h}}=this,l={annotationType:V.INK,color:gt._colorManager.convert(a),opacity:o,thickness:h,paths:{lines:s,points:i},pageIndex:this.pageIndex,rect:r,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return e?l:this.annotationElementId&&!A(this,Pl,qp).call(this,l)?null:(l.id=this.annotationElementId,l)}renderAnnotationElement(e){const{points:s,rect:i}=this.serializeDraw(!1);return e.updateEdited({rect:i,thickness:this._drawingOptions["stroke-width"],points:s}),null}};Pl=new WeakSet,qp=function(e){const{color:s,thickness:i,opacity:r,pageIndex:a}=this._initialData;return this._hasBeenMoved||this._hasBeenResized||e.color.some((o,h)=>o!==s[h])||e.thickness!==i||e.opacity!==r||e.pageIndex!==a},F(gr,"_type","ink"),F(gr,"_editorType",V.INK),F(gr,"_defaultDrawingOptions",null);let sd=gr;var ft,$t,Fi,Qs,Ni,va,vs,ys,Se,ya,J,za,ja,tl,nd,el,rd,sl,Xp;const Va=class Va extends gt{constructor(e){super({...e,name:"stampEditor"});b(this,J);b(this,ft,null);b(this,$t,null);b(this,Fi,null);b(this,Qs,null);b(this,Ni,null);b(this,va,"");b(this,vs,null);b(this,ys,null);b(this,Se,!1);b(this,ya,!1);u(this,Qs,e.bitmapUrl),u(this,Ni,e.bitmapFile)}static initialize(e,s){gt.initialize(e,s)}static get supportedTypes(){return X(this,"supportedTypes",["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"].map(e=>`image/${e}`))}static get supportedTypesStr(){return X(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(e){return this.supportedTypes.includes(e)}static paste(e,s){s.pasteEditor(V.STAMP,{bitmapFile:e.getAsFile()})}altTextFinish(){this._uiManager.useNewAltTextFlow&&(this.div.hidden=!1),super.altTextFinish()}get telemetryFinalData(){return{type:"stamp",hasAltText:!!this.altTextData?.altText}}static computeTelemetryFinalData(e){const s=e.get("hasAltText");return{hasAltText:s.get(!0)??0,hasNoAltText:s.get(!1)??0}}async mlGuessAltText(e=null,s=!0){if(this.hasAltTextData())return null;const{mlManager:i}=this._uiManager;if(!i)throw new Error("No ML.");if(!await i.isEnabledFor("altText"))throw new Error("ML isn't enabled for alt text.");const{data:r,width:a,height:o}=e||this.copyCanvas(null,null,!0).imageData,h=await i.guess({name:"altText",request:{data:r,width:a,height:o,channels:r.length/(a*o)}});if(!h)throw new Error("No response from the AI service.");if(h.error)throw new Error("Error from the AI service.");if(h.cancel)return null;if(!h.output)throw new Error("No valid response from the AI service.");const l=h.output;return await this.setGuessedAltText(l),s&&!this.hasAltTextData()&&(this.altTextData={alt:l,decorative:!1}),l}remove(){n(this,$t)&&(u(this,ft,null),this._uiManager.imageManager.deleteId(n(this,$t)),n(this,vs)?.remove(),u(this,vs,null),n(this,ys)&&(clearTimeout(n(this,ys)),u(this,ys,null))),super.remove()}rebuild(){this.parent?(super.rebuild(),this.div!==null&&(n(this,$t)&&n(this,vs)===null&&A(this,J,tl).call(this),this.isAttachedToDOM||this.parent.add(this))):n(this,$t)&&A(this,J,tl).call(this)}onceAdded(e){this._isDraggable=!0,e&&this.div.focus()}isEmpty(){return!(n(this,Fi)||n(this,ft)||n(this,Qs)||n(this,Ni)||n(this,$t))}get isResizable(){return!0}render(){if(this.div)return this.div;let e,s;if(this.width&&(e=this.x,s=this.y),super.render(),this.div.hidden=!0,this.div.setAttribute("role","figure"),this.addAltTextButton(),n(this,ft)?A(this,J,nd).call(this):A(this,J,tl).call(this),this.width&&!this.annotationElementId){const[i,r]=this.parentDimensions;this.setAt(e*i,s*r,this.width*i,this.height*r)}return this._uiManager.addShouldRescale(this),this.div}_onResized(){this.onScaleChanging()}onScaleChanging(){this.parent&&(n(this,ys)!==null&&clearTimeout(n(this,ys)),u(this,ys,setTimeout(()=>{u(this,ys,null),A(this,J,rd).call(this)},200)))}copyCanvas(e,s,i=!1){e||(e=224);const{width:r,height:a}=n(this,ft),o=new ec;let h=n(this,ft),l=r,c=a,d=null;if(s){if(r>s||a>s){const E=Math.min(s/r,s/a);l=Math.floor(r*E),c=Math.floor(a*E)}d=document.createElement("canvas");const g=d.width=Math.ceil(l*o.sx),m=d.height=Math.ceil(c*o.sy);n(this,Se)||(h=A(this,J,el).call(this,g,m));const v=d.getContext("2d");v.filter=this._uiManager.hcmFilter;let y="white",w="#cfcfd8";this._uiManager.hcmFilter!=="none"?w="black":window.matchMedia?.("(prefers-color-scheme: dark)").matches&&(y="#8f8f9d",w="#42414d");const _=15,x=_*o.sx,S=_*o.sy,C=new OffscreenCanvas(2*x,2*S),k=C.getContext("2d");k.fillStyle=y,k.fillRect(0,0,2*x,2*S),k.fillStyle=w,k.fillRect(0,0,x,S),k.fillRect(x,S,x,S),v.fillStyle=v.createPattern(C,"repeat"),v.fillRect(0,0,g,m),v.drawImage(h,0,0,h.width,h.height,0,0,g,m)}let p=null;if(i){let g,m;if(o.symmetric&&h.width<e&&h.height<e)g=h.width,m=h.height;else if(h=n(this,ft),r>e||a>e){const y=Math.min(e/r,e/a);g=Math.floor(r*y),m=Math.floor(a*y),n(this,Se)||(h=A(this,J,el).call(this,g,m))}const v=new OffscreenCanvas(g,m).getContext("2d",{willReadFrequently:!0});v.drawImage(h,0,0,h.width,h.height,0,0,g,m),p={width:g,height:m,data:v.getImageData(0,0,g,m).data}}return{canvas:d,width:l,height:c,imageData:p}}getImageForAltText(){return n(this,vs)}static async deserialize(e,s,i){let r=null;if(e instanceof gp){const{data:{rect:v,rotation:y,id:w,structParent:_,popupRef:x},container:S,parent:{page:{pageNumber:C}}}=e,k=S.querySelector("canvas"),E=i.imageManager.getFromCanvas(S.id,k);k.remove();const L=(await s._structTree.getAriaAttributes(`${fd}${w}`))?.get("aria-label")||"";r=e={annotationType:V.STAMP,bitmapId:E.id,bitmap:E.bitmap,pageIndex:C-1,rect:v.slice(0),rotation:y,id:w,deleted:!1,accessibilityData:{decorative:!1,altText:L},isSvg:!1,structParent:_,popupRef:x}}const a=await super.deserialize(e,s,i),{rect:o,bitmap:h,bitmapUrl:l,bitmapId:c,isSvg:d,accessibilityData:p}=e;c&&i.imageManager.isValidId(c)?(u(a,$t,c),h&&u(a,ft,h)):u(a,Qs,l),u(a,Se,d);const[g,m]=a.pageDimensions;return a.width=(o[2]-o[0])/g,a.height=(o[3]-o[1])/m,a.annotationElementId=e.id||null,p&&(a.altTextData=p),a._initialData=r,u(a,ya,!!r),a}serialize(e=!1,s=null){if(this.isEmpty())return null;if(this.deleted)return this.serializeDeleted();const i={annotationType:V.STAMP,bitmapId:n(this,$t),pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:n(this,Se),structTreeParentId:this._structTreeParentId};if(e)return i.bitmapUrl=A(this,J,sl).call(this,!0),i.accessibilityData=this.serializeAltText(!0),i;const{decorative:r,altText:a}=this.serializeAltText(!1);if(!r&&a&&(i.accessibilityData={type:"Figure",alt:a}),this.annotationElementId){const h=A(this,J,Xp).call(this,i);if(h.isSame)return null;h.isSameAltText?delete i.accessibilityData:i.accessibilityData.structParent=this._initialData.structParent??-1}if(i.id=this.annotationElementId,s===null)return i;s.stamps||(s.stamps=new Map);const o=n(this,Se)?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(s.stamps.has(n(this,$t))){if(n(this,Se)){const h=s.stamps.get(n(this,$t));o>h.area&&(h.area=o,h.serialized.bitmap.close(),h.serialized.bitmap=A(this,J,sl).call(this,!1))}}else s.stamps.set(n(this,$t),{area:o,serialized:i}),i.bitmap=A(this,J,sl).call(this,!1);return i}renderAnnotationElement(e){return e.updateEdited({rect:this.getRect(0,0)}),null}};ft=new WeakMap,$t=new WeakMap,Fi=new WeakMap,Qs=new WeakMap,Ni=new WeakMap,va=new WeakMap,vs=new WeakMap,ys=new WeakMap,Se=new WeakMap,ya=new WeakMap,J=new WeakSet,za=function(e,s=!1){e?(u(this,ft,e.bitmap),s||(u(this,$t,e.id),u(this,Se,e.isSvg)),e.file&&u(this,va,e.file.name),A(this,J,nd).call(this)):this.remove()},ja=function(){if(u(this,Fi,null),this._uiManager.enableWaiting(!1),n(this,vs))if(this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&n(this,ft))this._editToolbar.hide(),this._uiManager.editAltText(this,!0);else{if(!this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&n(this,ft)){this._reportTelemetry({action:"pdfjs.image.image_added",data:{alt_text_modal:!1,alt_text_type:"empty"}});try{this.mlGuessAltText()}catch{}}this.div.focus()}},tl=function(){if(n(this,$t)){this._uiManager.enableWaiting(!0),this._uiManager.imageManager.getFromId(n(this,$t)).then(i=>A(this,J,za).call(this,i,!0)).finally(()=>A(this,J,ja).call(this));return}if(n(this,Qs)){const i=n(this,Qs);u(this,Qs,null),this._uiManager.enableWaiting(!0),u(this,Fi,this._uiManager.imageManager.getFromUrl(i).then(r=>A(this,J,za).call(this,r)).finally(()=>A(this,J,ja).call(this)));return}if(n(this,Ni)){const i=n(this,Ni);u(this,Ni,null),this._uiManager.enableWaiting(!0),u(this,Fi,this._uiManager.imageManager.getFromFile(i).then(r=>A(this,J,za).call(this,r)).finally(()=>A(this,J,ja).call(this)));return}const e=document.createElement("input");e.type="file",e.accept=Va.supportedTypesStr;const s=this._uiManager._signal;u(this,Fi,new Promise(i=>{e.addEventListener("change",async()=>{if(e.files&&e.files.length!==0){this._uiManager.enableWaiting(!0);const r=await this._uiManager.imageManager.getFromFile(e.files[0]);this._reportTelemetry({action:"pdfjs.image.image_selected",data:{alt_text_modal:this._uiManager.useNewAltTextFlow}}),A(this,J,za).call(this,r)}else this.remove();i()},{signal:s}),e.addEventListener("cancel",()=>{this.remove(),i()},{signal:s})}).finally(()=>A(this,J,ja).call(this))),e.click()},nd=function(){const{div:e}=this;let{width:s,height:i}=n(this,ft);const[r,a]=this.pageDimensions,o=.75;if(this.width)s=this.width*r,i=this.height*a;else if(s>o*r||i>o*a){const d=Math.min(o*r/s,o*a/i);s*=d,i*=d}const[h,l]=this.parentDimensions;this.setDims(s*h/r,i*l/a),this._uiManager.enableWaiting(!1);const c=u(this,vs,document.createElement("canvas"));c.setAttribute("role","img"),this.addContainer(c),this.width=s/r,this.height=i/a,this._initialOptions?.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,this._uiManager.useNewAltTextWhenAddingImage&&this._uiManager.useNewAltTextFlow&&!this.annotationElementId||(e.hidden=!1),A(this,J,rd).call(this),n(this,ya)||(this.parent.addUndoableEditor(this),u(this,ya,!0)),this._reportTelemetry({action:"inserted_image"}),n(this,va)&&c.setAttribute("aria-label",n(this,va))},el=function(e,s){const{width:i,height:r}=n(this,ft);let a=i,o=r,h=n(this,ft);for(;a>2*e||o>2*s;){const l=a,c=o;a>2*e&&(a=a>=16384?Math.floor(a/2)-1:Math.ceil(a/2)),o>2*s&&(o=o>=16384?Math.floor(o/2)-1:Math.ceil(o/2));const d=new OffscreenCanvas(a,o);d.getContext("2d").drawImage(h,0,0,l,c,0,0,a,o),h=d.transferToImageBitmap()}return h},rd=function(){const[e,s]=this.parentDimensions,{width:i,height:r}=this,a=new ec,o=Math.ceil(i*e*a.sx),h=Math.ceil(r*s*a.sy),l=n(this,vs);if(!l||l.width===o&&l.height===h)return;l.width=o,l.height=h;const c=n(this,Se)?n(this,ft):A(this,J,el).call(this,o,h),d=l.getContext("2d");d.filter=this._uiManager.hcmFilter,d.drawImage(c,0,0,c.width,c.height,0,0,o,h)},sl=function(e){if(e){if(n(this,Se)){const i=this._uiManager.imageManager.getSvgUrl(n(this,$t));if(i)return i}const s=document.createElement("canvas");return{width:s.width,height:s.height}=n(this,ft),s.getContext("2d").drawImage(n(this,ft),0,0),s.toDataURL()}if(n(this,Se)){const[s,i]=this.pageDimensions,r=Math.round(this.width*s*ji.PDF_TO_CSS_UNITS),a=Math.round(this.height*i*ji.PDF_TO_CSS_UNITS),o=new OffscreenCanvas(r,a);return o.getContext("2d").drawImage(n(this,ft),0,0,n(this,ft).width,n(this,ft).height,0,0,r,a),o.transferToImageBitmap()}return structuredClone(n(this,ft))},Xp=function(e){const{pageIndex:s,accessibilityData:{altText:i}}=this._initialData,r=e.pageIndex===s,a=(e.accessibilityData?.alt||"")===i;return{isSame:!this._hasBeenMoved&&!this._hasBeenResized&&r&&a,isSameAltText:a}},F(Va,"_type","stamp"),F(Va,"_editorType",V.STAMP);let id=Va;var Zn,wa,ws,Oi,Js,Ne,Bi,_a,tr,Je,Zs,qt,ti,I,Hi,dt,Yp,ns,od,hd,il;const $e=class $e{constructor({uiManager:t,pageIndex:e,div:s,structTreeLayer:i,accessibilityManager:r,annotationLayer:a,drawLayer:o,textLayer:h,viewport:l,l10n:c}){b(this,dt);b(this,Zn);b(this,wa,!1);b(this,ws,null);b(this,Oi,null);b(this,Js,null);b(this,Ne,new Map);b(this,Bi,!1);b(this,_a,!1);b(this,tr,!1);b(this,Je,null);b(this,Zs,null);b(this,qt,null);b(this,ti,null);b(this,I);const d=[...n($e,Hi).values()];if(!$e._initialized){$e._initialized=!0;for(const p of d)p.initialize(c,t)}t.registerEditorTypes(d),u(this,I,t),this.pageIndex=e,this.div=s,u(this,Zn,r),u(this,ws,a),this.viewport=l,u(this,qt,h),this.drawLayer=o,this._structTree=i,n(this,I).addLayer(this)}get isEmpty(){return n(this,Ne).size===0}get isInvisible(){return this.isEmpty&&n(this,I).getMode()===V.NONE}updateToolbar(t){n(this,I).updateToolbar(t)}updateMode(t=n(this,I).getMode()){switch(A(this,dt,il).call(this),t){case V.NONE:this.disableTextSelection(),this.togglePointerEvents(!1),this.toggleAnnotationLayerPointerEvents(!0),this.disableClick();return;case V.INK:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick();break;case V.HIGHLIGHT:this.enableTextSelection(),this.togglePointerEvents(!1),this.disableClick();break;default:this.disableTextSelection(),this.togglePointerEvents(!0),this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const s of n($e,Hi).values())e.toggle(`${s._type}Editing`,t===s._editorType);this.div.hidden=!1}hasTextLayer(t){return t===n(this,qt)?.div}setEditingState(t){n(this,I).setEditingState(t)}addCommands(t){n(this,I).addCommands(t)}cleanUndoStack(t){n(this,I).cleanUndoStack(t)}toggleDrawing(t=!1){this.div.classList.toggle("drawing",!t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){n(this,ws)?.div.classList.toggle("disabled",!t)}async enable(){u(this,tr,!0),this.div.tabIndex=0,this.togglePointerEvents(!0);const t=new Set;for(const s of n(this,Ne).values())s.enableEditing(),s.show(!0),s.annotationElementId&&(n(this,I).removeChangedExistingAnnotation(s),t.add(s.annotationElementId));if(!n(this,ws)){u(this,tr,!1);return}const e=n(this,ws).getEditableAnnotations();for(const s of e){if(s.hide(),n(this,I).isDeletedAnnotationElement(s.data.id)||t.has(s.data.id))continue;const i=await this.deserialize(s);i&&(this.addOrRebuild(i),i.enableEditing())}u(this,tr,!1)}disable(){u(this,_a,!0),this.div.tabIndex=-1,this.togglePointerEvents(!1);const t=new Map,e=new Map;for(const i of n(this,Ne).values())i.disableEditing(),i.annotationElementId&&(i.serialize()===null?(e.set(i.annotationElementId,i),this.getEditableAnnotation(i.annotationElementId)?.show(),i.remove()):t.set(i.annotationElementId,i));if(n(this,ws)){const i=n(this,ws).getEditableAnnotations();for(const r of i){const{id:a}=r.data;if(n(this,I).isDeletedAnnotationElement(a))continue;let o=e.get(a);o?(o.resetAnnotationElement(r),o.show(!1),r.show()):(o=t.get(a),o&&(n(this,I).addChangedExistingAnnotation(o),o.renderAnnotationElement(r)&&o.show(!1)),r.show())}}A(this,dt,il).call(this),this.isEmpty&&(this.div.hidden=!0);const{classList:s}=this.div;for(const i of n($e,Hi).values())s.remove(`${i._type}Editing`);this.disableTextSelection(),this.toggleAnnotationLayerPointerEvents(!0),u(this,_a,!1)}getEditableAnnotation(t){return n(this,ws)?.getEditableAnnotation(t)||null}setActiveEditor(t){n(this,I).getActive()!==t&&n(this,I).setActiveEditor(t)}enableTextSelection(){if(this.div.tabIndex=-1,n(this,qt)?.div&&!n(this,ti)){u(this,ti,new AbortController);const t=n(this,I).combinedSignal(n(this,ti));n(this,qt).div.addEventListener("pointerdown",A(this,dt,Yp).bind(this),{signal:t}),n(this,qt).div.classList.add("highlighting")}}disableTextSelection(){this.div.tabIndex=0,n(this,qt)?.div&&n(this,ti)&&(n(this,ti).abort(),u(this,ti,null),n(this,qt).div.classList.remove("highlighting"))}enableClick(){if(n(this,Oi))return;u(this,Oi,new AbortController);const t=n(this,I).combinedSignal(n(this,Oi));this.div.addEventListener("pointerdown",this.pointerdown.bind(this),{signal:t});const e=this.pointerup.bind(this);this.div.addEventListener("pointerup",e,{signal:t}),this.div.addEventListener("pointercancel",e,{signal:t})}disableClick(){n(this,Oi)?.abort(),u(this,Oi,null)}attach(t){n(this,Ne).set(t.id,t);const{annotationElementId:e}=t;e&&n(this,I).isDeletedAnnotationElement(e)&&n(this,I).removeDeletedAnnotationElement(t)}detach(t){n(this,Ne).delete(t.id),n(this,Zn)?.removePointerInTextLayer(t.contentDiv),!n(this,_a)&&t.annotationElementId&&n(this,I).addDeletedAnnotationElement(t)}remove(t){this.detach(t),n(this,I).removeEditor(t),t.div.remove(),t.isAttachedToDOM=!1}changeParent(t){t.parent!==this&&(t.parent&&t.annotationElementId&&(n(this,I).addDeletedAnnotationElement(t.annotationElementId),gt.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),t.parent?.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(t.parent!==this||!t.isAttachedToDOM){if(this.changeParent(t),n(this,I).addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(!n(this,tr)),n(this,I).addToAnnotationStorage(t),t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;t.div.contains(e)&&!n(this,Js)&&(t._focusEventsAllowed=!1,u(this,Js,setTimeout(()=>{u(this,Js,null),t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",()=>{t._focusEventsAllowed=!0},{once:!0,signal:n(this,I)._signal}),e.focus())},0))),t._structTreeParentId=n(this,Zn)?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?(t.parent||(t.parent=this),t.rebuild(),t.show()):this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return n(this,I).getId()}combinedSignal(t){return n(this,I).combinedSignal(t)}canCreateNewEmptyEditor(){return n(this,dt,ns)?.canCreateNewEmptyEditor()}pasteEditor(t,e){n(this,I).updateToolbar(t),n(this,I).updateMode(t);const{offsetX:s,offsetY:i}=A(this,dt,hd).call(this),r=this.getNextId(),a=A(this,dt,od).call(this,{parent:this,id:r,x:s,y:i,uiManager:n(this,I),isCentered:!0,...e});a&&this.add(a)}async deserialize(t){return await n($e,Hi).get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,n(this,I))||null}createAndAddNewEditor(t,e,s={}){const i=this.getNextId(),r=A(this,dt,od).call(this,{parent:this,id:i,x:t.offsetX,y:t.offsetY,uiManager:n(this,I),isCentered:e,...s});return r&&this.add(r),r}addNewEditor(){this.createAndAddNewEditor(A(this,dt,hd).call(this),!0)}setSelected(t){n(this,I).setSelected(t)}toggleSelected(t){n(this,I).toggleSelected(t)}unselect(t){n(this,I).unselect(t)}pointerup(t){const{isMac:e}=se.platform;!(t.button!==0||t.ctrlKey&&e)&&t.target===this.div&&n(this,Bi)&&(u(this,Bi,!1),n(this,dt,ns)?.isDrawer&&n(this,dt,ns).supportMultipleDrawings||(n(this,wa)?n(this,I).getMode()!==V.STAMP?this.createAndAddNewEditor(t,!1):n(this,I).unselectAll():u(this,wa,!0)))}pointerdown(t){if(n(this,I).getMode()===V.HIGHLIGHT&&this.enableTextSelection(),n(this,Bi)){u(this,Bi,!1);return}const{isMac:e}=se.platform;if(t.button!==0||t.ctrlKey&&e||t.target!==this.div)return;if(u(this,Bi,!0),n(this,dt,ns)?.isDrawer){this.startDrawingSession(t);return}const s=n(this,I).getActive();u(this,wa,!s||s.isEmpty())}startDrawingSession(t){if(this.div.focus(),n(this,Je)){n(this,dt,ns).startDrawing(this,n(this,I),!1,t);return}n(this,I).setCurrentDrawingSession(this),u(this,Je,new AbortController);const e=n(this,I).combinedSignal(n(this,Je));this.div.addEventListener("blur",({relatedTarget:s})=>{s&&!this.div.contains(s)&&(u(this,Zs,null),this.commitOrRemove())},{signal:e}),n(this,dt,ns).startDrawing(this,n(this,I),!1,t)}pause(t){if(t){const{activeElement:e}=document;this.div.contains(e)&&u(this,Zs,e)}else n(this,Zs)&&setTimeout(()=>{n(this,Zs)?.focus(),u(this,Zs,null)},0)}endDrawingSession(t=!1){return n(this,Je)?(n(this,I).setCurrentDrawingSession(null),n(this,Je).abort(),u(this,Je,null),u(this,Zs,null),n(this,dt,ns).endDrawing(t)):null}findNewParent(t,e,s){const i=n(this,I).findParent(e,s);return i===null||i===this?!1:(i.changeParent(t),!0)}commitOrRemove(){return n(this,Je)?(this.endDrawingSession(),!0):!1}onScaleChanging(){n(this,Je)&&n(this,dt,ns).onScaleChangingWhenDrawing(this)}destroy(){this.commitOrRemove(),n(this,I).getActive()?.parent===this&&(n(this,I).commitOrRemove(),n(this,I).setActiveEditor(null)),n(this,Js)&&(clearTimeout(n(this,Js)),u(this,Js,null));for(const t of n(this,Ne).values())n(this,Zn)?.removePointerInTextLayer(t.contentDiv),t.setParent(null),t.isAttachedToDOM=!1,t.div.remove();this.div=null,n(this,Ne).clear(),n(this,I).removeLayer(this)}render({viewport:t}){this.viewport=t,sr(this.div,t);for(const e of n(this,I).getEditors(this.pageIndex))this.add(e),e.rebuild();this.updateMode()}update({viewport:t}){n(this,I).commitOrRemove(),A(this,dt,il).call(this);const e=this.viewport.rotation,s=t.rotation;if(this.viewport=t,sr(this.div,{rotation:s}),e!==s)for(const i of n(this,Ne).values())i.rotate(s)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return n(this,I).viewParameters.realScale}};Zn=new WeakMap,wa=new WeakMap,ws=new WeakMap,Oi=new WeakMap,Js=new WeakMap,Ne=new WeakMap,Bi=new WeakMap,_a=new WeakMap,tr=new WeakMap,Je=new WeakMap,Zs=new WeakMap,qt=new WeakMap,ti=new WeakMap,I=new WeakMap,Hi=new WeakMap,dt=new WeakSet,Yp=function(t){n(this,I).unselectAll();const{target:e}=t;if(e===n(this,qt).div||(e.getAttribute("role")==="img"||e.classList.contains("endOfContent"))&&n(this,qt).div.contains(e)){const{isMac:s}=se.platform;if(t.button!==0||t.ctrlKey&&s)return;n(this,I).showAllEditors("highlight",!0,!0),n(this,qt).div.classList.add("free"),this.toggleDrawing(),ul.startHighlighting(this,n(this,I).direction==="ltr",{target:n(this,qt).div,x:t.x,y:t.y}),n(this,qt).div.addEventListener("pointerup",()=>{n(this,qt).div.classList.remove("free"),this.toggleDrawing(!0)},{once:!0,signal:n(this,I)._signal}),t.preventDefault()}},ns=function(){return n($e,Hi).get(n(this,I).getMode())},od=function(t){const e=n(this,dt,ns);return e?new e.prototype.constructor(t):null},hd=function(){const{x:t,y:e,width:s,height:i}=this.div.getBoundingClientRect(),r=Math.max(0,t),a=Math.max(0,e),o=(r+Math.min(window.innerWidth,t+s))/2-t,h=(a+Math.min(window.innerHeight,e+i))/2-e,[l,c]=this.viewport.rotation%180==0?[o,h]:[h,o];return{offsetX:l,offsetY:c}},il=function(){for(const t of n(this,Ne).values())t.isEmpty()&&t.remove()},F($e,"_initialized",!1),b($e,Hi,new Map([zc,sd,id,ul].map(t=>[t._editorType,t])));let ad=$e;var Ze,vh,ee,er,Il,Kp,_s,cd,Qp,dd;const Gt=class Gt{constructor({pageIndex:t}){b(this,_s);b(this,Ze,null);b(this,vh,0);b(this,ee,new Map);b(this,er,new Map);this.pageIndex=t}setParent(t){if(n(this,Ze)){if(n(this,Ze)!==t){if(n(this,ee).size>0)for(const e of n(this,ee).values())e.remove(),t.append(e);u(this,Ze,t)}}else u(this,Ze,t)}static get _svgFactory(){return X(this,"_svgFactory",new yd)}draw(t,e=!1,s=!1){const i=Kt(this,vh)._++,r=A(this,_s,cd).call(this),a=Gt._svgFactory.createElement("defs");r.append(a);const o=Gt._svgFactory.createElement("path");a.append(o);const h=`path_p${this.pageIndex}_${i}`;o.setAttribute("id",h),o.setAttribute("vector-effect","non-scaling-stroke"),e&&n(this,er).set(i,o);const l=s?A(this,_s,Qp).call(this,a,h):null,c=Gt._svgFactory.createElement("use");return r.append(c),c.setAttribute("href",`#${h}`),this.updateProperties(r,t),n(this,ee).set(i,r),{id:i,clipPathId:`url(#${l})`}}drawOutline(t,e){const s=Kt(this,vh)._++,i=A(this,_s,cd).call(this),r=Gt._svgFactory.createElement("defs");i.append(r);const a=Gt._svgFactory.createElement("path");r.append(a);const o=`path_p${this.pageIndex}_${s}`;a.setAttribute("id",o),a.setAttribute("vector-effect","non-scaling-stroke");let h;if(e){const d=Gt._svgFactory.createElement("mask");r.append(d),h=`mask_p${this.pageIndex}_${s}`,d.setAttribute("id",h),d.setAttribute("maskUnits","objectBoundingBox");const p=Gt._svgFactory.createElement("rect");d.append(p),p.setAttribute("width","1"),p.setAttribute("height","1"),p.setAttribute("fill","white");const g=Gt._svgFactory.createElement("use");d.append(g),g.setAttribute("href",`#${o}`),g.setAttribute("stroke","none"),g.setAttribute("fill","black"),g.setAttribute("fill-rule","nonzero"),g.classList.add("mask")}const l=Gt._svgFactory.createElement("use");i.append(l),l.setAttribute("href",`#${o}`),h&&l.setAttribute("mask",`url(#${h})`);const c=l.cloneNode();return i.append(c),l.classList.add("mainOutline"),c.classList.add("secondaryOutline"),this.updateProperties(i,t),n(this,ee).set(s,i),s}finalizeDraw(t,e){n(this,er).delete(t),this.updateProperties(t,e)}updateProperties(t,e){var h;if(!e)return;const{root:s,bbox:i,rootClass:r,path:a}=e,o=typeof t=="number"?n(this,ee).get(t):t;if(o){if(s&&A(this,_s,dd).call(this,o,s),i&&A(h=Gt,Il,Kp).call(h,o,i),r){const{classList:l}=o;for(const[c,d]of Object.entries(r))l.toggle(c,d)}if(a){const l=o.firstChild.firstChild;A(this,_s,dd).call(this,l,a)}}}updateParent(t,e){if(e===this)return;const s=n(this,ee).get(t);s&&(n(e,Ze).append(s),n(this,ee).delete(t),n(e,ee).set(t,s))}remove(t){n(this,er).delete(t),n(this,Ze)!==null&&(n(this,ee).get(t).remove(),n(this,ee).delete(t))}destroy(){u(this,Ze,null);for(const t of n(this,ee).values())t.remove();n(this,ee).clear(),n(this,er).clear()}};Ze=new WeakMap,vh=new WeakMap,ee=new WeakMap,er=new WeakMap,Il=new WeakSet,Kp=function(t,[e,s,i,r]){const{style:a}=t;a.top=100*s+"%",a.left=100*e+"%",a.width=100*i+"%",a.height=100*r+"%"},_s=new WeakSet,cd=function(){const t=Gt._svgFactory.create(1,1,!0);return n(this,Ze).append(t),t.setAttribute("aria-hidden",!0),t},Qp=function(t,e){const s=Gt._svgFactory.createElement("clipPath");t.append(s);const i=`clip_${e}`;s.setAttribute("id",i),s.setAttribute("clipPathUnits","objectBoundingBox");const r=Gt._svgFactory.createElement("use");return s.append(r),r.setAttribute("href",`#${e}`),r.classList.add("clip"),i},dd=function(t,e){for(const[s,i]of Object.entries(e))i===null?t.removeAttribute(s):t.setAttribute(s,i)},b(Gt,Il);let ld=Gt;globalThis.pdfjsTestingUtils={HighlightOutliner:Gc};var cm=H.AbortException,dm=H.AnnotationEditorLayer,um=H.AnnotationEditorParamsType,pm=H.AnnotationEditorType,fm=H.AnnotationEditorUIManager,gm=H.AnnotationLayer,mm=H.AnnotationMode,bm=H.ColorPicker,Am=H.DOMSVGFactory,vm=H.DrawLayer,ym=H.FeatureTest,wm=H.GlobalWorkerOptions,_m=H.ImageKind,xm=H.InvalidPDFException,Sm=H.MissingPDFException,Em=H.OPS,Cm=H.OutputScale,Mm=H.PDFDataRangeTransport,km=H.PDFDateString,Rm=H.PDFWorker,Tm=H.PasswordResponses,Pm=H.PermissionFlag,Im=H.PixelsPerInch,Dm=H.RenderingCancelledException,Lm=H.TextLayer,Fm=H.TouchManager,Nm=H.UnexpectedResponseException,Om=H.Util,Bm=H.VerbosityLevel,Hm=H.XfaLayer,$m=H.build,zm=H.createValidAbsoluteUrl,jm=H.fetchData,Gm=H.getDocument,Vm=H.getFilenameFromUrl,Um=H.getPdfFilenameFromUrl,Wm=H.getXfaPageViewport,qm=H.isDataScheme,Xm=H.isPdfFile,Ym=H.noContextMenu,Km=H.normalizeUnicode,Qm=H.setLayerDimensions,Jm=H.shadow,Zm=H.stopEvent,tb=H.version;export{cm as AbortException,dm as AnnotationEditorLayer,um as AnnotationEditorParamsType,pm as AnnotationEditorType,fm as AnnotationEditorUIManager,gm as AnnotationLayer,mm as AnnotationMode,bm as ColorPicker,Am as DOMSVGFactory,vm as DrawLayer,ym as FeatureTest,wm as GlobalWorkerOptions,_m as ImageKind,xm as InvalidPDFException,Sm as MissingPDFException,Em as OPS,Cm as OutputScale,Mm as PDFDataRangeTransport,km as PDFDateString,Rm as PDFWorker,Tm as PasswordResponses,Pm as PermissionFlag,Im as PixelsPerInch,Dm as RenderingCancelledException,Lm as TextLayer,Fm as TouchManager,Nm as UnexpectedResponseException,Om as Util,Bm as VerbosityLevel,Hm as XfaLayer,$m as build,zm as createValidAbsoluteUrl,jm as fetchData,Gm as getDocument,Vm as getFilenameFromUrl,Um as getPdfFilenameFromUrl,Wm as getXfaPageViewport,qm as isDataScheme,Xm as isPdfFile,Ym as noContextMenu,Km as normalizeUnicode,Qm as setLayerDimensions,Jm as shadow,Zm as stopEvent,tb as version};
