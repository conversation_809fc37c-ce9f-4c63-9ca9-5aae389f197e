# 🚀 DEPLOY AGORA - Comando Único

## ❌ Problema: "Missing script: build"
Você está executando o comando do diretório errado!

## ✅ SOLUÇÃO RÁPIDA

### Copie e cole este comando único:

```bash
cd C:\Users\<USER>\Downloads\AISTUDIOCHATRAG && npm install && npm run build && netlify deploy --dir=dist
```

### Se der tudo certo, execute para produção:

```bash
netlify deploy --prod --dir=dist
```

## 🔧 Passo a Passo Detalhado

### 1. Navegar para o diretório correto
```bash
cd C:\Users\<USER>\Downloads\AISTUDIOCHATRAG
```

### 2. Verificar se está no lugar certo
```bash
ls package.json
# Deve mostrar o arquivo package.json
```

### 3. Instalar dependências
```bash
npm install
```

### 4. Fazer build
```bash
npm run build
```

### 5. Deploy de teste
```bash
netlify deploy --dir=dist
```

### 6. Se tudo OK, deploy para produção
```bash
netlify deploy --prod --dir=dist
```

## 📋 Variáveis de Ambiente

Configure no painel do Netlify (Site settings → Environment variables):

```
VITE_GEMINI_API_KEY=sua_chave_gemini
VITE_SUPABASE_URL=https://seu-projeto.supabase.co
VITE_SUPABASE_ANON_KEY=sua_chave_supabase
VITE_SUPABASE_SERVICE_ROLE_KEY=sua_chave_service
NODE_ENV=production
```

## 🎯 Alternativa: Drag & Drop

Se ainda der problema:

1. Execute: `cd C:\Users\<USER>\Downloads\AISTUDIOCHATRAG && npm run build`
2. Vá para [netlify.com](https://netlify.com)
3. Arraste a pasta `dist` para o Netlify

## 📞 Suporte

Se ainda não funcionar, verifique:
- [ ] Node.js instalado (versão 16+)
- [ ] Netlify CLI instalado: `npm install -g netlify-cli`
- [ ] Logado no Netlify: `netlify login`
- [ ] No diretório correto: `pwd` deve mostrar `Downloads\AISTUDIOCHATRAG`

---

**🎯 A chave é executar os comandos do diretório correto: `Downloads\AISTUDIOCHATRAG`**
