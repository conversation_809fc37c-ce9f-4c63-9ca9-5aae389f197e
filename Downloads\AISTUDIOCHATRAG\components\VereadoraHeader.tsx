// Componente de cabeçalho personalizado para a Vereadora Rafaela de Nilda
import React from 'react';
import { VEREADORA_CONFIG } from '../config/vereadora';

interface VereadoraHeaderProps {
  ragStatus?: 'initializing' | 'ready' | 'error';
  documentsCount?: number;
}

export const VereadoraHeader: React.FC<VereadoraHeaderProps> = ({ 
  ragStatus = 'initializing', 
  documentsCount = 0 
}) => {
  const config = VEREADORA_CONFIG;

  const getStatusColor = () => {
    switch (ragStatus) {
      case 'ready': return 'text-green-600';
      case 'error': return 'text-red-600';
      default: return 'text-yellow-600';
    }
  };

  const getStatusText = () => {
    switch (ragStatus) {
      case 'ready': return 'Sistema Ativo';
      case 'error': return 'Sistema com Erro';
      default: return 'Inicializando...';
    }
  };

  return (
    <div className="bg-gradient-to-r from-blue-800 to-blue-900 text-white p-6 rounded-lg shadow-lg mb-6">
      {/* Header Principal */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          {/* Avatar/Logo */}
          <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center text-2xl font-bold">
            {config.pessoal.nome.split(' ').map(n => n[0]).join('')}
          </div>
          
          {/* Informações Principais */}
          <div>
            <h1 className="text-2xl font-bold">{config.pessoal.nome}</h1>
            <p className="text-blue-200">
              {config.pessoal.cargo} de {config.pessoal.municipio}/{config.pessoal.estado}
            </p>
            <p className="text-blue-300 text-sm">
              Mandato: {config.pessoal.mandato}
            </p>
          </div>
        </div>

        {/* Status do Sistema */}
        <div className="text-right">
          <div className={`text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </div>
          {documentsCount > 0 && (
            <div className="text-blue-200 text-xs mt-1">
              {documentsCount} documentos indexados
            </div>
          )}
        </div>
      </div>

      {/* Áreas de Atuação */}
      <div className="border-t border-blue-700 pt-4">
        <h3 className="text-sm font-semibold text-blue-200 mb-2">Principais Áreas de Atuação:</h3>
        <div className="flex flex-wrap gap-2">
          {config.atuacao.areas_prioritarias.slice(0, 6).map((area, index) => (
            <span 
              key={index}
              className="bg-blue-700 text-blue-100 px-3 py-1 rounded-full text-xs"
            >
              {area}
            </span>
          ))}
        </div>
      </div>

      {/* Informações de Contato */}
      <div className="border-t border-blue-700 pt-3 mt-3">
        <div className="flex flex-wrap gap-4 text-sm text-blue-200">
          <div className="flex items-center space-x-1">
            <span>📍</span>
            <span>{config.municipio.nome} - {config.municipio.populacao}</span>
          </div>
          <div className="flex items-center space-x-1">
            <span>🕒</span>
            <span>{config.atendimento.horarios}</span>
          </div>
          <div className="flex items-center space-x-1">
            <span>💬</span>
            <span>Atendimento via WhatsApp disponível</span>
          </div>
        </div>
      </div>
    </div>
  );
};

// Componente de estatísticas do sistema
interface SystemStatsProps {
  ragInitialized: boolean;
  documentsProcessed: number;
  lastUpdate?: Date;
  averageResponseTime?: number;
}

export const SystemStats: React.FC<SystemStatsProps> = ({
  ragInitialized,
  documentsProcessed,
  lastUpdate,
  averageResponseTime
}) => {
  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
      <h3 className="text-sm font-semibold text-gray-700 mb-3">Status do Sistema RAG</h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div className="text-center">
          <div className={`text-2xl font-bold ${ragInitialized ? 'text-green-600' : 'text-gray-400'}`}>
            {ragInitialized ? '✓' : '○'}
          </div>
          <div className="text-gray-600">Sistema</div>
        </div>
        
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {documentsProcessed}
          </div>
          <div className="text-gray-600">Documentos</div>
        </div>
        
        {lastUpdate && (
          <div className="text-center">
            <div className="text-lg font-bold text-gray-700">
              {lastUpdate.toLocaleDateString()}
            </div>
            <div className="text-gray-600">Última Atualização</div>
          </div>
        )}
        
        {averageResponseTime && (
          <div className="text-center">
            <div className="text-lg font-bold text-purple-600">
              {averageResponseTime.toFixed(0)}ms
            </div>
            <div className="text-gray-600">Tempo Médio</div>
          </div>
        )}
      </div>
    </div>
  );
};

// Componente de dicas para o usuário
export const UserTips: React.FC = () => {
  const tips = [
    "Pergunte sobre políticas públicas de Parnamirim",
    "Solicite orientações sobre serviços municipais",
    "Compartilhe suas demandas e sugestões",
    "Consulte sobre projetos em andamento",
    "Tire dúvidas sobre processos legislativos",
    "Informe-se sobre programas sociais disponíveis"
  ];

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
      <h3 className="text-sm font-semibold text-blue-800 mb-3">
        💡 Como posso ajudá-lo hoje?
      </h3>
      
      <div className="space-y-2">
        {tips.map((tip, index) => (
          <div key={index} className="flex items-start space-x-2 text-sm text-blue-700">
            <span className="text-blue-500 mt-0.5">•</span>
            <span>{tip}</span>
          </div>
        ))}
      </div>
      
      <div className="mt-3 pt-3 border-t border-blue-200 text-xs text-blue-600">
        <strong>Dica:</strong> Você pode enviar documentos para que eu tenha mais contexto sobre sua solicitação.
      </div>
    </div>
  );
};

// Componente de informações sobre Parnamirim
export const ParnamiriInfo: React.FC = () => {
  const config = VEREADORA_CONFIG;

  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
      <h3 className="text-sm font-semibold text-green-800 mb-3 flex items-center">
        🏛️ Sobre Parnamirim/RN
      </h3>
      
      <div className="space-y-2 text-sm text-green-700">
        <div>
          <strong>População:</strong> {config.municipio.populacao}
        </div>
        <div>
          <strong>Região:</strong> {config.municipio.regiao}
        </div>
        <div>
          <strong>Características:</strong> {config.municipio.caracteristicas.slice(0, 3).join(', ')}
        </div>
      </div>
      
      <div className="mt-3 pt-3 border-t border-green-200">
        <h4 className="text-xs font-semibold text-green-800 mb-2">Principais Desafios:</h4>
        <div className="flex flex-wrap gap-1">
          {config.municipio.desafios.slice(0, 4).map((desafio, index) => (
            <span 
              key={index}
              className="bg-green-100 text-green-700 px-2 py-1 rounded text-xs"
            >
              {desafio}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

export default VereadoraHeader;
