
import React, { useState, useEffect, useCallback, useRef } from 'react';
import { ChatMessage } from './types';
import ChatInterface from './components/ChatInterface';
import DocumentInput from './components/DocumentInput';
import { DocumentManager } from './components/DocumentManager';
import { TabNavigation } from './components/TabNavigation';
import { WhatsAppTab } from './components/WhatsAppTab';
import HistoryPanel from './components/HistoryPanel';
import { VereadoraHeader, SystemStats, UserTips, ParnamiriInfo } from './components/VereadoraHeader';
import { generateGeminiResponse } from './services/geminiService';
import { SYSTEM_INSTRUCTION_RAG, SYSTEM_INSTRUCTION_GENERAL, VEREADORA_PERSONA } from './constants';
import { databaseUtils, conversationService } from './services/databaseService';
import { RAGPipeline, DEFAULT_RAG_CONFIG } from './services/ragPipeline';
import { IngestionService } from './services/ingestionService';
import { VectorStore } from './services/vectorStore';
import { RerankingService } from './services/rerankingService';
import { VEREADORA_CONFIG, gerarInstrucaoPersonalizada } from './config/vereadora';

interface SendMessagePayload {
  text?: string;
  audio?: {
    mimeType: string;
    data: string; // base64
  };
}

const App: React.FC = () => {
  const [apiKeyLoaded, setApiKeyLoaded] = useState<boolean>(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [documentContent, setDocumentContent] = useState<string | null>(null);
  const [useGoogleSearch, setUseGoogleSearch] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('chat');

  // WhatsApp State
  const [lastBotResponseText, setLastBotResponseText] = useState<string | null>(null);

  // Database State
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);

  // RAG System State
  const [ragPipeline, setRagPipeline] = useState<RAGPipeline | null>(null);
  const [ingestionService, setIngestionService] = useState<IngestionService | null>(null);
  const [vectorStore, setVectorStore] = useState<VectorStore | null>(null);
  const [rerankingService, setRerankingService] = useState<RerankingService | null>(null);
  const [ragInitialized, setRagInitialized] = useState<boolean>(false);

  const chatInterfaceRef = useRef<HTMLDivElement>(null);

  // Funções para gerenciar conversas
  const handleLoadConversation = useCallback((loadedMessages: ChatMessage[], conversationId: string) => {
    setMessages(loadedMessages);
    setCurrentConversationId(conversationId);
    console.log('Conversation loaded:', conversationId);
  }, []);

  const handleNewConversation = useCallback(() => {
    const welcomeMessage = `Olá! Eu sou a ${VEREADORA_CONFIG.pessoal.nome}, ${VEREADORA_CONFIG.pessoal.cargo} de ${VEREADORA_CONFIG.pessoal.municipio}/${VEREADORA_CONFIG.pessoal.estado}.

Como posso ajudá-lo hoje? Estou aqui para:
• Esclarecer dúvidas sobre políticas públicas
• Orientar sobre serviços municipais
• Receber demandas e sugestões
• Informar sobre projetos e iniciativas do meu mandato

Fique à vontade para conversar comigo!`;

    setMessages([{
      id: 'initial-system-message',
      text: welcomeMessage,
      sender: 'system',
      timestamp: new Date()
    }]);
    setCurrentConversationId(null);
    setLastBotResponseText(null);
    setDocumentContent(null);
    console.log('New conversation started with Vereadora Rafaela');
  }, []);

  // Auto-save conversation when messages change
  useEffect(() => {
    if (messages.length > 1) { // Skip initial system message
      const saveConversation = async () => {
        try {
          if (!currentConversationId) {
            // Create new conversation
            const title = `Conversa ${new Date().toLocaleString()}`;
            const result = await conversationService.create(title);
            if (result.success) {
              setCurrentConversationId(result.data.id);
              console.log('New conversation created:', result.data.id);
            }
          } else {
            // Save current conversation
            await databaseUtils.saveConversation(messages, `Conversa ${new Date().toLocaleString()}`);
            console.log('Conversation auto-saved');
          }
        } catch (error) {
          console.warn('Failed to auto-save conversation:', error);
        }
      };

      // Debounce auto-save
      const timeoutId = setTimeout(saveConversation, 2000);
      return () => clearTimeout(timeoutId);
    }
  }, [messages, currentConversationId]);

  // Inicialização do Sistema RAG
  const initializeRAGSystem = useCallback(async () => {
    try {
      console.log('[RAG] Inicializando sistema RAG da Vereadora Rafaela...');

      const apiKey = (import.meta.env as any).VITE_GEMINI_API_KEY;
      if (!apiKey) {
        console.error('[RAG] API Key do Gemini não encontrada');
        return;
      }

      const supabaseUrl = (import.meta.env as any).VITE_SUPABASE_URL;
      const supabaseKey = (import.meta.env as any).VITE_SUPABASE_ANON_KEY;

      if (!supabaseUrl || !supabaseKey) {
        console.error('[RAG] Configurações do Supabase não encontradas');
        return;
      }

      // Inicializar serviços
      const pipeline = new RAGPipeline(apiKey, DEFAULT_RAG_CONFIG);
      setRagPipeline(pipeline);

      const ingestion = new IngestionService({
        supportedFormats: ['pdf', 'txt', 'docx', 'xlsx', 'csv', 'image'],
        maxFileSize: 50, // 50MB
        extractMetadata: true,
        cleanContent: true,
        detectLanguage: true
      });
      setIngestionService(ingestion);

      const vectorStoreConfig = {
        dimensions: 768,
        indexType: 'hnsw' as const,
        distanceMetric: 'cosine' as const,
        enableCache: true,
        cacheSize: 1000,
        enableCompression: true
      };
      const vectorStoreInstance = new VectorStore(supabaseUrl, supabaseKey, vectorStoreConfig);
      setVectorStore(vectorStoreInstance);

      const rerankingConfig = {
        enabled: true,
        strategy: 'hybrid' as const,
        maxCandidates: 20,
        diversityWeight: 0.3,
        recencyWeight: 0.2,
        authorityWeight: 0.3,
        relevanceThreshold: 0.5
      };

      const contextConfig = {
        maxTokens: 4000,
        compressionRatio: 0.7,
        preserveStructure: true,
        enableSummarization: true,
        prioritizeRecent: true,
        includeMetadata: true
      };

      const reranking = new RerankingService(apiKey, rerankingConfig, contextConfig);
      setRerankingService(reranking);

      setRagInitialized(true);
      console.log('[RAG] Sistema RAG inicializado com sucesso!');

    } catch (error) {
      console.error('[RAG] Erro ao inicializar sistema RAG:', error);
    }
  }, []);

  useEffect(() => {
    const apiKey = (import.meta.env as any).VITE_GEMINI_API_KEY;
    if (apiKey) {
      setApiKeyLoaded(true);
      setMessages([]); // Iniciar sem mensagens

      // Inicializar sistema RAG
      initializeRAGSystem();
    } else {
      setMessages([
        {
          id: 'error-system-message',
          text: "ERRO: Chave API não configurada. Configure a variável de ambiente VITE_GEMINI_API_KEY para funcionalidade do chatbot.",
          sender: 'system',
          timestamp: new Date()
        }
      ]);
    }
  }, [initializeRAGSystem]);

  const handleDocumentLoad = useCallback((content: string) => {
    setDocumentContent(content);
    setMessages(prev => [...prev, {
      id: `doc-loaded-${Date.now()}`,
      text: content ? `Document loaded (${(content.length / 1024).toFixed(2)} KB). I will now use this document as primary context.` : 'Document context cleared.',
      sender: 'system',
      timestamp: new Date()
    }]);
  }, []); // Dependency array changed to []

  const handleSendMessage = useCallback(async (payload: SendMessagePayload) => {
    const { text: userInput, audio: audioInput } = payload;

    if (!userInput?.trim() && !audioInput) { // Must have text or audio
      console.warn("Attempted to send empty message.");
      return;
    }
    if (!apiKeyLoaded) return;

    let displayUserMessageText: string;
    if (audioInput) {
      displayUserMessageText = `[Audio Input${userInput?.trim() ? `: "${userInput.trim()}"` : ''}]`;
    } else {
      displayUserMessageText = userInput!;
    }

    const newUserMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      text: displayUserMessageText,
      sender: 'user',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, newUserMessage]);
    setIsLoading(true);
    setLastBotResponseText(null); // Clear previous bot response before new one arrives

    try {
      let result;

      // Usar sistema RAG se inicializado e há documentos
      if (ragInitialized && ragPipeline && documentContent) {
        console.log('[RAG] Usando sistema RAG avançado para resposta...');

        // Recuperar chunks relevantes
        const retrievalResult = await ragPipeline.retrieveRelevantChunks(userInput || "", 5);

        // Reranking se disponível
        let optimizedChunks = retrievalResult.chunks;
        if (rerankingService) {
          const rerankingResult = await rerankingService.rerankResults(
            userInput || "",
            retrievalResult.chunks.map(chunk => ({
              chunk,
              score: chunk.score || 0,
              rank: 1,
              searchType: 'hybrid' as const
            })),
            3
          );
          optimizedChunks = rerankingResult.rankedChunks;
        }

        // Otimizar contexto
        let contextOptimized = optimizedChunks;
        if (rerankingService) {
          const optimizationResult = await rerankingService.optimizeContext(optimizedChunks, userInput || "");
          contextOptimized = optimizationResult.chunks;
        }

        // Preparar contexto para o LLM
        const ragContext = contextOptimized.map(chunk => chunk.content).join('\n\n');

        // Usar instrução personalizada da vereadora
        const systemInstruction = gerarInstrucaoPersonalizada('rag');

        result = await generateGeminiResponse(
          userInput || "",
          systemInstruction,
          ragContext,
          useGoogleSearch,
          audioInput
        );

        // Adicionar informações sobre fontes
        if (contextOptimized.length > 0) {
          result.sources = contextOptimized.map(chunk => ({
            title: chunk.metadata.title || 'Documento',
            source: chunk.metadata.source || 'Desconhecido',
            type: chunk.metadata.type || 'document'
          }));
        }

      } else {
        // Usar sistema tradicional com persona da vereadora
        const systemInstruction = documentContent
          ? gerarInstrucaoPersonalizada('rag')
          : gerarInstrucaoPersonalizada('geral');

        result = await generateGeminiResponse(
          userInput || "",
          systemInstruction,
          documentContent,
          useGoogleSearch,
          audioInput
        );
      }
      
      const botMessage: ChatMessage = {
        id: `bot-${Date.now()}`,
        text: result.text,
        sender: 'bot',
        timestamp: new Date(),
        sources: result.sources
      };
      setMessages(prev => [...prev, botMessage]);
      setLastBotResponseText(result.text); // Store bot response for WhatsApp

    } catch (error) {
      console.error("Error generating response:", error);
      const errorMessageText = `Sorry, I encountered an error. ${error instanceof Error ? error.message : 'Please try again.'}`;
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        text: errorMessageText,
        sender: 'bot',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
      setLastBotResponseText(errorMessageText); // Also store error as last "bot" response
    } finally {
      setIsLoading(false);
    }
  }, [apiKeyLoaded, documentContent, useGoogleSearch]);

  return (
    <div className="flex flex-col h-screen font-sans bg-gray-50 text-gray-900">
      {/* Header da Vereadora */}
      <div className="p-6 pb-0">
        <VereadoraHeader
          ragStatus={ragInitialized ? 'ready' : 'initializing'}
          documentsCount={documentContent ? 1 : 0}
        />
      </div>

      {/* Navegação por Abas */}
      <TabNavigation
        tabs={[
          {
            id: 'chat',
            label: 'Chat',
            icon: (
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            )
          },
          {
            id: 'documents',
            label: 'Documentos',
            icon: (
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            )
          },
          {
            id: 'whatsapp',
            label: 'WhatsApp',
            icon: (
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21l4-7 4 7M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
              </svg>
            )
          },
          {
            id: 'history',
            label: 'Histórico',
            icon: (
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )
          }
        ]}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* Aviso de API Key */}
      {!apiKeyLoaded && (
        <div className="mx-6 mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
          <div className="flex items-center space-x-3">
            <svg className="w-5 h-5 text-amber-600 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div>
              <p className="text-amber-800 font-medium">Chave API Necessária</p>
              <p className="text-amber-700 text-sm">Configure sua chave API do Gemini para habilitar o chat</p>
            </div>
          </div>
        </div>
      )}

      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar com Abas */}
        {activeTab !== 'chat' && (
          <aside className="w-full md:w-80 bg-white border-r border-gray-200 flex flex-col overflow-hidden">
            <div className="flex-1 overflow-y-auto">
              {activeTab === 'documents' && (
                <DocumentManager
                  currentDocument={documentContent || undefined}
                  onDocumentSelect={handleDocumentLoad}
                  onDocumentDelete={(id) => {
                    // Implementar lógica de deletar documento se necessário
                    console.log('Documento deletado:', id);
                  }}
                />
              )}

              {activeTab === 'whatsapp' && (
                <WhatsAppTab lastBotResponseText={lastBotResponseText} />
              )}

              {activeTab === 'history' && (
                <div className="p-6">
                  <HistoryPanel
                    onLoadConversation={handleLoadConversation}
                    onNewConversation={handleNewConversation}
                    currentConversationId={currentConversationId}
                  />
                </div>
              )}
            </div>
          </aside>
        )}

        {/* Área Principal */}
        <main
          ref={chatInterfaceRef}
          className="flex-1 flex flex-col bg-white"
        >
          {activeTab === 'chat' ? (
            <div className="flex flex-1 overflow-hidden">
              {/* Sidebar do Chat */}
              <aside className="w-full md:w-80 bg-white border-r border-gray-200 flex flex-col overflow-hidden">
                <div className="p-6 space-y-6 overflow-y-auto">

                  {/* Upload de Documentos */}
                  <DocumentInput onDocumentLoad={handleDocumentLoad} />

                  {/* Toggle do Google Search */}
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <label htmlFor="googleSearchToggle" className="flex items-center justify-between cursor-pointer">
                      <div className="flex items-center space-x-3">
                        <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <div>
                          <span className="text-sm font-medium text-gray-900">Busca Google</span>
                          <p className="text-xs text-gray-500">
                            {useGoogleSearch ? 'Busca web habilitada' : 'Busca web desabilitada'}
                          </p>
                        </div>
                      </div>
                      <input
                        type="checkbox"
                        id="googleSearchToggle"
                        checked={useGoogleSearch}
                        onChange={(e) => setUseGoogleSearch(e.target.checked)}
                        className="sr-only peer"
                      />
                      <div className="relative w-11 h-6 bg-gray-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gray-900"></div>
                    </label>
                  </div>

                  {/* Status do Documento */}
                  {documentContent && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center space-x-3">
                        <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <div>
                          <h3 className="text-sm font-medium text-green-800">Documento Ativo</h3>
                          <p className="text-xs text-green-600">
                            {(documentContent.length / 1024).toFixed(2)} KB carregados • RAG habilitado
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </aside>

              {/* Área do Chat */}
              <div className="flex-1 flex flex-col">
                <ChatInterface
                  messages={messages}
                  onSendMessage={handleSendMessage}
                  isLoading={isLoading}
                  apiKeyLoaded={apiKeyLoaded}
                />
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">
                  {activeTab === 'documents' && 'Gerenciar Documentos'}
                  {activeTab === 'whatsapp' && 'Integração WhatsApp'}
                  {activeTab === 'history' && 'Histórico de Conversas'}
                </h2>
                <p className="text-gray-600">
                  {activeTab === 'documents' && 'Visualize e gerencie seus documentos enviados'}
                  {activeTab === 'whatsapp' && 'Configure e gerencie a integração com WhatsApp'}
                  {activeTab === 'history' && 'Acesse suas conversas anteriores'}
                </p>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default App;
