# 🚀 Guia de Deploy no Netlify - Sistema da Vereadora Rafaela de Nilda

Este guia fornece instruções completas para fazer deploy do sistema no Netlify.

## 📋 Pré-requisitos

### 1. Contas Necessárias
- ✅ Conta no [Netlify](https://netlify.com)
- ✅ Conta no [Supabase](https://supabase.com)
- ✅ API Key do [Google Gemini](https://makersuite.google.com/app/apikey)

### 2. Ferramentas Locais
```bash
# Instalar Netlify CLI
npm install -g netlify-cli

# Fazer login no Netlify
netlify login

# Verificar se está logado
netlify status
```

## 🔧 Configuração Inicial

### 1. Configurar Supabase

1. **Criar projeto no Supabase**
   - Acesse [supabase.com](https://supabase.com)
   - Crie um novo projeto
   - Anote a URL e as chaves

2. **Executar scripts SQL**
   ```sql
   -- No SQL Editor do Supabase, execute:
   
   -- Habilitar extensão vetorial
   CREATE EXTENSION IF NOT EXISTS vector;
   
   -- Executar schema principal
   -- (<PERSON> o conteúdo de database/schema.sql)
   ```

3. **Configurar RLS (Row Level Security)**
   - Configure as políticas de segurança conforme necessário
   - Para desenvolvimento, pode desabilitar temporariamente

### 2. Obter API Keys

1. **Google Gemini API**
   - Acesse [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Crie uma nova API key
   - Guarde a chave com segurança

2. **Supabase Keys**
   - No painel do Supabase, vá em Settings > API
   - Copie a URL do projeto
   - Copie a chave anônima (anon key)
   - Copie a chave de serviço (service_role key)

## 🌐 Deploy no Netlify

### Método 1: Deploy Manual (Recomendado para primeiro deploy)

1. **Preparar o projeto**
   ```bash
   # Clonar/baixar o projeto
   cd AISTUDIOCHATRAG
   
   # Instalar dependências
   npm install
   
   # Criar arquivo de ambiente local (opcional para teste)
   cp .env.example .env.local
   # Editar .env.local com suas chaves
   ```

2. **Fazer build local**
   ```bash
   # Testar build
   npm run build
   
   # Testar preview local
   npm run preview
   ```

3. **Deploy inicial**
   ```bash
   # Tornar script executável (Linux/Mac)
   chmod +x deploy.sh
   
   # Deploy de preview
   ./deploy.sh preview
   
   # Ou usar comando direto
   netlify deploy --dir=dist
   ```

4. **Configurar variáveis de ambiente no Netlify**
   - Acesse o painel do Netlify
   - Vá em Site settings > Environment variables
   - Adicione as variáveis:
     ```
     VITE_GEMINI_API_KEY=sua_chave_gemini
     VITE_SUPABASE_URL=https://seu-projeto.supabase.co
     VITE_SUPABASE_ANON_KEY=sua_chave_anonima
     VITE_SUPABASE_SERVICE_ROLE_KEY=sua_chave_service
     NODE_ENV=production
     ```

5. **Deploy para produção**
   ```bash
   # Após testar o preview
   ./deploy.sh production
   
   # Ou usar comando direto
   netlify deploy --prod --dir=dist
   ```

### Método 2: Deploy Automático via Git

1. **Conectar repositório**
   - No painel do Netlify, clique em "New site from Git"
   - Conecte seu repositório GitHub/GitLab
   - Configure as opções de build:
     ```
     Build command: npm run build
     Publish directory: dist
     ```

2. **Configurar variáveis de ambiente**
   - Adicione todas as variáveis listadas acima
   - O deploy será automático a cada push

## 🔍 Verificação Pós-Deploy

### 1. Testes Básicos
```bash
# Verificar se o site está no ar
curl -I https://seu-site.netlify.app

# Testar health endpoint
curl https://seu-site.netlify.app/api/health

# Executar testes automatizados (local)
node test-vereadora-system.js
```

### 2. Funcionalidades a Testar
- ✅ Carregamento da página inicial
- ✅ Chat com a persona da vereadora
- ✅ Upload de documentos
- ✅ Sistema RAG funcionando
- ✅ Integração WhatsApp (se configurada)
- ✅ Dashboard de monitoramento
- ✅ Responsividade mobile

### 3. Performance
- Verificar no [PageSpeed Insights](https://pagespeed.web.dev/)
- Testar carregamento em diferentes dispositivos
- Verificar se PWA está funcionando

## 🛠️ Configurações Avançadas

### 1. Domínio Customizado
```bash
# Adicionar domínio customizado
netlify sites:update --name=vereadora-rafaela

# Configurar DNS
# Aponte seu domínio para o Netlify
```

### 2. HTTPS e Segurança
- HTTPS é automático no Netlify
- Configurar headers de segurança (já incluído no netlify.toml)
- Configurar CSP se necessário

### 3. Monitoramento
- Configurar alertas no Netlify
- Integrar com serviços de monitoramento
- Configurar logs de erro

## 🚨 Troubleshooting

### Problemas Comuns

1. **Build falha**
   ```bash
   # Verificar logs de build
   netlify logs
   
   # Testar build local
   npm run build
   
   # Verificar dependências
   npm ci
   ```

2. **Variáveis de ambiente não funcionam**
   - Verificar se começam com `VITE_`
   - Verificar se estão configuradas no painel do Netlify
   - Fazer redeploy após adicionar variáveis

3. **Erro 404 em rotas**
   - Verificar se `_redirects` está correto
   - Verificar configuração SPA no `netlify.toml`

4. **PDF.js não funciona**
   - Verificar se worker está sendo carregado
   - Verificar headers CORS
   - Verificar paths dos workers

5. **Supabase não conecta**
   - Verificar URLs e chaves
   - Verificar configurações de CORS no Supabase
   - Verificar RLS policies

### Logs e Debug
```bash
# Ver logs do Netlify
netlify logs

# Ver logs de função
netlify functions:log

# Debug local
npm run dev
```

## 📊 Monitoramento Contínuo

### 1. Métricas do Netlify
- Acessar Analytics no painel
- Monitorar performance
- Verificar uptime

### 2. Métricas do Sistema
- Dashboard interno de monitoramento
- Logs de erro do Supabase
- Métricas de uso da API Gemini

### 3. Alertas
- Configurar alertas de downtime
- Alertas de erro de build
- Alertas de limite de API

## 🔄 Atualizações

### Deploy de Atualizações
```bash
# Para atualizações menores
./deploy.sh preview  # Testar primeiro
./deploy.sh production  # Deploy para produção

# Para atualizações maiores
git push origin main  # Se usando deploy automático
```

### Rollback
```bash
# Rollback para deploy anterior
netlify rollback

# Ou pelo painel web do Netlify
```

## 📞 Suporte

### Recursos Úteis
- [Documentação do Netlify](https://docs.netlify.com/)
- [Documentação do Supabase](https://supabase.com/docs)
- [Documentação do Gemini](https://ai.google.dev/docs)

### Contato
- Abrir issue no repositório GitHub
- Consultar logs de erro
- Verificar status dos serviços

---

**🎉 Parabéns! Seu sistema da Vereadora Rafaela está no ar!**

Lembre-se de:
- Monitorar regularmente o sistema
- Manter as dependências atualizadas
- Fazer backup das configurações
- Testar antes de fazer deploy para produção
