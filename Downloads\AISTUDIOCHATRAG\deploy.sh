#!/bin/bash

# Script de Deploy para Netlify - Sistema da Vereadora Rafaela de Nilda
# Uso: ./deploy.sh [preview|production]

set -e  # Parar em caso de erro

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar se o Netlify CLI está instalado
if ! command -v netlify &> /dev/null; then
    error "Netlify CLI não encontrado. Instale com: npm install -g netlify-cli"
    exit 1
fi

# Verificar se está logado no Netlify
if ! netlify status &> /dev/null; then
    warning "Não está logado no Netlify. Execute: netlify login"
    exit 1
fi

# Determinar tipo de deploy
DEPLOY_TYPE=${1:-preview}

log "Iniciando deploy do Sistema da Vereadora Rafaela de Nilda..."
log "Tipo de deploy: $DEPLOY_TYPE"

# Verificar se as variáveis de ambiente estão configuradas
log "Verificando variáveis de ambiente..."

if [ -z "$VITE_GEMINI_API_KEY" ] && [ ! -f ".env.local" ]; then
    warning "VITE_GEMINI_API_KEY não encontrada. Certifique-se de configurar no Netlify."
fi

if [ -z "$VITE_SUPABASE_URL" ] && [ ! -f ".env.local" ]; then
    warning "VITE_SUPABASE_URL não encontrada. Certifique-se de configurar no Netlify."
fi

# Limpar build anterior
log "Limpando build anterior..."
rm -rf dist/
rm -rf node_modules/.vite/

# Instalar dependências
log "Instalando dependências..."
npm ci

# Executar testes (se existirem)
if [ -f "test-vereadora-system.js" ]; then
    log "Executando testes do sistema..."
    # npm test || warning "Alguns testes falharam, mas continuando deploy..."
fi

# Build para produção
log "Executando build para produção..."
NODE_ENV=production npm run build

# Verificar se o build foi bem-sucedido
if [ ! -d "dist" ]; then
    error "Build falhou - diretório dist não encontrado"
    exit 1
fi

# Verificar arquivos essenciais
log "Verificando arquivos essenciais..."
ESSENTIAL_FILES=("dist/index.html" "dist/assets")

for file in "${ESSENTIAL_FILES[@]}"; do
    if [ ! -e "$file" ]; then
        error "Arquivo essencial não encontrado: $file"
        exit 1
    fi
done

success "Build concluído com sucesso!"

# Deploy baseado no tipo
if [ "$DEPLOY_TYPE" = "production" ]; then
    log "Fazendo deploy para PRODUÇÃO..."
    
    # Confirmar deploy para produção
    read -p "Tem certeza que deseja fazer deploy para PRODUÇÃO? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log "Deploy cancelado pelo usuário."
        exit 0
    fi
    
    # Deploy para produção
    netlify deploy --prod --dir=dist --message="Deploy produção - Sistema Vereadora Rafaela v$(date +%Y%m%d-%H%M%S)"
    
    success "Deploy para PRODUÇÃO concluído!"
    log "Site disponível em: $(netlify status --json | jq -r '.site.url')"
    
else
    log "Fazendo deploy de PREVIEW..."
    
    # Deploy de preview
    DEPLOY_URL=$(netlify deploy --dir=dist --message="Deploy preview - Sistema Vereadora Rafaela v$(date +%Y%m%d-%H%M%S)" --json | jq -r '.deploy_url')
    
    success "Deploy de PREVIEW concluído!"
    log "Preview disponível em: $DEPLOY_URL"
fi

# Executar verificações pós-deploy
log "Executando verificações pós-deploy..."

# Verificar se o site está respondendo
SITE_URL=$(netlify status --json | jq -r '.site.url')
if [ "$DEPLOY_TYPE" = "production" ]; then
    CHECK_URL="$SITE_URL"
else
    CHECK_URL="$DEPLOY_URL"
fi

log "Verificando se o site está acessível: $CHECK_URL"

# Aguardar um pouco para o deploy propagar
sleep 10

# Verificar resposta HTTP
HTTP_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$CHECK_URL" || echo "000")

if [ "$HTTP_STATUS" = "200" ]; then
    success "Site está respondendo corretamente (HTTP $HTTP_STATUS)"
else
    warning "Site pode não estar respondendo corretamente (HTTP $HTTP_STATUS)"
fi

# Verificar health endpoint se existir
HEALTH_URL="$CHECK_URL/api/health"
HEALTH_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$HEALTH_URL" || echo "000")

if [ "$HEALTH_STATUS" = "200" ]; then
    success "Health check passou (HTTP $HEALTH_STATUS)"
else
    log "Health check não disponível ou falhou (HTTP $HEALTH_STATUS)"
fi

# Mostrar informações finais
echo
log "=== INFORMAÇÕES DO DEPLOY ==="
log "Tipo: $DEPLOY_TYPE"
log "URL: $CHECK_URL"
log "Timestamp: $(date)"
log "Commit: $(git rev-parse --short HEAD 2>/dev/null || echo 'N/A')"
echo

# Instruções pós-deploy
log "=== PRÓXIMOS PASSOS ==="
log "1. Teste o sistema no ambiente de $DEPLOY_TYPE"
log "2. Verifique se todas as funcionalidades estão funcionando"
log "3. Configure as variáveis de ambiente no painel do Netlify se necessário"
log "4. Monitore os logs para possíveis erros"

if [ "$DEPLOY_TYPE" = "preview" ]; then
    log "5. Se tudo estiver OK, execute: ./deploy.sh production"
fi

success "Deploy concluído com sucesso! 🚀"
