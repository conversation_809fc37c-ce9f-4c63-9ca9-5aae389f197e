{"name": "Vereadora Rafaela de Nilda - Atendimento Digital", "short_name": "Vereadora Rafaela", "description": "Sistema de atendimento digital da Vereadora Rafaela de Nilda de Parnamirim/RN com tecnologia RAG avançada", "version": "1.0.0", "start_url": "/", "display": "standalone", "orientation": "portrait-primary", "theme_color": "#1e40af", "background_color": "#ffffff", "scope": "/", "lang": "pt-BR", "dir": "ltr", "icons": [{"src": "/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "screenshots": [{"src": "/screenshot-desktop.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Interface principal do sistema"}, {"src": "/screenshot-mobile.png", "sizes": "375x667", "type": "image/png", "form_factor": "narrow", "label": "Interface mobile do sistema"}], "categories": ["government", "productivity", "social"], "shortcuts": [{"name": "Chat com a Vereadora", "short_name": "Cha<PERSON>", "description": "Conversar diretamente com a Vereadora Rafaela", "url": "/chat", "icons": [{"src": "/icon-chat.png", "sizes": "96x96"}]}, {"name": "Enviar Documento", "short_name": "Documentos", "description": "Enviar documentos para análise", "url": "/documents", "icons": [{"src": "/icon-document.png", "sizes": "96x96"}]}, {"name": "WhatsApp", "short_name": "WhatsApp", "description": "Atendimento via WhatsApp", "url": "/whatsapp", "icons": [{"src": "/icon-whatsapp.png", "sizes": "96x96"}]}], "related_applications": [{"platform": "web", "url": "https://vereadora-rafaela.netlify.app"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "protocol_handlers": [{"protocol": "web+vereadora", "url": "/?action=%s"}]}