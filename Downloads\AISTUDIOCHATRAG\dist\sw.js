// Service Worker para o Sistema da Vereadora Rafaela de Nilda
const CACHE_NAME = 'vereadora-rafaela-v1.0.0';
const STATIC_CACHE = 'static-v1';
const DYNAMIC_CACHE = 'dynamic-v1';

// Arquivos para cache estático
const STATIC_FILES = [
  '/',
  '/index.html',
  '/index.css',
  '/manifest.json',
  '/favicon.ico'
];

// Arquivos que não devem ser cacheados
const EXCLUDE_FROM_CACHE = [
  '/api/',
  '/.netlify/',
  '/admin',
  'chrome-extension'
];

// Instalação do Service Worker
self.addEventListener('install', event => {
  console.log('[SW] Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('[SW] Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('[SW] Installation complete');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('[SW] Installation failed:', error);
      })
  );
});

// Ativação do Service Worker
self.addEventListener('activate', event => {
  console.log('[SW] Activating...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('[SW] Deleting old cache:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('[SW] Activation complete');
        return self.clients.claim();
      })
  );
});

// Interceptação de requests
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Ignorar requests que não devem ser cacheados
  if (EXCLUDE_FROM_CACHE.some(path => url.pathname.startsWith(path))) {
    return;
  }
  
  // Estratégia de cache
  if (request.method === 'GET') {
    event.respondWith(handleGetRequest(request));
  }
});

// Manipular requests GET
async function handleGetRequest(request) {
  const url = new URL(request.url);
  
  try {
    // Para arquivos estáticos, usar cache first
    if (STATIC_FILES.includes(url.pathname) || isStaticAsset(url.pathname)) {
      return await cacheFirst(request);
    }
    
    // Para APIs e conteúdo dinâmico, usar network first
    if (url.pathname.startsWith('/api/') || isDynamicContent(url.pathname)) {
      return await networkFirst(request);
    }
    
    // Para outros recursos, usar stale while revalidate
    return await staleWhileRevalidate(request);
    
  } catch (error) {
    console.error('[SW] Fetch error:', error);
    
    // Fallback para página offline se disponível
    if (request.destination === 'document') {
      return await caches.match('/offline.html') || 
             await caches.match('/index.html');
    }
    
    return new Response('Offline', { status: 503 });
  }
}

// Estratégia Cache First
async function cacheFirst(request) {
  const cachedResponse = await caches.match(request);
  if (cachedResponse) {
    return cachedResponse;
  }
  
  const networkResponse = await fetch(request);
  if (networkResponse.ok) {
    const cache = await caches.open(STATIC_CACHE);
    cache.put(request, networkResponse.clone());
  }
  
  return networkResponse;
}

// Estratégia Network First
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    throw error;
  }
}

// Estratégia Stale While Revalidate
async function staleWhileRevalidate(request) {
  const cachedResponse = await caches.match(request);
  
  const networkResponsePromise = fetch(request)
    .then(response => {
      if (response.ok) {
        const cache = caches.open(DYNAMIC_CACHE);
        cache.then(c => c.put(request, response.clone()));
      }
      return response;
    })
    .catch(() => null);
  
  return cachedResponse || await networkResponsePromise;
}

// Verificar se é um asset estático
function isStaticAsset(pathname) {
  const staticExtensions = ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2'];
  return staticExtensions.some(ext => pathname.endsWith(ext));
}

// Verificar se é conteúdo dinâmico
function isDynamicContent(pathname) {
  const dynamicPaths = ['/chat', '/documents', '/whatsapp', '/monitoring'];
  return dynamicPaths.some(path => pathname.startsWith(path));
}

// Manipular mensagens do cliente
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});

// Sincronização em background (para futuras funcionalidades)
self.addEventListener('sync', event => {
  if (event.tag === 'background-sync') {
    event.waitUntil(doBackgroundSync());
  }
});

async function doBackgroundSync() {
  console.log('[SW] Background sync triggered');
  // Implementar sincronização de dados offline quando necessário
}

// Notificações push (para futuras funcionalidades)
self.addEventListener('push', event => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/icon-192x192.png',
      badge: '/badge-72x72.png',
      tag: 'vereadora-notification',
      requireInteraction: true,
      actions: [
        {
          action: 'open',
          title: 'Abrir',
          icon: '/icon-open.png'
        },
        {
          action: 'close',
          title: 'Fechar',
          icon: '/icon-close.png'
        }
      ]
    };
    
    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// Clique em notificação
self.addEventListener('notificationclick', event => {
  event.notification.close();
  
  if (event.action === 'open') {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

console.log('[SW] Service Worker loaded for Vereadora Rafaela System');
