# Headers para otimização no Netlify

# Headers globais
/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin

# Headers para arquivos estáticos
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Headers para service workers
/sw.js
  Cache-Control: no-cache

# Headers para PDF.js worker
/pdf.worker.min.js
  Content-Type: application/javascript
  Cache-Control: public, max-age=86400

# Headers para fontes
/fonts/*
  Cache-Control: public, max-age=31536000
  Access-Control-Allow-Origin: *

# Headers para imagens
/images/*
  Cache-Control: public, max-age=86400

# Headers para API endpoints
/api/*
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization

# Headers para arquivos de configuração
/.well-known/*
  Cache-Control: public, max-age=3600
