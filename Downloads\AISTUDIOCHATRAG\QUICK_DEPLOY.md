# 🚀 Deploy Rápido no Netlify - 5 Minutos

## ⚡ Passo a Passo Rápido

### 1. Pré-requisitos (2 min)
```bash
# Instalar Netlify CLI
npm install -g netlify-cli

# Login no Netlify
netlify login
```

### 2. Configurar APIs (2 min)
- **Gemini API**: [makersuite.google.com/app/apikey](https://makersuite.google.com/app/apikey)
- **Supabase**: [supabase.com](https://supabase.com) → Criar projeto → Settings → API

### 3. Deploy (1 min)
```bash
# No diretório do projeto
npm install
npm run build
netlify deploy --dir=dist

# Para produção (após testar preview)
netlify deploy --prod --dir=dist
```

### 4. Configurar Variáveis no Netlify
No painel do Netlify → Site settings → Environment variables:

```
VITE_GEMINI_API_KEY=sua_chave_gemini
VITE_SUPABASE_URL=https://seu-projeto.supabase.co
VITE_SUPABASE_ANON_KEY=sua_chave_supabase_anonima
VITE_SUPABASE_SERVICE_ROLE_KEY=sua_chave_supabase_service
NODE_ENV=production
```

### 5. Configurar Banco (Supabase)
No SQL Editor do Supabase:
```sql
CREATE EXTENSION IF NOT EXISTS vector;
-- Cole o conteúdo de database/schema.sql
```

## ✅ Verificação
- Site carregando: ✅
- Chat funcionando: ✅
- Upload de documentos: ✅
- Sistema RAG ativo: ✅

## 🆘 Problemas?
1. **Build falha**: Verificar variáveis de ambiente
2. **404 em rotas**: Verificar `netlify.toml`
3. **API não funciona**: Verificar chaves no painel Netlify

## 📞 Suporte
- Ver `DEPLOY_GUIDE.md` para instruções completas
- Logs: `netlify logs`
- Status: `netlify status`

---
**🎉 Pronto! Sistema da Vereadora Rafaela no ar!**
