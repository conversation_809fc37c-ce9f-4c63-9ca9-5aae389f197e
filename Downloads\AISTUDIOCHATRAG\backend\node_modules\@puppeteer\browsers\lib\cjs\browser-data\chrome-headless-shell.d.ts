import { BrowserPlatform } from './types.js';
export declare function resolveDownloadUrl(platform: BrowserPlatform, buildId: string, baseUrl?: string): string;
export declare function resolveDownloadPath(platform: BrowserPlatform, buildId: string): string[];
export declare function relativeExecutablePath(platform: BrowserPlatform, _buildId: string): string;
export { resolveBuildId, compareVersions } from './chrome.js';
//# sourceMappingURL=chrome-headless-shell.d.ts.map