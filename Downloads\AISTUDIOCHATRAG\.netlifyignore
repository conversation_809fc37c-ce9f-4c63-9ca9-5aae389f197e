# Arquivos e diretórios a serem ignorados no deploy do Netlify

# Dependências de desenvolvimento
node_modules/
.npm/
.yarn/

# Arquivos de build local
dist/
build/
.vite/
.cache/

# Arquivos de ambiente local
.env.local
.env.development
.env.test

# Arquivos de teste
test/
tests/
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts
test-vereadora-system.js

# Arquivos de desenvolvimento
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Arquivos temporários
tmp/
temp/
.tmp/

# Arquivos do sistema
.DS_Store
Thumbs.db
desktop.ini

# Arquivos de backup
*.bak
*.backup
*.old

# Documentação de desenvolvimento
docs/development/
DEVELOPMENT.md
CONTRIBUTING.md

# Scripts de desenvolvimento
scripts/dev/
deploy.sh

# Arquivos de configuração local
.editorconfig
.gitignore
.prettierrc
.eslintrc*

# Arquivos de análise
coverage/
.nyc_output/
.coverage/

# Arquivos de banco de dados local
*.db
*.sqlite
*.sqlite3

# Arquivos de certificados locais
*.pem
*.key
*.crt

# Arquivos grandes desnecessários
*.zip
*.tar.gz
*.rar

# Diretórios específicos do projeto
backend/node_modules/
backend/logs/
backend/.env*
