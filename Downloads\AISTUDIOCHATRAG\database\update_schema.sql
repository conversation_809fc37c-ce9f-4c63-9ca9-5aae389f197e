-- SCRIPT DE ATUALIZAÇÃO PARA CORRIGIR OS ERROS RPC
-- Execute este script no SQL Editor do Supabase para corrigir os erros

-- 1. <PERSON><PERSON><PERSON><PERSON> que a extensão vector existe
CREATE EXTENSION IF NOT EXISTS vector;

-- 2. <PERSON><PERSON><PERSON> as funções RPC que o sistema está tentando chamar
CREATE OR REPLACE FUNCTION create_extension_if_not_exists()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Verificar se a extensão vector já existe
    IF NOT EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'vector') THEN
        CREATE EXTENSION vector;
        RETURN 'Extensão vector criada com sucesso';
    ELSE
        RETURN 'Extensão vector já existe';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RETURN 'Erro ao criar extensão: ' || SQLERRM;
END;
$$;

CREATE OR REPLACE FUNCTION create_vector_chunks_table()
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Verificar se a tabela document_chunks existe
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'document_chunks') THEN
        RETURN 'Tabela document_chunks já existe e está pronta';
    ELSE
        -- Criar a tabela se não existir
        CREATE TABLE document_chunks (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            content TEXT NOT NULL,
            metadata JSONB DEFAULT '{}',
            embedding vector(768),
            source TEXT,
            chunk_index INTEGER,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Criar índice vetorial
        CREATE INDEX IF NOT EXISTS document_chunks_embedding_idx 
        ON document_chunks USING ivfflat (embedding vector_cosine_ops)
        WITH (lists = 100);
        
        -- Habilitar RLS
        ALTER TABLE document_chunks ENABLE ROW LEVEL SECURITY;
        
        -- Política para permitir acesso
        CREATE POLICY "Allow all operations on document_chunks" 
        ON document_chunks FOR ALL USING (true);
        
        RETURN 'Tabela document_chunks criada com sucesso';
    END IF;
END;
$$;

-- 3. Testar as funções
SELECT create_extension_if_not_exists();
SELECT create_vector_chunks_table();

-- 4. Verificar se tudo foi criado corretamente
SELECT 
    'Extensão vector' as item,
    CASE WHEN EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'vector') 
         THEN 'OK' ELSE 'ERRO' END as status
UNION ALL
SELECT 
    'Tabela document_chunks' as item,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'document_chunks') 
         THEN 'OK' ELSE 'ERRO' END as status
UNION ALL
SELECT 
    'Função create_extension_if_not_exists' as item,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'create_extension_if_not_exists') 
         THEN 'OK' ELSE 'ERRO' END as status
UNION ALL
SELECT 
    'Função create_vector_chunks_table' as item,
    CASE WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'create_vector_chunks_table') 
         THEN 'OK' ELSE 'ERRO' END as status;
