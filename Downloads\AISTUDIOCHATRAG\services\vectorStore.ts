// Sistema de Armazenamento Vetorial e Retrieval Híbrido
import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { DocumentChunk } from './ragPipeline';

export interface VectorStoreConfig {
  dimensions: number;
  indexType: 'flat' | 'ivf' | 'hnsw';
  distanceMetric: 'cosine' | 'euclidean' | 'dot_product';
  enableCache: boolean;
  cacheSize: number;
  enableCompression: boolean;
}

export interface SearchQuery {
  vector?: number[];
  text?: string;
  filters?: {
    category?: string[];
    dateRange?: { start: Date; end: Date };
    source?: string[];
    type?: string[];
    tags?: string[];
  };
  limit?: number;
  threshold?: number;
}

export interface SearchResult {
  chunk: DocumentChunk;
  score: number;
  rank: number;
  searchType: 'vector' | 'text' | 'hybrid';
}

export interface IndexStats {
  totalDocuments: number;
  totalChunks: number;
  indexSize: number;
  lastUpdated: Date;
  categories: Record<string, number>;
  sources: Record<string, number>;
}

// Implementação do armazenamento vetorial usando Supabase + pgvector
export class VectorStore {
  private supabase: SupabaseClient;
  private config: VectorStoreConfig;
  private cache: Map<string, SearchResult[]> = new Map();
  private bm25Index: Map<string, any> = new Map();

  constructor(supabaseUrl: string, supabaseKey: string, config: VectorStoreConfig) {
    this.supabase = createClient(supabaseUrl, supabaseKey);
    this.config = config;
    this.initializeDatabase();
  }

  // INICIALIZAÇÃO DO BANCO DE DADOS
  private async initializeDatabase(): Promise<void> {
    console.log('[VectorStore] Inicializando banco de dados vetorial...');

    try {
      // Criar extensão pgvector se não existir
      const { data: extensionResult, error: extensionError } = await this.supabase.rpc('create_extension_if_not_exists');

      if (extensionError) {
        console.error('[VectorStore] Erro ao criar extensão:', extensionError);
      } else {
        console.log('[VectorStore] Extensão vetorial:', extensionResult);
      }

      // Criar tabela de chunks vetoriais se não existir
      const { data: tableResult, error: tableError } = await this.supabase.rpc('create_vector_chunks_table');

      if (tableError) {
        console.error('[VectorStore] Erro ao criar tabela:', tableError);
      } else {
        console.log('[VectorStore] Tabela de chunks:', tableResult);
        console.log('[VectorStore] Banco de dados vetorial inicializado com sucesso');
      }
    } catch (error) {
      console.error('[VectorStore] Erro na inicialização:', error);
    }
  }

  // ARMAZENAMENTO DE CHUNKS
  async storeChunks(chunks: DocumentChunk[]): Promise<string[]> {
    console.log(`[VectorStore] Armazenando ${chunks.length} chunks...`);
    
    const storedIds: string[] = [];
    
    for (const chunk of chunks) {
      try {
        const { data, error } = await this.supabase
          .from('document_chunks')
          .insert({
            id: chunk.id,
            content: chunk.content,
            embedding: chunk.embedding,
            metadata: chunk.metadata,
            source: chunk.metadata?.source || 'unknown',
            chunk_index: chunk.metadata?.chunkIndex || 0,
            created_at: new Date().toISOString()
          })
          .select('id')
          .single();
        
        if (error) {
          console.error(`[VectorStore] Erro ao armazenar chunk ${chunk.id}:`, error);
        } else {
          storedIds.push(data.id);
          
          // Indexar para BM25
          this.indexChunkForBM25(chunk);
        }
      } catch (error) {
        console.error(`[VectorStore] Erro inesperado ao armazenar chunk ${chunk.id}:`, error);
      }
    }
    
    console.log(`[VectorStore] ${storedIds.length} chunks armazenados com sucesso`);
    return storedIds;
  }

  // BUSCA VETORIAL
  async vectorSearch(
    queryVector: number[],
    limit: number = 10,
    threshold: number = 0.7,
    filters?: SearchQuery['filters']
  ): Promise<SearchResult[]> {
    console.log(`[VectorStore] Executando busca vetorial (limite: ${limit}, threshold: ${threshold})`);
    
    try {
      // Usar a função hybrid_search que criamos no banco
      const { data, error } = await this.supabase.rpc('hybrid_search', {
        query_text: '',
        query_embedding: queryVector,
        match_threshold: threshold,
        match_count: limit
      });

      if (error) {
        console.error('[VectorStore] Erro na busca vetorial:', error);
        return [];
      }

      if (!data || data.length === 0) {
        console.log('[VectorStore] Nenhum resultado encontrado na busca vetorial');
        return [];
      }

      return data.map((item: any, index: number) => ({
        chunk: {
          id: item.id,
          content: item.content,
          metadata: item.metadata,
          embedding: null // Não retornamos o embedding para economizar banda
        },
        score: item.similarity,
        rank: index + 1,
        searchType: 'vector' as const
      }));

    } catch (error) {
      console.error('[VectorStore] Erro inesperado na busca vetorial:', error);
      return [];
    }
  }

  // BUSCA TEXTUAL (BM25)
  async textSearch(
    queryText: string,
    limit: number = 10,
    filters?: SearchQuery['filters']
  ): Promise<SearchResult[]> {
    console.log(`[VectorStore] Executando busca textual: "${queryText}"`);
    
    try {
      let query = this.supabase
        .from('document_chunks')
        .select('*')
        .textSearch('content', queryText, {
          type: 'websearch',
          config: 'portuguese'
        })
        .limit(limit);
      
      // Aplicar filtros
      if (filters) {
        query = this.applyFilters(query, filters);
      }
      
      const { data, error } = await query;
      
      if (error) {
        console.error('[VectorStore] Erro na busca textual:', error);
        return [];
      }
      
      // Calcular scores BM25 (implementação simplificada)
      return data.map((item: any, index: number) => {
        const score = this.calculateBM25Score(queryText, item.content);
        return {
          chunk: {
            id: item.id,
            content: item.content,
            metadata: item.metadata,
            embedding: item.embedding
          },
          score,
          rank: index + 1,
          searchType: 'text' as const
        };
      }).sort((a, b) => b.score - a.score);
      
    } catch (error) {
      console.error('[VectorStore] Erro inesperado na busca textual:', error);
      return [];
    }
  }

  // BUSCA HÍBRIDA
  async hybridSearch(query: SearchQuery): Promise<SearchResult[]> {
    console.log('[VectorStore] Executando busca híbrida...');
    
    const results: SearchResult[] = [];
    
    // Busca vetorial se vector fornecido
    if (query.vector) {
      const vectorResults = await this.vectorSearch(
        query.vector,
        query.limit || 20,
        query.threshold || 0.7,
        query.filters
      );
      results.push(...vectorResults);
    }
    
    // Busca textual se texto fornecido
    if (query.text) {
      const textResults = await this.textSearch(
        query.text,
        query.limit || 20,
        query.filters
      );
      results.push(...textResults);
    }
    
    // Fusão de resultados usando Reciprocal Rank Fusion (RRF)
    const fusedResults = this.fuseSearchResults(results, query.limit || 10);
    
    // Marcar como busca híbrida
    return fusedResults.map(result => ({
      ...result,
      searchType: 'hybrid' as const
    }));
  }

  // BUSCA TEMPORAL
  async temporalSearch(
    query: SearchQuery,
    timeWeight: number = 0.3
  ): Promise<SearchResult[]> {
    console.log('[VectorStore] Executando busca temporal...');
    
    // Executar busca híbrida normal
    const baseResults = await this.hybridSearch(query);
    
    // Aplicar peso temporal
    const now = new Date();
    return baseResults.map(result => {
      const createdAt = new Date(result.chunk.metadata.date || now);
      const daysDiff = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60 * 24);
      
      // Decaimento temporal exponencial
      const temporalScore = Math.exp(-daysDiff / 30); // 30 dias de meia-vida
      const adjustedScore = result.score * (1 - timeWeight) + temporalScore * timeWeight;
      
      return {
        ...result,
        score: adjustedScore
      };
    }).sort((a, b) => b.score - a.score);
  }

  // BUSCA POR GRAFO (relacionamentos)
  async graphSearch(
    seedChunkId: string,
    depth: number = 2,
    limit: number = 10
  ): Promise<SearchResult[]> {
    console.log(`[VectorStore] Executando busca por grafo a partir de ${seedChunkId}`);
    
    try {
      // Buscar chunk inicial
      const { data: seedChunk, error } = await this.supabase
        .from('document_chunks')
        .select('*')
        .eq('id', seedChunkId)
        .single();
      
      if (error || !seedChunk) {
        console.error('[VectorStore] Chunk inicial não encontrado:', error);
        return [];
      }
      
      // Buscar chunks relacionados por similaridade
      const relatedResults = await this.vectorSearch(
        seedChunk.embedding,
        limit * 2,
        0.6
      );
      
      // Filtrar o chunk inicial e aplicar limite
      return relatedResults
        .filter(result => result.chunk.id !== seedChunkId)
        .slice(0, limit);
      
    } catch (error) {
      console.error('[VectorStore] Erro na busca por grafo:', error);
      return [];
    }
  }

  // ATUALIZAÇÃO DE CHUNKS
  async updateChunk(chunkId: string, updates: Partial<DocumentChunk>): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('document_chunks')
        .update({
          content: updates.content,
          embedding: updates.embedding,
          metadata: updates.metadata,
          updated_at: new Date().toISOString()
        })
        .eq('id', chunkId);
      
      if (error) {
        console.error(`[VectorStore] Erro ao atualizar chunk ${chunkId}:`, error);
        return false;
      }
      
      // Atualizar índice BM25 se necessário
      if (updates.content) {
        this.updateBM25Index(chunkId, updates.content);
      }
      
      return true;
    } catch (error) {
      console.error(`[VectorStore] Erro inesperado ao atualizar chunk ${chunkId}:`, error);
      return false;
    }
  }

  // REMOÇÃO DE CHUNKS
  async deleteChunk(chunkId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('document_chunks')
        .delete()
        .eq('id', chunkId);
      
      if (error) {
        console.error(`[VectorStore] Erro ao deletar chunk ${chunkId}:`, error);
        return false;
      }
      
      // Remover do índice BM25
      this.bm25Index.delete(chunkId);
      
      return true;
    } catch (error) {
      console.error(`[VectorStore] Erro inesperado ao deletar chunk ${chunkId}:`, error);
      return false;
    }
  }

  // ESTATÍSTICAS DO ÍNDICE
  async getIndexStats(): Promise<IndexStats> {
    try {
      const { data, error } = await this.supabase
        .from('document_chunks')
        .select('metadata, created_at');
      
      if (error) {
        console.error('[VectorStore] Erro ao obter estatísticas:', error);
        return this.getEmptyStats();
      }
      
      const stats: IndexStats = {
        totalDocuments: 0,
        totalChunks: data.length,
        indexSize: 0,
        lastUpdated: new Date(),
        categories: {},
        sources: {}
      };
      
      const documents = new Set<string>();
      
      data.forEach(item => {
        // Contar documentos únicos
        if (item.metadata?.source) {
          documents.add(item.metadata.source);
        }
        
        // Contar categorias
        const category = item.metadata?.category || 'uncategorized';
        stats.categories[category] = (stats.categories[category] || 0) + 1;
        
        // Contar fontes
        const source = item.metadata?.source || 'unknown';
        stats.sources[source] = (stats.sources[source] || 0) + 1;
        
        // Atualizar última atualização
        const createdAt = new Date(item.created_at);
        if (createdAt > stats.lastUpdated) {
          stats.lastUpdated = createdAt;
        }
      });
      
      stats.totalDocuments = documents.size;
      stats.indexSize = data.length * this.config.dimensions * 4; // Aproximação em bytes
      
      return stats;
    } catch (error) {
      console.error('[VectorStore] Erro inesperado ao obter estatísticas:', error);
      return this.getEmptyStats();
    }
  }

  // MÉTODOS AUXILIARES
  private applyFilters(query: any, filters: SearchQuery['filters']): any {
    if (filters?.category) {
      query = query.in('metadata->category', filters.category);
    }
    
    if (filters?.source) {
      query = query.in('metadata->source', filters.source);
    }
    
    if (filters?.type) {
      query = query.in('metadata->type', filters.type);
    }
    
    if (filters?.dateRange) {
      query = query
        .gte('created_at', filters.dateRange.start.toISOString())
        .lte('created_at', filters.dateRange.end.toISOString());
    }
    
    return query;
  }

  private fuseSearchResults(results: SearchResult[], limit: number): SearchResult[] {
    // Reciprocal Rank Fusion (RRF)
    const fusedScores = new Map<string, { result: SearchResult; score: number }>();
    
    // Agrupar por tipo de busca
    const vectorResults = results.filter(r => r.searchType === 'vector');
    const textResults = results.filter(r => r.searchType === 'text');
    
    // Processar resultados vetoriais
    vectorResults.forEach((result, index) => {
      const rrf = 1 / (60 + index + 1);
      const existing = fusedScores.get(result.chunk.id);
      fusedScores.set(result.chunk.id, {
        result,
        score: (existing?.score || 0) + rrf
      });
    });
    
    // Processar resultados textuais
    textResults.forEach((result, index) => {
      const rrf = 1 / (60 + index + 1);
      const existing = fusedScores.get(result.chunk.id);
      fusedScores.set(result.chunk.id, {
        result,
        score: (existing?.score || 0) + rrf
      });
    });
    
    // Ordenar e retornar
    return Array.from(fusedScores.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(({ result, score }, index) => ({
        ...result,
        score,
        rank: index + 1
      }));
  }

  private indexChunkForBM25(chunk: DocumentChunk): void {
    // Implementação simplificada do índice BM25
    const terms = chunk.content.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(term => term.length > 2);
    
    this.bm25Index.set(chunk.id, {
      terms,
      termFreq: this.calculateTermFrequency(terms),
      docLength: terms.length
    });
  }

  private updateBM25Index(chunkId: string, content: string): void {
    const terms = content.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(term => term.length > 2);
    
    this.bm25Index.set(chunkId, {
      terms,
      termFreq: this.calculateTermFrequency(terms),
      docLength: terms.length
    });
  }

  private calculateTermFrequency(terms: string[]): Record<string, number> {
    const freq: Record<string, number> = {};
    terms.forEach(term => {
      freq[term] = (freq[term] || 0) + 1;
    });
    return freq;
  }

  private calculateBM25Score(query: string, content: string): number {
    // Implementação simplificada do BM25
    const queryTerms = query.toLowerCase().split(/\s+/);
    const contentTerms = content.toLowerCase().split(/\s+/);
    
    let score = 0;
    const k1 = 1.2;
    const b = 0.75;
    const avgDocLength = 100; // Aproximação
    
    queryTerms.forEach(term => {
      const termFreq = (contentTerms.filter(t => t === term).length);
      if (termFreq > 0) {
        const idf = Math.log((1000 + 1) / (termFreq + 1)); // Aproximação
        const tf = (termFreq * (k1 + 1)) / (termFreq + k1 * (1 - b + b * (contentTerms.length / avgDocLength)));
        score += idf * tf;
      }
    });
    
    return score;
  }

  private getEmptyStats(): IndexStats {
    return {
      totalDocuments: 0,
      totalChunks: 0,
      indexSize: 0,
      lastUpdated: new Date(),
      categories: {},
      sources: {}
    };
  }

  // CACHE
  private getCacheKey(query: SearchQuery): string {
    return JSON.stringify({
      vector: query.vector?.slice(0, 10), // Apenas primeiros 10 elementos
      text: query.text,
      filters: query.filters,
      limit: query.limit
    });
  }

  private getFromCache(key: string): SearchResult[] | null {
    if (!this.config.enableCache) return null;
    return this.cache.get(key) || null;
  }

  private setCache(key: string, results: SearchResult[]): void {
    if (!this.config.enableCache) return;
    
    if (this.cache.size >= this.config.cacheSize) {
      // Remover entrada mais antiga
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, results);
  }
}
