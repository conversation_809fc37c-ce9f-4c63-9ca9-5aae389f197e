// Sistema de Ingestão e Pré-processamento para o Gabinete da Vereadora
import * as pdfjsLib from 'pdfjs-dist';

// Configurar worker do PDF.js
pdfjsLib.GlobalWorkerOptions.workerSrc = '/node_modules/pdfjs-dist/build/pdf.worker.min.js';

export interface IngestionConfig {
  supportedFormats: string[];
  maxFileSize: number; // em MB
  extractMetadata: boolean;
  cleanContent: boolean;
  detectLanguage: boolean;
}

export interface ProcessedDocument {
  id: string;
  originalName: string;
  content: string;
  metadata: {
    type: 'pdf' | 'docx' | 'txt' | 'xlsx' | 'csv' | 'image' | 'audio' | 'video' | 'api';
    size: number;
    pages?: number;
    author?: string;
    title?: string;
    subject?: string;
    keywords?: string[];
    createdDate?: Date;
    modifiedDate?: Date;
    language?: string;
    category?: string;
    tags?: string[];
    source: string;
    processingDate: Date;
  };
  extractedData?: {
    tables?: any[];
    images?: any[];
    links?: string[];
    entities?: any[];
  };
}

export interface APISource {
  name: string;
  url: string;
  method: 'GET' | 'POST';
  headers?: Record<string, string>;
  params?: Record<string, any>;
  dataPath?: string; // JSONPath para extrair dados específicos
  updateFrequency: 'realtime' | 'hourly' | 'daily' | 'weekly';
}

export class IngestionService {
  private config: IngestionConfig;
  private apiSources: Map<string, APISource> = new Map();

  constructor(config: IngestionConfig) {
    this.config = config;
    this.setupDefaultAPISources();
  }

  // INGESTÃO DE DOCUMENTOS
  async processFile(file: File): Promise<ProcessedDocument> {
    console.log(`[Ingestão] Processando arquivo: ${file.name}`);
    
    // Validar arquivo
    this.validateFile(file);
    
    // Determinar tipo e processar
    const fileType = this.detectFileType(file);
    let content = '';
    let extractedData: any = {};
    
    switch (fileType) {
      case 'pdf':
        const pdfResult = await this.processPDF(file);
        content = pdfResult.content;
        extractedData = pdfResult.extractedData;
        break;
        
      case 'txt':
        content = await this.processTextFile(file);
        break;
        
      case 'xlsx':
      case 'csv':
        const spreadsheetResult = await this.processSpreadsheet(file);
        content = spreadsheetResult.content;
        extractedData = spreadsheetResult.extractedData;
        break;
        
      case 'docx':
        content = await this.processWordDocument(file);
        break;
        
      case 'image':
        const imageResult = await this.processImage(file);
        content = imageResult.content;
        extractedData = imageResult.extractedData;
        break;
        
      default:
        throw new Error(`Tipo de arquivo não suportado: ${fileType}`);
    }
    
    // Limpeza e normalização
    if (this.config.cleanContent) {
      content = this.cleanContent(content);
    }
    
    // Extração de metadados
    const metadata = await this.extractMetadata(file, fileType, content);
    
    // Detecção de idioma
    if (this.config.detectLanguage) {
      metadata.language = this.detectLanguage(content);
    }
    
    return {
      id: this.generateDocumentId(),
      originalName: file.name,
      content,
      metadata: {
        ...metadata,
        type: fileType as any,
        size: file.size,
        source: `upload_${file.name}`,
        processingDate: new Date()
      },
      extractedData
    };
  }

  // PROCESSAMENTO DE PDF
  private async processPDF(file: File): Promise<{content: string, extractedData: any}> {
    const arrayBuffer = await file.arrayBuffer();
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    let fullText = '';
    const extractedData: any = {
      pages: pdf.numPages,
      images: [],
      links: []
    };
    
    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      
      // Extrair texto
      const textContent = await page.getTextContent();
      const pageText = textContent.items
        .map((item: any) => item.str)
        .join(' ');
      
      fullText += `\n\n--- Página ${pageNum} ---\n${pageText}`;
      
      // Extrair anotações e links
      const annotations = await page.getAnnotations();
      for (const annotation of annotations) {
        if (annotation.url) {
          extractedData.links.push(annotation.url);
        }
      }
    }
    
    return {
      content: fullText.trim(),
      extractedData
    };
  }

  // PROCESSAMENTO DE PLANILHAS
  private async processSpreadsheet(file: File): Promise<{content: string, extractedData: any}> {
    // Para implementação completa, usar bibliotecas como xlsx ou csv-parser
    const text = await file.text();
    
    let content = '';
    const extractedData: any = {
      tables: [],
      rows: 0,
      columns: 0
    };
    
    if (file.name.endsWith('.csv')) {
      const lines = text.split('\n');
      extractedData.rows = lines.length;
      
      // Converter CSV para texto legível
      const headers = lines[0]?.split(',') || [];
      extractedData.columns = headers.length;
      
      content = `Planilha CSV com ${lines.length} linhas e ${headers.length} colunas.\n\n`;
      content += `Cabeçalhos: ${headers.join(', ')}\n\n`;
      
      // Adicionar algumas linhas de exemplo
      for (let i = 1; i < Math.min(6, lines.length); i++) {
        const row = lines[i].split(',');
        content += `Linha ${i}: ${row.join(' | ')}\n`;
      }
      
      extractedData.tables.push({
        headers,
        rows: lines.slice(1).map(line => line.split(','))
      });
    }
    
    return { content, extractedData };
  }

  // PROCESSAMENTO DE IMAGENS (OCR)
  private async processImage(file: File): Promise<{content: string, extractedData: any}> {
    // Para implementação completa, integrar com serviços de OCR como Google Vision API
    console.log(`[Ingestão] Processando imagem: ${file.name}`);
    
    const extractedData: any = {
      imageType: file.type,
      ocrText: '',
      detectedObjects: [],
      faces: []
    };
    
    // Mock de OCR - em produção, usar serviço real
    const content = `Imagem processada: ${file.name}. 
    Tipo: ${file.type}. 
    Tamanho: ${(file.size / 1024).toFixed(2)} KB.
    [OCR seria aplicado aqui para extrair texto da imagem]`;
    
    return { content, extractedData };
  }

  // PROCESSAMENTO DE TEXTO SIMPLES
  private async processTextFile(file: File): Promise<string> {
    return await file.text();
  }

  // PROCESSAMENTO DE DOCUMENTOS WORD
  private async processWordDocument(file: File): Promise<string> {
    // Para implementação completa, usar biblioteca como mammoth.js
    console.log(`[Ingestão] Processando documento Word: ${file.name}`);
    
    // Mock - em produção, extrair texto real do DOCX
    return `Documento Word processado: ${file.name}. 
    [Conteúdo seria extraído aqui usando mammoth.js ou similar]`;
  }

  // INGESTÃO DE APIs
  async ingestFromAPI(sourceName: string): Promise<ProcessedDocument[]> {
    const source = this.apiSources.get(sourceName);
    if (!source) {
      throw new Error(`Fonte de API não encontrada: ${sourceName}`);
    }
    
    console.log(`[Ingestão] Coletando dados da API: ${source.name}`);
    
    try {
      const response = await fetch(source.url, {
        method: source.method,
        headers: source.headers || {},
        body: source.method === 'POST' ? JSON.stringify(source.params) : undefined
      });
      
      if (!response.ok) {
        throw new Error(`Erro na API ${source.name}: ${response.status}`);
      }
      
      const data = await response.json();
      
      // Extrair dados usando JSONPath se especificado
      const extractedData = source.dataPath ? this.extractDataByPath(data, source.dataPath) : data;
      
      // Converter dados para documentos
      return this.convertAPIDataToDocuments(extractedData, source);
      
    } catch (error) {
      console.error(`[Ingestão] Erro ao processar API ${source.name}:`, error);
      throw error;
    }
  }

  // CONFIGURAÇÃO DE FONTES DE API PADRÃO
  private setupDefaultAPISources(): void {
    // API da Câmara Municipal (exemplo)
    this.apiSources.set('camara_projetos', {
      name: 'Projetos da Câmara',
      url: 'https://api.camara.parnamirim.rn.gov.br/projetos',
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      dataPath: '$.projetos',
      updateFrequency: 'daily'
    });
    
    // API de Transparência (exemplo)
    this.apiSources.set('transparencia', {
      name: 'Portal da Transparência',
      url: 'https://transparencia.parnamirim.rn.gov.br/api/gastos',
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      dataPath: '$.gastos',
      updateFrequency: 'weekly'
    });
    
    // RSS de Notícias (exemplo)
    this.apiSources.set('noticias_municipio', {
      name: 'Notícias do Município',
      url: 'https://parnamirim.rn.gov.br/feed/rss',
      method: 'GET',
      updateFrequency: 'hourly'
    });
  }

  // MÉTODOS AUXILIARES
  private validateFile(file: File): void {
    // Verificar tamanho
    const maxSizeBytes = this.config.maxFileSize * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      throw new Error(`Arquivo muito grande. Máximo: ${this.config.maxFileSize}MB`);
    }
    
    // Verificar formato
    const fileType = this.detectFileType(file);
    if (!this.config.supportedFormats.includes(fileType)) {
      throw new Error(`Formato não suportado: ${fileType}`);
    }
  }

  private detectFileType(file: File): string {
    const extension = file.name.split('.').pop()?.toLowerCase() || '';
    
    const typeMap: Record<string, string> = {
      'pdf': 'pdf',
      'txt': 'txt',
      'docx': 'docx',
      'doc': 'docx',
      'xlsx': 'xlsx',
      'xls': 'xlsx',
      'csv': 'csv',
      'jpg': 'image',
      'jpeg': 'image',
      'png': 'image',
      'gif': 'image',
      'mp3': 'audio',
      'wav': 'audio',
      'mp4': 'video',
      'avi': 'video'
    };
    
    return typeMap[extension] || 'unknown';
  }

  private cleanContent(content: string): string {
    return content
      // Remover caracteres especiais desnecessários
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/g, '')
      // Normalizar espaços em branco
      .replace(/\s+/g, ' ')
      // Normalizar quebras de linha
      .replace(/\n\s*\n\s*\n/g, '\n\n')
      // Remover espaços no início e fim
      .trim();
  }

  private async extractMetadata(file: File, fileType: string, content: string): Promise<any> {
    const metadata: any = {
      type: fileType,
      size: file.size,
      processingDate: new Date()
    };
    
    if (this.config.extractMetadata) {
      // Extrair título do conteúdo
      const lines = content.split('\n').filter(line => line.trim());
      if (lines.length > 0) {
        metadata.title = lines[0].substring(0, 100);
      }
      
      // Detectar palavras-chave
      metadata.keywords = this.extractKeywords(content);
      
      // Categorizar automaticamente
      metadata.category = this.categorizeContent(content);
    }
    
    return metadata;
  }

  private detectLanguage(content: string): string {
    // Implementação simples - em produção, usar biblioteca de detecção de idioma
    const portugueseWords = ['que', 'para', 'com', 'uma', 'por', 'não', 'dos', 'das', 'como', 'mais'];
    const englishWords = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had'];
    
    const words = content.toLowerCase().split(/\s+/).slice(0, 100);
    
    let ptCount = 0;
    let enCount = 0;
    
    words.forEach(word => {
      if (portugueseWords.includes(word)) ptCount++;
      if (englishWords.includes(word)) enCount++;
    });
    
    return ptCount > enCount ? 'pt-BR' : 'en';
  }

  private extractKeywords(content: string): string[] {
    // Implementação simples de extração de palavras-chave
    const words = content.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    const frequency: Record<string, number> = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });
    
    return Object.entries(frequency)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);
  }

  private categorizeContent(content: string): string {
    const categories = {
      'legislacao': ['lei', 'projeto', 'emenda', 'decreto', 'portaria', 'resolução'],
      'orcamento': ['orçamento', 'verba', 'recurso', 'gasto', 'despesa', 'receita'],
      'social': ['assistência', 'social', 'família', 'criança', 'idoso', 'deficiente'],
      'saude': ['saúde', 'hospital', 'posto', 'médico', 'enfermeiro', 'medicamento'],
      'educacao': ['educação', 'escola', 'professor', 'aluno', 'ensino', 'creche'],
      'infraestrutura': ['obra', 'rua', 'asfalto', 'saneamento', 'água', 'esgoto']
    };
    
    const contentLower = content.toLowerCase();
    let bestCategory = 'geral';
    let maxScore = 0;
    
    Object.entries(categories).forEach(([category, keywords]) => {
      const score = keywords.reduce((sum, keyword) => {
        const matches = (contentLower.match(new RegExp(keyword, 'g')) || []).length;
        return sum + matches;
      }, 0);
      
      if (score > maxScore) {
        maxScore = score;
        bestCategory = category;
      }
    });
    
    return bestCategory;
  }

  private extractDataByPath(data: any, path: string): any {
    // Implementação simples de JSONPath - em produção, usar biblioteca específica
    return data; // Por enquanto, retornar dados completos
  }

  private convertAPIDataToDocuments(data: any, source: APISource): ProcessedDocument[] {
    // Converter dados da API em documentos processados
    const documents: ProcessedDocument[] = [];
    
    if (Array.isArray(data)) {
      data.forEach((item, index) => {
        documents.push({
          id: this.generateDocumentId(),
          originalName: `${source.name}_${index}`,
          content: JSON.stringify(item, null, 2),
          metadata: {
            type: 'api',
            size: JSON.stringify(item).length,
            source: source.name,
            category: 'api_data',
            processingDate: new Date()
          }
        });
      });
    } else {
      documents.push({
        id: this.generateDocumentId(),
        originalName: source.name,
        content: JSON.stringify(data, null, 2),
        metadata: {
          type: 'api',
          size: JSON.stringify(data).length,
          source: source.name,
          category: 'api_data',
          processingDate: new Date()
        }
      });
    }
    
    return documents;
  }

  private generateDocumentId(): string {
    return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // MÉTODOS PÚBLICOS PARA CONFIGURAÇÃO
  addAPISource(name: string, source: APISource): void {
    this.apiSources.set(name, source);
  }

  removeAPISource(name: string): void {
    this.apiSources.delete(name);
  }

  listAPISources(): string[] {
    return Array.from(this.apiSources.keys());
  }
}
